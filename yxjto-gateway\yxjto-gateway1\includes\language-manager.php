<?php
/**
 * WordPress Multi Database Plugin - Language Manager
 * 语言文件管理和自动生成功能
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 语言管理器类
 */
class WP_Multi_Database_Language_Manager {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 语言目录路径
     */
    private $languages_dir;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->languages_dir = YXJTO_GATEWAY_PLUGIN_DIR . 'languages/';

        // 确保languages目录存在
        if (!is_dir($this->languages_dir)) {
            wp_mkdir_p($this->languages_dir);
            $this->log_info("Created languages directory: {$this->languages_dir}");
        }
    }
    
    /**
     * 自动生成.mo文件
     */
    public function auto_generate_mo_file($locale) {
        $po_file = $this->languages_dir . 'yxjto-gateway-' . $locale . '.po';
        $mo_file = $this->languages_dir . 'yxjto-gateway-' . $locale . '.mo';
        
        // 如果.mo文件不存在但.po文件存在，则自动生成.mo文件
        if (!file_exists($mo_file) && file_exists($po_file)) {
            $this->log_info("Auto-generating .mo file for locale: {$locale}");
            
            if ($this->convert_po_to_mo($po_file, $mo_file)) {
                $this->log_info("Successfully generated .mo file: {$mo_file}");
                return true;
            } else {
                $this->log_error("Failed to generate .mo file: {$mo_file}");
                return false;
            }
        }
        
        // 如果都不存在，但是是支持的语言，则创建基础文件
        if (!file_exists($mo_file) && !file_exists($po_file)) {
            return $this->create_language_files_if_supported($locale);
        }
        
        return file_exists($mo_file);
    }
    
    /**
     * 将.po文件转换为.mo文件
     */
    public function convert_po_to_mo($po_file, $mo_file) {
        try {
            $po_content = file_get_contents($po_file);
            if (!$po_content) {
                return false;
            }
            
            $translations = $this->parse_po_file($po_content);
            if (empty($translations)) {
                return false;
            }
            
            return $this->generate_mo_file($translations, $mo_file);
            
        } catch (Exception $e) {
            $this->log_error("Error converting PO to MO: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 解析.po文件内容
     */
    private function parse_po_file($content) {
        $translations = [];
        $lines = explode("\n", $content);
        $current_msgid = '';
        $current_msgstr = '';
        $in_msgid = false;
        $in_msgstr = false;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // 跳过注释和空行
            if (empty($line) || $line[0] === '#') {
                continue;
            }
            
            // 处理msgid
            if (strpos($line, 'msgid ') === 0) {
                // 保存上一个翻译
                if (!empty($current_msgid) && !empty($current_msgstr)) {
                    $translations[$current_msgid] = $current_msgstr;
                }
                
                $current_msgid = $this->extract_quoted_string($line);
                $current_msgstr = '';
                $in_msgid = true;
                $in_msgstr = false;
            }
            // 处理msgstr
            elseif (strpos($line, 'msgstr ') === 0) {
                $current_msgstr = $this->extract_quoted_string($line);
                $in_msgid = false;
                $in_msgstr = true;
            }
            // 处理多行字符串
            elseif ($line[0] === '"' && $line[strlen($line) - 1] === '"') {
                $string_content = $this->extract_quoted_string($line);
                if ($in_msgid) {
                    $current_msgid .= $string_content;
                } elseif ($in_msgstr) {
                    $current_msgstr .= $string_content;
                }
            }
        }
        
        // 保存最后一个翻译
        if (!empty($current_msgid) && !empty($current_msgstr)) {
            $translations[$current_msgid] = $current_msgstr;
        }
        
        return $translations;
    }
    
    /**
     * 提取引号内的字符串
     */
    private function extract_quoted_string($line) {
        if (preg_match('/"(.*)"/', $line, $matches)) {
            return stripcslashes($matches[1]);
        }
        return '';
    }
    
    /**
     * 生成.mo文件
     */
    public function generate_mo_file($translations, $filename) {
        try {
            $mo = '';
            
            // MO文件头部
            $mo .= pack('V', 0x950412de); // 魔数
            $mo .= pack('V', 0); // 版本
            $mo .= pack('V', count($translations)); // 字符串数量
            $mo .= pack('V', 28); // 原始字符串偏移
            $mo .= pack('V', 28 + count($translations) * 8); // 翻译字符串偏移
            $mo .= pack('V', 0); // 哈希表大小
            $mo .= pack('V', 0); // 哈希表偏移
            
            $keys = array_keys($translations);
            $values = array_values($translations);
            
            // 计算字符串偏移
            $key_offsets = [];
            $value_offsets = [];
            $current_offset = 28 + count($translations) * 16;
            
            // 原始字符串偏移
            foreach ($keys as $key) {
                $key_offsets[] = $current_offset;
                $current_offset += strlen($key) + 1;
            }
            
            // 翻译字符串偏移
            foreach ($values as $value) {
                $value_offsets[] = $current_offset;
                $current_offset += strlen($value) + 1;
            }
            
            // 写入偏移表
            for ($i = 0; $i < count($translations); $i++) {
                $mo .= pack('V', strlen($keys[$i])); // 长度
                $mo .= pack('V', $key_offsets[$i]); // 偏移
            }
            
            for ($i = 0; $i < count($translations); $i++) {
                $mo .= pack('V', strlen($values[$i])); // 长度
                $mo .= pack('V', $value_offsets[$i]); // 偏移
            }
            
            // 写入字符串
            foreach ($keys as $key) {
                $mo .= $key . "\0";
            }
            
            foreach ($values as $value) {
                $mo .= $value . "\0";
            }
            
            // 确保目录存在
            $dir = dirname($filename);
            if (!is_dir($dir)) {
                wp_mkdir_p($dir);
            }

            $result = file_put_contents($filename, $mo);
            if ($result === false) {
                $this->log_error("Failed to write MO file: {$filename}");
                return false;
            }

            $this->log_info("Successfully created MO file: {$filename}");
            return true;
            
        } catch (Exception $e) {
            $this->log_error("Error generating MO file: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 为支持的语言创建基础文件
     */
    private function create_language_files_if_supported($locale) {
        $supported_languages = $this->get_supported_languages();
        
        if (isset($supported_languages[$locale])) {
            $this->log_info("Creating language files for supported locale: {$locale}");
            
            // 创建.po文件
            $po_file = $this->languages_dir . 'yxjto-gateway-' . $locale . '.po';
            $this->create_po_file($po_file, $locale, $supported_languages[$locale]);

            // 生成.mo文件
            $mo_file = $this->languages_dir . 'yxjto-gateway-' . $locale . '.mo';
            return $this->generate_mo_file($supported_languages[$locale], $mo_file);
        }
        
        return false;
    }
    
    /**
     * 获取支持的语言列表
     */
    public function get_supported_languages() {
        return [
            'zh_CN' => $this->get_chinese_translations(),
            'zh_TW' => $this->get_traditional_chinese_translations(),
            'ja' => $this->get_japanese_translations(),
            'ko' => $this->get_korean_translations()
        ];
    }
    
    /**
     * 创建.po文件
     */
    private function create_po_file($filename, $locale, $translations) {
        try {
            // 确保目录存在
            $dir = dirname($filename);
            if (!is_dir($dir)) {
                wp_mkdir_p($dir);
            }

            $po_content = $this->generate_po_header($locale);

            foreach ($translations as $msgid => $msgstr) {
                $po_content .= "\nmsgid \"" . addcslashes($msgid, '"\\') . "\"\n";
                $po_content .= "msgstr \"" . addcslashes($msgstr, '"\\') . "\"\n";
            }

            $result = file_put_contents($filename, $po_content);
            if ($result === false) {
                $this->log_error("Failed to create PO file: {$filename}");
                return false;
            }

            $this->log_info("Successfully created PO file: {$filename}");
            return true;

        } catch (Exception $e) {
            $this->log_error("Error creating PO file {$filename}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成.po文件头部
     */
    private function generate_po_header($locale) {
        $language_names = [
            'zh_CN' => 'Chinese (Simplified)',
            'zh_TW' => 'Chinese (Traditional)',
            'ja' => 'Japanese',
            'ko' => 'Korean'
        ];
        
        $language_name = isset($language_names[$locale]) ? $language_names[$locale] : $locale;
        
        return "# WordPress Multi Database Plugin - {$language_name}\n" .
               "# Copyright (C) " . date('Y') . "\n" .
               "msgid \"\"\n" .
               "msgstr \"\"\n" .
               "\"Project-Id-Version: WordPress Multi Database Plugin 1.0.0\\n\"\n" .
               "\"Report-Msgid-Bugs-To: \\n\"\n" .
               "\"POT-Creation-Date: " . date('Y-m-d H:i') . "+0000\\n\"\n" .
               "\"PO-Revision-Date: " . date('Y-m-d H:i') . "+0000\\n\"\n" .
               "\"Last-Translator: \\n\"\n" .
               "\"Language-Team: {$language_name}\\n\"\n" .
               "\"Language: {$locale}\\n\"\n" .
               "\"MIME-Version: 1.0\\n\"\n" .
               "\"Content-Type: text/plain; charset=UTF-8\\n\"\n" .
               "\"Content-Transfer-Encoding: 8bit\\n\"\n" .
               "\"Plural-Forms: nplurals=1; plural=0;\\n\"\n";
    }

    /**
     * 获取中文翻译
     */
    public function get_chinese_translations() {
        return [
            'Multi Database Settings' => '多数据库设置',
            'Multi Database' => '多数据库',
            'Insufficient permissions' => '权限不足',
            'Testing connection...' => '正在测试连接...',
            'Connection successful!' => '连接成功！',
            'Connection failed!' => '连接失败！',
            'Switching database...' => '正在切换数据库...',
            'Database switched successfully!' => '数据库切换成功！',
            'Database switch failed!' => '数据库切换失败！',
            'Databases' => '数据库',
            'IP Rules' => 'IP规则',
            'URL Rules' => 'URL规则',
            'Status' => '状态',
            'Logs' => '日志',
            'Database Configurations' => '数据库配置',
            'Configure your database connections here.' => '在这里配置您的数据库连接。',
            'Current Database' => '当前数据库',
            'Test Connection' => '测试连接',
            'Switch to This' => '切换到此数据库',
            'Remove' => '删除',
            'Name' => '名称',
            'Host' => '主机',
            'Database' => '数据库',
            'Username' => '用户名',
            'Password' => '密码',
            'Charset' => '字符集',
            'Enabled' => '启用',
            'Add New Database' => '添加新数据库',
            'IP-based Database Switching Rules' => '基于IP的数据库切换规则',
            'Configure rules to automatically switch databases based on visitor IP addresses.' => '配置规则以根据访问者IP地址自动切换数据库。',
            'Rule %d' => '规则 %d',
            'IP Range' => 'IP范围',
            'Examples: ***********, ***********/24, ***********-***********00' => '示例：***********, ***********/24, ***********-***********00',
            'Add IP Rule' => '添加IP规则',
            'URL Parameter Database Switching Rules' => '基于URL参数的数据库切换规则',
            'Configure rules to switch databases based on URL parameters.' => '配置规则以根据URL参数切换数据库。',
            'Parameter Name' => '参数名称',
            'Parameter Value' => '参数值',
            'Add URL Rule' => '添加URL规则',
            'System Status' => '系统状态',
            'Monitor the status of your database connections and system performance.' => '监控您的数据库连接状态和系统性能。',
            'Current Status' => '当前状态',
            'Active Database' => '活动数据库',
            'Plugin Version' => '插件版本',
            'WordPress Version' => 'WordPress版本',
            'PHP Version' => 'PHP版本',
            'Database Connections' => '数据库连接',
            'Disabled' => '已禁用',
            'Switching Rules' => '切换规则',
            'Active IP Rules' => '活动IP规则',
            'Active URL Rules' => '活动URL规则',
            'Performance' => '性能',
            'Memory Usage' => '内存使用',
            'Peak Memory' => '峰值内存',
            'Debug Mode' => '调试模式',
            'Activity Logs' => '活动日志',
            'View database switching activity and system events.' => '查看数据库切换活动和系统事件。',
            'Logs table not found. Please deactivate and reactivate the plugin.' => '未找到日志表。请停用并重新激活插件。',
            'No logs found.' => '未找到日志。',
            'Timestamp' => '时间戳',
            'Action' => '操作',
            'From Database' => '源数据库',
            'To Database' => '目标数据库',
            'IP Address' => 'IP地址',
            'Message' => '消息',
            'Success' => '成功',
            'Failed' => '失败',
            'Refresh Logs' => '刷新日志',
            'Clear Logs' => '清除日志',
            'Are you sure you want to clear all logs?' => '您确定要清除所有日志吗？',
            'Failed to clear logs.' => '清除日志失败。',
            'Settings saved successfully!' => '设置保存成功！',
            'Logs cleared successfully!' => '日志清除成功！',
            'Failed to clear logs!' => '清除日志失败！',
            'Select Database' => '选择数据库',
            'Are you sure you want to remove this database configuration?' => '您确定要删除此数据库配置吗？',
            'Are you sure you want to remove this IP rule?' => '您确定要删除此IP规则吗？',
            'Are you sure you want to remove this URL rule?' => '您确定要删除此URL规则吗？',
            'Are you sure you want to switch to this database?' => '您确定要切换到此数据库吗？',
            'New Database' => '新数据库',
            'Priority Level: High' => '优先级：高',
            'Priority Level: Medium' => '优先级：中',
            'High' => '高',
            'Medium' => '中',
            'Low' => '低',
            'URL parameter rules have the highest priority. They will override IP-based rules when both conditions are met.' => 'URL参数规则具有最高优先级。当两个条件都满足时，URL参数规则将覆盖基于IP的规则。',
            'IP rules have lower priority than URL parameter rules. If both rules match, URL parameter rules will take precedence.' => 'IP规则的优先级低于URL参数规则。如果两个规则都匹配，URL参数规则将优先生效。',
            'Security check failed' => '安全检查失败',
            'Cannot delete the default database configuration.' => '无法删除默认数据库配置。',
            'Database configuration deleted successfully.' => '数据库配置删除成功。',
            'Failed to delete database configuration.' => '删除数据库配置失败。',
            'URL rule deleted successfully.' => 'URL规则删除成功。',
            'Failed to delete URL rule.' => '删除URL规则失败。',
            'IP rule deleted successfully.' => 'IP规则删除成功。',
            'Failed to delete IP rule.' => '删除IP规则失败。',
            'Crawler Detection' => '爬虫检测',
            'Highest' => '最高',
            'Crawler Detection Settings' => '爬虫检测设置',
            'Configure crawler detection to automatically use the default database when search engine crawlers visit your site. This ensures consistent SEO indexing.' => '配置爬虫检测以在搜索引擎爬虫访问您的网站时自动使用默认数据库。这确保了一致的SEO索引。',
            'General Settings' => '常规设置',
            'Enable Crawler Detection' => '启用爬虫检测',
            'Automatically detect crawlers and use default database' => '自动检测爬虫并使用默认数据库',
            'When enabled, detected crawlers will always access the default database regardless of other rules.' => '启用后，检测到的爬虫将始终访问默认数据库，无论其他规则如何。',
            'Log Crawler Visits' => '记录爬虫访问',
            'Log crawler visits for monitoring' => '记录爬虫访问以便监控',
            'Keep a log of crawler visits to help with SEO monitoring and debugging.' => '保留爬虫访问日志以帮助SEO监控和调试。',
            'Supported Crawlers' => '支持的爬虫',
            'The following crawlers are automatically detected. You can enable/disable detection for specific crawlers.' => '以下爬虫会被自动检测。您可以启用/禁用特定爬虫的检测。',
            'Crawler Name' => '爬虫名称',
            'User Agent Patterns' => '用户代理模式',
            'Description' => '描述',
            'Custom Crawler Patterns' => '自定义爬虫模式',
            'Add custom User-Agent patterns to detect additional crawlers. One pattern per line.' => '添加自定义用户代理模式以检测其他爬虫。每行一个模式。',
            'Custom Patterns' => '自定义模式',
            'Examples: MyBot, CustomCrawler, SpecialSpider' => '示例：MyBot, CustomCrawler, SpecialSpider',
            'Crawler detection settings saved successfully!' => '爬虫检测设置保存成功！',
            'Failed to save crawler detection settings.' => '保存爬虫检测设置失败。',
            'Enable IP Detection' => '启用IP检测',
            'Detect crawlers by IP address ranges' => '通过IP地址段检测爬虫',
            'Use known IP ranges to identify crawlers. This is more reliable than User-Agent detection.' => '使用已知IP段识别爬虫。这比User-Agent检测更可靠。',
            'Enable IP Verification' => '启用IP验证',
            'Verify User-Agent claims with IP address' => '用IP地址验证User-Agent声明',
            'When a User-Agent claims to be a crawler, verify that the IP address matches known crawler IP ranges.' => '当User-Agent声称是爬虫时，验证IP地址是否匹配已知的爬虫IP段。',
            'Enable Reverse DNS Verification' => '启用反向DNS验证',
            'Verify crawlers using reverse DNS lookup' => '使用反向DNS查询验证爬虫',
            'Perform reverse DNS lookup to verify that the IP truly belongs to the claimed crawler. Most secure but slower.' => '执行反向DNS查询以验证IP确实属于声称的爬虫。最安全但较慢。',
            'IP Address Management' => 'IP地址管理',
            'Manage IP address ranges for crawler detection. The plugin includes built-in IP ranges for major crawlers.' => '管理用于爬虫检测的IP地址段。插件包含主要爬虫的内置IP段。',
            'Built-in IP Ranges Statistics' => '内置IP段统计',
            'IP Ranges Count' => 'IP段数量',
            'Sample IP Ranges' => '示例IP段',
            'Custom IP Ranges' => '自定义IP段',
            'Add custom IP ranges for additional crawler detection. Use CIDR notation (e.g., ***********/24) or single IPs.' => '添加自定义IP段以进行额外的爬虫检测。使用CIDR表示法（如***********/24）或单个IP。',
            'Examples: ***********/24, ********, ***********/24' => '示例：***********/24, ********, ***********/24'
        ];
    }

    /**
     * 获取繁体中文翻译
     */
    public function get_traditional_chinese_translations() {
        return [
            'Multi Database Settings' => '多資料庫設定',
            'Multi Database' => '多資料庫',
            'Insufficient permissions' => '權限不足',
            'Testing connection...' => '正在測試連接...',
            'Connection successful!' => '連接成功！',
            'Connection failed!' => '連接失敗！',
            'Switching database...' => '正在切換資料庫...',
            'Database switched successfully!' => '資料庫切換成功！',
            'Database switch failed!' => '資料庫切換失敗！',
            'Databases' => '資料庫',
            'IP Rules' => 'IP規則',
            'URL Rules' => 'URL規則',
            'Status' => '狀態',
            'Logs' => '日誌',
            'Database Configurations' => '資料庫配置',
            'Configure your database connections here.' => '在這裡配置您的資料庫連接。',
            'Current Database' => '當前資料庫',
            'Test Connection' => '測試連接',
            'Switch to This' => '切換到此資料庫',
            'Remove' => '刪除',
            'Name' => '名稱',
            'Host' => '主機',
            'Database' => '資料庫',
            'Username' => '用戶名',
            'Password' => '密碼',
            'Charset' => '字符集',
            'Enabled' => '啟用',
            'Add New Database' => '添加新資料庫',
            'IP-based Database Switching Rules' => '基於IP的資料庫切換規則',
            'Configure rules to automatically switch databases based on visitor IP addresses.' => '配置規則以根據訪問者IP地址自動切換資料庫。',
            'Rule %d' => '規則 %d',
            'IP Range' => 'IP範圍',
            'Examples: ***********, ***********/24, ***********-***********00' => '示例：***********, ***********/24, ***********-***********00',
            'Add IP Rule' => '添加IP規則',
            'URL Parameter Database Switching Rules' => '基於URL參數的資料庫切換規則',
            'Configure rules to switch databases based on URL parameters.' => '配置規則以根據URL參數切換資料庫。',
            'Parameter Name' => '參數名稱',
            'Parameter Value' => '參數值',
            'Add URL Rule' => '添加URL規則',
            'System Status' => '系統狀態',
            'Monitor the status of your database connections and system performance.' => '監控您的資料庫連接狀態和系統性能。',
            'Current Status' => '當前狀態',
            'Active Database' => '活動資料庫',
            'Plugin Version' => '插件版本',
            'WordPress Version' => 'WordPress版本',
            'PHP Version' => 'PHP版本',
            'Database Connections' => '資料庫連接',
            'Disabled' => '已禁用',
            'Switching Rules' => '切換規則',
            'Active IP Rules' => '活動IP規則',
            'Active URL Rules' => '活動URL規則',
            'Performance' => '性能',
            'Memory Usage' => '內存使用',
            'Peak Memory' => '峰值內存',
            'Debug Mode' => '調試模式',
            'Activity Logs' => '活動日誌',
            'View database switching activity and system events.' => '查看資料庫切換活動和系統事件。',
            'Select Database' => '選擇資料庫',
            'New Database' => '新資料庫'
        ];
    }

    /**
     * 获取日语翻译
     */
    public function get_japanese_translations() {
        return [
            'Multi Database Settings' => 'マルチデータベース設定',
            'Multi Database' => 'マルチデータベース',
            'Insufficient permissions' => '権限が不足しています',
            'Testing connection...' => '接続をテスト中...',
            'Connection successful!' => '接続成功！',
            'Connection failed!' => '接続失敗！',
            'Switching database...' => 'データベースを切り替え中...',
            'Database switched successfully!' => 'データベースの切り替えが成功しました！',
            'Database switch failed!' => 'データベースの切り替えに失敗しました！',
            'Databases' => 'データベース',
            'IP Rules' => 'IPルール',
            'URL Rules' => 'URLルール',
            'Status' => 'ステータス',
            'Logs' => 'ログ',
            'Database Configurations' => 'データベース設定',
            'Configure your database connections here.' => 'ここでデータベース接続を設定してください。',
            'Current Database' => '現在のデータベース',
            'Test Connection' => '接続テスト',
            'Switch to This' => 'これに切り替え',
            'Remove' => '削除',
            'Name' => '名前',
            'Host' => 'ホスト',
            'Database' => 'データベース',
            'Username' => 'ユーザー名',
            'Password' => 'パスワード',
            'Charset' => '文字セット',
            'Enabled' => '有効',
            'Add New Database' => '新しいデータベースを追加',
            'IP-based Database Switching Rules' => 'IPベースのデータベース切り替えルール',
            'Configure rules to automatically switch databases based on visitor IP addresses.' => '訪問者のIPアドレスに基づいてデータベースを自動的に切り替えるルールを設定します。',
            'Rule %d' => 'ルール %d',
            'IP Range' => 'IP範囲',
            'Examples: ***********, ***********/24, ***********-***********00' => '例：***********, ***********/24, ***********-***********00',
            'Add IP Rule' => 'IPルールを追加',
            'URL Parameter Database Switching Rules' => 'URLパラメータベースのデータベース切り替えルール',
            'Configure rules to switch databases based on URL parameters.' => 'URLパラメータに基づいてデータベースを切り替えるルールを設定します。',
            'Parameter Name' => 'パラメータ名',
            'Parameter Value' => 'パラメータ値',
            'Add URL Rule' => 'URLルールを追加',
            'System Status' => 'システムステータス',
            'Monitor the status of your database connections and system performance.' => 'データベース接続とシステムパフォーマンスのステータスを監視します。',
            'Current Status' => '現在のステータス',
            'Active Database' => 'アクティブなデータベース',
            'Plugin Version' => 'プラグインバージョン',
            'WordPress Version' => 'WordPressバージョン',
            'PHP Version' => 'PHPバージョン',
            'Database Connections' => 'データベース接続',
            'Disabled' => '無効',
            'Switching Rules' => '切り替えルール',
            'Active IP Rules' => 'アクティブなIPルール',
            'Active URL Rules' => 'アクティブなURLルール',
            'Performance' => 'パフォーマンス',
            'Memory Usage' => 'メモリ使用量',
            'Peak Memory' => 'ピークメモリ',
            'Debug Mode' => 'デバッグモード',
            'Activity Logs' => 'アクティビティログ',
            'View database switching activity and system events.' => 'データベース切り替えアクティビティとシステムイベントを表示します。',
            'Select Database' => 'データベースを選択',
            'New Database' => '新しいデータベース'
        ];
    }

    /**
     * 获取韩语翻译
     */
    public function get_korean_translations() {
        return [
            'Multi Database Settings' => '다중 데이터베이스 설정',
            'Multi Database' => '다중 데이터베이스',
            'Insufficient permissions' => '권한이 부족합니다',
            'Testing connection...' => '연결 테스트 중...',
            'Connection successful!' => '연결 성공!',
            'Connection failed!' => '연결 실패!',
            'Switching database...' => '데이터베이스 전환 중...',
            'Database switched successfully!' => '데이터베이스 전환 성공!',
            'Database switch failed!' => '데이터베이스 전환 실패!',
            'Databases' => '데이터베이스',
            'IP Rules' => 'IP 규칙',
            'URL Rules' => 'URL 규칙',
            'Status' => '상태',
            'Logs' => '로그',
            'Database Configurations' => '데이터베이스 구성',
            'Configure your database connections here.' => '여기서 데이터베이스 연결을 구성하세요.',
            'Current Database' => '현재 데이터베이스',
            'Test Connection' => '연결 테스트',
            'Switch to This' => '이것으로 전환',
            'Remove' => '제거',
            'Name' => '이름',
            'Host' => '호스트',
            'Database' => '데이터베이스',
            'Username' => '사용자명',
            'Password' => '비밀번호',
            'Charset' => '문자셋',
            'Enabled' => '활성화',
            'Add New Database' => '새 데이터베이스 추가',
            'IP-based Database Switching Rules' => 'IP 기반 데이터베이스 전환 규칙',
            'Configure rules to automatically switch databases based on visitor IP addresses.' => '방문자 IP 주소를 기반으로 데이터베이스를 자동으로 전환하는 규칙을 구성합니다.',
            'Rule %d' => '규칙 %d',
            'IP Range' => 'IP 범위',
            'Examples: ***********, ***********/24, ***********-***********00' => '예시: ***********, ***********/24, ***********-***********00',
            'Add IP Rule' => 'IP 규칙 추가',
            'URL Parameter Database Switching Rules' => 'URL 매개변수 기반 데이터베이스 전환 규칙',
            'Configure rules to switch databases based on URL parameters.' => 'URL 매개변수를 기반으로 데이터베이스를 전환하는 규칙을 구성합니다.',
            'Parameter Name' => '매개변수 이름',
            'Parameter Value' => '매개변수 값',
            'Add URL Rule' => 'URL 규칙 추가',
            'System Status' => '시스템 상태',
            'Monitor the status of your database connections and system performance.' => '데이터베이스 연결 상태와 시스템 성능을 모니터링합니다.',
            'Current Status' => '현재 상태',
            'Active Database' => '활성 데이터베이스',
            'Plugin Version' => '플러그인 버전',
            'WordPress Version' => 'WordPress 버전',
            'PHP Version' => 'PHP 버전',
            'Database Connections' => '데이터베이스 연결',
            'Disabled' => '비활성화',
            'Switching Rules' => '전환 규칙',
            'Active IP Rules' => '활성 IP 규칙',
            'Active URL Rules' => '활성 URL 규칙',
            'Performance' => '성능',
            'Memory Usage' => '메모리 사용량',
            'Peak Memory' => '최대 메모리',
            'Debug Mode' => '디버그 모드',
            'Activity Logs' => '활동 로그',
            'View database switching activity and system events.' => '데이터베이스 전환 활동과 시스템 이벤트를 확인합니다.',
            'Select Database' => '데이터베이스 선택',
            'New Database' => '새 데이터베이스'
        ];
    }

    /**
     * 记录信息日志
     */
    private function log_info($message) {
        if (defined('YXJTO_GATEWAY_DEBUG') && YXJTO_GATEWAY_DEBUG) {
            error_log("[YXJTO Gateway Language Manager INFO] " . $message);
        }
    }

    /**
     * 记录错误日志
     */
    private function log_error($message) {
        error_log("[WP Multi DB Language Manager ERROR] " . $message);
    }
}
