<?php
/**
 * YXJTO Gateway 优惠券同步类
 * 处理WooCommerce优惠券在多数据库之间的同步
 *
 * @package YXJTO_Gateway
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Coupon_Sync {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 同步设置
     */
    private $settings = [];
    
    /**
     * 日志记录器
     */
    private $logger = null;
    
    /**
     * 正在同步的优惠券ID列表（防止递归）
     */
    private $syncing_coupons = [];
    
    /**
     * 临时禁用同步标志
     */
    private static $sync_disabled = false;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->load_settings();
        $this->init_logger();
        $this->init_hooks();
    }
    
    /**
     * 加载设置
     */
    private function load_settings() {
        $this->settings = WP_Multi_DB_Config_Manager::get_coupon_sync_settings();
    }
    
    /**
     * 初始化日志记录器
     */
    private function init_logger() {
        if (class_exists('YXJTO_Payment_Debug_Logger')) {
            $this->logger = YXJTO_Payment_Debug_Logger::get_instance();
        }
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        if (!$this->settings['enable_coupon_sync']) {
            return;
        }
        
        // 优惠券创建/更新/删除钩子
        add_action('save_post', [$this, 'handle_coupon_save'], 10, 2);
        add_action('before_delete_post', [$this, 'handle_coupon_before_delete'], 10, 2);
        add_action('delete_post', [$this, 'handle_coupon_delete'], 10);
        add_action('transition_post_status', [$this, 'handle_coupon_status_change'], 10, 3);
        
        // 回收站相关钩子
        add_action('wp_trash_post', [$this, 'handle_coupon_trash'], 10);
        add_action('untrash_post', [$this, 'handle_coupon_untrash'], 10);
        
        // WP Cron钩子
        add_action('yxjto_coupon_batch_sync', [$this, 'run_batch_sync']);
        
        // AJAX钩子已移至Admin类处理，避免重复注册
        
        // // 清理过期优惠券钩子
        // add_action('yxjto_coupon_cleanup_expired', [$this, 'cleanup_expired_coupons']);
        
        // // 调度清理任务
        // if (!wp_next_scheduled('yxjto_coupon_cleanup_expired')) {
        //     wp_schedule_event(time(), 'daily', 'yxjto_coupon_cleanup_expired');
        // }
    }
    
    /**
     * 临时禁用同步功能
     */
    public static function disable_sync() {
        self::$sync_disabled = true;
    }
    
    /**
     * 重新启用同步功能
     */
    public static function enable_sync() {
        self::$sync_disabled = false;
    }
    
    /**
     * 检查同步是否被禁用
     */
    public static function is_sync_disabled() {
        return self::$sync_disabled;
    }
    
    /**
     * 处理优惠券保存
     */
    public function handle_coupon_save($post_id, $post) {
        // 检查是否是优惠券
        if ($post->post_type !== 'shop_coupon') {
            return;
        }
        
        // 检查同步是否被临时禁用
        if (self::is_sync_disabled()) {
            $this->log_debug("Sync is temporarily disabled, skipping coupon save: {$post_id}");
            return;
        }
        
        // 检查是否是自动保存或修订
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }
        
        // 检查用户权限
        if (!current_user_can('edit_shop_coupon', $post_id)) {
            return;
        }
        
        // 只有在适当的操作时才同步
        if (!$this->is_manual_publish_or_update()) {
            $this->log_debug("Skipping sync - not a triggering action for coupon: {$post_id}");
            return;
        }
        
        // 防止递归同步
        if (in_array($post_id, $this->syncing_coupons)) {
            $this->log_debug("Skipping recursive sync for coupon: {$post_id}");
            return;
        }
        
        // 检查是否是同步操作产生的保存（通过元数据标记）
        if (get_post_meta($post_id, '_yxjto_coupon_syncing', true)) {
            $this->log_debug("Skipping sync-triggered save for coupon: {$post_id}");
            return;
        }
        
        $this->log_debug("Coupon save/update action detected: {$post_id}");
        
        if ($this->settings['sync_mode'] === 'realtime') {
            $this->sync_coupon_to_all_databases($post_id);
        }
    }
    
    /**
     * 检测是否应该触发同步
     */
    private function is_manual_publish_or_update() {
        // 如果不在管理后台，检查是否是API请求
        if (!is_admin() && !(defined('REST_REQUEST') && REST_REQUEST)) {
            return false;
        }
        
        // 允许AJAX请求触发同步（批量编辑、快速编辑等）
        // 允许REST API请求触发同步（API调用、导入等）
        
        // 检查是否只是自动保存（需要排除）
        if (wp_is_post_autosave($_POST['post_ID'] ?? 0)) {
            return false;
        }
        
        // 如果有POST数据且包含优惠券相关信息，允许同步
        if (isset($_POST['post_type']) && $_POST['post_type'] === 'shop_coupon') {
            return true;
        }
        
        // 如果是REST API请求且涉及优惠券，允许同步
        if (defined('REST_REQUEST') && REST_REQUEST) {
            // 检查URL是否涉及优惠券
            $request_uri = $_SERVER['REQUEST_URI'] ?? '';
            if (strpos($request_uri, '/coupons') !== false || strpos($request_uri, 'shop_coupon') !== false) {
                return true;
            }
        }
        
        // 如果是AJAX请求，检查action
        if (wp_doing_ajax()) {
            $action = $_POST['action'] ?? $_GET['action'] ?? '';
            // 允许的AJAX actions
            $allowed_actions = [
                'inline-save',           // 快速编辑
                'edit-post',            // 编辑操作
                'bulk-posts',           // 批量操作
                'import',               // 导入操作
                'woocommerce_',         // WooCommerce相关操作
            ];
            
            foreach ($allowed_actions as $allowed) {
                if (strpos($action, $allowed) === 0) {
                    return true;
                }
            }
        }
        
        // 传统的手动发布/更新检查
        if (isset($_POST['publish']) || isset($_POST['save'])) {
            return true;
        }
        
        // 默认允许同步（更宽松的策略）
        return true;
    }
    
    /**
     * 处理优惠券删除前的操作
     */
    public function handle_coupon_before_delete($post_id, $post) {
        if (!$post || $post->post_type !== 'shop_coupon') {
            return;
        }

        // 检查同步是否被临时禁用
        if (self::is_sync_disabled()) {
            $this->log_debug("Sync is temporarily disabled, skipping coupon before delete: {$post_id}");
            return;
        }

        // 检查是否已经在删除过程中
        if (get_post_meta($post_id, '_yxjto_coupon_deleting', true)) {
            return;
        }

        // 标记正在删除
        update_post_meta($post_id, '_yxjto_coupon_deleting', true);

        $this->log_info("Coupon permanent delete detected: {$post_id}");

        // 获取优惠券数据（在删除前）
        $coupon_data = $this->get_coupon_data($post_id);
        if (!$coupon_data) {
            $this->log_error("Could not get coupon data before deletion: {$post_id}");
            return;
        }

        // 存储优惠券数据以供删除后使用
        update_post_meta($post_id, '_yxjto_coupon_delete_data', $coupon_data);

        // 如果是实时同步，立即从其他数据库删除
        if ($this->config['sync_mode'] === 'realtime') {
            try {
                $coupon_code = $coupon_data['code'] ?? '';
                if ($coupon_code) {
                    $this->delete_coupon_from_all_databases($coupon_code, $post_id);
                }
            } catch (Exception $e) {
                $this->log_error("Error during coupon before deletion sync: " . $e->getMessage());
            }
        }
    }

    /**
     * 处理优惠券删除
     */
    public function handle_coupon_delete($post_id) {
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'shop_coupon') {
            return;
        }
        
        // 检查同步是否被临时禁用
        if (self::is_sync_disabled()) {
            $this->log_debug("Sync is temporarily disabled, skipping coupon delete: {$post_id}");
            return;
        }
        
        // 检查是否正在同步中，避免递归删除
        if (get_post_meta($post_id, '_yxjto_coupon_syncing', true)) {
            $this->log_debug("Skipping sync-triggered delete for coupon: {$post_id}");
            return;
        }
        
        $this->log_debug("Coupon delete detected: {$post_id}");
        
        // 添加删除保护标记
        update_post_meta($post_id, '_yxjto_coupon_deleting', true);
        
        if ($this->settings['sync_mode'] === 'realtime') {
            try {
                // 获取优惠券码
                $coupon = new WC_Coupon($post_id);
                $coupon_code = $coupon->get_code();

                if ($coupon_code) {
                    $this->delete_coupon_from_all_databases($coupon_code, $post_id);
                }
            } catch (Exception $e) {
                $this->log_error("Error during coupon deletion sync: " . $e->getMessage());
            } finally {
                // 清理删除标记
                delete_post_meta($post_id, '_yxjto_coupon_deleting');
            }
        } else {
            // 如果不是实时同步，直接清理标记
            delete_post_meta($post_id, '_yxjto_coupon_deleting');
        }
    }
    
    /**
     * 处理优惠券状态变化
     */
    public function handle_coupon_status_change($new_status, $old_status, $post) {
        if ($post->post_type !== 'shop_coupon') {
            return;
        }
        
        // 只有在适当的操作时才同步状态变化
        if (!$this->is_manual_publish_or_update()) {
            $this->log_debug("Skipping sync - status change not from triggering action: {$post->ID} ({$old_status} -> {$new_status})");
            return;
        }
        
        $this->log_debug("Coupon status change detected: {$post->ID} ({$old_status} -> {$new_status})");
        
        if ($this->settings['sync_mode'] === 'realtime') {
            $this->sync_coupon_to_all_databases($post->ID);
        }
    }
    
    /**
     * 处理优惠券移动到回收站
     */
    public function handle_coupon_trash($post_id) {
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'shop_coupon') {
            return;
        }
        
        // 检查同步是否被临时禁用
        if (self::is_sync_disabled()) {
            $this->log_debug("Sync is temporarily disabled, skipping coupon trash: {$post_id}");
            return;
        }
        
        // 检查是否正在同步中，避免递归操作
        if (get_post_meta($post_id, '_yxjto_coupon_syncing', true)) {
            $this->log_debug("Skipping sync-triggered trash for coupon: {$post_id}");
            return;
        }
        
        $this->log_debug("Coupon trash detected: {$post_id}");
        
        if ($this->settings['sync_mode'] === 'realtime') {
            $this->trash_coupon_in_all_databases($post_id);
        }
    }
    
    /**
     * 处理优惠券从回收站恢复
     */
    public function handle_coupon_untrash($post_id) {
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'shop_coupon') {
            return;
        }
        
        // 检查同步是否被临时禁用
        if (self::is_sync_disabled()) {
            $this->log_debug("Sync is temporarily disabled, skipping coupon untrash: {$post_id}");
            return;
        }
        
        // 检查是否正在同步中，避免递归操作
        if (get_post_meta($post_id, '_yxjto_coupon_syncing', true)) {
            $this->log_debug("Skipping sync-triggered untrash for coupon: {$post_id}");
            return;
        }
        
        $this->log_debug("Coupon untrash detected: {$post_id}");
        
        if ($this->settings['sync_mode'] === 'realtime') {
            $this->untrash_coupon_in_all_databases($post_id);
        }
    }
    
    /**
     * 同步优惠券到所有数据库
     */
    public function sync_coupon_to_all_databases($coupon_id) {
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            $this->log_error("Config manager not found");
            return false;
        }
        
        // 防止递归同步
        if (in_array($coupon_id, $this->syncing_coupons)) {
            $this->log_debug("Coupon {$coupon_id} is already syncing, skipping to prevent recursion");
            return false;
        }
        
        // 添加到正在同步列表
        $this->syncing_coupons[] = $coupon_id;
        
        try {
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $current_database = $this->get_current_database();
            $sync_results = [];
            
            // 获取优惠券数据
            $coupon_data = $this->get_coupon_data($coupon_id);
            if (!$coupon_data) {
                $this->log_error("Failed to get coupon data for ID: {$coupon_id}");
                return false;
            }
            
            $this->log_debug("Starting coupon sync for ID: {$coupon_id} from database: {$current_database}");
            
            foreach ($databases as $db_key => $db_config) {
                if (!$db_config['enabled'] || $db_key === $current_database) {
                    continue;
                }
                
                $result = $this->sync_coupon_to_database($coupon_data, $db_key);
                $sync_results[$db_key] = $result;
                
                if ($result['success']) {
                    $this->log_info("Coupon {$coupon_id} synced successfully to database: {$db_key}");
                    // 清除失败标记（如果存在）
                    delete_post_meta($coupon_id, '_yxjto_coupon_sync_failed');
                } else {
                    $this->log_error("Failed to sync coupon {$coupon_id} to database {$db_key}: " . $result['error']);
                    // 标记同步失败
                    update_post_meta($coupon_id, '_yxjto_coupon_sync_failed', current_time('mysql'));
                }
            }

            // 检查是否有任何同步失败
            $has_failures = false;
            foreach ($sync_results as $result) {
                if (!$result['success']) {
                    $has_failures = true;
                    break;
                }
            }

            // 如果所有同步都成功，标记为已同步
            if (!$has_failures) {
                update_post_meta($coupon_id, '_yxjto_coupon_synced', current_time('mysql'));
                delete_post_meta($coupon_id, '_yxjto_coupon_sync_failed');
            }

            // 更新同步时间
            $this->update_last_sync_time();
            
            return $sync_results;
            
        } finally {
            // 从正在同步列表中移除
            $key = array_search($coupon_id, $this->syncing_coupons);
            if ($key !== false) {
                unset($this->syncing_coupons[$key]);
            }
        }
    }
    
    /**
     * 从所有数据库删除优惠券（使用优惠券码）
     */
    public function delete_coupon_from_all_databases($coupon_code, $coupon_id = null) {
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            return false;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        // $current_database = $this->get_current_database();
        $delete_results = [];

        // 验证优惠券码
        if (empty($coupon_code)) {
            $this->log_error("Empty coupon code provided for deletion");
            return false;
        }

        $this->log_debug("Starting coupon deletion for code: {$coupon_code} from all databases");

        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled'] ) { //|| $db_key === $current_database
                continue;
            }

            $result = $this->delete_coupon_from_database_by_code($coupon_code, $db_key);
            $delete_results[$db_key] = $result;
            
            if ($result['success']) {
                $this->log_info("Coupon {$coupon_code} deleted successfully from database: {$db_key}");
            } else {
                $this->log_error("Failed to delete coupon {$coupon_code} from database {$db_key}: " . $result['error']);
            }
        }

        // 清理删除数据（如果有coupon_id）
        if ($coupon_id) {
            delete_post_meta($coupon_id, '_yxjto_coupon_delete_data');
        }

        return $delete_results;
    }

    /**
     * 根据优惠券码从指定数据库删除优惠券
     */
    private function delete_coupon_from_database_by_code($coupon_code, $target_db) {
        if (!class_exists('YXJTO_Gateway')) {
            return ['success' => false, 'error' => 'Multi-database gateway not available'];
        }

        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();

        try {
            // 切换到目标数据库
            $gateway->switch_database($target_db);

            // 禁用钩子防止递归删除
            $this->remove_hooks();

            // 根据优惠券码查找要删除的优惠券
            $existing_coupon = $this->find_existing_coupon($coupon_code);
            if (!$existing_coupon || $existing_coupon->post_type !== 'shop_coupon') {
                return ['success' => true, 'action' => 'not_found'];
            }

            // 永久删除优惠券
            $result = wp_delete_post($existing_coupon->ID, true);

            if ($result) {
                return ['success' => true, 'action' => 'deleted'];
            } else {
                return ['success' => false, 'error' => 'Failed to delete coupon'];
            }

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // 恢复钩子
            $this->restore_hooks();

            // 切换回原数据库
            $gateway->switch_database($original_db);
        }
    }
    
    /**
     * 在所有数据库中将优惠券移动到回收站
     */
    public function trash_coupon_in_all_databases($coupon_id) {
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            return false;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_database = $this->get_current_database();
        $trash_results = [];
        
        $this->log_debug("Starting coupon trash for ID: {$coupon_id} in all databases");
        
        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled'] || $db_key === $current_database) {
                continue;
            }
            
            $result = $this->trash_coupon_in_database($coupon_id, $db_key);
            $trash_results[$db_key] = $result;
            
            if ($result['success']) {
                $this->log_info("Coupon {$coupon_id} moved to trash successfully in database: {$db_key}");
            } else {
                $this->log_error("Failed to move coupon {$coupon_id} to trash in database {$db_key}: " . $result['error']);
            }
        }
        
        return $trash_results;
    }
    
    /**
     * 在所有数据库中从回收站恢复优惠券
     */
    public function untrash_coupon_in_all_databases($coupon_id) {
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            return false;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_database = $this->get_current_database();
        $untrash_results = [];
        
        $this->log_debug("Starting coupon untrash for ID: {$coupon_id} in all databases");
        
        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled'] || $db_key === $current_database) {
                continue;
            }
            
            $result = $this->untrash_coupon_in_database($coupon_id, $db_key);
            $untrash_results[$db_key] = $result;
            
            if ($result['success']) {
                $this->log_info("Coupon {$coupon_id} restored from trash successfully in database: {$db_key}");
            } else {
                $this->log_error("Failed to restore coupon {$coupon_id} from trash in database {$db_key}: " . $result['error']);
            }
        }
        
        return $untrash_results;
    }
    
    /**
     * 获取优惠券数据
     */
    private function get_coupon_data($coupon_id) {
        $coupon = new WC_Coupon($coupon_id);
        
        if (!$coupon->get_id()) {
            return false;
        }
        
        // 检查是否排除过期优惠券
        if ($this->settings['exclude_expired'] && $this->is_coupon_expired($coupon)) {
            $this->log_debug("Skipping expired coupon: {$coupon_id}");
            return false;
        }
        
        return [
            'id' => $coupon->get_id(),
            'code' => $coupon->get_code(),
            'amount' => $coupon->get_amount(),
            'discount_type' => $coupon->get_discount_type(),
            'description' => $coupon->get_description(),
            'date_expires' => $this->get_date_for_sync($coupon->get_date_expires()),
            'usage_count' => $coupon->get_usage_count(),
            'individual_use' => $coupon->get_individual_use(),
            'product_ids' => $coupon->get_product_ids(),
            'excluded_product_ids' => $coupon->get_excluded_product_ids(),
            'usage_limit' => $coupon->get_usage_limit(),
            'usage_limit_per_user' => $coupon->get_usage_limit_per_user(),
            'limit_usage_to_x_items' => $coupon->get_limit_usage_to_x_items(),
            'free_shipping' => $coupon->get_free_shipping(),
            'product_categories' => $coupon->get_product_categories(),
            'excluded_product_categories' => $coupon->get_excluded_product_categories(),
            'exclude_sale_items' => $coupon->get_exclude_sale_items(),
            'minimum_amount' => $coupon->get_minimum_amount(),
            'maximum_amount' => $coupon->get_maximum_amount(),
            'email_restrictions' => $coupon->get_email_restrictions(),
            'meta_data' => $coupon->get_meta_data(),
            'post_status' => get_post_status($coupon_id),
            'post_title' => get_the_title($coupon_id),
            'post_content' => get_post_field('post_content', $coupon_id),
            'post_excerpt' => get_post_field('post_excerpt', $coupon_id),
            'date_created' => $this->get_date_for_sync($coupon->get_date_created()),
            'date_modified' => $this->get_date_for_sync($coupon->get_date_modified())
        ];
    }
    
    /**
     * 同步优惠券到指定数据库
     */
    private function sync_coupon_to_database($coupon_data, $target_db) {
        if (!class_exists('YXJTO_Gateway')) {
            return ['success' => false, 'error' => 'Gateway class not found'];
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        // 临时移除钩子防止递归
        $this->remove_hooks_temporarily();
        
        try {
            // 切换到目标数据库
            $gateway->switch_database($target_db);
            
            // 严格检查优惠券是否已存在（防止重复创建）
            $existing_coupon = $this->find_existing_coupon($coupon_data['code']);
            
            // 如果找到现有优惠券，检查是否需要更新
            if ($existing_coupon) {
                // 检查是否是同一个优惠券的同步标记（使用优惠券码作为标识）
                $existing_source_code = get_post_meta($existing_coupon->ID, '_yxjto_coupon_source_code', true);

                if ($existing_source_code && $existing_source_code == $coupon_data['code']) {
                    // 这是同一个优惠券的现有副本，更新即可
                    $this->log_debug("Found existing synced copy of coupon '{$coupon_data['code']}' in database {$target_db}");
                } else {
                    // 这是不同的优惠券但有相同代码，处理冲突
                    $action = $this->resolve_conflict($coupon_data, $existing_coupon);
                    if ($action === 'skip') {
                        return ['success' => true, 'action' => 'skipped', 'reason' => 'conflict_resolution'];
                    }
                }
            }
            
            // 创建或更新优惠券
            $coupon_id = $this->create_or_update_coupon($coupon_data, $existing_coupon);
            
            if ($coupon_id) {
                return ['success' => true, 'coupon_id' => $coupon_id, 'action' => $existing_coupon ? 'updated' : 'created'];
            } else {
                return ['success' => false, 'error' => 'Failed to create/update coupon'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // 切换回原数据库
            $gateway->switch_database($original_db);
            
            // 恢复钩子
            $this->restore_hooks();
        }
    }
    
    /**
     * 从指定数据库删除优惠券
     */
    private function delete_coupon_from_database($coupon_id, $target_db) {
        if (!class_exists('YXJTO_Gateway')) {
            return ['success' => false, 'error' => 'Gateway class not found'];
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        try {
            // 切换到目标数据库
            $gateway->switch_database($target_db);
            
            // 禁用钩子防止递归删除
            $this->remove_hooks();
            
            // 查找要删除的优惠券
            $existing_coupon = get_post($coupon_id);
            if (!$existing_coupon || $existing_coupon->post_type !== 'shop_coupon') {
                return ['success' => true, 'action' => 'not_found'];
            }
            
            // 添加删除标记防止同步触发
            update_post_meta($coupon_id, '_yxjto_coupon_syncing', true);
            
            // 删除优惠券
            $result = wp_delete_post($coupon_id, true);
            
            if ($result) {
                return ['success' => true, 'action' => 'deleted'];
            } else {
                return ['success' => false, 'error' => 'Failed to delete coupon'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // 恢复钩子
            $this->restore_hooks();
            
            // 切换回原数据库
            $gateway->switch_database($original_db);
        }
    }
    
    /**
     * 在指定数据库中将优惠券移动到回收站
     */
    private function trash_coupon_in_database($coupon_id, $target_db) {
        if (!class_exists('YXJTO_Gateway')) {
            return ['success' => false, 'error' => 'Gateway class not found'];
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        try {
            // 切换到目标数据库
            $gateway->switch_database($target_db);
            
            // 禁用钩子防止递归操作
            $this->remove_hooks();
            
            // 查找要移动到回收站的优惠券
            $existing_coupon = get_post($coupon_id);
            if (!$existing_coupon || $existing_coupon->post_type !== 'shop_coupon') {
                return ['success' => true, 'action' => 'not_found'];
            }
            
            // 检查优惠券是否已经在回收站
            if ($existing_coupon->post_status === 'trash') {
                return ['success' => true, 'action' => 'already_trashed'];
            }
            
            // 添加同步标记防止同步触发
            update_post_meta($coupon_id, '_yxjto_coupon_syncing', true);
            
            // 移动到回收站
            $result = wp_trash_post($coupon_id);
            
            if ($result) {
                return ['success' => true, 'action' => 'trashed'];
            } else {
                return ['success' => false, 'error' => 'Failed to trash coupon'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // 恢复钩子
            $this->restore_hooks();
            
            // 切换回原数据库
            $gateway->switch_database($original_db);
        }
    }
    
    /**
     * 在指定数据库中从回收站恢复优惠券
     */
    private function untrash_coupon_in_database($coupon_id, $target_db) {
        if (!class_exists('YXJTO_Gateway')) {
            return ['success' => false, 'error' => 'Gateway class not found'];
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        try {
            // 切换到目标数据库
            $gateway->switch_database($target_db);
            
            // 禁用钩子防止递归操作
            $this->remove_hooks();
            
            // 查找要从回收站恢复的优惠券
            $existing_coupon = get_post($coupon_id);
            if (!$existing_coupon || $existing_coupon->post_type !== 'shop_coupon') {
                return ['success' => true, 'action' => 'not_found'];
            }
            
            // 检查优惠券是否在回收站
            if ($existing_coupon->post_status !== 'trash') {
                return ['success' => true, 'action' => 'not_trashed'];
            }
            
            // 添加同步标记防止同步触发
            update_post_meta($coupon_id, '_yxjto_coupon_syncing', true);
            
            // 从回收站恢复
            $result = wp_untrash_post($coupon_id);
            
            if ($result) {
                return ['success' => true, 'action' => 'untrashed'];
            } else {
                return ['success' => false, 'error' => 'Failed to untrash coupon'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // 恢复钩子
            $this->restore_hooks();
            
            // 切换回原数据库
            $gateway->switch_database($original_db);
        }
    }
    
    /**
     * 查找已存在的优惠券（以优惠券码为唯一标识符）
     */
    private function find_existing_coupon($coupon_code, $original_id = null) {
        // 主要方法：按优惠券代码查找，包括所有状态
        $existing_posts = get_posts([
            'post_type' => 'shop_coupon',
            'meta_query' => [
                [
                    'key' => '_coupon_code',
                    'value' => $coupon_code,
                    'compare' => '='
                ]
            ],
            'post_status' => ['publish', 'draft', 'private', 'trash', 'pending'],
            'posts_per_page' => 1,
            'fields' => 'ids'
        ]);

        if (!empty($existing_posts)) {
            return get_post($existing_posts[0]);
        }

        // 备用方法：使用WooCommerce内置函数
        $existing_id = wc_get_coupon_id_by_code($coupon_code);
        if ($existing_id) {
            return get_post($existing_id);
        }

        // 最后的备用方法：直接查询数据库（按post_title查找）
        global $wpdb;
        $existing_id = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts}
             WHERE post_type = 'shop_coupon'
             AND post_title = %s
             AND post_status IN ('publish', 'draft', 'private', 'trash', 'pending')
             LIMIT 1",
            $coupon_code
        ));

        if ($existing_id) {
            return get_post($existing_id);
        }

        return null;
    }
    
    /**
     * 解决冲突
     */
    private function resolve_conflict($new_coupon_data, $existing_coupon) {
        switch ($this->settings['conflict_resolution']) {
            case 'newest':
                $new_modified = $this->get_datetime_timestamp($new_coupon_data['date_modified']);
                $existing_modified = strtotime(get_post_modified_time('Y-m-d H:i:s', true, $existing_coupon->ID));
                return $new_modified > $existing_modified ? 'update' : 'skip';
                
            case 'source_priority':
                return 'update';
                
            case 'manual':
                // 对于手动解决，暂时跳过
                return 'skip';
                
            default:
                return 'update';
        }
    }
    
    /**
     * 创建或更新优惠券
     */
    private function create_or_update_coupon($coupon_data, $existing_coupon = null) {
        if ($existing_coupon) {
            $coupon_id = $existing_coupon->ID;
            $coupon = new WC_Coupon($coupon_id);
        } else {
            // 最后一次检查：确保不会创建重复的优惠券（以优惠券码为准）
            $final_check = $this->find_existing_coupon($coupon_data['code']);
            if ($final_check) {
                $this->log_debug("Prevented duplicate creation: coupon with code '{$coupon_data['code']}' already exists (ID: {$final_check->ID})");
                // 使用现有的优惠券进行更新
                $coupon_id = $final_check->ID;
                $coupon = new WC_Coupon($coupon_id);
            } else {
                $coupon_id = wp_insert_post([
                    'post_type' => 'shop_coupon',
                    'post_title' => $coupon_data['code'],
                    'post_content' => $coupon_data['post_content'] ?? '',
                    'post_excerpt' => $coupon_data['post_excerpt'] ?? '',
                    'post_status' => $coupon_data['post_status'] ?? 'publish'
                ]);
                
                if (is_wp_error($coupon_id)) {
                    return false;
                }
                
                $coupon = new WC_Coupon($coupon_id);
                $this->log_debug("Created new coupon: {$coupon_data['code']} (ID: {$coupon_id})");
            }
        }
        
        // 设置优惠券属性
        $coupon->set_code($coupon_data['code']);
        $coupon->set_amount($coupon_data['amount']);
        $coupon->set_discount_type($coupon_data['discount_type']);
        $coupon->set_description($coupon_data['description']);
        
        // 安全设置过期日期 - 从时间戳重建WC_DateTime对象
        if (isset($coupon_data['date_expires']) && $coupon_data['date_expires'] !== null) {
            try {
                $expiry_date = $this->rebuild_date_from_sync($coupon_data['date_expires']);
                if ($expiry_date !== null) {
                    $coupon->set_date_expires($expiry_date);
                } else {
                    $this->log_debug("Skipping invalid expiry date for coupon");
                }
            } catch (Exception $e) {
                $this->log_error("Failed to set expiry date for coupon: " . $e->getMessage());
                // 如果设置失败，继续处理其他属性
            }
        }
        
        // 安全设置其他属性
        try {
            $coupon->set_usage_count($coupon_data['usage_count'] ?? 0);
            $coupon->set_individual_use($coupon_data['individual_use'] ?? false);
            $coupon->set_product_ids($coupon_data['product_ids'] ?? []);
            $coupon->set_excluded_product_ids($coupon_data['excluded_product_ids'] ?? []);
            $coupon->set_usage_limit($coupon_data['usage_limit'] ?? '');
            $coupon->set_usage_limit_per_user($coupon_data['usage_limit_per_user'] ?? '');
            $coupon->set_limit_usage_to_x_items($coupon_data['limit_usage_to_x_items'] ?? '');
            $coupon->set_free_shipping($coupon_data['free_shipping'] ?? false);
            $coupon->set_product_categories($coupon_data['product_categories'] ?? []);
            $coupon->set_excluded_product_categories($coupon_data['excluded_product_categories'] ?? []);
            $coupon->set_exclude_sale_items($coupon_data['exclude_sale_items'] ?? false);
            $coupon->set_minimum_amount($coupon_data['minimum_amount'] ?? '');
            $coupon->set_maximum_amount($coupon_data['maximum_amount'] ?? '');
            $coupon->set_email_restrictions($coupon_data['email_restrictions'] ?? []);
        } catch (Exception $e) {
            $this->log_error("Failed to set coupon attributes: " . $e->getMessage());
            // 继续尝试保存，可能部分属性设置成功
        }
        
        // 安全保存元数据
        if (!empty($coupon_data['meta_data']) && is_array($coupon_data['meta_data'])) {
            foreach ($coupon_data['meta_data'] as $meta) {
                if (is_object($meta) && isset($meta->key) && isset($meta->value)) {
                    try {
                        $coupon->update_meta_data($meta->key, $meta->value);
                    } catch (Exception $e) {
                        $this->log_error("Failed to set meta data {$meta->key}: " . $e->getMessage());
                        // 继续处理下一个元数据
                    }
                }
            }
        }
        
        // 添加同步标记，防止递归触发
        update_post_meta($coupon_id, '_yxjto_coupon_syncing', true);

        // 保存优惠券
        $result = $coupon->save();

        // 更新文章状态（WC_Coupon::save()不会更新post_status）
        if ($result && isset($coupon_data['post_status'])) {
            wp_update_post([
                'ID' => $coupon_id,
                'post_status' => $coupon_data['post_status']
            ]);
        }

        // 移除同步标记
        delete_post_meta($coupon_id, '_yxjto_coupon_syncing');
        
        if ($result) {
            // 添加同步完成标记（使用优惠券码作为标识）
            update_post_meta($coupon_id, '_yxjto_coupon_synced', time());
            update_post_meta($coupon_id, '_yxjto_coupon_source_code', $coupon_data['code']);
            update_post_meta($coupon_id, '_yxjto_coupon_source_id', $coupon_data['id']); // 保留ID作为备用

            return $coupon_id;
        }
        
        return false;
    }
    
    /**
     * 批量同步
     */
    public function run_batch_sync() {
        if (!$this->settings['enable_coupon_sync'] || $this->settings['sync_mode'] !== 'batch') {
            return;
        }
        
        $this->log_info("Starting batch coupon sync");
        
        // 获取所有优惠券
        $coupons = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => ['publish', 'draft', 'private'],
            'numberposts' => -1,
            'meta_query' => [
                [
                    'key' => '_yxjto_coupon_batch_synced',
                    'compare' => 'NOT EXISTS'
                ]
            ]
        ]);
        
        $sync_count = 0;
        $error_count = 0;
        
        foreach ($coupons as $coupon_post) {
            $results = $this->sync_coupon_to_all_databases($coupon_post->ID);
            
            $success = true;
            foreach ($results as $result) {
                if (!$result['success']) {
                    $success = false;
                    $error_count++;
                    break;
                }
            }
            
            if ($success) {
                $sync_count++;
                update_post_meta($coupon_post->ID, '_yxjto_coupon_batch_synced', time());
            }
        }
        
        $this->log_info("Batch sync completed: {$sync_count} synced, {$error_count} errors");
        $this->update_last_sync_time();
    }
    
    /**
     * AJAX手动同步
     */
    public function ajax_manual_sync() {
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_manual_coupon_sync')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $this->log_info("Starting manual coupon sync with duplicate prevention");
        
        // 首先清理可能的重复优惠券
        $this->cleanup_duplicate_coupons();
        
        // 获取所有优惠券
        $coupons = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => ['publish', 'draft', 'private'],
            'numberposts' => -1
        ]);
        
        $sync_count = 0;
        $error_count = 0;
        $skipped_count = 0;
        $errors = [];
        
        foreach ($coupons as $coupon_post) {
            // 检查这个优惠券是否最近已经同步过
            $last_sync = get_post_meta($coupon_post->ID, '_yxjto_coupon_last_sync', true);
            if ($last_sync && (time() - $last_sync) < 60) { // 1分钟内同步过的跳过
                $skipped_count++;
                continue;
            }
            
            $results = $this->sync_coupon_to_all_databases($coupon_post->ID);
            
            $coupon_success = true;
            foreach ($results as $db_key => $result) {
                if (!$result['success']) {
                    $coupon_success = false;
                    $error_count++;
                    $errors[] = "Coupon {$coupon_post->post_title} to {$db_key}: " . $result['error'];
                }
            }
            
            if ($coupon_success) {
                $sync_count++;
                // 记录最后同步时间
                update_post_meta($coupon_post->ID, '_yxjto_coupon_last_sync', time());
            }
        }
        
        $this->update_last_sync_time();
        
        $message = sprintf(
            __('Manual sync completed: %d coupons synced, %d errors, %d skipped', 'yxjto-gateway'),
            $sync_count,
            $error_count,
            $skipped_count
        );
        
        // 添加清理信息
        $cleaned_count = $this->cleanup_duplicate_coupons();
        if ($cleaned_count > 0) {
            $message .= sprintf(__(' (cleaned %d duplicates)', 'yxjto-gateway'), $cleaned_count);
        }
        
        if (!empty($errors)) {
            $message .= "\n\nErrors:\n" . implode("\n", array_slice($errors, 0, 10));
            if (count($errors) > 10) {
                $message .= "\n... and " . (count($errors) - 10) . " more errors";
            }
        }
        
        wp_send_json_success([
            'message' => $message, 
            'synced' => $sync_count, 
            'errors' => $error_count,
            'skipped' => $skipped_count,
            'cleaned' => $cleaned_count
        ]);
    }
    
    /**
     * 清理过期优惠券
     */
    public function cleanup_expired_coupons() {
        if (!$this->settings['enable_coupon_sync'] || !$this->settings['exclude_expired']) {
            return;
        }
        
        $this->log_debug("Starting expired coupon cleanup");
        
        // 删除过期优惠券的同步标记，让它们不会被同步
        $all_coupons = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => 'any',
            'numberposts' => -1
        ]);
        
        $expired_count = 0;
        foreach ($all_coupons as $coupon_post) {
            $coupon = new WC_Coupon($coupon_post->ID);
            if ($this->is_coupon_expired($coupon)) {
                delete_post_meta($coupon_post->ID, '_yxjto_coupon_synced');
                delete_post_meta($coupon_post->ID, '_yxjto_coupon_batch_synced');
                $expired_count++;
            }
        }
        
        $this->log_debug("Expired coupon cleanup completed: " . $expired_count . " coupons processed");
    }
    
    /**
     * 获取当前数据库
     */
    private function get_current_database() {
        if (class_exists('YXJTO_Gateway')) {
            return YXJTO_Gateway::get_instance()->get_current_database();
        }
        return 'default';
    }
    
    /**
     * 更新最后同步时间
     */
    private function update_last_sync_time() {
        $settings = $this->settings;
        $settings['last_sync'] = current_time('Y-m-d H:i:s');
        WP_Multi_DB_Config_Manager::save_coupon_sync_settings($settings);
        $this->settings = $settings;
    }
    
    /**
     * 日志记录方法
     */
    private function log_debug($message) {
        if ($this->settings['enable_sync_logging'] && $this->logger) {
            $this->logger->log_debug("Coupon Sync: " . $message);
        }
    }
    
    private function log_info($message) {
        if ($this->settings['enable_sync_logging'] && $this->logger) {
            $this->logger->log_info("Coupon Sync: " . $message);
        }
    }
    
    private function log_error($message) {
        if ($this->settings['enable_sync_logging'] && $this->logger) {
            $this->logger->log_error("Coupon Sync: " . $message);
        }
        error_log("YXJTO Coupon Sync Error: " . $message);
    }
    
    /**
     * 手动同步所有优惠券
     */
    public function run_manual_sync() {
        try {
            $this->log_info("Starting manual coupon sync with duplicate prevention");

            // 首先清理可能的重复优惠券
            $this->cleanup_duplicate_coupons();

            // 获取所有优惠券
            $coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => ['publish', 'draft', 'private'],
                'numberposts' => -1,
                'fields' => 'ids'
            ]);

            $synced_count = 0;
            $errors = [];

            foreach ($coupons as $coupon_id) {
                if (in_array($coupon_id, $this->syncing_coupons)) {
                    continue; // 跳过正在同步的优惠券
                }

                $result = $this->sync_coupon_to_all_databases($coupon_id);
                if ($result) {
                    $synced_count++;
                } else {
                    $errors[] = "Failed to sync coupon ID: $coupon_id";
                }
            }

            $this->update_last_sync_time();

            return [
                'success' => true,
                'synced_count' => $synced_count,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            $this->log_error("Manual sync failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 同步回收站状态
     */
    public function sync_trash_status() {
        try {
            $this->log_info("Starting trash status synchronization");

            // 获取所有处于回收站状态的优惠券
            $trashed_coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => 'trash',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]);

            $synced_count = 0;

            foreach ($trashed_coupons as $coupon_id) {
                $result = $this->sync_coupon_to_all_databases($coupon_id);
                if ($result) {
                    $synced_count++;
                }
            }

            return [
                'success' => true,
                'synced_count' => $synced_count
            ];

        } catch (Exception $e) {
            $this->log_error("Trash status sync failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 清理回收站中的优惠券
     */
    public function cleanup_trashed_coupons() {
        try {
            $this->log_info("Starting trashed coupons cleanup");

            // 获取所有处于回收站状态的优惠券
            $trashed_coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => 'trash',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]);

            $cleaned_count = 0;

            foreach ($trashed_coupons as $coupon_id) {
                wp_delete_post($coupon_id, true); // 永久删除
                $cleaned_count++;
            }

            return [
                'success' => true,
                'cleaned_count' => $cleaned_count
            ];

        } catch (Exception $e) {
            $this->log_error("Cleanup failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * SQL直接删除所有数据库中的优惠券
     */
    public function sql_delete_all_coupons() {
        try {
            $this->log_info("Starting SQL delete all coupons from all databases");

            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $total_deleted = 0;
            $results = [];

            foreach ($databases as $db_key => $db_config) {
                if (!$db_config['enabled']) {
                    continue;
                }

                try {
                    // 连接到目标数据库
                    $target_db = new wpdb(
                        $db_config['username'],
                        $db_config['password'],
                        $db_config['database'],
                        $db_config['host']
                    );

                    if ($target_db->last_error) {
                        $results[$db_key] = [
                            'success' => false,
                            'error' => "Database connection failed: " . $target_db->last_error
                        ];
                        continue;
                    }

                    // 获取表前缀
                    $table_prefix = $db_config['table_prefix'] ?? 'wp_';
                    $posts_table = $table_prefix . 'posts';
                    $postmeta_table = $table_prefix . 'postmeta';

                    // 获取所有优惠券ID
                    $coupon_ids = $target_db->get_col("
                        SELECT ID FROM {$posts_table}
                        WHERE post_type = 'shop_coupon'
                    ");

                    $deleted_count = 0;

                    if (!empty($coupon_ids)) {
                        $ids_string = implode(',', array_map('intval', $coupon_ids));

                        // 删除元数据
                        $target_db->query("DELETE FROM {$postmeta_table} WHERE post_id IN ($ids_string)");

                        // 删除文章
                        $target_db->query("DELETE FROM {$posts_table} WHERE ID IN ($ids_string)");

                        $deleted_count = count($coupon_ids);
                        $total_deleted += $deleted_count;
                    }

                    $results[$db_key] = [
                        'success' => true,
                        'deleted_count' => $deleted_count
                    ];

                    $this->log_info("Deleted {$deleted_count} coupons from database: {$db_key}");

                } catch (Exception $e) {
                    $results[$db_key] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    $this->log_error("Failed to delete coupons from database {$db_key}: " . $e->getMessage());
                }
            }

            return [
                'success' => true,
                'total_deleted' => $total_deleted,
                'results' => $results
            ];

        } catch (Exception $e) {
            $this->log_error("SQL delete all databases failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取同步统计
     */
    public function get_sync_stats() {
        $stats = [
            'total_coupons' => 0,
            'synced_coupons' => 0,
            'failed_syncs' => 0,
            'pending_coupons' => 0,
            'expired_coupons' => 0,
            'last_sync' => $this->settings['last_sync'] ?? __('Never', 'yxjto-gateway'),
            'next_batch_sync' => null
        ];
        
        // 总优惠券数
        $stats['total_coupons'] = wp_count_posts('shop_coupon')->publish;
        
        // 已同步优惠券数
        $synced_coupons = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => 'any',
            'numberposts' => -1,
            'meta_query' => [
                [
                    'key' => '_yxjto_coupon_synced',
                    'compare' => 'EXISTS'
                ]
            ],
            'fields' => 'ids'
        ]);
        $stats['synced_coupons'] = count($synced_coupons);

        // 失败同步优惠券数
        $failed_sync_coupons = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => 'any',
            'numberposts' => -1,
            'meta_query' => [
                [
                    'key' => '_yxjto_coupon_sync_failed',
                    'compare' => 'EXISTS'
                ]
            ],
            'fields' => 'ids'
        ]);
        $stats['failed_syncs'] = count($failed_sync_coupons);

        // 待同步优惠券数
        $stats['pending_coupons'] = $stats['total_coupons'] - $stats['synced_coupons'] - $stats['failed_syncs'];
        
        // 过期优惠券数 - 使用兼容方式统计
        $all_coupons_for_expiry = get_posts([
            'post_type' => 'shop_coupon',
            'post_status' => 'any',
            'numberposts' => -1,
            'fields' => 'ids'
        ]);
        
        $expired_count = 0;
        foreach ($all_coupons_for_expiry as $coupon_id) {
            $coupon = new WC_Coupon($coupon_id);
            if ($this->is_coupon_expired($coupon)) {
                $expired_count++;
            }
        }
        $stats['expired_coupons'] = $expired_count;
        
        // 下次批量同步时间
        if ($this->settings['sync_mode'] === 'batch') {
            $next_sync = wp_next_scheduled('yxjto_coupon_batch_sync');
            if ($next_sync) {
                $stats['next_batch_sync'] = date('Y-m-d H:i:s', $next_sync);
            }
        }
        
        return $stats;
    }
    
    /**
     * 检查优惠券是否过期（兼容方法）
     */
    private function is_coupon_expired($coupon) {
        // 如果WC_Coupon有is_expired方法，直接使用
        if (method_exists($coupon, 'is_expired')) {
            return $coupon->is_expired();
        }
        
        // 否则手动检查过期时间
        $expiry_date = $coupon->get_date_expires();
        if (!$expiry_date) {
            return false; // 没有过期时间，不算过期
        }
        
        // 处理不同的日期格式
        $current_timestamp = current_time('timestamp');
        $expiry_timestamp = $this->get_datetime_timestamp($expiry_date);
        
        return $current_timestamp > $expiry_timestamp;
    }
    
    /**
     * 从各种日期时间格式获取时间戳
     */
    private function get_datetime_timestamp($datetime) {
        if (!$datetime) {
            return 0;
        }
        
        if ($datetime instanceof WC_DateTime) {
            // WooCommerce DateTime 对象
            return $datetime->getTimestamp();
        } elseif ($datetime instanceof DateTime) {
            // 标准 DateTime 对象
            return $datetime->getTimestamp();
        } elseif (is_numeric($datetime)) {
            // 时间戳
            return (int) $datetime;
        } elseif (is_string($datetime)) {
            // 字符串日期
            $timestamp = strtotime($datetime);
            return $timestamp !== false ? $timestamp : 0;
        } else {
            // 无法识别的格式
            return 0;
        }
    }
    
    /**
     * 安全获取日期对象，处理 null 值
     */
    private function safe_get_date($date_obj) {
        if ($date_obj === null || $date_obj === '') {
            return null;
        }
        
        // 如果已经是有效的日期对象，验证其完整性
        if ($date_obj instanceof WC_DateTime) {
            if ($this->is_valid_date_object($date_obj)) {
                return $date_obj;
            } else {
                return null;
            }
        }
        
        if ($date_obj instanceof DateTime) {
            try {
                // 转换为 WC_DateTime 以保持一致性
                return new WC_DateTime($date_obj->format('Y-m-d H:i:s'), $date_obj->getTimezone());
            } catch (Exception $e) {
                return null;
            }
        }
        
        // 如果是时间戳或字符串，尝试转换
        if (is_numeric($date_obj)) {
            try {
                $wc_date = new WC_DateTime('@' . $date_obj);
                return $this->is_valid_date_object($wc_date) ? $wc_date : null;
            } catch (Exception $e) {
                return null;
            }
        }
        
        if (is_string($date_obj)) {
            try {
                $wc_date = new WC_DateTime($date_obj);
                return $this->is_valid_date_object($wc_date) ? $wc_date : null;
            } catch (Exception $e) {
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 获取用于同步的日期格式（时间戳）
     */
    private function get_date_for_sync($date_obj) {
        if ($date_obj === null || $date_obj === '') {
            return null;
        }
        
        try {
            if ($date_obj instanceof WC_DateTime || $date_obj instanceof DateTime) {
                $timestamp = $date_obj->getTimestamp();
                return is_numeric($timestamp) && $timestamp > 0 ? $timestamp : null;
            }
            
            if (is_numeric($date_obj)) {
                return (int) $date_obj;
            }
            
            if (is_string($date_obj)) {
                $timestamp = strtotime($date_obj);
                return $timestamp !== false ? $timestamp : null;
            }
        } catch (Exception $e) {
            $this->log_debug("Failed to convert date for sync: " . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * 从同步数据重建日期对象
     */
    private function rebuild_date_from_sync($timestamp) {
        if ($timestamp === null || !is_numeric($timestamp) || $timestamp <= 0) {
            return null;
        }
        
        try {
            return new WC_DateTime('@' . $timestamp);
        } catch (Exception $e) {
            $this->log_debug("Failed to rebuild date from timestamp: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 验证日期对象是否有效
     */
    private function is_valid_date_object($date_obj) {
        if ($date_obj === null || $date_obj === '') {
            return false;
        }
        
        // 检查是否是有效的日期对象
        if ($date_obj instanceof WC_DateTime) {
            try {
                // 尝试调用可能出问题的方法来验证对象完整性
                $timestamp = $date_obj->getTimestamp();
                return is_numeric($timestamp) && $timestamp > 0;
            } catch (Exception $e) {
                return false;
            }
        }
        
        if ($date_obj instanceof DateTime) {
            try {
                $timestamp = $date_obj->getTimestamp();
                return is_numeric($timestamp) && $timestamp > 0;
            } catch (Exception $e) {
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * 临时移除钩子防止递归
     */
    private function remove_hooks_temporarily() {
        remove_action('save_post', [$this, 'handle_coupon_save'], 10);
        remove_action('before_delete_post', [$this, 'handle_coupon_before_delete'], 10);
        remove_action('delete_post', [$this, 'handle_coupon_delete'], 10);
        remove_action('transition_post_status', [$this, 'handle_coupon_status_change'], 10);
    }

    /**
     * 移除钩子防止递归（别名方法）
     */
    private function remove_hooks() {
        $this->remove_hooks_temporarily();
    }

    /**
     * 恢复钩子
     */
    private function restore_hooks() {
        add_action('save_post', [$this, 'handle_coupon_save'], 10, 2);
        add_action('before_delete_post', [$this, 'handle_coupon_before_delete'], 10, 2);
        add_action('delete_post', [$this, 'handle_coupon_delete'], 10);
        add_action('transition_post_status', [$this, 'handle_coupon_status_change'], 10, 3);
    }
    
    /**
     * AJAX处理：同步回收站状态
     */
    public function ajax_sync_trash_status() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sync_trash_status')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            $this->log_info("Starting trash status synchronization");
            
            // 获取所有处于回收站状态的优惠券
            $trashed_coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => 'trash',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]);
            
            $sync_count = 0;
            foreach ($trashed_coupons as $coupon_id) {
                $result = $this->trash_coupon_in_all_databases($coupon_id);
                if ($result) {
                    $sync_count++;
                }
            }
            
            // 获取所有非回收站状态的优惠券，确保在其他数据库中也不是回收站状态
            $active_coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => ['publish', 'draft', 'private'],
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]);
            
            foreach ($active_coupons as $coupon_id) {
                $result = $this->untrash_coupon_in_all_databases($coupon_id);
                if ($result) {
                    $sync_count++;
                }
            }
            
            wp_send_json_success([
                'message' => sprintf(__('Synchronized %d coupons trash status across all databases.', 'yxjto-gateway'), $sync_count)
            ]);
            
        } catch (Exception $e) {
            $this->log_error("Trash status sync failed: " . $e->getMessage());
            wp_send_json_error('Sync failed: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX处理：清理回收站中的优惠券
     */
    public function ajax_cleanup_trashed_coupons() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_cleanup_trashed_coupons')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            $this->log_info("Starting trashed coupons cleanup");
            
            // 获取所有处于回收站状态的优惠券
            $trashed_coupons = get_posts([
                'post_type' => 'shop_coupon',
                'post_status' => 'trash',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]);
            
            $deleted_count = 0;
            foreach ($trashed_coupons as $coupon_id) {
                try {
                    // 获取优惠券码
                    $coupon = new WC_Coupon($coupon_id);
                    $coupon_code = $coupon->get_code();

                    if ($coupon_code) {
                        // 从所有数据库中永久删除
                        $this->delete_coupon_from_all_databases($coupon_code, $coupon_id);
                    }

                    // 从当前数据库永久删除
                    $result = wp_delete_post($coupon_id, true);
                    if ($result) {
                        $deleted_count++;
                    }
                } catch (Exception $e) {
                    $this->log_error("Failed to delete trashed coupon {$coupon_id}: " . $e->getMessage());
                }
            }
            
            wp_send_json_success([
                'message' => sprintf(__('Permanently deleted %d trashed coupons from all databases.', 'yxjto-gateway'), $deleted_count)
            ]);
            
        } catch (Exception $e) {
            $this->log_error("Trashed coupons cleanup failed: " . $e->getMessage());
            wp_send_json_error('Cleanup failed: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX处理：SQL直接删除所有优惠券
     */
    public function ajax_sql_delete_all_coupons() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sql_delete_all_coupons')) {
            wp_die('Security check failed');
        }
        
        // 验证权限 - 需要最高权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            $this->log_info("Starting SQL deletion of all coupons");
            
            // 临时禁用所有同步相关钩子
            $this->remove_all_sync_hooks();
            
            $total_deleted = 0;
            $databases_processed = 0;
            
            // 获取所有数据库配置
            if (class_exists('WP_Multi_DB_Config_Manager')) {
                $databases = WP_Multi_DB_Config_Manager::get_databases();
                
                foreach ($databases as $db_key => $db_config) {
                    if (!$db_config['enabled']) {
                        continue;
                    }
                    
                    $deleted_count = $this->sql_delete_coupons_in_database($db_key);
                    $total_deleted += $deleted_count;
                    $databases_processed++;
                    
                    $this->log_info("Deleted {$deleted_count} coupons from database: {$db_key}");
                }
            } else {
                // 如果没有多数据库配置，只删除当前数据库
                $deleted_count = $this->sql_delete_coupons_in_current_database();
                $total_deleted += $deleted_count;
                $databases_processed = 1;
                
                $this->log_info("Deleted {$deleted_count} coupons from current database");
            }
            
            wp_send_json_success([
                'message' => sprintf(
                    __('Successfully deleted %d coupons from %d database(s) using direct SQL queries.', 'yxjto-gateway'),
                    $total_deleted,
                    $databases_processed
                )
            ]);
            
        } catch (Exception $e) {
            $this->log_error("SQL deletion failed: " . $e->getMessage());
            wp_send_json_error('SQL deletion failed: ' . $e->getMessage());
        } finally {
            // 重新启用钩子
            $this->restore_all_sync_hooks();
        }
    }
    
    /**
     * 在指定数据库中使用SQL直接删除所有优惠券
     */
    private function sql_delete_coupons_in_database($db_key) {
        if (!class_exists('YXJTO_Gateway')) {
            return 0;
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        try {
            // 切换到目标数据库
            $gateway->switch_database($db_key);
            
            return $this->execute_coupon_deletion_sql();
            
        } catch (Exception $e) {
            $this->log_error("Failed to delete coupons in database {$db_key}: " . $e->getMessage());
            return 0;
        } finally {
            // 切换回原数据库
            $gateway->switch_database($original_db);
        }
    }
    
    /**
     * 在当前数据库中使用SQL直接删除所有优惠券
     */
    private function sql_delete_coupons_in_current_database() {
        try {
            return $this->execute_coupon_deletion_sql();
        } catch (Exception $e) {
            $this->log_error("Failed to delete coupons in current database: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 执行优惠券删除SQL
     */
    private function execute_coupon_deletion_sql() {
        global $wpdb;
        
        // 获取所有优惠券ID
        $coupon_ids = $wpdb->get_col(
            "SELECT ID FROM {$wpdb->posts} WHERE post_type = 'shop_coupon'"
        );
        
        if (empty($coupon_ids)) {
            return 0;
        }
        
        $coupon_ids_string = implode(',', array_map('intval', $coupon_ids));
        
        // 删除相关的元数据
        $wpdb->query(
            "DELETE FROM {$wpdb->postmeta} WHERE post_id IN ({$coupon_ids_string})"
        );
        
        // 删除优惠券posts
        $deleted_count = $wpdb->query(
            "DELETE FROM {$wpdb->posts} WHERE post_type = 'shop_coupon'"
        );
        
        // 删除相关的term relationships（如果有分类关联）
        $wpdb->query(
            "DELETE tr FROM {$wpdb->term_relationships} tr 
             LEFT JOIN {$wpdb->posts} p ON tr.object_id = p.ID 
             WHERE p.ID IS NULL"
        );
        
        // 清理孤立的评论（如果有）
        $wpdb->query(
            "DELETE c FROM {$wpdb->comments} c 
             LEFT JOIN {$wpdb->posts} p ON c.comment_post_ID = p.ID 
             WHERE p.ID IS NULL"
        );
        
        return $deleted_count;
    }
    
    /**
     * 临时移除所有同步相关钩子
     */
    private function remove_all_sync_hooks() {
        remove_action('save_post', [$this, 'handle_coupon_save'], 10);
        remove_action('delete_post', [$this, 'handle_coupon_delete'], 10);
        remove_action('transition_post_status', [$this, 'handle_coupon_status_change'], 10);
        remove_action('wp_trash_post', [$this, 'handle_coupon_trash'], 10);
        remove_action('untrash_post', [$this, 'handle_coupon_untrash'], 10);
        
        // 也移除其他可能的WooCommerce钩子
        remove_all_actions('woocommerce_coupon_object_updated_props');
        remove_all_actions('woocommerce_delete_coupon');
        remove_all_actions('woocommerce_trash_coupon');
        remove_all_actions('woocommerce_untrash_coupon');
    }
    
    /**
     * 清理重复的优惠券
     */
    private function cleanup_duplicate_coupons() {
        global $wpdb;
        
        $this->log_info("Starting duplicate coupon cleanup");
        
        // 查找重复的优惠券代码
        $duplicates = $wpdb->get_results(
            "SELECT post_title, COUNT(*) as count, GROUP_CONCAT(ID) as ids 
             FROM {$wpdb->posts} 
             WHERE post_type = 'shop_coupon' 
             AND post_status IN ('publish', 'draft', 'private') 
             GROUP BY post_title 
             HAVING count > 1"
        );
        
        $cleaned_count = 0;
        
        foreach ($duplicates as $duplicate) {
            $ids = explode(',', $duplicate->ids);
            $keep_id = array_shift($ids); // 保留第一个
            
            $this->log_debug("Found {$duplicate->count} duplicate coupons for code '{$duplicate->post_title}', keeping ID {$keep_id}");
            
            // 删除其他重复的
            foreach ($ids as $delete_id) {
                // 检查是否有同步标记，如果有则合并元数据
                $source_id = get_post_meta($delete_id, '_yxjto_coupon_source_id', true);
                if ($source_id && !get_post_meta($keep_id, '_yxjto_coupon_source_id', true)) {
                    update_post_meta($keep_id, '_yxjto_coupon_source_id', $source_id);
                }
                
                wp_delete_post($delete_id, true);
                $cleaned_count++;
                $this->log_debug("Deleted duplicate coupon ID {$delete_id}");
            }
        }
        
        if ($cleaned_count > 0) {
            $this->log_info("Cleaned up {$cleaned_count} duplicate coupons");
        }
        
        return $cleaned_count;
    }
    
    /**
     * 恢复所有同步相关钩子
     */
    private function restore_all_sync_hooks() {
        add_action('save_post', [$this, 'handle_coupon_save'], 10, 2);
        add_action('delete_post', [$this, 'handle_coupon_delete'], 10);
        add_action('transition_post_status', [$this, 'handle_coupon_status_change'], 10, 3);
        add_action('wp_trash_post', [$this, 'handle_coupon_trash'], 10);
        add_action('untrash_post', [$this, 'handle_coupon_untrash'], 10);
    }
}
