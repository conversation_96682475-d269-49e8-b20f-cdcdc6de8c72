<?php
/**
 * PayPal 多账号网关支付类
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Payment extends WC_Payment_Gateway {

    /**
     * 测试模式标志
     * @var bool
     */
    public $testmode;

    public function __construct() {
        $this->id = 'yxjto_paypal_multi_gateway';
        $this->icon = $this->get_icon_url();
        $this->has_fields = false;
        $this->method_title = __('PayPal Multi Gateway', 'yxjto-paypal-multi-gateway');
        $this->method_description = __('Accept payments via PayPal with multi-account load balancing and failover support.', 'yxjto-paypal-multi-gateway');

        // 支持的功能 - 参考 WooCommerce PayPal Payments
        $this->supports = array(
            'products',
            'refunds',
            'subscriptions',
            'subscription_cancellation',
            'subscription_suspension',
            'subscription_reactivation',
            'subscription_amount_changes',
            'subscription_date_changes',
            'multiple_subscriptions'
        );

        // 加载设置
        $this->init_form_fields();
        $this->init_settings();

        // 定义用户设置变量
        $this->title = $this->get_option('title', 'PayPal');
        $this->description = $this->get_option('description', __('Pay securely with PayPal. You can pay with your PayPal account or credit card.', 'yxjto-paypal-multi-gateway'));
        $this->enabled = $this->get_option('enabled', 'no');

        // 从插件设置中获取测试模式
        $plugin_settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $this->testmode = 'yes' === $plugin_settings['test_mode'];

        // 挂钩到 WooCommerce
        $this->init_hooks();
    }

    /**
     * 获取 PayPal 图标 URL
     */
    private function get_icon_url() {
        // 优先使用 SVG 图标
        $svg_icon = YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL . 'assets/images/paypal-logo.svg';
        if (file_exists(YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_DIR . 'assets/images/paypal-logo.svg')) {
            return apply_filters('paypal_multi_gateway_icon_url', $svg_icon);
        }

        // 备用 PNG 图标
        $png_icon = YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL . 'assets/images/paypal-logo.png';
        if (file_exists(YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_DIR . 'assets/images/paypal-logo.png')) {
            return apply_filters('paypal_multi_gateway_icon_url', $png_icon);
        }

        // 最后使用官方图标
        return apply_filters('paypal_multi_gateway_icon_url', 'https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png');
    }

    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 管理钩子
        add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));

        // API 钩子
        add_action('woocommerce_api_' . strtolower(get_class($this)), array($this, 'check_response'));
        add_action('woocommerce_api_paypal_multi_gateway_return', array($this, 'handle_return'));
        add_action('woocommerce_api_paypal_multi_gateway_cancel', array($this, 'handle_cancel'));

        // 不同账户类型的专用回调处理
        add_action('woocommerce_api_paypal_multi_gateway_ipn', array($this, 'handle_ipn')); // Email 账户 IPN
        add_action('woocommerce_api_paypal_multi_gateway_paypalme_return', array($this, 'handle_paypalme_return')); // PayPal.me 返回

        // 收据页面
        add_action('woocommerce_receipt_' . $this->id, array($this, 'receipt_page'));

        // 前端钩子
        add_action('wp_enqueue_scripts', array($this, 'payment_scripts'));

        // 结账钩子 - 参考 WooCommerce PayPal Payments
        add_action('woocommerce_checkout_process', array($this, 'validate_checkout'));

        // 订单接收页面钩子 - 使用默认的WooCommerce thankyou页面处理
        add_action('woocommerce_thankyou_' . $this->id, array($this, 'thankyou_page'), 10, 1);


    }

    /**
     * 检查网关是否可用
     */
    public function is_available() {
        if (!parent::is_available()) {
            return false;
        }

        // 检查是否有有效账户（邮箱和URL类型跳过深度验证）
        if (!$this->has_valid_accounts_for_checkout()) {
            return false;
        }

        return true;
    }

    /**
     * 支付脚本 - 参考 WooCommerce PayPal Payments
     */
    public function payment_scripts() {
        if (!is_admin() && !is_cart() && !is_checkout() && !isset($_GET['pay_for_order'])) {
            return;
        }

        if ('no' === $this->enabled) {
            return;
        }

        if (!$this->is_available()) {
            return;
        }

        wp_enqueue_script(
            'paypal-multi-gateway-checkout',
            YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL . 'assets/js/paypal-multi-gateway-checkout.js',
            array('jquery'),
            YXJTO_PAYPAL_MULTI_GATEWAY_VERSION,
            true
        );

        wp_localize_script('paypal-multi-gateway-checkout', 'yxjto_paypal_multi_gateway_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_checkout'),
            'testmode' => $this->testmode ? 'yes' : 'no',
            'messages' => array(
                'processing' => __('Processing payment...', 'yxjto-paypal-multi-gateway'),
                'error' => __('Payment processing error, please try again.', 'yxjto-paypal-multi-gateway'),
                'redirect' => __('Redirecting to PayPal...', 'yxjto-paypal-multi-gateway')
            ),
            'strings' => array(
                'security_info' => __('Your payment information is securely protected by PayPal', 'yxjto-paypal-multi-gateway'),
                'payment_process_info' => __('After clicking "Pay Now", you will be redirected to PayPal to complete payment.', 'yxjto-paypal-multi-gateway'),
                'status_check_failed' => __('Unable to check PayPal account status', 'yxjto-paypal-multi-gateway'),
                'network_error' => __('Network error, please try again', 'yxjto-paypal-multi-gateway'),
                'accounts_available' => __('%d PayPal accounts available', 'yxjto-paypal-multi-gateway'),
                'no_accounts_available' => __('No PayPal accounts available', 'yxjto-paypal-multi-gateway'),
                'required_fields' => __('Please fill in all required fields', 'yxjto-paypal-multi-gateway'),
                'currency_not_supported' => __('Current currency is not supported for PayPal payments', 'yxjto-paypal-multi-gateway'),
                'checking_status' => __('Checking PayPal status...', 'yxjto-paypal-multi-gateway')
            )
        ));

        // 为 WooCommerce Blocks 提供数据
        wp_localize_script('paypal-multi-gateway-checkout', 'paypalMultiGatewayData', array(
            'enabled' => $this->enabled,
            'title' => $this->get_title(),
            'description' => $this->get_description(),
            'testmode' => $this->testmode ? 'yes' : 'no',
            'supports' => $this->supports,
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_checkout'),
            'plugin_url' => YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL,
            'strings' => array(
                'switch_account' => __('🔄 Switch Account', 'yxjto-paypal-multi-gateway'),
                'switching' => __('🔄 Switching...', 'yxjto-paypal-multi-gateway'),
                'switching_account' => __('Switching account...', 'yxjto-paypal-multi-gateway'),
                'switch_success' => __('Switch successful!', 'yxjto-paypal-multi-gateway'),
                'switch_failed' => __('Switch failed: ', 'yxjto-paypal-multi-gateway'),
                'paypal_available' => __('PayPal payment available', 'yxjto-paypal-multi-gateway'),
                'account_locked' => __('🔒 Account locked, this account will be used during payment', 'yxjto-paypal-multi-gateway')
            )
        ));

        wp_enqueue_style(
            'paypal-multi-gateway-checkout',
            YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL . 'assets/css/paypal-multi-gateway-checkout.css',
            array(),
            YXJTO_PAYPAL_MULTI_GATEWAY_VERSION
        );
    }

    /**
     * Get gateway icon - 重写父类方法（不显示图标）
     */
    public function get_icon() {
        // 返回空字符串，不显示任何图标
        return apply_filters('woocommerce_gateway_icon', '', $this->id);
    }

    /**
     * Get gateway title - 重写父类方法
     */
    public function get_title() {
        $title = parent::get_title();

        // 在结账页面显示更详细的标题
        if (is_checkout() && !is_admin()) {
            $accounts_count = $this->get_active_accounts_count();
            if ($accounts_count > 1) {
                return $title . ' <small>(' . sprintf(__('%d accounts', 'yxjto-paypal-multi-gateway'), $accounts_count) . ')</small>';
            }
        }

        return $title;
    }

    /**
     * Get gateway description - 重写父类方法
     */
    public function get_description() {
        $description = parent::get_description();

        if (is_checkout() && !is_admin()) {
            $custom_description = $description;

            // 添加测试模式提示
            if ($this->testmode) {
                $custom_description .= '<br><small style="color: #ff6600;"><strong>' . __('Test Mode', 'yxjto-paypal-multi-gateway') . '</strong> - ' . __('No real transactions will be processed', 'yxjto-paypal-multi-gateway') . '</small>';
            }

            // 添加支持的支付方式
            $payment_methods = array();
            $payment_methods[] = __('PayPal Account', 'yxjto-paypal-multi-gateway');

            if ($this->supports_credit_cards()) {
                $payment_methods[] = __('Credit/Debit Cards', 'yxjto-paypal-multi-gateway');
            }

            if (!empty($payment_methods)) {
                $custom_description .= '<br><small>' . __('Supports:', 'yxjto-paypal-multi-gateway') . ' ' . implode(', ', $payment_methods) . '</small>';
            }

            return $custom_description;
        }

        return $description;
    }

    /**
     * Validate checkout - 参考 WooCommerce PayPal Payments
     */
    public function validate_checkout() {
        if (!isset($_POST['payment_method']) || $_POST['payment_method'] !== $this->id) {
            return;
        }

        // 检查是否有有效账户（邮箱和URL类型跳过深度验证）
        if (!$this->has_valid_accounts_for_checkout()) {
            wc_add_notice(__('PayPal payment is temporarily unavailable. Please check account configuration or choose another payment method.', 'yxjto-paypal-multi-gateway'), 'error');
            return;
        }

        // 检查订单金额
        $total = WC()->cart->get_total('');
        if ($total <= 0) {
            wc_add_notice(__('Invalid order amount.', 'yxjto-paypal-multi-gateway'), 'error');
            return;
        }

        // 检查货币支持
        if (!$this->is_currency_supported()) {
            wc_add_notice(__('Current currency is not supported for PayPal payments.', 'yxjto-paypal-multi-gateway'), 'error');
            return;
        }
    }

    /**
     * Check if gateway supports credit cards
     */
    private function supports_credit_cards() {
        // 检查是否有 API 类型的账户支持信用卡
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            $active_accounts = $accounts->get_active_accounts();

            foreach ($active_accounts as $account) {
                if ($account->account_type === 'api') {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get active accounts count
     */
    private function get_active_accounts_count() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            return count($accounts->get_active_accounts());
        }

        return 0;
    }

    /**
     * Check if has active accounts
     */
    private function has_active_accounts() {
        return $this->get_active_accounts_count() > 0;
    }

    /**
     * Check if has valid accounts for checkout (skip validation for email/url types)
     */
    private function has_valid_accounts_for_checkout() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            $active_accounts = $accounts->get_active_accounts();

            if (empty($active_accounts)) {
                return false;
            }

            // 对于邮箱和 URL 类型，只要是活跃状态就认为有效
            // 对于 API 类型，需要进一步验证
            foreach ($active_accounts as $account) {
                if (in_array($account->account_type, ['email', 'paypal_me'])) {
                    // 邮箱和 URL 类型：只检查基本格式
                    $account_data = json_decode($account->account_data, true);

                    if ($account->account_type === 'email') {
                        if (!empty($account_data['email']) && is_email($account_data['email'])) {
                            return true; // 有至少一个有效的邮箱账户
                        }
                    } elseif ($account->account_type === 'paypal_me') {
                        if (!empty($account_data['paypal_me_url']) && filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL)) {
                            return true; // 有至少一个有效的 PayPal.me 账户
                        }
                    }
                } elseif ($account->account_type === 'api') {
                    // API 类型：检查基本配置（不进行实际连接测试）
                    $account_data = json_decode($account->account_data, true);
                    if (!empty($account_data['client_id']) && !empty($account_data['client_secret'])) {
                        return true; // 有至少一个配置完整的 API 账户
                    }
                }
            }

            return false; // 没有找到有效的账户
        }

        return false;
    }

    /**
     * Check if currency is supported
     */
    private function is_currency_supported() {
        $supported_currencies = array(
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'NOK', 'SEK', 'DKK',
            'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP', 'TWD', 'THB',
            'SGD', 'HKD', 'CNY', 'NZD', 'TRY', 'INR', 'RUB'
        );

        $current_currency = get_woocommerce_currency();
        return in_array($current_currency, $supported_currencies);
    }

    /**
     * Initialize Gateway Settings Form Fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title' => __('Enable/Disable', 'yxjto-paypal-multi-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable PayPal Multi Gateway', 'yxjto-paypal-multi-gateway'),
                'default' => 'no'
            ),
            'title' => array(
                'title' => __('Title', 'yxjto-paypal-multi-gateway'),
                'type' => 'text',
                'description' => __('This controls the title which the user sees during checkout.', 'yxjto-paypal-multi-gateway'),
                'default' => 'PayPal',
                'desc_tip' => true,
            ),
            'description' => array(
                'title' => __('Description', 'yxjto-paypal-multi-gateway'),
                'type' => 'textarea',
                'description' => __('This controls the description which the user sees during checkout.', 'yxjto-paypal-multi-gateway'),
                'default' => __('Pay via PayPal; you can pay with your credit card if you don\'t have a PayPal account.', 'yxjto-paypal-multi-gateway'),
                'desc_tip' => true,
            ),
            'advanced_settings_notice' => array(
                'title' => __('Advanced Settings', 'yxjto-paypal-multi-gateway'),
                'type' => 'title',
                'description' => sprintf(__('Advanced settings such as load balancing, retry count, timeout, test mode can be configured in the <a href="%s">plugin settings page</a>.', 'yxjto-paypal-multi-gateway'), admin_url('admin.php?page=paypal-multi-gateway')),
            ),
        );
    }

    /**
     * Add account configuration fields
     * 注释：此函数已移除，账户配置现在在插件设置页面进行管理
     * WooCommerce支付设置页面只保留基本设置（启用、标题、描述）
     */
    private function add_account_fields() {
        // 此函数已被移除，账户管理功能已迁移到插件设置页面
        // 请在 WooCommerce → PayPal 多账号网关 中管理账户配置
        return;
    }

    /**
     * Process the payment and return the result
     */
    public function process_payment($order_id) {
        // 获取当前数据库
        $gateway = YXJTO_Gateway::get_instance();
        $current_database = $gateway->get_current_database();
        $this->log_debug("PayPal payment processing started in database: {$current_database}");

        
        // 获取订单复制设置
        $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
        $enable_default_db_redirect = !empty($order_settings['enable_default_db_payment_redirect']);

        // 首先进行订单验证和数据库切换
        if (class_exists('YXJTO_Order_Replication') && class_exists('YXJTO_Gateway')) {
            $order_replication = YXJTO_Order_Replication::get_instance();


            // 临时获取订单进行验证
            $temp_order = wc_get_order($order_id);
            if (!$temp_order) {
                $error_message = __('Order not found.', 'yxjto-paypal-multi-gateway');
                wc_add_notice($error_message, 'error');
                return array('result' => 'fail', 'redirect' => '', 'message' => $error_message);
            }

            // 更新订单到所有数据库（如果订单存在）
            if ($temp_order && $order_replication) {
                $this->log_debug("Updating order #{$order_id} to all databases before payment verification");
                try {
                    // 使用新的完全同步方法来更新复制订单
                    $this->sync_order_to_all_databases_completely($order_id, $temp_order, $gateway, $order_replication);

                    $this->log_debug("Order #{$order_id} successfully synchronized to all databases with complete sync");
                } catch (Exception $e) {
                    $this->log_error("Failed to synchronize order #{$order_id} to all databases: " . $e->getMessage());
                    // 不阻止支付流程，只记录错误
                }
            }

            // 验证订单并确保切换到默认数据库
            $verification_result = apply_filters('yxjto_order_replication_verify_payment', true, $temp_order);

            if (!$verification_result) {
                // 记录验证失败但不阻止支付，只记录警告
                $warning_message = __('Order verification warning: There may be price differences between databases, but payment will proceed.', 'yxjto-paypal-multi-gateway');
                $temp_order->add_order_note($warning_message);
                $temp_order->save();
                $this->log_debug("Order verification failed for order #{$order_id}, but allowing payment to proceed");
                
                // 不返回错误，继续支付流程
                // return array(
                //     'result' => 'fail',
                //     'redirect' => '',
                //     'message' => $error_message
                // );
            }

            // 标记当前正在进行支付处理，这样回调时可以识别
            $temp_order->update_meta_data('_paypal_multi_gateway_payment_in_progress', 'yes');
            $temp_order->update_meta_data('_paypal_multi_gateway_source_database', $current_database);
            $temp_order->save();

            $this->log_debug("_paypal_multi_gateway_source_database: {$current_database}");
            $this->log_debug("Order verification passed, now using default database for payment processing");
        }

        // 根据订单复制设置决定是否切换数据库
        // $gateway = YXJTO_Gateway::get_instance();
        // $current_database = $gateway->get_current_database();
        $source_database = $current_database;


        $this->log_debug("Payment processing - Current database: {$current_database}, Enable default DB redirect: " . ($enable_default_db_redirect ? 'Yes' : 'No'));


        // 决定支付处理使用的数据库
        $payment_database = $this->determine_payment_database($current_database, $enable_default_db_redirect);
        $this->log_debug("Payment processing - Current database: {$current_database}, payment_database: {$payment_database}");
        // 如果需要切换数据库
        if ($payment_database !== $current_database || $payment_database !== $gateway->get_current_database()) {
            $this->log_debug("Switching from {$current_database} to {$payment_database} for payment processing");
            $switch_result = $gateway->switch_database($payment_database);
            if (!$switch_result) {
                $error_message = __('Failed to switch database for payment processing.', 'yxjto-paypal-multi-gateway');
                $this->log_error("Failed to switch to {$payment_database} database for order #{$order_id} payment processing");
                wc_add_notice($error_message, 'error');
                return array('result' => 'fail', 'redirect' => '', 'message' => $error_message);
            }
        } else {
            $this->log_debug("Using current database {$current_database} for payment processing");
        }

        $this->log_debug("Successfully switched to default database for order #{$order_id} payment processing");

        // 现在从默认数据库重新获取订单信息
        $order = wc_get_order($order_id);
        if (!$order) {
            $error_message = __('Order not found in database.', 'yxjto-paypal-multi-gateway');
            wc_add_notice($error_message, 'error');
            return array('result' => 'fail', 'redirect' => '', 'message' => $error_message);
        }

        // Hook: Before payment processing
        do_action('paypal_multi_gateway_before_payment', $order);

        // 获取提交的账户ID（支持传统结账和 Blocks）
        $submitted_account_id = null;

        // 调试日志：记录所有可能的数据源
        error_log("PayPal Multi Gateway [Payment Debug]: Starting payment processing for Order ID: {$order_id}");
        error_log("PayPal Multi Gateway [Payment Debug]: POST data: " . print_r($_POST, true));
        error_log("PayPal Multi Gateway [Payment Debug]: GET data: " . print_r($_GET, true));
        error_log("PayPal Multi Gateway [Payment Debug]: REQUEST data: " . print_r($_REQUEST, true));

        // 1. 检查传统结账表单数据
        if (isset($_POST['paypal_account_id']) && !empty($_POST['paypal_account_id'])) {
            $submitted_account_id = sanitize_text_field($_POST['paypal_account_id']);
            error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from traditional form: {$submitted_account_id}");
        }

        // 2. 检查 WooCommerce Blocks 支付数据
        // Blocks 数据可能在不同的位置
        if (!$submitted_account_id) {
            // 检查 $_POST['payment_data']
            if (isset($_POST['payment_data'])) {
                $payment_data = json_decode(stripslashes($_POST['payment_data']), true);
                error_log("PayPal Multi Gateway [Payment Debug]: Blocks payment_data: " . print_r($payment_data, true));
                if (isset($payment_data['paypal_account_id']) && !empty($payment_data['paypal_account_id'])) {
                    $submitted_account_id = sanitize_text_field($payment_data['paypal_account_id']);
                    error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from Blocks payment_data: {$submitted_account_id}");
                }
            }

            // 检查直接的 POST 数据（Blocks 可能直接传递）
            if (!$submitted_account_id && isset($_POST['yxjto_paypal_multi_gateway']) && isset($_POST['paypal_account_id'])) {
                $submitted_account_id = sanitize_text_field($_POST['paypal_account_id']);
                error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from direct Blocks POST: {$submitted_account_id}");
            }

            // 检查嵌套的支付方法数据
            if (!$submitted_account_id && isset($_POST['payment_method_data'])) {
                $payment_method_data = $_POST['payment_method_data'];
                if (is_string($payment_method_data)) {
                    $payment_method_data = json_decode(stripslashes($payment_method_data), true);
                }
                error_log("PayPal Multi Gateway [Payment Debug]: payment_method_data: " . print_r($payment_method_data, true));
                if (isset($payment_method_data['paypal_account_id']) && !empty($payment_method_data['paypal_account_id'])) {
                    $submitted_account_id = sanitize_text_field($payment_method_data['paypal_account_id']);
                    error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from payment_method_data: {$submitted_account_id}");
                }
            }

            // 检查 WooCommerce Blocks 的特殊格式
            if (!$submitted_account_id) {
                // 检查是否有 Blocks 特有的数据格式
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'yxjto_paypal_multi_gateway') !== false || strpos($key, 'paypal_account_id') !== false) {
                        error_log("PayPal Multi Gateway [Payment Debug]: Found potential Blocks data - Key: {$key}, Value: " . print_r($value, true));

                        if ($key === 'paypal_account_id' && !empty($value)) {
                            $submitted_account_id = sanitize_text_field($value);
                            error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from direct key: {$submitted_account_id}");
                            break;
                        }
                    }
                }
            }
        }

        // 3. 检查订单元数据（如果之前已保存）
        if (!$submitted_account_id) {
            $meta_account_id = $order->get_meta('_paypal_multi_gateway_account_id');
            if ($meta_account_id) {
                $submitted_account_id = sanitize_text_field($meta_account_id);
                error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from order meta: {$submitted_account_id}");
            }
        }
        // 31. 检查临时订单元数据（如果之前已保存）
        if (!$submitted_account_id) {
            $meta_account_id = $temp_order->get_meta('_paypal_multi_gateway_account_id');
            if ($meta_account_id) {
                $submitted_account_id = sanitize_text_field($meta_account_id);
                error_log("PayPal Multi Gateway [Payment Debug]: Found account ID from order meta: {$submitted_account_id}");
            }
        }
        error_log("PayPal Multi Gateway [Payment Debug]: Final submitted_account_id: " . ($submitted_account_id ?: 'null'));

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $selected_account = null; // 初始化变量

        if ($submitted_account_id) {
            // 使用提交的账户ID
            error_log("PayPal Multi Gateway [Payment Debug]: Attempting to get account for payment with ID: {$submitted_account_id}");
            $selected_account = $accounts->get_account_for_payment($submitted_account_id);

            if ($selected_account) {
                error_log("PayPal Multi Gateway [Payment Debug]: Successfully found submitted account: {$selected_account->account_id}");
                // 保存账户ID到订单元数据
                $order->update_meta_data('_paypal_multi_gateway_account_id', $selected_account->account_id);
                $order->update_meta_data('_paypal_multi_gateway_selection_method', 'user_selected');
                $order->save();
            } else {
                error_log("PayPal Multi Gateway [Payment Debug]: Failed to find submitted account ID: {$submitted_account_id}");
            }
        }

        // 如果没有有效的提交账户，使用负载均衡选择
        if (!$selected_account) {
            error_log("PayPal Multi Gateway [Payment Debug]: No submitted account found, using load balancing");
            $selected_account = $accounts->select_account_for_checkout_with_load_balancing();

            if ($selected_account) {
                error_log("PayPal Multi Gateway [Payment Debug]: Load balancing selected account: {$selected_account->account_id}");
                // 保存负载均衡选择的账户ID
                $order->update_meta_data('_paypal_multi_gateway_account_id', $selected_account->account_id);
                $order->update_meta_data('_paypal_multi_gateway_selection_method', 'load_balancing');
                $order->save();
            } else {
                error_log("PayPal Multi Gateway [Payment Debug]: Load balancing failed to select account");
            }
        }

        // 记录订单支付信息到独立文件（在账户选择和价格调整之前）
        if ($selected_account) {
            $order_record = YXJTO_PayPal_Order_Record::get_instance();
            $additional_data = array(
                'payment_method' => $this->id,
                'selection_method' => $order->get_meta('_paypal_multi_gateway_selection_method') ?: 'unknown',
                'payment_database' => $payment_database,
                'current_database_at_record' => $current_database,
                'submitted_account_id' => $submitted_account_id
            );
            
            $order_record->record_order(
                $order_id,
                $selected_account->account_id,
                $source_database,
                $additional_data
            );
            
            $this->log_debug("Order #{$order_id} payment info recorded: Account={$selected_account->account_id}, Source DB={$source_database}");
        }

        // 在账户选择之前调整价格和其他费用
        if($payment_database !== $current_database){
            $this->adjust_order_pricing_before_payment_v2($order);
        }
        

        // Allow developers to modify account selection
        $selected_account = apply_filters('paypal_multi_gateway_account_selection', $selected_account, $accounts->get_active_accounts());

        if (!$selected_account) {
            $error_message = __('Payment error: No available PayPal accounts.', 'yxjto-paypal-multi-gateway') . ($submitted_account_id ? ' (' . sprintf(__('Submitted account ID: %s', 'yxjto-paypal-multi-gateway'), $submitted_account_id) . ')' : '');

            wc_add_notice($error_message, 'error');

            // 恢复原来的数据库连接
            if (isset($current_database) && $current_database !== 'default') {
                $gateway->switch_database($current_database);
            }

            return array(
                'result' => 'fail',
                'redirect' => '',
                'tt' => __('Payment Error', 'yxjto-paypal-multi-gateway'),
                'submitted_account_id' => $submitted_account_id
            );
        }

        // 自动添加IP规则（如果启用）- 在支付处理前执行
        $this->auto_add_ip_rule_before_payment($order);

        // Process payment based on account type (带重试逻辑)
        try {
            $api = new YXJTO_PayPal_Multi_Gateway_API();
            $plugin_settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
            $max_retries = intval($plugin_settings['retry_count']);
            $retry_count = 0;
            $result = null;
            $last_error = '';

            // 重试逻辑
            while ($retry_count <= $max_retries) {
                $result = $api->create_payment($order, $selected_account);

                if ($result['success']) {
                    break; // 成功则跳出重试循环
                }

                $last_error = $result['message'];
                $retry_count++;

                if ($retry_count <= $max_retries) {
                    // 选择下一个可用账户进行重试
                    $next_account = $accounts->select_account();
                    if ($next_account && $next_account->account_id !== $selected_account->account_id) {
                        $selected_account = $next_account;
                        // 更新订单元数据，保存新选择的账户ID
                        $order->update_meta_data('_paypal_multi_gateway_account_id', $next_account->account_id);
                        $order->update_meta_data('_paypal_multi_gateway_selection_method', 'retry_fallback');
                        $order->save();
                        
                        error_log("PayPal Multi Gateway [Retry Debug]: Switched to account {$next_account->account_id} for retry #{$retry_count}");
                    }

                    // 短暂延迟后重试
                    sleep(1);
                }
            }

            if ($result && $result['success']) {
                // 根据账户类型设置不同的订单状态消息
                $status_message = '';
                switch ($selected_account->account_type) {
                    case 'email':
                        $status_message = __('Redirecting to PayPal email payment page, please complete payment', 'yxjto-paypal-multi-gateway');
                        break;
                    case 'paypal_me':
                        $status_message = __('Redirecting to PayPal.me payment page, please complete payment', 'yxjto-paypal-multi-gateway');
                        break;
                    case 'api':
                        $status_message = __('Redirecting to PayPal Express Checkout, please complete payment', 'yxjto-paypal-multi-gateway');
                        break;
                    default:
                        $status_message = __('Redirecting to PayPal payment page, please complete payment', 'yxjto-paypal-multi-gateway');
                }

                // Mark as on-hold (we're awaiting the payment)
                $order->update_status('on-hold', $status_message);

                // Store comprehensive payment info in order meta
                $order->update_meta_data('_paypal_multi_gateway_account_id', $selected_account->account_id);
                $order->update_meta_data('_paypal_multi_gateway_payment_id', $result['payment_id']);
                $order->update_meta_data('_paypal_multi_gateway_payment_url', $result['approval_url']);
                $order->update_meta_data('_paypal_multi_gateway_account_type', $selected_account->account_type);
                $order->update_meta_data('_paypal_multi_gateway_testmode', $this->testmode ? 'yes' : 'no');
                $order->update_meta_data('_paypal_multi_gateway_created_at', current_time('mysql'));

                // Store customer info for PayPal
                $order->update_meta_data('_paypal_multi_gateway_customer_email', $order->get_billing_email());
                $order->update_meta_data('_paypal_multi_gateway_customer_name', $order->get_billing_first_name() . ' ' . $order->get_billing_last_name());

                $order->save();

                // Add order note with detailed info
                $note = sprintf(
                    __('PayPal payment created. Account: %s (%s), Payment ID: %s', 'yxjto-paypal-multi-gateway'),
                    $selected_account->account_id,
                    $selected_account->account_type,
                    $result['payment_id']
                );
                $order->add_order_note($note);

                // Reduce stock levels
                wc_reduce_stock_levels($order_id);

                // 注意：购物车清空已由订单复制模块在订单创建时处理
                // 这确保了无论支付是否成功，购物车都会被清空，避免重复下单
                // 此处不再需要手动清空购物车

                // 记录交易日志到数据库 - 增强版本
                $this->log_debug("About to log transaction - Current database check");
                
                // 确保在正确的数据库中记录事务日志
                $source_database = 'default';
                if (class_exists('YXJTO_Gateway')) {
                    $gateway = YXJTO_Gateway::get_instance();
                    if ($gateway) {
                        $source_database = $gateway->get_current_database();
                        $this->log_debug("Creating payment in database: {$source_database}");
                        
                        // 如果不在默认数据库，切换到默认数据库记录事务日志
                        if ($source_database !== 'default') {
                            $this->log_debug("Switching to default database for transaction logging");
                            $gateway->switch_database('default');
                        }
                    }
                }

                $log_result = $this->safe_log_transaction(
                    $result['payment_id'],
                    $order_id,
                    $selected_account->account_id,
                    $order->get_total(),
                    $order->get_currency(),
                    'pending',
                    array(
                        'account_type' => $selected_account->account_type,
                        'account_name' => $selected_account->account_id,
                        'approval_url' => $result['approval_url'],
                        'created_at' => current_time('mysql'),
                        'source_database' => $source_database
                    )
                );

                if ($log_result === false) {
                    $this->log_error("Failed to log transaction for payment ID: {$result['payment_id']}");
                } else {
                    $this->log_debug("Successfully logged transaction with log ID: {$log_result}");
                }

                // 如果之前切换了数据库，切换回源数据库
                if (isset($gateway) && $source_database !== 'default') {
                    $this->log_debug("Switching back to source database: {$source_database}");
                    $gateway->switch_database($source_database);
                }

                // // 在订单中记录源数据库信息，用于回调时正确切换
                // $order->update_meta_data('_paypal_multi_gateway_source_database', $source_database);
                // $order->save();

                // Hook: After successful payment creation
                do_action('paypal_multi_gateway_after_payment', $order, $result);

                // 触发订单复制钩子
                do_action('yxjto_order_replication_payment_created', $order_id, $order, $result);

                // 准备返回数据
                $redirect_result = array(
                    'result' => 'success',
                    'redirect' => $result['approval_url']
                );

                // 根据账户类型决定重定向方式
                switch ($selected_account->account_type) {
                    case 'email':
                        // 邮箱类型：直接重定向到 PayPal 标准支付页面
                        return $redirect_result;

                    case 'paypal_me':
                        // PayPal.me 类型：直接重定向到 PayPal.me 链接
                        return $redirect_result;

                    case 'api':
                        // API 类型：重定向到 PayPal Express Checkout
                        return $redirect_result;

                    default:
                        // 未知类型：使用默认处理
                        // 恢复原来的数据库连接
                        if (isset($current_database) && $current_database !== 'default') {
                            $gateway->switch_database($current_database);
                        }

                        return array(
                            'result' => 'success',
                            'redirect' => $result['approval_url']
                        );
                }

            } else {
                // 记录失败详情（包含重试信息）
                $error_message = sprintf(__('Payment creation failed (still failed after %d retries): %s', 'yxjto-paypal-multi-gateway'), $retry_count, ($result ? $result['message'] : $last_error));

                $order->add_order_note($error_message);
                $order->update_status('failed', $error_message);

                wc_add_notice($error_message, 'error');

                // 记录失败的交易日志
                $this->safe_log_transaction(
                    'failed_' . $order_id . '_' . time(),
                    $order_id,
                    $selected_account ? $selected_account->account_id : 'unknown',
                    $order->get_total(),
                    $order->get_currency(),
                    'failed',
                    array(
                        'error_message' => $result['message'],
                        'account_type' => $selected_account ? $selected_account->account_type : 'unknown',
                        'failed_at' => current_time('mysql')
                    )
                );

                // Hook: After failed payment
                do_action('paypal_multi_gateway_payment_failed', $order, $result);

                // 恢复原来的数据库连接
                if (isset($current_database) && $current_database !== 'default') {
                    $gateway->switch_database($current_database);
                }

                return array(
                    'result' => 'fail',
                    'redirect' => '',
                    'tt' => $error_message,
                    'submitted_account_id' => $submitted_account_id
                );
            }

        } catch (Exception $e) {
            $error_message = __('Payment processing exception:', 'yxjto-paypal-multi-gateway') . ' ' . $e->getMessage();

            $order->add_order_note($error_message);
            $order->update_status('failed', $error_message);

            wc_add_notice($error_message, 'error');

            // 记录异常的交易日志
            $this->safe_log_transaction(
                'error_' . $order_id . '_' . time(),
                $order_id,
                isset($selected_account) ? $selected_account->account_id : 'unknown',
                $order->get_total(),
                $order->get_currency(),
                'error',
                array(
                    'exception_message' => $e->getMessage(),
                    'exception_trace' => $e->getTraceAsString(),
                    'error_at' => current_time('mysql')
                )
            );

            // 恢复原来的数据库连接
            if (isset($current_database) && $current_database !== 'default') {
                $gateway->switch_database($current_database);
            }

            return array(
                'result' => 'fail',
                'redirect' => '',
                'tt' => $error_message ,
                'submitted_account_id' => $submitted_account_id
            );
        }
    }

    /**
     * Check for PayPal response
     */
    public function check_response() {
        // 记录所有回调参数用于调试
        $this->log_debug("PayPal response callback - GET params: " . print_r($_GET, true));
        $this->log_debug("PayPal response callback - POST params: " . print_r($_POST, true));
        
        if (isset($_GET['paymentId']) && isset($_GET['PayerID'])) {
            // API 支付返回
            $this->handle_payment_return();
        } elseif (isset($_GET['cancel']) || isset($_GET['cancelled'])) {
            // 支付取消
            $this->handle_payment_cancel();
        } elseif (isset($_GET['token']) && isset($_GET['PayerID'])) {
            // Express Checkout 返回
            $this->handle_express_checkout_return();
        } elseif (isset($_POST['payment_status']) || isset($_POST['txn_id'])) {
            // IPN 通知
            $this->handle_ipn();
        } elseif (isset($_POST['tx']) || isset($_POST['amt']) || isset($_POST['st']) || 
                  (isset($_GET['order_id']) && (isset($_POST['payer_id']) || isset($_GET['PayerID'])))) {
            // PayPal 标准支付（邮箱）返回 - PDT 或 POST 返回
            $this->handle_standard_payment_return();
        } else {
            // 未识别的回调类型，记录日志
            $this->log_error("Unrecognized PayPal callback type. GET: " . print_r($_GET, true) . " POST: " . print_r($_POST, true));
            
            // 尝试从通用参数中获取订单信息
            $order_id = 0;
            if (isset($_GET['order_id'])) {
                $order_id = absint($_GET['order_id']);
            } elseif (isset($_POST['custom'])) {
                $order_id = intval($_POST['custom']);
            } elseif (isset($_GET['invoice'])) {
                // 从invoice中提取订单ID
                $invoice = sanitize_text_field($_GET['invoice']);
                if (preg_match('/order_(\d+)/', $invoice, $matches)) {
                    $order_id = intval($matches[1]);
                }
            }
            
            if ($order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    // 记录感谢页面显示所需的数据库信息
                    $current_database = '';
                    if (class_exists('YXJTO_Gateway')) {
                        $gateway = YXJTO_Gateway::get_instance();
                        if ($gateway) {
                            $current_database = $gateway->get_current_database();
                            $order->update_meta_data('_paypal_thankyou_page_database', $current_database);
                            $order->save();
                            $this->log_debug("Saved thankyou page database reference: {$current_database} for generic redirect order #{$order_id}");
                        }
                    }
                    
                    // 构建标准的感谢页面URL（恢复默认行为）
                    $return_url = $this->get_return_url($order);
                    
                    $this->log_debug("Redirecting to order success page for order: {$order_id}");
                    wp_redirect($return_url);
                    exit;
                }
            }
            
            // 如果都无法确定，重定向到结账页面
            wp_redirect(wc_get_checkout_url());
            exit;
        }
    }

    /**
     * Handle payment return from PayPal
     */
    private function handle_payment_return() {
        $payment_id = sanitize_text_field($_GET['paymentId']);
        $payer_id = sanitize_text_field($_GET['PayerID']);
        $order_id = isset($_GET['order_id']) ? absint($_GET['order_id']) : 0;

        // 调试日志：记录回调参数
        $this->log_debug("PayPal return callback - Payment ID: {$payment_id}, Payer ID: {$payer_id}, Order ID: {$order_id}");
        $this->log_debug("PayPal return callback - All GET params: " . print_r($_GET, true));

        // 如果没有直接的order_id，尝试从其他方式获取
        if (!$order_id) {
            // 方法1：从PayPal payment ID中尝试提取订单ID（针对email类型）
            if (strpos($payment_id, 'email_') === 0) {
                // email_123_1234567890 格式
                $parts = explode('_', $payment_id);
                if (count($parts) >= 2 && is_numeric($parts[1])) {
                    $order_id = intval($parts[1]);
                    $this->log_debug("PayPal return callback - Extracted order ID from email payment ID: {$order_id}");
                }
            }
            
            // 方法2：从数据库中查找匹配的订单
            if (!$order_id) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'postmeta';
                
                $order_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT post_id FROM {$table_name} 
                     WHERE meta_key = '_paypal_multi_gateway_payment_id' 
                     AND meta_value = %s 
                     LIMIT 1",
                    $payment_id
                ));
                
                if ($order_id) {
                    $order_id = intval($order_id);
                    $this->log_debug("PayPal return callback - Found order ID from database: {$order_id}");
                }
            }
            
            // 方法3：如果使用HPOS，从订单表中查找
            if (!$order_id && class_exists('Automattic\WooCommerce\Utilities\OrderUtil') && 
                \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {
                
                global $wpdb;
                $orders_table = $wpdb->prefix . 'wc_orders';
                $order_meta_table = $wpdb->prefix . 'wc_orders_meta';
                
                $order_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT order_id FROM {$order_meta_table} 
                     WHERE meta_key = '_paypal_multi_gateway_payment_id' 
                     AND meta_value = %s 
                     LIMIT 1",
                    $payment_id
                ));
                
                if ($order_id) {
                    $order_id = intval($order_id);
                    $this->log_debug("PayPal return callback - Found order ID from HPOS: {$order_id}");
                }
            }
        }

        if (!$order_id) {
            $this->log_error("PayPal return callback - Could not determine order ID from payment ID: {$payment_id}");
            wp_die(__('Order information could not be found. Please contact support with your payment details.', 'yxjto-paypal-multi-gateway'));
        }

        // 根据订单复制设置决定回调处理使用的数据库
        $source_database = 'default';
        if (class_exists('YXJTO_Gateway')) {
            $gateway = YXJTO_Gateway::get_instance();
            $current_database = $gateway->get_current_database();

            // 获取订单复制设置
            $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
            $enable_default_db_redirect = !empty($order_settings['enable_default_db_payment_redirect']);

            $this->log_debug("PayPal return callback - Current database: {$current_database}, Enable default DB redirect: " . ($enable_default_db_redirect ? 'Yes' : 'No'));

            // 决定回调处理使用的数据库
            $callback_database = $this->determine_payment_database($current_database, $enable_default_db_redirect);

            // 首先尝试在当前数据库中查找订单
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_debug("Order #{$order_id} not found in current database: {$current_database}");

                // 如果需要切换到其他数据库
                if ($callback_database !== $current_database) {
                    $this->log_debug("Switching to {$callback_database} database to find order");
                    $switch_result = $gateway->switch_database($callback_database);
                    
                    if (!$switch_result) {
                        $this->log_error("Failed to switch to default database");
                        wp_die(__('Database switch failed during payment processing.', 'yxjto-paypal-multi-gateway'));
                    }
                    
                    $order = wc_get_order($order_id);
                    if ($order) {
                        $source_database = 'default';
                        $this->log_debug("Found order #{$order_id} in default database");
                    }
                }
                
                // 如果仍然找不到，尝试在所有数据库中查找
                if (!$order) {
                    $this->log_debug("Order #{$order_id} not found in default database, trying all databases");
                    $order = $this->find_order_in_databases($order_id, $gateway);
                    if ($order) {
                        $source_database = $gateway->get_current_database();
                        $this->log_debug("Found order #{$order_id} in database: {$source_database}");
                    }
                }
            } else {
                $source_database = $current_database;
                $this->log_debug("Found order #{$order_id} in current database: {$current_database}");
            }
        } else {
            $order = wc_get_order($order_id);
        }

        if (!$order) {
            $this->log_error("Order #{$order_id} not found in any database");
            wp_die(__('Order not found.', 'yxjto-paypal-multi-gateway'));
        }

        // 记录找到订单的数据库
        $this->log_debug("Processing payment return in database: {$source_database} for order #{$order_id}");

        // 保存源数据库信息，稍后需要切换回去
        $original_source_database = $source_database;

        // 确保在 execute_payment 之前使用默认数据库的订单
        if ($enable_default_db_redirect && $source_database !== 'default' ) {
            $this->log_debug("Switching to default database for execute_payment operation");
            $switch_result = $gateway->switch_database('default');
            if (!$switch_result) {
                $this->log_error("Failed to switch to default database for execute_payment");
                wp_die(__('Database switch failed during payment execution.', 'yxjto-paypal-multi-gateway'));
            }

            // 从默认数据库重新获取订单
            $default_order = wc_get_order($order_id);
            if (!$default_order) {
                $this->log_error("Order #{$order_id} not found in default database for execute_payment");
                wp_die(__('Order not found in default database for payment execution.', 'yxjto-paypal-multi-gateway'));
            }

            $this->log_debug("Using default database order for execute_payment");
            $order_for_execute = $default_order;
        } else {
            $this->log_debug("Already in default database, using current order for execute_payment");
            $order_for_execute = $order;
        }
        // 优先从订单记录文件获取支付账号和源数据库信息
        $order_record = YXJTO_PayPal_Order_Record::get_instance();
        $recorded_info = $order_record->get_order_record($order_id);
        
        if ($recorded_info) {

            $account_id = $recorded_info['payment_account_id'];
   
        }else {
            // 如果没有记录，使用订单元数据
            $account_id = $order_for_execute->get_meta('_paypal_multi_gateway_account_id');
        }
        $this->log_debug("PayPal return callback - Account ID from default database order: " . ($account_id ?: 'null'));

        // Execute payment（使用默认数据库的订单）
        $api = new YXJTO_PayPal_Multi_Gateway_API();
        $result = $api->execute_payment($payment_id, $payer_id, $order_for_execute, $account_id);
        $this->log_debug("result " . json_encode($result));

        // execute_payment 完成后，切换到源数据库进行后续处理
        if ($original_source_database !== 'default') {
            $this->log_debug("Switching back to source database ({$original_source_database}) for post-execution processing");
            $switch_result = $gateway->switch_database($original_source_database);
            if (!$switch_result) {
                $this->log_error("Failed to switch back to source database ({$original_source_database}) after execute_payment");
                // 不中断流程，但记录错误
            } else {
                // 重新获取源数据库中的订单用于后续处理
                $source_order = wc_get_order($order_id);
                if ($source_order) {
                    $order = $source_order;
                    $this->log_debug("Using source database order for post-execution processing");
                } else {
                    $this->log_error("Order #{$order_id} not found in source database ({$original_source_database}) after execute_payment");
                    // 继续使用默认数据库的订单
                }
            }
        }

        if ($result['success']) {
            $order->payment_complete($result['transaction_id']);
            $order->add_order_note(sprintf(__('PayPal payment completed. Transaction ID: %s', 'yxjto-paypal-multi-gateway'), $result['transaction_id']));

            // 清理支付处理标记
            $order->delete_meta_data('_paypal_multi_gateway_payment_in_progress');
            $source_database = $order->get_meta('_paypal_multi_gateway_source_database');
            if ($source_database) {
                $order->update_meta_data('_paypal_multi_gateway_payment_completed_from', $source_database);
                // $order->delete_meta_data('_paypal_multi_gateway_source_database');
            }
            $order->save();

            // 确保购物车在支付成功后被清空（在源数据库中）
            // 注意：购物车通常已在订单创建时被清空，这里是额外的安全检查
            if (function_exists('WC') && WC()->cart && !WC()->cart->is_empty()) {
                WC()->cart->empty_cart();
                $this->log_debug("Cart cleared after successful PayPal payment for order #{$order_id} (safety check)");
            } else {
                $this->log_debug("Cart was already empty after PayPal payment for order #{$order_id}");
            }

            // 触发支付信息复制
            do_action('yxjto_order_replication_payment_completed', $order_id, $order, $result);

            // 记录支付完成日志 - 增强版本，确保在正确数据库中记录
            $account_id = $order->get_meta('_paypal_multi_gateway_account_id');
            
            // 确保事务日志记录在默认数据库中
            $current_database_for_log = $source_database;
            if (isset($gateway) && $source_database !== 'default') {
                $this->log_debug("Switching to default database for transaction logging");
                $gateway->switch_database('default');
                $current_database_for_log = 'default';
            }

            $log_result = $this->safe_log_transaction(
                $result['transaction_id'],
                $order_id,
                $account_id ?: 'unknown',
                $order->get_total(),
                $order->get_currency(),
                'completed',
                array(
                    'payment_method' => 'api_return',
                    'payment_id' => $payment_id,
                    'payer_id' => $payer_id,
                    'execution_result' => $result,
                    'completed_at' => current_time('mysql'),
                    'source_database' => $source_database,
                    'log_database' => $current_database_for_log
                )
            );

            if ($log_result === false) {
                $this->log_error("Failed to log completed transaction for order #{$order_id}");
            } else {
                $this->log_debug("Successfully logged completed transaction with log ID: {$log_result}");
            }

            // 恢复到源数据库
            if (isset($gateway) && $source_database !== 'default') {
                $gateway->switch_database($source_database);
            }

            // 记录感谢页面显示所需的数据库信息
            // $current_database = '';
            // if (class_exists('YXJTO_Gateway')) {
            //     $gateway = YXJTO_Gateway::get_instance();
            //     if ($gateway) {
            //         $current_database = $gateway->get_current_database();
            //         // 记录当前数据库，以便感谢页面正确显示
            //         $order->update_meta_data('_paypal_thankyou_page_database', $current_database);
            //         $order->save();
            //         $this->log_debug("Saved thankyou page database reference: {$current_database} for order #{$order_id}");
            //     }
            // }
            // 记录当前数据库，以便感谢页面正确显示
            $current_database = $source_database;
            $order->update_meta_data('_paypal_thankyou_page_database', $current_database);
            $order->save();
            $this->log_debug("Saved thankyou page database reference: {$current_database} for order #{$order_id}");

            // 在构建感谢页面URL之前，将默认数据库订单的支付meta信息复制到其他所有数据库
            $this->replicate_payment_meta_to_all_databases($order_id, $gateway);

            // 复制更新订单状态到所有数据库（从源数据库复制到其他数据库）
            $this->replicate_order_status_to_all_databases($order_id, $gateway, $current_database);

            // 构建标准的感谢页面URL（恢复默认行为）
            $return_url = $this->get_return_url($order);

            wp_redirect($return_url);
            exit;
        } else {
            // 记录支付执行失败日志 - 增强版本
            $account_id = $order->get_meta('_paypal_multi_gateway_account_id');
            $error_message = isset($result['message']) ? $result['message'] : __('Unknown error occurred', 'yxjto-paypal-multi-gateway');

            // 确保失败日志也记录在默认数据库中
            $current_database_for_log = $source_database;
            if (isset($gateway) && $source_database !== 'default') {
                $this->log_debug("Switching to default database for failed transaction logging");
                $gateway->switch_database('default');
                $current_database_for_log = 'default';
            }

            $log_result = $this->safe_log_transaction(
                'failed_execution_' . $order_id . '_' . time(),
                $order_id,
                $account_id ?: 'unknown',
                $order->get_total(),
                $order->get_currency(),
                'failed',
                array(
                    'error_message' => $error_message,
                    'payment_id' => $payment_id,
                    'payer_id' => $payer_id,
                    'execution_failed_at' => current_time('mysql'),
                    'full_result' => $result,
                    'source_database' => $source_database,
                    'log_database' => $current_database_for_log
                )
            );

            if ($log_result === false) {
                $this->log_error("Failed to log failed transaction for order #{$order_id}");
            } else {
                $this->log_debug("Successfully logged failed transaction with log ID: {$log_result}");
            }

            // 恢复到源数据库
            if (isset($gateway) && $source_database !== 'default') {
                $gateway->switch_database($source_database);
            }

            // 更新订单状态为失败
            $order->update_status('failed', sprintf(__('PayPal payment execution failed: %s', 'yxjto-paypal-multi-gateway'), $error_message));
            $order->add_order_note(sprintf(__('PayPal payment execution failed: %s (Payment ID: %s, Payer ID: %s)', 'yxjto-paypal-multi-gateway'), $error_message, $payment_id, $payer_id));

            // 添加用户友好的错误消息
            wc_add_notice(sprintf(__('Payment failed: %s. Please try again or contact support.', 'yxjto-paypal-multi-gateway'), $error_message), 'error');

            // 重定向到结账页面而不是显示wp_die
            wp_redirect(wc_get_checkout_url());
            exit;
        }
    }

    /**
     * Handle payment cancel
     */
    private function handle_payment_cancel() {
        $order_id = isset($_GET['order_id']) ? absint($_GET['order_id']) : 0;

        // 调试日志：记录取消回调参数
        $this->log_debug("PayPal cancel callback - Order ID: {$order_id}");
        $this->log_debug("PayPal cancel callback - All GET params: " . print_r($_GET, true));

        // 如果没有直接的order_id，尝试从其他方式获取
        if (!$order_id) {
            // 从token获取（Express Checkout）
            if (isset($_GET['token'])) {
                $token = sanitize_text_field($_GET['token']);
                global $wpdb;
                
                $order_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT post_id FROM {$wpdb->postmeta} 
                     WHERE meta_key = '_paypal_multi_gateway_token' 
                     AND meta_value = %s 
                     LIMIT 1",
                    $token
                ));
                
                if ($order_id) {
                    $order_id = intval($order_id);
                    $this->log_debug("PayPal cancel callback - Found order ID from token: {$order_id}");
                }
            }
            
            // 从invoice获取
            if (!$order_id && isset($_GET['invoice'])) {
                $invoice = sanitize_text_field($_GET['invoice']);
                if (preg_match('/order_(\d+)/', $invoice, $matches)) {
                    $order_id = intval($matches[1]);
                    $this->log_debug("PayPal cancel callback - Extracted order ID from invoice: {$order_id}");
                }
            }
        }

        // 根据订单复制设置决定取消回调处理使用的数据库
        if (class_exists('YXJTO_Gateway')) {
            $gateway = YXJTO_Gateway::get_instance();
            $current_database = $gateway->get_current_database();

            // 获取订单复制设置
            $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
            $enable_default_db_redirect = !empty($order_settings['enable_default_db_payment_redirect']);

            $this->log_debug("PayPal cancel callback - Current database: {$current_database}, Enable default DB redirect: " . ($enable_default_db_redirect ? 'Yes' : 'No'));

            // 决定取消回调处理使用的数据库
            $callback_database = $this->determine_payment_database($current_database, $enable_default_db_redirect);

            if ($current_database !== $callback_database) {
                $this->log_debug("PayPal cancel callback - Switching to {$callback_database} database");
                $switch_result = $gateway->switch_database($callback_database);

                if (!$switch_result) {
                    $this->log_error("PayPal cancel callback - Failed to switch to {$callback_database} database");
                } else {
                    $this->log_debug("PayPal cancel callback - Successfully switched to {$callback_database} database");
                }
            } else {
                $this->log_debug("PayPal cancel callback - Using current database {$current_database}");
            }
        }

        if ($order_id) {
            $order = wc_get_order($order_id);
            if ($order) {
                $order->update_status('cancelled', __('Payment cancelled by customer.', 'yxjto-paypal-multi-gateway'));

                // 记录取消日志
                $account_id = $order->get_meta('_paypal_multi_gateway_account_id');
                $this->safe_log_transaction(
                    'cancelled_' . $order_id . '_' . time(),
                    $order_id,
                    $account_id ?: 'unknown',
                    $order->get_total(),
                    $order->get_currency(),
                    'cancelled',
                    array(
                        'cancelled_by' => 'customer',
                        'cancelled_at' => current_time('mysql'),
                        'cancel_params' => $_GET
                    )
                );
                
                // 添加用户友好的取消消息
                wc_add_notice(__('Payment was cancelled. You can try again or choose a different payment method.', 'yxjto-paypal-multi-gateway'), 'notice');
                
                $this->log_debug("PayPal cancel callback - Order {$order_id} marked as cancelled");
            } else {
                $this->log_error("PayPal cancel callback - Order {$order_id} not found");
            }
        } else {
            $this->log_error("PayPal cancel callback - Could not determine order ID");
            wc_add_notice(__('Payment was cancelled.', 'yxjto-paypal-multi-gateway'), 'notice');
        }

        wp_redirect(wc_get_checkout_url());
        exit;
    }

    /**
     * Handle Express Checkout return
     */
    private function handle_express_checkout_return() {
        $token = sanitize_text_field($_GET['token']);
        $payer_id = sanitize_text_field($_GET['PayerID']);
        $order_id = isset($_GET['order_id']) ? absint($_GET['order_id']) : 0;

        $this->log_debug("PayPal Express Checkout return - Token: {$token}, Payer ID: {$payer_id}, Order ID: {$order_id}");

        // 如果没有order_id，尝试从token中获取
        if (!$order_id) {
            global $wpdb;
            
            // 从postmeta表查找
            $order_id = $wpdb->get_var($wpdb->prepare(
                "SELECT post_id FROM {$wpdb->postmeta} 
                 WHERE meta_key = '_paypal_multi_gateway_token' 
                 AND meta_value = %s 
                 LIMIT 1",
                $token
            ));
            
            if ($order_id) {
                $order_id = intval($order_id);
                $this->log_debug("Express Checkout - Found order ID from token: {$order_id}");
            }
        }

        if (!$order_id) {
            $this->log_error("Express Checkout - Could not determine order ID from token: {$token}");
            wp_die(__('Order information could not be found. Please contact support.', 'yxjto-paypal-multi-gateway'));
        }

        // 使用与普通return相同的处理逻辑
        $_GET['paymentId'] = $token; // 将token作为payment_id处理
        $this->handle_payment_return();
    }

    /**
     * Safe log transaction - 防止数据库错误影响支付流程
     */
    private function safe_log_transaction($transaction_id, $order_id, $account_id, $amount, $currency, $status, $gateway_response = array()) {
        try {
            global $wpdb;

            $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

            // 增强调试日志 - 记录事务记录尝试
            $this->log_debug("Attempting to log transaction: ID={$transaction_id}, Order={$order_id}, Account={$account_id}, Amount={$amount}, Status={$status}");

            // 检查表是否存在
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if (!$table_exists) {
                $this->log_error("Transaction log table does not exist: {$table_name}");
                
                // 尝试创建表
                $this->create_transaction_log_table();
                
                // 重新检查表是否存在
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
                if (!$table_exists) {
                    $this->log_error("Failed to create transaction log table: {$table_name}");
                    return false;
                }
                
                $this->log_debug("Successfully created transaction log table: {$table_name}");
            }

            // 记录当前数据库信息
            if (class_exists('YXJTO_Gateway')) {
                $gateway = YXJTO_Gateway::get_instance();
                if ($gateway) {
                    $current_db = $gateway->get_current_database();
                    $this->log_debug("Logging transaction in database: {$current_db}");
                }
            }

            $result = $wpdb->insert(
                $table_name,
                array(
                    'transaction_id' => $transaction_id,
                    'account_id' => $account_id,
                    'order_id' => $order_id,
                    'amount' => $amount,
                    'currency' => $currency,
                    'status' => $status,
                    'gateway_response' => wp_json_encode($gateway_response),
                    'created_at' => current_time('mysql')
                ),
                array('%s', '%s', '%d', '%f', '%s', '%s', '%s', '%s')
            );

            if ($result === false) {
                $this->log_error("Failed to insert transaction log: " . $wpdb->last_error);
                $this->log_error("Failed query: " . $wpdb->last_query);
                return false;
            }

            $insert_id = $wpdb->insert_id;
            $this->log_debug("Successfully logged transaction with ID: {$insert_id}");
            
            // 额外验证：立即查询刚插入的记录
            $verify_record = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE id = %d",
                $insert_id
            ));
            
            if ($verify_record) {
                $this->log_debug("Transaction record verified in database: " . json_encode($verify_record));
            } else {
                $this->log_error("Transaction record not found after insertion - possible database issue");
            }

            return $insert_id;
            
        } catch (Exception $e) {
            // 记录到错误日志但不影响支付流程
            $this->log_error('Failed to log transaction - Exception: ' . $e->getMessage());
            $this->log_error('Exception trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 创建事务日志表
     */
    private function create_transaction_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            transaction_id varchar(255) NOT NULL,
            account_id varchar(255) NOT NULL,
            order_id bigint(20) NOT NULL,
            amount decimal(10,2) NOT NULL,
            currency varchar(10) NOT NULL,
            status varchar(50) NOT NULL,
            gateway_response longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY transaction_id (transaction_id),
            KEY order_id (order_id),
            KEY account_id (account_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);
        
        $this->log_debug("Table creation result: " . print_r($result, true));
    }

    /**
     * 在所有数据库中查找订单
     */
    private function find_order_in_databases($order_id, $gateway) {
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            return null;
        }

        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $original_database = $gateway->get_current_database();

        $this->log_debug("Searching for order #{$order_id} in " . count($databases) . " databases");

        foreach ($databases as $db_name => $db_config) {
            if (!$db_config['enabled']) {
                continue;
            }

            $this->log_debug("Checking database: {$db_name}");

            try {
                $switch_result = $gateway->switch_database($db_name);
                if (!$switch_result) {
                    $this->log_debug("Failed to switch to database: {$db_name}");
                    continue;
                }

                $order = wc_get_order($order_id);
                if ($order) {
                    $this->log_debug("Order #{$order_id} found in database: {$db_name}");
                    return $order;
                }

            } catch (Exception $e) {
                $this->log_error("Error checking database {$db_name}: " . $e->getMessage());
            }
        }

        // 如果没找到，恢复到原数据库
        try {
            $gateway->switch_database($original_database);
        } catch (Exception $e) {
            $this->log_error("Failed to restore original database: " . $e->getMessage());
        }

        $this->log_error("Order #{$order_id} not found in any database");
        return null;
    }

    /**
     * Handle standard PayPal payment return (for email-based payments)
     */
    private function handle_standard_payment_return() {
        $this->log_debug("PayPal standard payment return - Processing email-based payment return");
        
        // 从GET参数获取订单ID
        $order_id = isset($_GET['order_id']) ? absint($_GET['order_id']) : 0;
        
        // 从POST数据获取PayPal返回的信息
        $payer_id = isset($_POST['payer_id']) ? sanitize_text_field($_POST['payer_id']) : '';
        $payment_status = isset($_POST['payment_status']) ? sanitize_text_field($_POST['payment_status']) : '';
        $transaction_id = isset($_POST['txn_id']) ? sanitize_text_field($_POST['txn_id']) : '';
        $payment_amount = isset($_POST['mc_gross']) ? sanitize_text_field($_POST['mc_gross']) : '';
        $payment_currency = isset($_POST['mc_currency']) ? sanitize_text_field($_POST['mc_currency']) : '';
        
        // 如果没有从POST获取，尝试从GET获取PayerID
        if (!$payer_id && isset($_GET['PayerID'])) {
            $payer_id = sanitize_text_field($_GET['PayerID']);
        }
        
        $this->log_debug("PayPal standard return - Order ID: {$order_id}, Payer ID: {$payer_id}, Status: {$payment_status}, Transaction ID: {$transaction_id}");
        
        if (!$order_id) {
            $this->log_error("PayPal standard return - No order ID found");
            wp_redirect(wc_get_checkout_url());
            exit;
        }
        
        // 查找订单
        $order = wc_get_order($order_id);
        if (!$order) {
            // 尝试在所有数据库中查找订单
            if (class_exists('YXJTO_Gateway')) {
                $gateway = YXJTO_Gateway::get_instance();
                $order = $this->find_order_in_databases($order_id, $gateway);
            }
        }
        
        if (!$order) {
            $this->log_error("PayPal standard return - Order #{$order_id} not found");
            wp_redirect(wc_get_checkout_url());
            exit;
        }
        
        // 检查订单状态
        if ($order->get_status() === 'completed' || $order->get_status() === 'processing') {
            $this->log_debug("PayPal standard return - Order #{$order_id} already completed, redirecting to success page");
            wp_redirect($this->get_return_url($order));
            exit;
        }
        
        // 如果有支付状态信息，处理支付完成
        if ($payment_status === 'Completed' && $transaction_id) {
            $this->log_debug("PayPal standard return - Payment completed with transaction ID: {$transaction_id}");
            
            // 验证金额
            if ($payment_amount && abs($payment_amount - $order->get_total()) > 0.01) {
                $this->log_error("PayPal standard return - Amount mismatch. Expected: {$order->get_total()}, Received: {$payment_amount}");
                $order->add_order_note(sprintf(__('PayPal payment amount mismatch. Expected: %s, Received: %s', 'yxjto-paypal-multi-gateway'), $order->get_total(), $payment_amount));
            }
            
            // 完成支付
            $order->payment_complete($transaction_id);
            $order->add_order_note(sprintf(__('PayPal standard payment completed. Transaction ID: %s', 'yxjto-paypal-multi-gateway'), $transaction_id));
            
            // 清理支付处理标记
            $order->delete_meta_data('_paypal_multi_gateway_payment_in_progress');
            $source_database = $order->get_meta('_paypal_multi_gateway_source_database');
            if ($source_database) {
                $order->update_meta_data('_paypal_multi_gateway_payment_completed_from', $source_database);
                // $order->delete_meta_data('_paypal_multi_gateway_source_database');
            }
            $order->save();
            
            // 触发支付完成钩子
            do_action('yxjto_order_replication_payment_completed', $order_id, $order, array(
                'transaction_id' => $transaction_id,
                'payment_method' => 'standard_return'
            ));
        } elseif ($payer_id) {
            // 有PayerID但没有完整的支付信息，可能需要等待IPN
            $this->log_debug("PayPal standard return - Payer ID received, waiting for IPN confirmation");
            
            // 创建模拟的transaction ID用于追踪
            $temp_transaction_id = 'standard_' . $order_id . '_' . time() . '_' . substr($payer_id, 0, 8);
            
            // 暂时标记为processing，等待IPN确认
            $order->update_status('processing', __('PayPal payment pending IPN verification.', 'yxjto-paypal-multi-gateway'));
            $order->add_order_note(sprintf(__('PayPal standard payment return received. Payer ID: %s. Awaiting IPN confirmation.', 'yxjto-paypal-multi-gateway'), $payer_id));
            
            // 保存临时交易信息
            $order->update_meta_data('_paypal_multi_gateway_temp_transaction_id', $temp_transaction_id);
            $order->update_meta_data('_paypal_multi_gateway_payer_id', $payer_id);
            $order->save();
        } else {
            $this->log_debug("PayPal standard return - No payment confirmation data, treating as standard return");
        }
        
        // 重定向到成功页面
        wp_redirect($this->get_return_url($order));
        exit;
    }

    /**
     * Handle return from PayPal
     */
    public function handle_return() {
        $this->handle_payment_return();
    }

    /**
     * Handle cancel from PayPal
     */
    public function handle_cancel() {
        $this->handle_payment_cancel();
    }

    /**
     * Handle IPN from PayPal
     */
    public function handle_ipn() {
        // 根据订单复制设置决定IPN回调处理使用的数据库
        if (class_exists('YXJTO_Gateway')) {
            $gateway = YXJTO_Gateway::get_instance();
            $current_database = $gateway->get_current_database();

            // 获取订单复制设置
            $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
            $enable_default_db_redirect = !empty($order_settings['enable_default_db_payment_redirect']);

            $this->log_debug("PayPal IPN callback - Current database: {$current_database}, Enable default DB redirect: " . ($enable_default_db_redirect ? 'Yes' : 'No'));

            // 决定IPN回调处理使用的数据库
            $callback_database = $this->determine_payment_database($current_database, $enable_default_db_redirect);

            if ($current_database !== $callback_database) {
                $this->log_debug("PayPal IPN callback - Switching to {$callback_database} database");
                $switch_result = $gateway->switch_database($callback_database);

                if (!$switch_result) {
                    $this->log_error("PayPal IPN callback - Failed to switch to {$callback_database} database");
                    exit;
                }

                $this->log_debug("PayPal IPN callback - Successfully switched to {$callback_database} database");
            } else {
                $this->log_debug("PayPal IPN callback - Using current database {$current_database}");
            }
        }

        // IPN 处理逻辑
        $raw_post_data = file_get_contents('php://input');
        $this->log_debug("PayPal IPN - Raw POST data: " . $raw_post_data);
        
        $raw_post_array = explode('&', $raw_post_data);
        $myPost = array();

        foreach ($raw_post_array as $keyval) {
            $keyval = explode('=', $keyval);
            if (count($keyval) == 2) {
                $myPost[$keyval[0]] = urldecode($keyval[1]);
            }
        }
        
        $this->log_debug("PayPal IPN - Parsed data: " . print_r($myPost, true));

        // 验证 IPN
        $req = 'cmd=_notify-validate';
        foreach ($myPost as $key => $value) {
            $value = urlencode($value);
            $req .= "&$key=$value";
        }

        // 发送验证请求到 PayPal
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $paypal_url = $settings['test_mode'] === 'yes' ? 'https://ipnpb.sandbox.paypal.com/cgi-bin/webscr' : 'https://ipnpb.paypal.com/cgi-bin/webscr';
        
        $this->log_debug("PayPal IPN - Validating with URL: {$paypal_url}");

        $response = wp_remote_post($paypal_url, array(
            'body' => $req,
            'timeout' => 60,
            'httpversion' => '1.1',
            'compress' => false,
            'decompress' => false
        ));

        $this->log_debug("PayPal IPN - Validation response: " . print_r($response, true));

        if (!is_wp_error($response) && $response['response']['code'] == 200 && strpos($response['body'], 'VERIFIED') === 0) {
            // IPN 验证成功，处理支付状态
            $this->log_debug("PayPal IPN - Verification successful, processing payment");
            $this->process_ipn_payment($myPost);
        } else {
            // IPN 验证失败
            $error_msg = is_wp_error($response) ? $response->get_error_message() : 'PayPal validation failed: ' . $response['body'];
            $this->log_error("PayPal IPN - Verification failed: " . $error_msg);
        }

        exit;
    }

    /**
     * Process IPN payment
     */
    private function process_ipn_payment($post_data) {
        $this->log_debug("IPN处理开始: " . print_r($post_data, true));
        
        if (isset($post_data['custom'])) {
            $order_id = intval($post_data['custom']);
            
            // 优先从订单记录文件获取支付账号和源数据库信息
            $order_record = YXJTO_PayPal_Order_Record::get_instance();
            $recorded_info = $order_record->get_order_record($order_id);
            
            if ($recorded_info) {
                $this->log_debug("IPN回调 - 从记录文件获取到订单 #{$order_id} 信息: 支付账号={$recorded_info['payment_account_id']}, 源数据库={$recorded_info['source_database']}");
                
                // 如果需要，可以根据记录的源数据库信息切换数据库
                if (class_exists('YXJTO_Gateway')) {
                    $gateway = YXJTO_Gateway::get_instance();
                    $current_database = $gateway->get_current_database();
                    
                    if ($current_database !== $recorded_info['source_database']) {
                        $this->log_debug("IPN回调 - 根据记录文件切换数据库: {$current_database} -> {$recorded_info['source_database']}");
                        $switch_result = $gateway->switch_database($recorded_info['source_database']);
                        if (!$switch_result) {
                            $this->log_error("IPN回调 - 切换到源数据库 {$recorded_info['source_database']} 失败");
                        }
                    }
                }
            } else {
                $this->log_debug("IPN回调 - 订单 #{$order_id} 在记录文件中未找到，使用默认处理流程");
            }
            
            $order = wc_get_order($order_id);

            if ($order) {
                $payment_status = strtolower($post_data['payment_status']);
                $transaction_id = isset($post_data['txn_id']) ? $post_data['txn_id'] : '';
                $payment_amount = isset($post_data['mc_gross']) ? floatval($post_data['mc_gross']) : 0;
                $payment_currency = isset($post_data['mc_currency']) ? $post_data['mc_currency'] : '';
                
                $this->log_debug("订单 {$order_id} IPN处理 - 状态: {$payment_status}, 交易ID: {$transaction_id}, 金额: {$payment_amount} {$payment_currency}");
                
                // 验证金额和货币
                if ($payment_amount && $payment_amount != $order->get_total()) {
                    $this->log_error("IPN金额验证失败 - 订单 {$order_id}: 预期 {$order->get_total()}, 实际 {$payment_amount}");
                    $order->add_order_note("PayPal IPN金额验证失败");
                }
                
                if ($payment_currency && $payment_currency != $order->get_currency()) {
                    $this->log_error("IPN货币验证失败 - 订单 {$order_id}: 预期 {$order->get_currency()}, 实际 {$payment_currency}");
                    $order->add_order_note("PayPal IPN货币验证失败");
                }

                switch ($payment_status) {
                    case 'completed':
                        if ($order->get_status() !== 'processing' && $order->get_status() !== 'completed') {
                            $order->payment_complete($post_data['txn_id']);
                            $order->add_order_note(sprintf('PayPal IPN支付完成 (交易ID: %s)', $transaction_id));
                            $this->log_debug("订单 {$order_id} 通过IPN支付完成");
                        }
                        
                        // 清理支付处理标记
                        $order->delete_meta_data('_paypal_multi_gateway_payment_in_progress');
                        $source_database = $order->get_meta('_paypal_multi_gateway_source_database');
                        if ($source_database) {
                            $order->update_meta_data('_paypal_multi_gateway_payment_completed_from', $source_database);
                            // $order->delete_meta_data('_paypal_multi_gateway_source_database');
                        }
                        $order->save();

                        // 清理订单记录文件中的信息（支付完成后）
                        if (isset($recorded_info)) {
                            $order_record->delete_order_record($order_id);
                            $this->log_debug("IPN回调 - 订单 #{$order_id} 支付完成，已从记录文件中清理");
                        }

                        // 确保购物车在支付成功后被清空（在源数据库中）
                        // 注意：购物车通常已在订单创建时被清空，这里是额外的安全检查
                        if (function_exists('WC') && WC()->cart && !WC()->cart->is_empty()) {
                            WC()->cart->empty_cart();
                            $this->log_debug("Cart cleared after successful PayPal IPN for order #{$order_id} (safety check)");
                        } else {
                            $this->log_debug("Cart was already empty after PayPal IPN for order #{$order_id}");
                        }
                        
                        // 执行订单复制
                        $this->execute_order_replication($order);
                        break;
                    case 'pending':
                        $pending_reason = isset($post_data['pending_reason']) ? $post_data['pending_reason'] : '';
                        $order->update_status('on-hold', sprintf(__('Payment pending: %s', 'yxjto-paypal-multi-gateway'), $pending_reason));
                        $this->log_debug("订单 {$order_id} IPN支付待处理: {$pending_reason}");
                        break;
                    case 'failed':
                    case 'denied':
                    case 'expired':
                        $order->update_status('failed', sprintf(__('Payment %s', 'yxjto-paypal-multi-gateway'), $payment_status));
                        $this->log_debug("订单 {$order_id} IPN支付失败: {$payment_status}");
                        break;
                    default:
                        $this->log_debug("订单 {$order_id} IPN未知支付状态: {$payment_status}");
                        $order->add_order_note(sprintf('PayPal IPN未知状态: %s (交易ID: %s)', $payment_status, $transaction_id));
                }

                // 记录 IPN 日志
                $account_id = $order->get_meta('_paypal_multi_gateway_account_id');
                $this->safe_log_transaction(
                    $post_data['txn_id'] ?? 'ipn_' . time(),
                    $order_id,
                    $account_id ?: 'unknown',
                    $post_data['mc_gross'] ?? $order->get_total(),
                    $post_data['mc_currency'] ?? $order->get_currency(),
                    $payment_status,
                    $post_data
                );
            } else {
                $this->log_error("IPN处理失败: 找不到订单 {$order_id}");
            }
        } else {
            $this->log_error("IPN处理失败: 无法获取订单ID");
        }
    }

    /**
     * 执行订单复制
     *
     * @param WC_Order $order 订单对象
     */
    private function execute_order_replication($order) {
        try {
            if (class_exists('YXJTO_Order_Replication')) {
                $replication = YXJTO_Order_Replication::get_instance();
                $result = $replication->replicate_order($order->get_id());
                if ($result) {
                    $this->log_debug("订单复制成功: " . $order->get_id());
                    $order->add_order_note('订单已复制到其他数据库');
                } else {
                    $this->log_error("订单复制失败: " . $order->get_id());
                    $order->add_order_note('订单复制失败');
                }
            } else {
                $this->log_debug("订单复制类不存在，跳过复制: " . $order->get_id());
            }
        } catch (Exception $e) {
            $this->log_error("订单复制异常: " . $e->getMessage());
            $order->add_order_note('订单复制异常: ' . $e->getMessage());
        }
    }

    /**
     * Handle PayPal.me return
     */
    public function handle_paypalme_return() {
        // PayPal.me 返回处理（通常需要手动确认）
        $order_id = isset($_GET['order_id']) ? absint($_GET['order_id']) : 0;

        if ($order_id) {
            $order = wc_get_order($order_id);
            if ($order) {
                $order->update_status('on-hold', __('PayPal.me payment initiated. Please verify payment manually.', 'yxjto-paypal-multi-gateway'));
                
                // 记录感谢页面显示所需的数据库信息
                $current_database = '';
                if (class_exists('YXJTO_Gateway')) {
                    $gateway = YXJTO_Gateway::get_instance();
                    if ($gateway) {
                        $current_database = $gateway->get_current_database();
                        $order->update_meta_data('_paypal_thankyou_page_database', $current_database);
                        $order->save();
                        $this->log_debug("Saved thankyou page database reference: {$current_database} for PayPal.me order #{$order_id}");
                    }
                }
                
                // 构建标准的感谢页面URL（恢复默认行为）
                $return_url = $this->get_return_url($order);
                
                wp_redirect($return_url);
                exit;
            }
        }

        wp_redirect(wc_get_checkout_url());
        exit;
    }

    /**
     * Receipt page
     */
    public function receipt_page($order_id) {
        $order = wc_get_order($order_id);
        $payment_url = $order->get_meta('_paypal_multi_gateway_payment_url');

        if ($payment_url) {
            echo '<p>' . __('Thank you for your order, please click the button below to pay with PayPal.', 'yxjto-paypal-multi-gateway') . '</p>';
            echo '<a class="button" href="' . esc_url($payment_url) . '">' . __('Pay via PayPal', 'yxjto-paypal-multi-gateway') . '</a>';
        }
    }



    /**
     * 安全的获取账户显示名称（用于AJAX）
     */
    private function get_account_display_name_safe($account) {
        try {
            return $this->get_account_display_name($account);
        } catch (Exception $e) {
            return __('PayPal Account', 'yxjto-paypal-multi-gateway');
        }
    }

    /**
     * 获取账户显示名称
     */
    private function get_account_display_name($account) {
        if (!$account || !isset($account->account_data) || !isset($account->account_type)) {
            return __('PayPal Account', 'yxjto-paypal-multi-gateway');
        }

        $account_data = is_string($account->account_data) ?
            json_decode($account->account_data, true) :
            $account->account_data;

        if (!is_array($account_data)) {
            $account_data = array();
        }

        switch ($account->account_type) {
            case 'email':
                return $account_data['email'] ?? __('Email Account', 'yxjto-paypal-multi-gateway');
            case 'paypal_me':
                return $account_data['paypal_me_url'] ?? __('PayPal.me Account', 'yxjto-paypal-multi-gateway');
            case 'api':
                $client_id = $account_data['client_id'] ?? '';
                return $client_id ? substr($client_id, 0, 20) . '...' : __('API Account', 'yxjto-paypal-multi-gateway');
            default:
                return __('PayPal Account', 'yxjto-paypal-multi-gateway');
        }
    }

    /**
     * 获取账户图标
     */
    private function get_account_icon($account_type) {
        switch ($account_type) {
            case 'email':
                return 'email';
            case 'paypal_me':
                return 'link';
            case 'api':
                return 'api';
            default:
                return 'paypal';
        }
    }

    /**
     * AJAX simulate checkout
     */
    public function ajax_simulate_checkout() {
        check_ajax_referer('yxjto_paypal_multi_gateway_checkout', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $selected_account = $accounts->select_account_for_checkout();

        if ($selected_account) {
            wp_send_json_success(array(
                'account_id' => $selected_account->account_id,
                'account_type' => $selected_account->account_type,
                'message' => __('Account selected successfully', 'yxjto-paypal-multi-gateway')
            ));
        } else {
            wp_send_json_error(__('No accounts available', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * 调试日志方法
     */
    public function log_debug($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PayPal Multi Gateway [Payment Debug]: " . $message);
        }
    }

    /**
     * 错误日志方法
     */
    public function log_error($message) {
        error_log("PayPal Multi Gateway [Payment Error]: " . $message);
    }

    /**
     * 处理订单感谢页面显示 - 恢复默认行为
     */
    public function thankyou_page($order_id) {
        $this->log_debug("Processing thankyou page for order #{$order_id} - using default WooCommerce behavior");
        
        // 使用WooCommerce默认的订单显示逻辑
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Order #{$order_id} not found for thankyou page");
            return;
        }
        
        // 仅记录基本信息，不进行数据库切换或复杂处理
        $this->log_debug("Thankyou page - Order #{$order_id} loaded successfully. Status: {$order->get_status()}, Total: {$order->get_total()}");
        
        // 检查是否是我们的支付网关
        $payment_method = $order->get_payment_method();
        if ($payment_method === $this->id) {
            $this->log_debug("Thankyou page - Order #{$order_id} uses PayPal Multi Gateway");
        }
        
        // 让WooCommerce处理剩余的显示逻辑
    }

    /**
     * 调试订单状态 - 简化版本，移除前端输出
     */
    private function debug_order_status($order_id) {
        $order = wc_get_order($order_id);
        
        if ($order) {
            $this->log_debug("Thankyou page - Order #{$order_id} found. Status: {$order->get_status()}, Total: {$order->get_total()}, Customer: {$order->get_billing_email()}");
            
            // 检查是否是我们的支付网关
            $payment_method = $order->get_payment_method();
            if ($payment_method === $this->id) {
                $this->log_debug("Thankyou page - Order #{$order_id} uses PayPal Multi Gateway");
            }
        } else {
            $this->log_error("Thankyou page - Order #{$order_id} not found!");
        }
    }

    /**
     * 确保订单显示正确的信息 - 简化版本，移除数据库切换
     */
    public function ensure_order_display($order_id) {
        // 简化的订单显示逻辑，不进行数据库切换
        $order = wc_get_order($order_id);
        
        if (!$order) {
            $this->log_error("Order #{$order_id} not found for display");
            return;
        }
        
        $this->log_debug("Order #{$order_id} loaded successfully for display");
    }

    /**
     * 将默认数据库订单的支付meta信息复制到其他所有数据库
     */
    private function replicate_payment_meta_to_all_databases($order_id, $gateway) {
        try {
            $this->log_debug("Starting payment meta replication for order #{$order_id}");

            // 确保当前在默认数据库以获取完整的支付meta信息
            $current_database = $gateway->get_current_database();
            if ($current_database !== 'default') {
                $this->log_debug("Switching to default database to get payment meta");
                $switch_result = $gateway->switch_database('default');
                if (!$switch_result) {
                    $this->log_error("Failed to switch to default database for payment meta replication");
                    return;
                }
            }

            // 从默认数据库获取订单和支付相关meta信息
            $default_order = wc_get_order($order_id);
            if (!$default_order) {
                $this->log_error("Order #{$order_id} not found in default database for payment meta replication");
                return;
            }

            // 定义需要复制的支付相关meta字段
            $payment_meta_keys = [
                '_order_discount',
                '_paypal_invoice_number',
                '_paypal_multi_gateway_payment_id',
                '_paypal_multi_gateway_account_id',
                '_paypal_multi_gateway_account_type',
                '_paypal_multi_gateway_selection_method',
                '_paypal_multi_gateway_created_at',
                '_paypal_multi_gateway_customer_email',
                '_paypal_multi_gateway_customer_name',
                '_paypal_multi_gateway_payment_completed_from',
                '_transaction_id',
                '_payment_method',
                '_payment_method_title',
                '_paypal_multi_gateway_payment_url',
                '_paid_date',
                '_paypal_multi_gateway_testmode',
                '_paypal_multi_gateway_payment_in_progress',
                '_paypal_multi_gateway_source_database',
                '_paypal_thankyou_page_database'
            ];

  
            // 提取支付meta数据
            $payment_meta_data = [];
            foreach ($payment_meta_keys as $meta_key) {
                $meta_value = $default_order->get_meta($meta_key);
                if (!empty($meta_value)) {
                    $payment_meta_data[$meta_key] = $meta_value;
                    $this->log_debug("Extracted payment meta: {$meta_key} = " . (is_string($meta_value) ? $meta_value : json_encode($meta_value)));
                }
            }

            if (empty($payment_meta_data)) {
                $this->log_debug("No payment meta data found to replicate for order #{$order_id}");
                return;
            }

            $this->log_debug("Found " . count($payment_meta_data) . " payment meta fields to replicate");

            // 获取所有数据库配置
            if (!class_exists('WP_Multi_DB_Config_Manager')) {
                $this->log_error("WP_Multi_DB_Config_Manager not available for payment meta replication");
                return;
            }

            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $replicated_count = 0;

            // 复制到每个数据库
            foreach ($databases as $db_name => $db_config) {
                // 跳过默认数据库（源数据库）和禁用的数据库
                if ($db_name === 'default' || !$db_config['enabled']) {
                    continue;
                }

                $this->log_debug("Replicating payment meta to database: {$db_name}");

                try {
                    // 切换到目标数据库
                    $switch_result = $gateway->switch_database($db_name);
                    if (!$switch_result) {
                        $this->log_error("Failed to switch to database {$db_name} for payment meta replication");
                        continue;
                    }

                    // 获取目标数据库中的订单
                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        $this->log_debug("Order #{$order_id} not found in database {$db_name}, skipping payment meta replication");
                        continue;
                    }

                    // 复制支付meta数据
                    $updated_fields = 0;
                    foreach ($payment_meta_data as $meta_key => $meta_value) {
                        $existing_value = $target_order->get_meta($meta_key);

                        // 只更新不存在或不同的meta值
                        if (empty($existing_value) || $existing_value !== $meta_value) {
                            $target_order->update_meta_data($meta_key, $meta_value);
                            $updated_fields++;
                            $this->log_debug("Updated payment meta in {$db_name}: {$meta_key}");
                        }
                    }

                    if ($updated_fields > 0) {
                        $target_order->save();
                        $this->log_debug("Saved {$updated_fields} payment meta fields to order #{$order_id} in database {$db_name}");
                        $replicated_count++;
                    } else {
                        $this->log_debug("No payment meta updates needed for order #{$order_id} in database {$db_name}");
                    }

                } catch (Exception $e) {
                    $this->log_error("Error replicating payment meta to database {$db_name}: " . $e->getMessage());
                    continue;
                }
            }

            $this->log_debug("Payment meta replication completed for order #{$order_id}. Replicated to {$replicated_count} databases");

            // 恢复到原始数据库
            if ($current_database !== 'default') {
                $gateway->switch_database($current_database);
                $this->log_debug("Restored to original database: {$current_database}");
            }

        } catch (Exception $e) {
            $this->log_error("Payment meta replication failed for order #{$order_id}: " . $e->getMessage());

            // 确保恢复到原始数据库
            if (isset($current_database) && $current_database !== 'default') {
                $gateway->switch_database($current_database);
            }
        }
    }

    /**
     * 复制更新订单状态到所有数据库
     */
    private function replicate_order_status_to_all_databases($order_id, $gateway, $source_database = null) {
        try {
            $this->log_debug("Starting order status replication for order #{$order_id}");

            // 确定源数据库
            $current_database = $gateway->get_current_database();
            if ($source_database) {
                $actual_source_database = $source_database;
            } else {
                $actual_source_database = $current_database;
            }

            $this->log_debug("Using source database '{$actual_source_database}' for order status replication");

            // 确保当前在源数据库以获取最新的订单状态
            if ($current_database !== $actual_source_database) {
                $this->log_debug("Switching to source database '{$actual_source_database}' to get order status");
                $switch_result = $gateway->switch_database($actual_source_database);
                if (!$switch_result) {
                    $this->log_error("Failed to switch to source database '{$actual_source_database}' for order status replication");
                    return;
                }
            }

            // 从源数据库获取订单和状态信息
            $source_order = wc_get_order($order_id);
            if (!$source_order) {
                $this->log_error("Order #{$order_id} not found in source database '{$actual_source_database}' for order status replication");
                return;
            }

            // 获取订单状态和相关信息
            $order_status = $source_order->get_status();
            $order_date_completed = $source_order->get_date_completed();
            $order_date_paid = $source_order->get_date_paid();
            $order_notes = wc_get_order_notes(['order_id' => $order_id, 'limit' => 5]); // 获取最近5条备注

            $this->log_debug("Order #{$order_id} status in source database '{$actual_source_database}': {$order_status}");
            $this->log_debug("Order #{$order_id} date completed: " . ($order_date_completed ? $order_date_completed->format('Y-m-d H:i:s') : 'null'));
            $this->log_debug("Order #{$order_id} date paid: " . ($order_date_paid ? $order_date_paid->format('Y-m-d H:i:s') : 'null'));
            $this->log_debug("Found " . count($order_notes) . " recent order notes to replicate");

            // 获取所有数据库配置
            if (!class_exists('WP_Multi_DB_Config_Manager')) {
                $this->log_error("WP_Multi_DB_Config_Manager not available for order status replication");
                return;
            }

            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $replicated_count = 0;

            // 复制到每个数据库
            foreach ($databases as $db_name => $db_config) {
                // 跳过源数据库和禁用的数据库
                if ($db_name === $actual_source_database || !$db_config['enabled']) {
                    $this->log_debug("Skipping database {$db_name} (source database or disabled)");
                    continue;
                }

                $this->log_debug("Replicating order status to database: {$db_name}");

                try {
                    // 切换到目标数据库
                    $switch_result = $gateway->switch_database($db_name);
                    if (!$switch_result) {
                        $this->log_error("Failed to switch to database {$db_name} for order status replication");
                        continue;
                    }

                    // 获取目标数据库中的订单
                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        $this->log_debug("Order #{$order_id} not found in database {$db_name}, skipping order status replication");
                        continue;
                    }

                    $target_current_status = $target_order->get_status();
                    $this->log_debug("Order #{$order_id} current status in {$db_name}: {$target_current_status}");

                    $status_updated = false;

                    // 更新订单状态（如果不同）
                    if ($target_current_status !== $order_status) {
                        $target_order->set_status($order_status);
                        $status_updated = true;
                        $this->log_debug("Updated order status in {$db_name}: {$target_current_status} -> {$order_status}");
                    }

                    // 更新完成日期（如果存在且不同）
                    if ($order_date_completed) {
                        $target_date_completed = $target_order->get_date_completed();
                        if (!$target_date_completed || $target_date_completed->format('Y-m-d H:i:s') !== $order_date_completed->format('Y-m-d H:i:s')) {
                            $target_order->set_date_completed($order_date_completed);
                            $status_updated = true;
                            $this->log_debug("Updated order completion date in {$db_name}");
                        }
                    }

                    // 更新支付日期（如果存在且不同）
                    if ($order_date_paid) {
                        $target_date_paid = $target_order->get_date_paid();
                        if (!$target_date_paid || $target_date_paid->format('Y-m-d H:i:s') !== $order_date_paid->format('Y-m-d H:i:s')) {
                            $target_order->set_date_paid($order_date_paid);
                            $status_updated = true;
                            $this->log_debug("Updated order paid date in {$db_name}");
                        }
                    }

                    // 保存订单状态更新
                    if ($status_updated) {
                        $target_order->save();
                        $this->log_debug("Saved order status updates to order #{$order_id} in database {$db_name}");
                        $replicated_count++;
                    } else {
                        $this->log_debug("No order status updates needed for order #{$order_id} in database {$db_name}");
                    }

                    // 复制最新的订单备注（如果有）
                    if (!empty($order_notes)) {
                        $target_notes = wc_get_order_notes(['order_id' => $order_id, 'limit' => 10]);
                        $target_note_contents = array_map(function($note) { return $note->content; }, $target_notes);

                        $notes_added = 0;
                        foreach ($order_notes as $note) {
                            // 检查备注是否已存在
                            if (!in_array($note->content, $target_note_contents)) {
                                $target_order->add_order_note($note->content, $note->customer_note);
                                $notes_added++;
                                $this->log_debug("Added order note to {$db_name}: " . substr($note->content, 0, 50) . "...");
                            }
                        }

                        if ($notes_added > 0) {
                            $this->log_debug("Added {$notes_added} order notes to order #{$order_id} in database {$db_name}");
                        }
                    }

                } catch (Exception $e) {
                    $this->log_error("Error replicating order status to database {$db_name}: " . $e->getMessage());
                    continue;
                }
            }

            $this->log_debug("Order status replication completed for order #{$order_id}. Replicated to {$replicated_count} databases");

            // 恢复到原始数据库
            if ($current_database !== $actual_source_database) {
                $gateway->switch_database($current_database);
                $this->log_debug("Restored to original database: {$current_database}");
            }

        } catch (Exception $e) {
            $this->log_error("Order status replication failed for order #{$order_id}: " . $e->getMessage());

            // 确保恢复到原始数据库
            if (isset($current_database) && $current_database !== $actual_source_database) {
                $gateway->switch_database($current_database);
            }
        }
    }
    
    /**
     * 确保订单折扣正确同步到所有数据库
     * 这是关键方法，用于修复折扣在数据库间同步时丢失的问题
     */
    private function ensure_discount_sync_to_all_databases($order_id, $gateway, $order_replication) {
        try {
            $current_database = $gateway->get_current_database();
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            
            // 获取源订单的折扣信息
            $source_order = wc_get_order($order_id);
            if (!$source_order) {
                $this->log_error("Source order #{$order_id} not found for discount sync");
                return;
            }
            
            $source_discount = $source_order->get_total_discount();
            $source_coupons = $source_order->get_items('coupon');
            
            $this->log_debug("DISCOUNT SYNC: Source order #{$order_id} discount: {$source_discount}, coupons: " . count($source_coupons));
            
            // 如果没有折扣，无需同步
            if ($source_discount <= 0 && empty($source_coupons)) {
                $this->log_debug("DISCOUNT SYNC: No discount to sync for order #{$order_id}");
                return;
            }
            
            // 收集所有优惠券数据
            $coupon_data = [];
            foreach ($source_coupons as $item_id => $coupon_item) {
                $coupon_data[] = [
                    'code' => $coupon_item->get_code(),
                    'discount' => $coupon_item->get_discount(),
                    'discount_tax' => $coupon_item->get_discount_tax()
                ];
                $this->log_debug("DISCOUNT SYNC: Source coupon '{$coupon_item->get_code()}': discount={$coupon_item->get_discount()}, tax={$coupon_item->get_discount_tax()}");
            }
            
            // 同步到所有其他数据库
            foreach ($databases as $db_name => $db_config) {
                if ($db_name === $current_database || !$db_config['enabled']) {
                    continue;
                }
                
                try {
                    // 切换到目标数据库
                    $gateway->switch_database($db_name);
                    
                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        $this->log_debug("DISCOUNT SYNC: Target order #{$order_id} not found in {$db_name}, skipping");
                        continue;
                    }
                    
                    $target_discount_before = $target_order->get_total_discount();
                    $this->log_debug("DISCOUNT SYNC: Target order #{$order_id} in {$db_name} - discount before: {$target_discount_before}");
                    
                    // 同步优惠券项目
                    $sync_needed = false;
                    foreach ($coupon_data as $coupon_info) {
                        $existing_coupon_found = false;
                        
                        // 检查优惠券是否已存在
                        foreach ($target_order->get_items('coupon') as $existing_coupon) {
                            if ($existing_coupon->get_code() === $coupon_info['code']) {
                                $existing_coupon_found = true;
                                // 检查金额是否匹配
                                if (abs($existing_coupon->get_discount() - $coupon_info['discount']) > 0.01) {
                                    $existing_coupon->set_discount($coupon_info['discount']);
                                    $existing_coupon->set_discount_tax($coupon_info['discount_tax']);
                                    $existing_coupon->save();
                                    $sync_needed = true;
                                    $this->log_debug("DISCOUNT SYNC: Updated existing coupon '{$coupon_info['code']}' in {$db_name}");
                                }
                                break;
                            }
                        }
                        
                        // 如果优惠券不存在，添加它
                        if (!$existing_coupon_found) {
                            $coupon_item = new WC_Order_Item_Coupon();
                            $coupon_item->set_code($coupon_info['code']);
                            $coupon_item->set_discount($coupon_info['discount']);
                            $coupon_item->set_discount_tax($coupon_info['discount_tax']);
                            $target_order->add_item($coupon_item);
                            $sync_needed = true;
                            $this->log_debug("DISCOUNT SYNC: Added missing coupon '{$coupon_info['code']}' to {$db_name}");
                        }
                    }
                    
                    // 强制设置折扣总额
                    if (abs($target_order->get_total_discount() - $source_discount) > 0.01) {
                        $target_order->set_discount_total($source_discount);
                        $sync_needed = true;
                        $this->log_debug("DISCOUNT SYNC: Force set discount total to {$source_discount} in {$db_name}");
                    }
                    
                    // 如果有更改，保存订单
                    if ($sync_needed) {
                        $target_order->save();
                        
                        // 重新计算订单总额以确保一致性
                        $target_order->calculate_totals();
                        
                        $target_discount_after = $target_order->get_total_discount();
                        $this->log_debug("DISCOUNT SYNC: Target order #{$order_id} in {$db_name} - discount after: {$target_discount_after}");
                        
                        if (abs($target_discount_after - $source_discount) > 0.01) {
                            $this->log_error("DISCOUNT SYNC: WARNING - Discount still mismatch in {$db_name}: expected {$source_discount}, got {$target_discount_after}");
                        } else {
                            $this->log_debug("DISCOUNT SYNC: SUCCESS - Discount correctly synced to {$db_name}");
                        }
                    } else {
                        $this->log_debug("DISCOUNT SYNC: No sync needed for {$db_name}");
                    }
                    
                } catch (Exception $e) {
                    $this->log_error("DISCOUNT SYNC: Failed to sync discount to {$db_name}: " . $e->getMessage());
                }
            }
            
            // 切换回原数据库
            $gateway->switch_database($current_database);
            
            $this->log_debug("DISCOUNT SYNC: Completed for order #{$order_id}");
            
        } catch (Exception $e) {
            $this->log_error("DISCOUNT SYNC: Error in ensure_discount_sync_to_all_databases: " . $e->getMessage());
        }
    }

    /**
     * 在支付前调整订单价格和其他费用
     * 确保价格合理，不将差价调到税里，而是调到最后一个商品的总价格里
     * 商品价格要平均分配当前商品的总价
     */
    private function adjust_order_pricing_before_payment($order) {
        try {
            $order_id = $order->get_id();
            $this->log_debug("Price adjustment started for order #{$order_id}");

            // 获取当前订单的各项金额
            $subtotal = $order->get_subtotal();
            $tax_total = $order->get_total_tax();
            $shipping_total = $order->get_shipping_total();
            $discount_total = $order->get_total_discount();
            $order_total = $order->get_total();

            $this->log_debug("Original amounts - Subtotal: {$subtotal}, Tax: {$tax_total}, Shipping: {$shipping_total}, Discount: {$discount_total}, Total: {$order_total}");

            // 验证运费和折扣数据的完整性
            $shipping_items = $order->get_items('shipping');
            $calculated_shipping = 0;
            foreach ($shipping_items as $shipping_item) {
                $calculated_shipping += $shipping_item->get_total();
            }

            if (abs($shipping_total - $calculated_shipping) > 0.01 && $calculated_shipping > 0) {
                $shipping_total = $calculated_shipping;
                $this->log_debug("Shipping adjusted to calculated value: {$shipping_total}");
            }

            $coupon_items = $order->get_items('coupon');
            $calculated_discount = 0;
            foreach ($coupon_items as $coupon_item) {
                $calculated_discount += $coupon_item->get_discount();
            }

            if (abs($discount_total - $calculated_discount) > 0.01 && $calculated_discount > 0) {
                $discount_total = $calculated_discount;
                $this->log_debug("Discount adjusted to calculated value: {$discount_total}");
            }

            // 计算实际的详细金额总和
            $calculated_total = $subtotal + $tax_total + $shipping_total - $discount_total;
            $difference = $order_total - $calculated_total;

            $this->log_debug("Calculated total: {$calculated_total}, Order total: {$order_total}, Difference: {$difference}");

            // 如果有差异，需要调整商品价格而不是税费
            if (abs($difference) > 0.01) {
                $this->adjust_item_prices_to_match_total($order, $difference);
            }

        } catch (Exception $e) {
            $this->log_error("Price adjustment error for order #{$order->get_id()}: " . $e->getMessage());
        }
    }

    /**
     * 调整商品价格以匹配订单总额
     * 将差价平均分配到所有商品，最后一个商品承担剩余差额
     */
    private function adjust_item_prices_to_match_total($order, $difference) {
        $order_id = $order->get_id();
        $items = $order->get_items();

        if (empty($items)) {
            $this->log_debug("No items found for price adjustment in order #{$order_id}");
            return;
        }

        $this->log_debug("Adjusting item prices for order #{$order_id}, difference: {$difference}");

        // 计算当前商品总价
        $current_items_total = 0;
        $item_data = array();

        foreach ($items as $item_id => $item) {
            $item_total = $item->get_total();
            $quantity = $item->get_quantity();
            $current_items_total += $item_total;

            $item_data[] = array(
                'item_id' => $item_id,
                'item' => $item,
                'original_total' => $item_total,
                'quantity' => $quantity,
                'unit_price' => $quantity > 0 ? $item_total / $quantity : $item_total
            );
        }

        // 新的商品总价应该是当前总价加上差价
        $target_items_total = $current_items_total + $difference;

        if ($target_items_total <= 0) {
            $this->log_debug("Target items total is zero or negative, skipping adjustment");
            return;
        }

        $this->log_debug("Current items total: {$current_items_total}, Target items total: {$target_items_total}");

        // 计算调整比例
        $adjustment_ratio = $target_items_total / $current_items_total;
        $adjusted_total = 0;

        // 调整前N-1个商品的价格
        for ($i = 0; $i < count($item_data) - 1; $i++) {
            $item_info = $item_data[$i];
            $new_total = $item_info['original_total'] * $adjustment_ratio;
            $new_unit_price = $item_info['quantity'] > 0 ? $new_total / $item_info['quantity'] : $new_total;

            // 更新商品价格
            $item_info['item']->set_total($new_total);
            $item_info['item']->set_subtotal($new_total);
            $item_info['item']->save();

            $adjusted_total += $new_total;

            $this->log_debug("Item {$i}: {$item_info['original_total']} -> {$new_total} (unit: {$new_unit_price})");
        }

        // 最后一个商品承担剩余差额
        if (count($item_data) > 0) {
            $last_index = count($item_data) - 1;
            $last_item_info = $item_data[$last_index];
            $remaining_amount = $target_items_total - $adjusted_total;
            $new_unit_price = $last_item_info['quantity'] > 0 ? $remaining_amount / $last_item_info['quantity'] : $remaining_amount;

            // 确保价格合理（不能为负数）
            if ($remaining_amount >= 0) {
                $last_item_info['item']->set_total($remaining_amount);
                $last_item_info['item']->set_subtotal($remaining_amount);
                $last_item_info['item']->save();

                $this->log_debug("Last item: {$last_item_info['original_total']} -> {$remaining_amount} (unit: {$new_unit_price})");
            } else {
                $this->log_debug("Remaining amount is negative ({$remaining_amount}), keeping original price for last item");
            }
        }

        // 重新计算订单总额以确保一致性
        $order->calculate_totals();
        $order->save();

        $new_order_total = $order->get_total();
        $this->log_debug("Order totals recalculated. New total: {$new_order_total}");
    }

    /**
     * 决定支付处理使用的数据库
     *
     * @param string $current_database 当前数据库
     * @param bool $enable_default_db_redirect 是否启用默认数据库支付跳转
     * @return string 支付处理应使用的数据库
     */
    private function determine_payment_database($current_database, $enable_default_db_redirect) {
        // 规则1: 源订单是默认数据库时，直接用默认数据库的订单进行支付跳转，不切换数据库
        if ($current_database === 'default') {
            $this->log_debug("Source order is from default database, using default database for payment");
            return 'default';
        }

        // 规则2: 源订单不是默认数据库时
        if ($enable_default_db_redirect) {
            // 启用了默认数据库支付跳转：切换到默认数据库进行支付
            $this->log_debug("Default DB payment redirect enabled, switching to default database");
            return 'default';
        } else {
            // 没有启用默认数据库支付跳转：用源数据库的订单进行支付跳转，不切换数据库
            $this->log_debug("Default DB payment redirect disabled, using source database {$current_database}");
            return $current_database;
        }
    }

    /**
     * 新版价格调整方法 - 使用重构后的价格调整器
     *
     * @param WC_Order $order 订单对象
     */
    private function adjust_order_pricing_before_payment_v2($order) {
        try {
            $order_id = $order->get_id();
            $this->log_debug("Starting v2 price adjustment for order #{$order_id}");

            // 加载价格调整器
            $adjuster_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-order-price-adjuster.php';
            if (!file_exists($adjuster_file)) {
                $this->log_error("Price adjuster class not found, falling back to legacy method");
                $this->adjust_order_pricing_before_payment($order);
                return;
            }

            require_once $adjuster_file;

            if (!class_exists('YXJTO_Order_Price_Adjuster')) {
                $this->log_error("Price adjuster class not available, falling back to legacy method");
                $this->adjust_order_pricing_before_payment($order);
                return;
            }

            // 创建价格调整器实例
            $config = $this->get_price_adjustment_config();
            $adjuster = new YXJTO_Order_Price_Adjuster($this, $config);

            // 执行价格调整
            $adjusted = $adjuster->adjust_order_pricing($order);

            if ($adjusted) {
                $this->log_debug("Price adjustment completed successfully for order #{$order_id}");
            } else {
                $this->log_debug("No price adjustment was needed for order #{$order_id}");
            }

        } catch (Exception $e) {
            $this->log_error("V2 price adjustment failed for order #{$order->get_id()}: " . $e->getMessage());
            $this->log_debug("Falling back to legacy price adjustment method");
            $this->adjust_order_pricing_before_payment($order);
        }
    }

    /**
     * 获取价格调整配置
     *
     * @return array 配置数组
     */
    private function get_price_adjustment_config() {
        // 从WordPress选项或配置文件获取配置
        $default_config = [
            'strategy' => 'auto', // auto, proportional, equal_distribution, highest_price_first
            'enable_validation' => true,
            'enable_recalculation' => true,
            'min_adjustment_threshold' => 0.01
        ];

        // 可以从数据库或配置文件加载自定义配置
        $saved_config = get_option('yxjto_price_adjustment_config', []);

        return array_merge($default_config, $saved_config);
    }

    /**
     * 完全同步订单到所有数据库
     * 如果复制订单存在，删除原有商品和meta，重新生成
     *
     * @param int $order_id 订单ID
     * @param WC_Order $source_order 源订单对象
     * @param YXJTO_Gateway $gateway 网关实例
     * @param YXJTO_Order_Replication $order_replication 订单复制实例
     */
    private function sync_order_to_all_databases_completely($order_id, $source_order, $gateway, $order_replication) {
        $this->log_debug("Starting complete synchronization for order #{$order_id}");

        // 获取当前数据库
        $source_database = $gateway->get_current_database();
        $this->log_debug("Source database: {$source_database}");

        // 获取所有启用的数据库
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        foreach ($databases as $db_name => $db_config) {
            if ($db_name === $source_database || !$db_config['enabled']) {
                continue;
            }

            $this->log_debug("Synchronizing order #{$order_id} to database: {$db_name}");

            try {
                // 切换到目标数据库
                $switch_result = $gateway->switch_database($db_name);
                if (!$switch_result) {
                    $this->log_error("Failed to switch to database {$db_name}");
                    continue;
                }

                // 检查目标数据库中是否存在订单
                $target_order = wc_get_order($order_id);

                if ($target_order) {
                    $this->log_debug("Order #{$order_id} exists in {$db_name}, performing complete sync with smart product replacement");

                    // 完全同步现有订单，使用智能商品替换
                    $this->sync_existing_order_with_smart_replacement($order_id, $source_order, $target_order, $db_name, $gateway, $order_replication);
                } else {
                    $this->log_debug("Order #{$order_id} does not exist in {$db_name}, creating new order");

                    // 切换回源数据库创建新订单
                    $gateway->switch_database($source_database);

                    // 提取源订单数据并复制到目标数据库
                    $order_data = $this->extract_order_data_for_replication($source_order);
                    $this->create_order_in_target_database($order_data, $db_name, $db_config, $gateway);
                }

            } catch (Exception $e) {
                $this->log_error("Failed to sync order #{$order_id} to {$db_name}: " . $e->getMessage());
            }
        }

        // 确保切换回源数据库
        $gateway->switch_database($source_database);
        $this->log_debug("Complete synchronization finished for order #{$order_id}");
    }

    /**
     * 完全同步现有订单
     * 删除原有商品和meta，重新生成
     *
     * @param int $order_id 订单ID
     * @param WC_Order $source_order 源订单对象
     * @param WC_Order $target_order 目标订单对象
     * @param string $target_db_name 目标数据库名称
     */
    private function sync_existing_order_completely($order_id, $source_order, $target_order, $target_db_name) {
        $this->log_debug("Starting complete sync for existing order #{$order_id} in {$target_db_name}");

        // 1. 删除目标订单的所有商品
        $this->delete_all_order_items($target_order);

        // 2. 删除目标订单的相关meta数据
        $this->delete_order_sync_meta($target_order);

        // 3. 从源订单复制所有商品
        $this->copy_order_items_from_source($source_order, $target_order);

        // 4. 从源订单复制meta数据
        $this->copy_order_meta_from_source($source_order, $target_order);

        // 5. 更新订单基本信息
        $this->update_order_basic_info($source_order, $target_order);

        // 6. 重新计算订单总额
        $target_order->calculate_totals();
        $target_order->save();

        $this->log_debug("Complete sync finished for order #{$order_id} in {$target_db_name}");
    }

    /**
     * 完全同步现有订单（带智能商品替换）
     *
     * @param int $order_id 订单ID
     * @param WC_Order $source_order 源订单对象
     * @param WC_Order $target_order 目标订单对象
     * @param string $target_db_name 目标数据库名称
     * @param YXJTO_Gateway $gateway 网关实例
     * @param YXJTO_Order_Replication $order_replication 订单复制实例
     */
    private function sync_existing_order_with_smart_replacement($order_id, $source_order, $target_order, $target_db_name, $gateway, $order_replication) {
        $this->log_debug("Starting complete sync with smart replacement for existing order #{$order_id} in {$target_db_name}");

        try {
            // 1. 删除目标订单的所有商品
            $this->delete_all_order_items($target_order);

            // 2. 删除目标订单的相关meta数据
            $this->delete_order_sync_meta($target_order);

            // 3. 切换回源数据库提取订单数据
            $source_database = $gateway->get_current_database();
            $original_db = $source_database;

            // 确保在源数据库中提取数据
            if ($gateway->get_current_database() !== $source_database) {
                $gateway->switch_database($source_database);
            }

            // 4. 提取源订单数据
            $order_data = $this->extract_order_data_for_replication($source_order);
            $this->log_debug("Extracted order data from source database: {$source_database}");

            // 5. 切换回目标数据库
            $gateway->switch_database($target_db_name);

            // 6. 应用智能商品替换（如果启用）
            if ($this->is_smart_product_replacement_enabled($order_replication)) {
                $this->log_debug("Applying smart product replacement for target database: {$target_db_name}");
                $order_data = $this->apply_smart_product_replacement_for_sync($order_data, $target_db_name, $order_replication);
                $this->log_debug("Smart product replacement completed for sync");
            } else {
                $this->log_debug("Smart product replacement is disabled, using original products");
            }

            // 7. 将处理后的商品添加到目标订单
            $this->add_processed_items_to_order($target_order, $order_data);

            // 8. 从源订单复制meta数据
            $this->copy_order_meta_from_source($source_order, $target_order);

            // 9. 更新订单基本信息
            $this->update_order_basic_info($source_order, $target_order);

            // 10. 重新计算订单总额
            $target_order->calculate_totals();
            $target_order->save();

            $this->log_debug("Complete sync with smart replacement finished for order #{$order_id} in {$target_db_name}");

        } catch (Exception $e) {
            $this->log_error("Failed to sync order #{$order_id} with smart replacement to {$target_db_name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查是否启用智能商品替换
     *
     * @param YXJTO_Order_Replication $order_replication 订单复制实例
     * @return bool
     */
    private function is_smart_product_replacement_enabled($order_replication) {
        try {
            // 通过反射访问私有属性
            $reflection = new ReflectionClass($order_replication);
            $settings_property = $reflection->getProperty('settings');
            $settings_property->setAccessible(true);
            $settings = $settings_property->getValue($order_replication);

            return isset($settings['enable_smart_product_replacement']) && $settings['enable_smart_product_replacement'];
        } catch (Exception $e) {
            $this->log_error("Failed to check smart product replacement setting: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 应用智能商品替换（用于同步）
     *
     * @param array $order_data 订单数据
     * @param string $target_db_name 目标数据库名称
     * @param YXJTO_Order_Replication $order_replication 订单复制实例
     * @return array 处理后的订单数据
     */
    private function apply_smart_product_replacement_for_sync($order_data, $target_db_name, $order_replication) {
        $this->log_debug("Applying smart product replacement for sync to {$target_db_name}");

        try {
            // 获取目标数据库连接
            $target_connection = $this->get_target_database_connection($target_db_name);
            if (!$target_connection) {
                $this->log_error("Failed to get target database connection for {$target_db_name}");
                return $order_data;
            }

            // 使用反射调用订单复制类的私有方法
            $reflection = new ReflectionClass($order_replication);
            $method = $reflection->getMethod('apply_smart_product_replacement');
            $method->setAccessible(true);

            $processed_order_data = $method->invoke($order_replication, $order_data, $target_connection);

            $this->log_debug("Smart product replacement completed for sync");

            return $processed_order_data;

        } catch (Exception $e) {
            $this->log_error("Failed to apply smart product replacement for sync: " . $e->getMessage());
            return $order_data;
        }
    }

    /**
     * 获取目标数据库连接
     *
     * @param string $target_db_name 目标数据库名称
     * @return mysqli|null 数据库连接
     */
    private function get_target_database_connection($target_db_name) {
        try {
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            if (!isset($databases[$target_db_name])) {
                $this->log_error("Target database {$target_db_name} not found in configuration");
                return null;
            }

            $db_config = $databases[$target_db_name];
            $connection = new mysqli(
                $db_config['host'],
                $db_config['username'],
                $db_config['password'],
                $db_config['database'],
                $db_config['port'] ?? 3306
            );

            if ($connection->connect_error) {
                $this->log_error("Failed to connect to target database {$target_db_name}: " . $connection->connect_error);
                return null;
            }

            $this->log_debug("Successfully connected to target database: {$target_db_name}");
            return $connection;

        } catch (Exception $e) {
            $this->log_error("Error getting target database connection for {$target_db_name}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 删除订单的所有商品项目
     *
     * @param WC_Order $order 订单对象
     */
    private function delete_all_order_items($order) {
        $this->log_debug("Deleting all items from order #{$order->get_id()}");

        // 获取所有类型的订单项目
        $item_types = ['line_item', 'fee', 'shipping', 'coupon', 'tax'];
        $deleted_count = 0;

        foreach ($item_types as $item_type) {
            $items = $order->get_items($item_type);
            foreach ($items as $item_id => $item) {
                $order->remove_item($item_id);
                $deleted_count++;
                $this->log_debug("Deleted {$item_type} item #{$item_id}: {$item->get_name()}");
            }
        }

        $this->log_debug("Deleted {$deleted_count} items from order #{$order->get_id()}");
    }

    /**
     * 删除订单的同步相关meta数据
     *
     * @param WC_Order $order 订单对象
     */
    private function delete_order_sync_meta($order) {
        $this->log_debug("Deleting sync meta from order #{$order->get_id()}");

        // 需要同步的meta键
        $sync_meta_keys = [
            '_order_total',
            '_order_tax',
            '_order_shipping',
            '_order_discount',
            '_cart_discount',
            '_cart_discount_tax',
            '_order_shipping_tax',
            '_order_version',
            '_prices_include_tax',
            '_billing_address_index',
            '_shipping_address_index'
        ];

        $deleted_count = 0;
        foreach ($sync_meta_keys as $meta_key) {
            if ($order->meta_exists($meta_key)) {
                $order->delete_meta_data($meta_key);
                $deleted_count++;
                $this->log_debug("Deleted meta: {$meta_key}");
            }
        }

        $this->log_debug("Deleted {$deleted_count} meta entries from order #{$order->get_id()}");
    }

    /**
     * 从源订单复制所有商品项目
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function copy_order_items_from_source($source_order, $target_order) {
        $this->log_debug("Copying items from source order #{$source_order->get_id()} to target order #{$target_order->get_id()}");

        // 复制所有类型的订单项目
        $item_types = ['line_item', 'fee', 'shipping', 'coupon', 'tax'];
        $copied_count = 0;

        foreach ($item_types as $item_type) {
            $source_items = $source_order->get_items($item_type);

            foreach ($source_items as $source_item) {
                $this->copy_single_order_item($source_item, $target_order, $item_type);
                $copied_count++;
            }
        }

        $this->log_debug("Copied {$copied_count} items to target order #{$target_order->get_id()}");
    }

    /**
     * 复制单个订单项目
     *
     * @param WC_Order_Item $source_item 源项目
     * @param WC_Order $target_order 目标订单
     * @param string $item_type 项目类型
     */
    private function copy_single_order_item($source_item, $target_order, $item_type) {
        $this->log_debug("Copying {$item_type} item: {$source_item->get_name()}");

        // 根据项目类型创建新项目
        switch ($item_type) {
            case 'line_item':
                $new_item = new WC_Order_Item_Product();
                $new_item->set_product_id($source_item->get_product_id());
                $new_item->set_variation_id($source_item->get_variation_id());
                $new_item->set_quantity($source_item->get_quantity());
                $new_item->set_name($source_item->get_name());
                $new_item->set_total($source_item->get_total());
                $new_item->set_subtotal($source_item->get_subtotal());
                $new_item->set_total_tax($source_item->get_total_tax());
                $new_item->set_subtotal_tax($source_item->get_subtotal_tax());
                break;

            case 'fee':
                $new_item = new WC_Order_Item_Fee();
                $new_item->set_name($source_item->get_name());
                $new_item->set_total($source_item->get_total());
                $new_item->set_total_tax($source_item->get_total_tax());
                break;

            case 'shipping':
                $new_item = new WC_Order_Item_Shipping();
                $new_item->set_method_title($source_item->get_method_title());
                $new_item->set_method_id($source_item->get_method_id());
                $new_item->set_total($source_item->get_total());
                $new_item->set_total_tax($source_item->get_total_tax());
                break;

            case 'coupon':
                $new_item = new WC_Order_Item_Coupon();
                $new_item->set_name($source_item->get_name());
                $new_item->set_code($source_item->get_code());
                $new_item->set_discount($source_item->get_discount());
                $new_item->set_discount_tax($source_item->get_discount_tax());
                break;

            case 'tax':
                $new_item = new WC_Order_Item_Tax();
                $new_item->set_rate_id($source_item->get_rate_id());
                $new_item->set_label($source_item->get_label());
                $new_item->set_compound($source_item->get_compound());
                $new_item->set_tax_total($source_item->get_tax_total());
                $new_item->set_shipping_tax_total($source_item->get_shipping_tax_total());
                break;

            default:
                $this->log_debug("Unknown item type: {$item_type}");
                return;
        }

        // 复制所有meta数据
        foreach ($source_item->get_meta_data() as $meta) {
            $new_item->add_meta_data($meta->key, $meta->value);
        }

        // 添加到目标订单
        $target_order->add_item($new_item);

        $this->log_debug("Successfully copied {$item_type} item: {$source_item->get_name()}");
    }

    /**
     * 从源订单复制meta数据
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function copy_order_meta_from_source($source_order, $target_order) {
        $this->log_debug("Copying meta data from source order #{$source_order->get_id()} to target order #{$target_order->get_id()}");

        // 需要同步的meta键
        $sync_meta_keys = [
            '_order_total',
            '_order_tax',
            '_order_shipping',
            '_order_discount',
            '_cart_discount',
            '_cart_discount_tax',
            '_order_shipping_tax',
            '_order_version',
            '_prices_include_tax',
            '_billing_address_index',
            '_shipping_address_index',
            '_payment_method',
            '_payment_method_title',
            '_transaction_id',
            '_customer_user',
            '_order_key'
        ];

        $copied_count = 0;
        foreach ($sync_meta_keys as $meta_key) {
            $meta_value = $source_order->get_meta($meta_key);
            if ($meta_value !== '') {
                $target_order->update_meta_data($meta_key, $meta_value);
                $copied_count++;
                $this->log_debug("Copied meta: {$meta_key} = {$meta_value}");
            }
        }

        $this->log_debug("Copied {$copied_count} meta entries to target order #{$target_order->get_id()}");
    }

    /**
     * 更新订单基本信息
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function update_order_basic_info($source_order, $target_order) {
        $this->log_debug("Updating basic info for target order #{$target_order->get_id()}");

        try {
            // 更新订单状态
            $target_order->set_status($source_order->get_status());

            // 更新客户信息
            $target_order->set_customer_id($source_order->get_customer_id());

            // 更新地址信息 - 使用兼容的方法
            $this->update_order_addresses($source_order, $target_order);

            // 更新支付信息
            $target_order->set_payment_method($source_order->get_payment_method());
            $target_order->set_payment_method_title($source_order->get_payment_method_title());
            $target_order->set_transaction_id($source_order->get_transaction_id());

            // 更新货币
            $target_order->set_currency($source_order->get_currency());

            $this->log_debug("Basic info updated for target order #{$target_order->get_id()}");

        } catch (Exception $e) {
            $this->log_error("Failed to update basic info for target order #{$target_order->get_id()}: " . $e->getMessage());
            // 继续执行，不中断同步流程
        }
    }

    /**
     * 更新订单地址信息（兼容不同版本的WooCommerce）
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function update_order_addresses($source_order, $target_order) {
        try {
            // 尝试使用新的方法获取地址信息
            if (method_exists($source_order, 'get_billing') && method_exists($target_order, 'set_billing')) {
                $target_order->set_billing($source_order->get_billing());
                $target_order->set_shipping($source_order->get_shipping());
                $this->log_debug("Updated addresses using get_billing/set_billing methods");
            } else {
                // 使用兼容的方法逐个设置地址字段
                $this->update_billing_address($source_order, $target_order);
                $this->update_shipping_address($source_order, $target_order);
                $this->log_debug("Updated addresses using individual field methods");
            }
        } catch (Exception $e) {
            $this->log_error("Failed to update addresses: " . $e->getMessage());
            // 尝试备用方法
            $this->update_addresses_fallback($source_order, $target_order);
        }
    }

    /**
     * 更新账单地址
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function update_billing_address($source_order, $target_order) {
        $billing_fields = [
            'first_name', 'last_name', 'company', 'address_1', 'address_2',
            'city', 'state', 'postcode', 'country', 'email', 'phone'
        ];

        foreach ($billing_fields as $field) {
            $getter = "get_billing_{$field}";
            $setter = "set_billing_{$field}";

            if (method_exists($source_order, $getter) && method_exists($target_order, $setter)) {
                $value = $source_order->$getter();
                $target_order->$setter($value);
            }
        }
    }

    /**
     * 更新配送地址
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function update_shipping_address($source_order, $target_order) {
        $shipping_fields = [
            'first_name', 'last_name', 'company', 'address_1', 'address_2',
            'city', 'state', 'postcode', 'country'
        ];

        foreach ($shipping_fields as $field) {
            $getter = "get_shipping_{$field}";
            $setter = "set_shipping_{$field}";

            if (method_exists($source_order, $getter) && method_exists($target_order, $setter)) {
                $value = $source_order->$getter();
                $target_order->$setter($value);
            }
        }
    }

    /**
     * 地址更新的备用方法
     *
     * @param WC_Order $source_order 源订单
     * @param WC_Order $target_order 目标订单
     */
    private function update_addresses_fallback($source_order, $target_order) {
        $this->log_debug("Using fallback method to update addresses");

        try {
            // 通过meta数据更新地址信息
            $address_meta_keys = [
                '_billing_first_name', '_billing_last_name', '_billing_company',
                '_billing_address_1', '_billing_address_2', '_billing_city',
                '_billing_state', '_billing_postcode', '_billing_country',
                '_billing_email', '_billing_phone',
                '_shipping_first_name', '_shipping_last_name', '_shipping_company',
                '_shipping_address_1', '_shipping_address_2', '_shipping_city',
                '_shipping_state', '_shipping_postcode', '_shipping_country'
            ];

            foreach ($address_meta_keys as $meta_key) {
                $value = $source_order->get_meta($meta_key);
                if ($value !== '') {
                    $target_order->update_meta_data($meta_key, $value);
                }
            }

            $this->log_debug("Updated addresses using meta data fallback");

        } catch (Exception $e) {
            $this->log_error("Fallback address update also failed: " . $e->getMessage());
        }
    }

    /**
     * 提取订单数据用于复制
     *
     * @param WC_Order $order 源订单
     * @return array 订单数据
     */
    private function extract_order_data_for_replication($order) {
        $this->log_debug("Extracting order data for replication from order #{$order->get_id()}");

        $order_data = [
            'id' => $order->get_id(),
            'status' => $order->get_status(),
            'currency' => $order->get_currency(),
            'total' => $order->get_total(),
            'subtotal' => $order->get_subtotal(),
            'tax_total' => $order->get_total_tax(),
            'shipping_total' => $order->get_shipping_total(),
            'discount_total' => $order->get_total_discount(),
            'customer_id' => $order->get_customer_id(),
            'billing' => $this->extract_billing_data($order),
            'shipping' => $this->extract_shipping_data($order),
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'transaction_id' => $order->get_transaction_id(),
            'order_key' => $order->get_order_key(),
            'items' => [],
            'meta_data' => []
        ];

        // 提取所有商品项目
        $item_types = ['line_item', 'fee', 'shipping', 'coupon', 'tax'];
        foreach ($item_types as $item_type) {
            $items = $order->get_items($item_type);
            foreach ($items as $item) {
                // 根据项目类型获取相应的金额
                $item_total = $this->get_item_total_by_type($item, $item_type);

                $item_data = [
                    'type' => $item_type,
                    'name' => $item->get_name(),
                    'total' => $item_total,
                    'meta_data' => []
                ];

                // 根据类型添加特定数据
                if ($item_type === 'line_item') {
                    $item_data['product_id'] = $item->get_product_id();
                    $item_data['variation_id'] = $item->get_variation_id();
                    $item_data['quantity'] = $item->get_quantity();
                    $item_data['subtotal'] = $item->get_subtotal();
                    $item_data['total_tax'] = $item->get_total_tax();
                    $item_data['subtotal_tax'] = $item->get_subtotal_tax();
                } elseif ($item_type === 'coupon') {
                    $item_data['code'] = $item->get_code();
                    $item_data['discount_amount'] = $item->get_discount();
                    $item_data['discount_tax'] = $item->get_discount_tax();
                } elseif ($item_type === 'shipping') {
                    $item_data['method_id'] = $item->get_method_id();
                    $item_data['method_title'] = $item->get_method_title();
                    $item_data['total_tax'] = $item->get_total_tax();
                } elseif ($item_type === 'fee') {
                    $item_data['total_tax'] = $item->get_total_tax();
                } elseif ($item_type === 'tax') {
                    $item_data['rate_id'] = $item->get_rate_id();
                    $item_data['label'] = $item->get_label();
                    $item_data['compound'] = $item->get_compound();
                    $item_data['tax_total'] = $item->get_tax_total();
                    $item_data['shipping_tax_total'] = $item->get_shipping_tax_total();
                }

                // 提取meta数据
                foreach ($item->get_meta_data() as $meta) {
                    $item_data['meta_data'][] = [
                        'key' => $meta->key,
                        'value' => $meta->value
                    ];
                }

                $order_data['items'][] = $item_data;
            }
        }

        // 提取订单meta数据
        foreach ($order->get_meta_data() as $meta) {
            $order_data['meta_data'][] = [
                'key' => $meta->key,
                'value' => $meta->value
            ];
        }

        $this->log_debug("Extracted " . count($order_data['items']) . " items and " . count($order_data['meta_data']) . " meta entries");

        return $order_data;
    }

    /**
     * 在目标数据库中创建订单
     *
     * @param array $order_data 订单数据
     * @param string $db_name 数据库名称
     * @param array $db_config 数据库配置
     * @param YXJTO_Gateway $gateway 网关实例
     */
    private function create_order_in_target_database($order_data, $db_name, $db_config, $gateway) {
        $this->log_debug("Creating order #{$order_data['id']} in target database: {$db_name}");

        // 切换到目标数据库
        $gateway->switch_database($db_name);

        // 创建新订单
        $new_order = wc_create_order([
            'status' => $order_data['status'],
            'customer_id' => $order_data['customer_id']
        ]);

        if (!$new_order) {
            $this->log_error("Failed to create order in {$db_name}");
            return false;
        }

        // 设置订单ID（如果可能）
        if ($new_order->get_id() !== $order_data['id']) {
            $this->log_debug("Order ID mismatch: expected {$order_data['id']}, got {$new_order->get_id()}");
        }

        // 设置基本信息
        $new_order->set_currency($order_data['currency']);

        // 设置地址信息（兼容方式）
        $this->set_order_addresses($new_order, $order_data['billing'], $order_data['shipping']);

        $new_order->set_payment_method($order_data['payment_method']);
        $new_order->set_payment_method_title($order_data['payment_method_title']);
        $new_order->set_transaction_id($order_data['transaction_id']);

        // 添加商品项目
        foreach ($order_data['items'] as $item_data) {
            $this->add_item_to_order($new_order, $item_data);
        }

        // 添加meta数据
        foreach ($order_data['meta_data'] as $meta) {
            // 处理meta数据，支持数组和对象格式
            $meta_key = null;
            $meta_value = null;

            if (is_array($meta)) {
                // 数组格式
                $meta_key = $meta['key'] ?? null;
                $meta_value = $meta['value'] ?? null;
            } elseif (is_object($meta)) {
                // 对象格式
                $meta_key = $meta->key ?? null;
                $meta_value = $meta->value ?? null;
            }

            if ($meta_key !== null && $meta_value !== null) {
                $new_order->update_meta_data($meta_key, $meta_value);
            }
        }

        // 计算总额并保存
        $new_order->calculate_totals();
        $new_order->save();

        $this->log_debug("Successfully created order #{$new_order->get_id()} in {$db_name}");

        return true;
    }

    /**
     * 向订单添加项目
     *
     * @param WC_Order $order 订单对象
     * @param array $item_data 项目数据
     */
    private function add_item_to_order($order, $item_data) {
        $item_type = $item_data['type'];

        switch ($item_type) {
            case 'line_item':
                $product = wc_get_product($item_data['product_id']);
                if ($product) {
                    $order->add_product($product, $item_data['quantity'], [
                        'subtotal' => $item_data['subtotal'],
                        'total' => $item_data['total']
                    ]);
                }
                break;

            case 'fee':
                $fee = new WC_Order_Item_Fee();
                $fee->set_name($item_data['name']);
                $fee->set_total($item_data['total']);
                $order->add_item($fee);
                break;

            case 'shipping':
                $shipping = new WC_Order_Item_Shipping();
                $shipping->set_method_title($item_data['name']);
                $shipping->set_total($item_data['total']);
                $order->add_item($shipping);
                break;

            case 'coupon':
                $coupon = new WC_Order_Item_Coupon();
                $coupon->set_name($item_data['name']);
                $coupon->set_discount($item_data['total']);
                $order->add_item($coupon);
                break;
        }

        $this->log_debug("Added {$item_type} item: {$item_data['name']}");
    }

    /**
     * 提取账单地址数据（兼容不同版本的WooCommerce）
     *
     * @param WC_Order $order 订单对象
     * @return array 账单地址数据
     */
    private function extract_billing_data($order) {
        try {
            // 尝试使用新的方法
            if (method_exists($order, 'get_billing')) {
                return $order->get_billing();
            }

            // 使用兼容的方法
            return [
                'first_name' => $order->get_billing_first_name(),
                'last_name' => $order->get_billing_last_name(),
                'company' => $order->get_billing_company(),
                'address_1' => $order->get_billing_address_1(),
                'address_2' => $order->get_billing_address_2(),
                'city' => $order->get_billing_city(),
                'state' => $order->get_billing_state(),
                'postcode' => $order->get_billing_postcode(),
                'country' => $order->get_billing_country(),
                'email' => $order->get_billing_email(),
                'phone' => $order->get_billing_phone()
            ];
        } catch (Exception $e) {
            $this->log_error("Failed to extract billing data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 提取配送地址数据（兼容不同版本的WooCommerce）
     *
     * @param WC_Order $order 订单对象
     * @return array 配送地址数据
     */
    private function extract_shipping_data($order) {
        try {
            // 尝试使用新的方法
            if (method_exists($order, 'get_shipping')) {
                return $order->get_shipping();
            }

            // 使用兼容的方法
            return [
                'first_name' => $order->get_shipping_first_name(),
                'last_name' => $order->get_shipping_last_name(),
                'company' => $order->get_shipping_company(),
                'address_1' => $order->get_shipping_address_1(),
                'address_2' => $order->get_shipping_address_2(),
                'city' => $order->get_shipping_city(),
                'state' => $order->get_shipping_state(),
                'postcode' => $order->get_shipping_postcode(),
                'country' => $order->get_shipping_country()
            ];
        } catch (Exception $e) {
            $this->log_error("Failed to extract shipping data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 设置订单地址信息（兼容不同版本的WooCommerce）
     *
     * @param WC_Order $order 订单对象
     * @param array $billing_data 账单地址数据
     * @param array $shipping_data 配送地址数据
     */
    private function set_order_addresses($order, $billing_data, $shipping_data) {
        try {
            // 尝试使用新的方法
            if (method_exists($order, 'set_billing') && is_array($billing_data)) {
                $order->set_billing($billing_data);
            } else {
                // 使用兼容的方法设置账单地址
                $this->set_billing_address_fields($order, $billing_data);
            }

            if (method_exists($order, 'set_shipping') && is_array($shipping_data)) {
                $order->set_shipping($shipping_data);
            } else {
                // 使用兼容的方法设置配送地址
                $this->set_shipping_address_fields($order, $shipping_data);
            }

            $this->log_debug("Set order addresses successfully");

        } catch (Exception $e) {
            $this->log_error("Failed to set order addresses: " . $e->getMessage());
        }
    }

    /**
     * 设置账单地址字段
     *
     * @param WC_Order $order 订单对象
     * @param array $billing_data 账单地址数据
     */
    private function set_billing_address_fields($order, $billing_data) {
        if (!is_array($billing_data)) {
            return;
        }

        $billing_fields = [
            'first_name', 'last_name', 'company', 'address_1', 'address_2',
            'city', 'state', 'postcode', 'country', 'email', 'phone'
        ];

        foreach ($billing_fields as $field) {
            if (isset($billing_data[$field])) {
                $setter = "set_billing_{$field}";
                if (method_exists($order, $setter)) {
                    $order->$setter($billing_data[$field]);
                }
            }
        }
    }

    /**
     * 设置配送地址字段
     *
     * @param WC_Order $order 订单对象
     * @param array $shipping_data 配送地址数据
     */
    private function set_shipping_address_fields($order, $shipping_data) {
        if (!is_array($shipping_data)) {
            return;
        }

        $shipping_fields = [
            'first_name', 'last_name', 'company', 'address_1', 'address_2',
            'city', 'state', 'postcode', 'country'
        ];

        foreach ($shipping_fields as $field) {
            if (isset($shipping_data[$field])) {
                $setter = "set_shipping_{$field}";
                if (method_exists($order, $setter)) {
                    $order->$setter($shipping_data[$field]);
                }
            }
        }
    }

    /**
     * 将处理后的商品添加到订单
     *
     * @param WC_Order $order 订单对象
     * @param array $order_data 订单数据
     */
    private function add_processed_items_to_order($order, $order_data) {
        $this->log_debug("Adding processed items to order #{$order->get_id()}");

        $added_count = 0;

        foreach ($order_data['items'] as $item_data) {
            // 只处理商品项目，跳过其他类型
            if (($item_data['type'] ?? 'line_item') !== 'line_item') {
                $this->log_debug("Skipping non-product item: {$item_data['name']} (type: " . ($item_data['type'] ?? 'line_item') . ")");
                continue;
            }

            $this->log_debug("Adding processed item: {$item_data['name']} (ID: {$item_data['product_id']}, Variation: " . ($item_data['variation_id'] ?? 0) . ", Qty: {$item_data['quantity']}, Total: {$item_data['total']})");

            // 创建新的商品项目
            $new_item = new WC_Order_Item_Product();
            $new_item->set_product_id($item_data['product_id']);
            $new_item->set_variation_id($item_data['variation_id'] ?? 0);
            $new_item->set_quantity($item_data['quantity']);
            $new_item->set_name($item_data['name']);
            $new_item->set_total($item_data['total']);
            $new_item->set_subtotal($item_data['subtotal'] ?? $item_data['total']);
            $new_item->set_total_tax($item_data['total_tax'] ?? 0);
            $new_item->set_subtotal_tax($item_data['subtotal_tax'] ?? 0);

            // 添加meta数据
            if (isset($item_data['meta_data']) && is_array($item_data['meta_data'])) {
                foreach ($item_data['meta_data'] as $meta) {
                    // 处理meta数据，支持数组和对象格式
                    $meta_key = null;
                    $meta_value = null;

                    if (is_array($meta)) {
                        // 数组格式
                        $meta_key = $meta['key'] ?? null;
                        $meta_value = $meta['value'] ?? null;
                    } elseif (is_object($meta)) {
                        // 对象格式
                        $meta_key = $meta->key ?? null;
                        $meta_value = $meta->value ?? null;
                    }

                    if ($meta_key !== null && $meta_value !== null) {
                        $new_item->add_meta_data($meta_key, $meta_value);
                    }
                }
            }

            // 添加到订单
            $order->add_item($new_item);
            $added_count++;

            $this->log_debug("Successfully added processed item: {$item_data['name']} with quantity {$item_data['quantity']} and total {$item_data['total']}");
        }

        // 处理其他类型的项目（费用、运费、优惠券、税费）
        foreach ($order_data['items'] as $item_data) {
            $item_type = $item_data['type'] ?? 'line_item';

            if ($item_type === 'line_item') {
                continue; // 已经处理过了
            }

            $this->log_debug("Adding {$item_type} item: {$item_data['name']}");

            switch ($item_type) {
                case 'fee':
                    $new_item = new WC_Order_Item_Fee();
                    $new_item->set_name($item_data['name']);
                    $new_item->set_total($item_data['total']);
                    $new_item->set_total_tax($item_data['total_tax'] ?? 0);
                    break;

                case 'shipping':
                    $new_item = new WC_Order_Item_Shipping();
                    $new_item->set_method_title($item_data['name']);
                    $new_item->set_method_id($item_data['method_id'] ?? '');
                    $new_item->set_total($item_data['total']);

                    // 使用meta数据设置运费税费，因为set_total_tax()是受保护的方法
                    $shipping_tax = $item_data['total_tax'] ?? 0;
                    if ($shipping_tax > 0) {
                        $new_item->add_meta_data('total_tax', $shipping_tax);
                    }
                    break;

                case 'coupon':
                    $new_item = new WC_Order_Item_Coupon();
                    $new_item->set_name($item_data['name']);
                    $new_item->set_code($item_data['code'] ?? $item_data['name']);

                    // 安全地设置折扣金额
                    $discount_amount = $item_data['discount_amount'] ?? abs($item_data['total']);
                    if (method_exists($new_item, 'set_discount')) {
                        $new_item->set_discount($discount_amount);
                    } else {
                        $new_item->add_meta_data('discount_amount', $discount_amount);
                    }

                    // 安全地设置折扣税费
                    $discount_tax = $item_data['discount_tax'] ?? 0;
                    if ($discount_tax > 0) {
                        if (method_exists($new_item, 'set_discount_tax')) {
                            $new_item->set_discount_tax($discount_tax);
                        } else {
                            $new_item->add_meta_data('discount_tax', $discount_tax);
                        }
                    }
                    break;

                case 'tax':
                    $new_item = new WC_Order_Item_Tax();

                    // 安全地设置税费属性
                    if (method_exists($new_item, 'set_rate_id')) {
                        $new_item->set_rate_id($item_data['rate_id'] ?? 0);
                    }

                    if (method_exists($new_item, 'set_label')) {
                        $new_item->set_label($item_data['name']);
                    }

                    if (method_exists($new_item, 'set_compound')) {
                        $new_item->set_compound($item_data['compound'] ?? false);
                    }

                    // 使用meta数据设置税费金额，以防方法受保护
                    $tax_total = $item_data['tax_total'] ?? $item_data['total'];
                    if (method_exists($new_item, 'set_tax_total')) {
                        try {
                            $new_item->set_tax_total($tax_total);
                        } catch (Error $e) {
                            $new_item->add_meta_data('tax_total', $tax_total);
                        }
                    } else {
                        $new_item->add_meta_data('tax_total', $tax_total);
                    }

                    $shipping_tax_total = $item_data['shipping_tax_total'] ?? 0;
                    if ($shipping_tax_total > 0) {
                        if (method_exists($new_item, 'set_shipping_tax_total')) {
                            try {
                                $new_item->set_shipping_tax_total($shipping_tax_total);
                            } catch (Error $e) {
                                $new_item->add_meta_data('shipping_tax_total', $shipping_tax_total);
                            }
                        } else {
                            $new_item->add_meta_data('shipping_tax_total', $shipping_tax_total);
                        }
                    }
                    break;

                default:
                    $this->log_debug("Unknown item type: {$item_type}, skipping");
                    continue 2;
            }

            // 添加meta数据
            if (isset($item_data['meta_data']) && is_array($item_data['meta_data'])) {
                foreach ($item_data['meta_data'] as $meta) {
                    // 处理meta数据，支持数组和对象格式
                    $meta_key = null;
                    $meta_value = null;

                    if (is_array($meta)) {
                        // 数组格式
                        $meta_key = $meta['key'] ?? null;
                        $meta_value = $meta['value'] ?? null;
                    } elseif (is_object($meta)) {
                        // 对象格式
                        $meta_key = $meta->key ?? null;
                        $meta_value = $meta->value ?? null;
                    }

                    if ($meta_key !== null && $meta_value !== null) {
                        $new_item->add_meta_data($meta_key, $meta_value);
                    }
                }
            }

            // 添加到订单
            $order->add_item($new_item);
            $added_count++;

            $this->log_debug("Successfully added {$item_type} item: {$item_data['name']}");
        }

        $this->log_debug("Added {$added_count} total items to order #{$order->get_id()}");
    }

    /**
     * 根据项目类型获取相应的金额
     *
     * @param WC_Order_Item $item 订单项目
     * @param string $item_type 项目类型
     * @return float 项目金额
     */
    private function get_item_total_by_type($item, $item_type) {
        try {
            switch ($item_type) {
                case 'line_item':
                    // 商品项目使用get_total()
                    return $item->get_total();

                case 'coupon':
                    // 优惠券使用get_discount()，返回负值表示折扣
                    return -abs($item->get_discount());

                case 'shipping':
                    // 运费项目使用get_total()
                    return $item->get_total();

                case 'fee':
                    // 费用项目使用get_total()
                    return $item->get_total();

                case 'tax':
                    // 税费项目使用get_tax_total()
                    return $item->get_tax_total();

                default:
                    // 默认尝试get_total()，如果失败返回0
                    if (method_exists($item, 'get_total')) {
                        return $item->get_total();
                    }
                    $this->log_debug("Unknown item type '{$item_type}' for item '{$item->get_name()}', returning 0");
                    return 0;
            }
        } catch (Exception $e) {
            $this->log_error("Failed to get total for {$item_type} item '{$item->get_name()}': " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 支付处理前自动添加IP规则
     *
     * @param WC_Order $order 订单对象
     */
    private function auto_add_ip_rule_before_payment($order) {
        try {
            // 检查是否启用了自动添加IP规则功能
            $settings = WP_Multi_DB_Config_Manager::get_settings();
            if (empty($settings['auto_add_ip_rules'])) {
                $this->log_debug("Auto add IP rules is disabled, skipping");
                return;
            }

            // 获取客户信息
            $customer_id = $order->get_customer_id();
            $customer_ip = $this->get_customer_ip();
            $current_database = YXJTO_Gateway::get_instance()->get_current_database();

            $this->log_debug("Auto add IP rule before payment: Customer ID: {$customer_id}, IP: {$customer_ip}, Database: {$current_database}");

            // 验证必要信息
            if (empty($customer_ip) || empty($current_database)) {
                $this->log_debug("Missing required information for auto IP rule: IP={$customer_ip}, DB={$current_database}");
                return;
            }

            // 如果是注册用户，检查是否已经有数据库偏好记录
            if ($customer_id > 0) {
                $existing_db_preference = get_user_meta($customer_id, '_yxjto_preferred_database', true);
                if (!empty($existing_db_preference)) {
                    $this->log_debug("Customer {$customer_id} already has database preference: {$existing_db_preference}, skipping");
                    return;
                }

                // 保存客户的数据库偏好
                update_user_meta($customer_id, '_yxjto_preferred_database', $current_database);
                $this->log_debug("Saved database preference for customer {$customer_id}: {$current_database}");
            }

            // 检查IP规则是否已存在
            if ($this->ip_rule_exists($customer_ip, $current_database)) {
                $this->log_debug("IP rule already exists for {$customer_ip} -> {$current_database}");
                return;
            }

            // 添加新的IP规则
            $this->add_ip_rule($customer_ip, $current_database);

            $this->log_debug("Successfully added IP rule: {$customer_ip} -> {$current_database}");

        } catch (Exception $e) {
            $this->log_debug("Error in auto_add_ip_rule_before_payment: " . $e->getMessage());
        }
    }

    /**
     * 获取客户IP地址
     *
     * @return string
     */
    private function get_customer_ip() {
        // 优先获取真实IP（考虑代理和CDN）
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];

                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // 如果没有找到有效的公网IP，返回REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    /**
     * 检查IP规则是否已存在
     *
     * @param string $ip IP地址
     * @param string $database 数据库名称
     * @return bool
     */
    private function ip_rule_exists($ip, $database) {
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();

        foreach ($ip_rules as $rule) {
            // 兼容新旧格式：检查 ip_range 或 ip 字段
            $rule_ip = isset($rule['ip_range']) ? $rule['ip_range'] : (isset($rule['ip']) ? $rule['ip'] : '');
            if ($rule_ip === $ip && $rule['database'] === $database) {
                return true;
            }
        }

        return false;
    }

    /**
     * 添加新的IP规则
     *
     * @param string $ip IP地址
     * @param string $database 数据库名称
     */
    private function add_ip_rule($ip, $database) {
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();

        // 创建新规则 - 使用 ip_range 字段以兼容现有格式
        $new_rule = array(
            'ip_range' => $ip,  // 使用 ip_range 而不是 ip
            'database' => $database,
            'enabled' => true,
            // 添加额外的元数据用于标识自动添加的规则
            'auto_added' => true,
            'created_at' => current_time('mysql'),
            'name' => sprintf(__('Auto-added before payment processing (%s)', 'yxjto-gateway'), date('Y-m-d H:i:s'))
        );

        // 添加到规则列表
        $ip_rules[] = $new_rule;

        // 保存规则
        WP_Multi_DB_Config_Manager::save_ip_rules($ip_rules);

        $this->log_debug("Added new IP rule: " . json_encode($new_rule));
    }
}
