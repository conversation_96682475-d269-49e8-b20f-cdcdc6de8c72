<?php
/**
 * 分析PayPal支付数据
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

echo "<h2>PayPal支付数据分析</h2>";

// 支付数据
$payment_data = [
    "intent" => "sale",
    "payer" => [
        "payment_method" => "paypal"
    ],
    "transactions" => [
        [
            "amount" => [
                "total" => "679.64",
                "currency" => "USD",
                "details" => [
                    "subtotal" => "992.00",
                    "tax" => "0.00",
                    "shipping" => "15.00",
                    "shipping_discount" => "327.36"
                ]
            ],
            "description" => "Order #29268 from asdf",
            "invoice_number" => "29268-asdf-1754024532-be10a6",
            "item_list" => [
                "items" => [
                    [
                        "name" => "asdf Augue adipiscing euismod",
                        "quantity" => 3,
                        "price" => "95.74",
                        "currency" => "USD",
                        "sku" => "MNK-001"
                    ],
                    [
                        "name" => "asdf Classic wooden chair",
                        "quantity" => 3,
                        "price" => "95.74",
                        "currency" => "USD"
                    ],
                    [
                        "name" => "asdf Henectus tincidunt - Blue",
                        "quantity" => 2,
                        "price" => "192.45",
                        "currency" => "USD"
                    ]
                ]
            ]
        ]
    ],
    "redirect_urls" => [
        "return_url" => "http://ksxsk1.com/?wc-api=yxjto_paypal_multi_gateway_payment&order_id=29268",
        "cancel_url" => "http://ksxsk1.com/?wc-api=yxjto_paypal_multi_gateway_payment&cancel=true&order_id=29268"
    ]
];

echo "<h3>1. 基本信息</h3>";
$transaction = $payment_data['transactions'][0];
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>字段</th><th>值</th></tr>";
echo "<tr><td>支付意图</td><td>" . $payment_data['intent'] . "</td></tr>";
echo "<tr><td>支付方式</td><td>" . $payment_data['payer']['payment_method'] . "</td></tr>";
echo "<tr><td>订单描述</td><td>" . $transaction['description'] . "</td></tr>";
echo "<tr><td>发票号</td><td>" . $transaction['invoice_number'] . "</td></tr>";
echo "<tr><td>货币</td><td>" . $transaction['amount']['currency'] . "</td></tr>";
echo "</table>";

echo "<h3>2. 金额计算验证</h3>";

$amount_details = $transaction['amount']['details'];
$items = $transaction['item_list']['items'];

// 计算商品小计
$calculated_subtotal = 0;
echo "<h4>商品明细:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>商品名称</th><th>SKU</th><th>数量</th><th>单价</th><th>小计</th></tr>";

foreach ($items as $item) {
    $item_total = floatval($item['price']) * intval($item['quantity']);
    $calculated_subtotal += $item_total;
    
    $sku = isset($item['sku']) ? $item['sku'] : 'N/A';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($item['name']) . "</td>";
    echo "<td>" . htmlspecialchars($sku) . "</td>";
    echo "<td>" . $item['quantity'] . "</td>";
    echo "<td>$" . number_format(floatval($item['price']), 2) . "</td>";
    echo "<td>$" . number_format($item_total, 2) . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h4>金额汇总:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>项目</th><th>PayPal数据</th><th>计算结果</th><th>状态</th></tr>";

$paypal_subtotal = floatval($amount_details['subtotal']);
$subtotal_match = abs($calculated_subtotal - $paypal_subtotal) < 0.01;

echo "<tr>";
echo "<td>商品小计</td>";
echo "<td>$" . number_format($paypal_subtotal, 2) . "</td>";
echo "<td>$" . number_format($calculated_subtotal, 2) . "</td>";
echo "<td style='color: " . ($subtotal_match ? 'green' : 'red') . ";'>" . ($subtotal_match ? '✓ 匹配' : '✗ 不匹配') . "</td>";
echo "</tr>";

$tax = floatval($amount_details['tax']);
$shipping = floatval($amount_details['shipping']);
$shipping_discount = floatval($amount_details['shipping_discount']);
$total = floatval($transaction['amount']['total']);

echo "<tr><td>税费</td><td>$" . number_format($tax, 2) . "</td><td>-</td><td>-</td></tr>";
echo "<tr><td>运费</td><td>$" . number_format($shipping, 2) . "</td><td>-</td><td>-</td></tr>";
echo "<tr><td>运费折扣</td><td>-$" . number_format($shipping_discount, 2) . "</td><td>-</td><td>-</td></tr>";

$calculated_total = $paypal_subtotal + $tax + $shipping - $shipping_discount;
$total_match = abs($calculated_total - $total) < 0.01;

echo "<tr>";
echo "<td><strong>总计</strong></td>";
echo "<td><strong>$" . number_format($total, 2) . "</strong></td>";
echo "<td><strong>$" . number_format($calculated_total, 2) . "</strong></td>";
echo "<td style='color: " . ($total_match ? 'green' : 'red') . ";'><strong>" . ($total_match ? '✓ 匹配' : '✗ 不匹配') . "</strong></td>";
echo "</tr>";
echo "</table>";

echo "<h3>3. 回调URL分析</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>类型</th><th>URL</th><th>参数</th></tr>";

$return_url = $payment_data['redirect_urls']['return_url'];
$cancel_url = $payment_data['redirect_urls']['cancel_url'];

// 解析返回URL
$return_parts = parse_url($return_url);
parse_str($return_parts['query'] ?? '', $return_params);

echo "<tr>";
echo "<td>成功返回</td>";
echo "<td>" . htmlspecialchars($return_url) . "</td>";
echo "<td>";
foreach ($return_params as $key => $value) {
    echo "{$key}={$value}<br>";
}
echo "</td>";
echo "</tr>";

// 解析取消URL
$cancel_parts = parse_url($cancel_url);
parse_str($cancel_parts['query'] ?? '', $cancel_params);

echo "<tr>";
echo "<td>取消返回</td>";
echo "<td>" . htmlspecialchars($cancel_url) . "</td>";
echo "<td>";
foreach ($cancel_params as $key => $value) {
    echo "{$key}={$value}<br>";
}
echo "</td>";
echo "</tr>";
echo "</table>";

echo "<h3>4. 数据完整性检查</h3>";

$issues = [];

// 检查必需字段
if (empty($payment_data['intent'])) {
    $issues[] = "缺少支付意图 (intent)";
}

if (empty($transaction['amount']['total'])) {
    $issues[] = "缺少总金额";
}

if (empty($transaction['invoice_number'])) {
    $issues[] = "缺少发票号";
}

if (empty($items)) {
    $issues[] = "缺少商品列表";
}

// 检查金额一致性
if (!$subtotal_match) {
    $issues[] = "商品小计与实际计算不匹配";
}

if (!$total_match) {
    $issues[] = "总金额计算不正确";
}

// 检查URL格式
if (!filter_var($return_url, FILTER_VALIDATE_URL)) {
    $issues[] = "返回URL格式无效";
}

if (!filter_var($cancel_url, FILTER_VALIDATE_URL)) {
    $issues[] = "取消URL格式无效";
}

if (empty($issues)) {
    echo "<p style='color: green;'>✓ 数据完整性检查通过，没有发现问题</p>";
} else {
    echo "<p style='color: red;'>✗ 发现以下问题:</p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: red;'>{$issue}</li>";
    }
    echo "</ul>";
}

echo "<h3>5. 建议和注意事项</h3>";

echo "<div style='background: #e7f3ff; padding: 20px; border-left: 4px solid #007cba; margin: 20px 0;'>";
echo "<h4>💡 数据分析结果</h4>";
echo "<ul>";
echo "<li><strong>订单来源:</strong> 来自 'asdf' 数据库</li>";
echo "<li><strong>商品数量:</strong> 总共8件商品（3+3+2）</li>";
echo "<li><strong>折扣情况:</strong> 运费有大额折扣 ($327.36)</li>";
echo "<li><strong>最终金额:</strong> $679.64 (原价$1007.00，折扣$327.36)</li>";
echo "</ul>";
echo "</div>";

if (!$subtotal_match || !$total_match) {
    echo "<div style='background: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
    echo "<h4>⚠️ 金额计算警告</h4>";
    echo "<p>检测到金额计算可能存在问题，建议检查:</p>";
    echo "<ul>";
    echo "<li>WooCommerce商品价格设置</li>";
    echo "<li>税费计算规则</li>";
    echo "<li>运费和折扣计算逻辑</li>";
    echo "<li>PayPal支付网关配置</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<div style='background: #d4edda; padding: 20px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h4>✅ 推荐操作</h4>";
echo "<ul>";
echo "<li>验证订单#29268在WooCommerce中的详细信息</li>";
echo "<li>检查多数据库同步是否正常工作</li>";
echo "<li>确认PayPal回调URL能够正确处理支付结果</li>";
echo "<li>监控支付完成后的订单状态更新</li>";
echo "</ul>";
echo "</div>";

echo "<h3>分析完成</h3>";
echo "<p><a href='javascript:history.back()'>返回</a></p>";
