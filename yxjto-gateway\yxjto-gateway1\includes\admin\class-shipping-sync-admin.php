<?php
/**
 * Shipping Sync Admin Page
 * 
 * @package YXJTO_Gateway
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Shipping_Sync_Admin {
    
    /**
     * Initialize the admin page
     */
    public static function init() {
        // add_action('admin_menu', [__CLASS__, 'add_admin_menu']); // 菜单已在主插件文件中注册
        add_action('admin_init', [__CLASS__, 'handle_form_submission']);
        add_action('wp_ajax_yxjto_create_test_shipping_data', [__CLASS__, 'ajax_create_test_shipping_data']);
        add_action('wp_ajax_yxjto_run_full_shipping_sync', [__CLASS__, 'ajax_run_full_shipping_sync']);
        add_action('wp_ajax_yxjto_debug_shipping_sync', [__CLASS__, 'ajax_debug_shipping_sync']);
        add_action('wp_ajax_yxjto_sync_specific_zone', [__CLASS__, 'ajax_sync_specific_zone']);
        add_action('wp_ajax_yxjto_delete_all_shipping_data', [__CLASS__, 'ajax_delete_all_shipping_data']);
        add_action('wp_ajax_yxjto_verify_shipping_completeness', [__CLASS__, 'ajax_verify_shipping_completeness']);
        add_action('wp_ajax_yxjto_clear_shipping_failure_stats', [__CLASS__, 'ajax_clear_failure_stats']);
    }
    
    /**
     * Add admin menu
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'yxjto',
            __('Shipping Sync', 'yxjto-gateway'),
            __('Shipping Sync', 'yxjto-gateway'),
            'manage_options',
            'yxjto-shipping-sync',
            [__CLASS__, 'render_page']
        );
    }
    
    /**
     * Render the admin page
     */
    public static function render_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Shipping Synchronization Settings', 'yxjto-gateway'); ?></h1>
            
            <?php settings_errors('yxjto_gateway_shipping_sync'); ?>
            
            <form method="post" action="" id="shipping-sync-form">
                <?php wp_nonce_field('yxjto_gateway_shipping_sync', 'yxjto_gateway_shipping_sync_nonce'); ?>
                <input type="hidden" name="action" value="save_shipping_sync" />

                <p><?php _e('Configure shipping zones, methods, and classes synchronization across multiple databases.', 'yxjto-gateway'); ?></p>
                
                <?php
                // 获取配送同步设置 - 使用配置管理器
                $shipping_settings = WP_Multi_DB_Config_Manager::get_config('shipping_replication');

                // 确保所有必需的键都存在，如果不存在则使用默认值
                $defaults = [
                    'enable_shipping_replication' => false,
                    'sync_mode' => 'realtime',
                    'batch_interval' => 'hourly',
                    'conflict_resolution' => 'newest',
                    'enable_shipping_zones_sync' => false,
                    'enable_shipping_methods_sync' => false,
                    'enable_shipping_classes_sync' => false,
                    'enable_local_pickup_sync' => false,
                    'enable_sync_logging' => true,
                    'exclude_disabled_zones' => false,
                    'exclude_disabled' => false,
                    'preserve_method_ids' => false,
                    'sync_method_settings' => true,
                    'sync_shipping_rates' => true,
                    'sync_free_shipping_conditions' => true,
                    'backup_before_sync' => false,
                    'max_retries' => 3,
                    'last_sync' => ''
                ];

                $shipping_settings = array_merge($defaults, $shipping_settings ?: []);
                ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Shipping Sync', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_shipping_replication" value="1"
                                       <?php checked(!empty($shipping_settings['enable_shipping_replication'])); ?> />
                                <?php _e('Enable automatic shipping synchronization across all databases', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, shipping zones, methods, and classes will be automatically synchronized to all configured databases.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sync Components', 'yxjto-gateway'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="enable_shipping_zones_sync" value="1"
                                           <?php checked(!empty($shipping_settings['enable_shipping_zones_sync'])); ?> />
                                    <?php _e('Shipping Zones', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="enable_shipping_methods_sync" value="1"
                                           <?php checked(!empty($shipping_settings['enable_shipping_methods_sync'])); ?> />
                                    <?php _e('Shipping Methods', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="enable_shipping_classes_sync" value="1"
                                           <?php checked(!empty($shipping_settings['enable_shipping_classes_sync'])); ?> />
                                    <?php _e('Shipping Classes', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="enable_local_pickup_sync" value="1"
                                           <?php checked(!empty($shipping_settings['enable_local_pickup_sync'])); ?> />
                                    <?php _e('Local Pickup', 'yxjto-gateway'); ?>
                                </label>
                            </fieldset>
                            <p class="description">
                                <?php _e('Select which shipping components to synchronize across databases.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sync Mode', 'yxjto-gateway'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="radio" name="sync_mode" value="realtime" 
                                           <?php checked($shipping_settings['sync_mode'] ?? 'realtime', 'realtime'); ?> />
                                    <?php _e('Real-time Sync', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="sync_mode" value="batch" 
                                           <?php checked($shipping_settings['sync_mode'] ?? 'realtime', 'batch'); ?> />
                                    <?php _e('Batch Sync', 'yxjto-gateway'); ?>
                                </label>
                            </fieldset>
                            <p class="description">
                                <?php _e('Real-time sync happens immediately when shipping settings are changed. Batch sync runs at scheduled intervals.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr id="batch-interval-row" style="<?php echo ($shipping_settings['sync_mode'] ?? 'realtime') === 'batch' ? '' : 'display: none;'; ?>">
                        <th scope="row"><?php _e('Batch Interval', 'yxjto-gateway'); ?></th>
                        <td>
                            <select name="batch_interval">
                                <option value="hourly" <?php selected($shipping_settings['batch_interval'] ?? 'hourly', 'hourly'); ?>><?php _e('Hourly', 'yxjto-gateway'); ?></option>
                                <option value="twicedaily" <?php selected($shipping_settings['batch_interval'] ?? 'hourly', 'twicedaily'); ?>><?php _e('Twice Daily', 'yxjto-gateway'); ?></option>
                                <option value="daily" <?php selected($shipping_settings['batch_interval'] ?? 'hourly', 'daily'); ?>><?php _e('Daily', 'yxjto-gateway'); ?></option>
                                <option value="weekly" <?php selected($shipping_settings['batch_interval'] ?? 'hourly', 'weekly'); ?>><?php _e('Weekly', 'yxjto-gateway'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('How often to run batch synchronization.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Conflict Resolution', 'yxjto-gateway'); ?></th>
                        <td>
                            <select name="conflict_resolution">
                                <option value="newest" <?php selected($shipping_settings['conflict_resolution'] ?? 'newest', 'newest'); ?>><?php _e('Use Newest', 'yxjto-gateway'); ?></option>
                                <option value="source" <?php selected($shipping_settings['conflict_resolution'] ?? 'newest', 'source'); ?>><?php _e('Source Wins', 'yxjto-gateway'); ?></option>
                                <option value="manual" <?php selected($shipping_settings['conflict_resolution'] ?? 'newest', 'manual'); ?>><?php _e('Manual Resolution', 'yxjto-gateway'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('How to handle conflicts when the same shipping configuration exists in multiple databases with different values.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Enable Logging', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_sync_logging" value="1" 
                                       <?php checked(!empty($shipping_settings['enable_sync_logging'])); ?> />
                                <?php _e('Log shipping synchronization activities', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Enable detailed logging of shipping sync operations for debugging and monitoring.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Exclude Disabled Methods', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="exclude_disabled" value="1" 
                                       <?php checked(!empty($shipping_settings['exclude_disabled'])); ?> />
                                <?php _e('Do not sync disabled shipping methods', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, disabled shipping methods will be excluded from synchronization.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Advanced Options', 'yxjto-gateway'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="preserve_method_ids" value="1" 
                                           <?php checked(!empty($shipping_settings['preserve_method_ids'])); ?> />
                                    <?php _e('Preserve Method IDs', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="sync_method_settings" value="1" 
                                           <?php checked(!empty($shipping_settings['sync_method_settings'])); ?> />
                                    <?php _e('Sync Method Settings', 'yxjto-gateway'); ?>
                                </label>
                            </fieldset>
                            <p class="description">
                                <?php _e('Advanced synchronization options for shipping methods and their configurations.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit_shipping_sync" class="button-primary" 
                           value="<?php _e('Save Shipping Sync Settings', 'yxjto-gateway'); ?>" />
                </p>
            </form>

            <!-- 同步状态和操作 -->
            <div class="shipping-sync-status">
                <h3><?php _e('Shipping Synchronization Status', 'yxjto-gateway'); ?></h3>
                
                <?php 
                // 获取配送同步统计信息
                if (class_exists('YXJTO_Shipping_Replication')) {
                    try {
                        $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                        $shipping_stats = $shipping_replication->get_shipping_stats();
                    } catch (Exception $e) {
                        $shipping_stats = [
                            'total_zones' => 0,
                            'total_methods' => 0,
                            'total_classes' => 0,
                            'synced_zones' => 0,
                            'synced_methods' => 0,
                            'synced_classes' => 0,
                            'failed_syncs' => 0,
                            'last_sync' => null
                        ];
                    }
                } else {
                    $shipping_stats = [
                        'total_zones' => 0,
                        'total_methods' => 0,
                        'total_classes' => 0,
                        'synced_zones' => 0,
                        'synced_methods' => 0,
                        'synced_classes' => 0,
                        'failed_syncs' => 0,
                        'last_sync' => null
                    ];
                }
                ?>
                
                <div class="sync-stats">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <strong><?php _e('Shipping Zones:', 'yxjto-gateway'); ?></strong> 
                            <?php echo esc_html($shipping_stats['total_zones']); ?> 
                            (<?php echo esc_html($shipping_stats['synced_zones']); ?> <?php _e('synced', 'yxjto-gateway'); ?>)
                        </div>
                        <div class="stat-item">
                            <strong><?php _e('Shipping Methods:', 'yxjto-gateway'); ?></strong> 
                            <?php echo esc_html($shipping_stats['total_methods']); ?> 
                            (<?php echo esc_html($shipping_stats['synced_methods']); ?> <?php _e('synced', 'yxjto-gateway'); ?>)
                        </div>
                        <div class="stat-item">
                            <strong><?php _e('Shipping Classes:', 'yxjto-gateway'); ?></strong> 
                            <?php echo esc_html($shipping_stats['total_classes']); ?> 
                            (<?php echo esc_html($shipping_stats['synced_classes']); ?> <?php _e('synced', 'yxjto-gateway'); ?>)
                        </div>
                        <div class="stat-item">
                            <strong><?php _e('Failed Syncs:', 'yxjto-gateway'); ?></strong>
                            <?php echo esc_html($shipping_stats['failed_syncs'] ?? 0); ?>
                            <?php if (($shipping_stats['failed_syncs'] ?? 0) > 0): ?>
                                <button type="button" class="button button-small" style="margin-left: 10px; background-color: #dc3545; border-color: #dc3545; color: white;" onclick="clearFailureStats()">
                                    <?php _e('Clear', 'yxjto-gateway'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <p>
                        <strong><?php _e('Last Sync:', 'yxjto-gateway'); ?></strong> 
                        <?php 
                        echo $shipping_stats['last_sync'] 
                            ? esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($shipping_stats['last_sync'])))
                            : __('Never', 'yxjto-gateway');
                       ?>
                    </p>
                    <?php if (empty($shipping_settings['enable_shipping_replication'])): ?>
                    <p class="sync-disabled-notice">
                        <strong><?php _e('Notice:', 'yxjto-gateway'); ?></strong> 
                        <?php _e('Shipping synchronization is currently disabled.', 'yxjto-gateway'); ?>
                    </p>
                    <?php endif; ?>
                </div>
                
                <div class="sync-actions">
                    <button type="button" class="button button-primary" id="create-test-data-btn" onclick="createTestShippingData()">
                        <?php _e('Create Test Data', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-primary" id="run-full-sync-btn" onclick="runFullShippingSync()">
                        <?php _e('Run Full Sync', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="debugShippingSync()">
                        <?php _e('🐛 Debug Sync', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="syncSpecificZone()">
                        <?php _e('🎯 Sync Specific Zone', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="viewShippingSyncLogs()">
                        <?php _e('📋 View Sync Logs', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="refreshShippingStatus()">
                        <?php _e('🔄 Refresh Status', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" style="background-color: #dc3545; border-color: #dc3545; color: white;" onclick="deleteAllShippingData()">
                        <?php _e('🗑️ Delete All Shipping Data', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="verifyShippingCompleteness()">
                        <?php _e('🔍 Verify Data Completeness', 'yxjto-gateway'); ?>
                    </button>
                </div>
                
                <div id="shipping-sync-result-message" style="display: none;"></div>
            </div>
        </div>

        <style>
        .shipping-sync-status {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        
        .sync-actions {
            margin-top: 15px;
        }
        
        .sync-actions .button {
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        .sync-disabled-notice {
            color: #d63638;
            font-weight: bold;
        }
        
        #batch-interval-row {
            transition: all 0.3s ease;
        }
        
        #shipping-sync-result-message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        
        #shipping-sync-result-message.success {
            background: #d1edff;
            border-left: 4px solid #0073aa;
            color: #0073aa;
        }
        
        #shipping-sync-result-message.error {
            background: #ffeaea;
            border-left: 4px solid #d63638;
            color: #d63638;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // 切换批量同步间隔显示
            $('input[name="sync_mode"]').change(function() {
                if ($(this).val() === 'batch') {
                    $('#batch-interval-row').show();
                } else {
                    $('#batch-interval-row').hide();
                }
            });
        });

        // 创建测试配送数据
        function createTestShippingData() {
            if (!confirm('<?php _e('This will create test shipping zones, methods, and classes. Continue?', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Creating...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_create_test_shipping_data',
                nonce: '<?php echo wp_create_nonce('yxjto_create_test_shipping_data'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Test shipping data created successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    showShippingSyncMessage('<?php _e('Failed to create test shipping data:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 运行完整配送同步
        function runFullShippingSync() {
            if (!confirm('<?php _e('Are you sure you want to run full shipping synchronization? This may take some time and will sync to all databases.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_run_full_shipping_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_run_full_shipping_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Full shipping synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    showShippingSyncMessage('<?php _e('Full shipping synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 调试配送同步状态
        function debugShippingSync() {
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Debugging...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_debug_shipping_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_debug_shipping_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    // 在新窗口中显示调试信息
                    const debugWindow = window.open('', 'shippingDebug', 'width=800,height=600,scrollbars=yes');
                    debugWindow.document.write('<html><head><title>Shipping Debug Info</title></head><body>');
                    debugWindow.document.write('<h2>Shipping Sync Debug Information</h2>');
                    debugWindow.document.write('<pre>' + (response.data.debug_info || 'No debug information available') + '</pre>');
                    debugWindow.document.write('</body></html>');
                    debugWindow.document.close();
                } else {
                    showShippingSyncMessage('<?php _e('Debug operation failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 查看配送同步日志
        function viewShippingSyncLogs() {
            // 在新窗口中显示日志
            window.open('<?php echo admin_url('admin.php?page=yxjto-shipping-sync-logs'); ?>', '_blank');
        }

        // 刷新配送状态
        function refreshShippingStatus() {
            location.reload();
        }

        // 删除所有配送数据
        function deleteAllShippingData() {
            if (!confirm('<?php _e('Are you sure you want to delete ALL shipping data from ALL databases? This action cannot be undone!', 'yxjto-gateway'); ?>')) {
                return;
            }

            if (!confirm('<?php _e('This will permanently delete all shipping zones, methods, locations, and classes from all configured databases. Are you absolutely sure?', 'yxjto-gateway'); ?>')) {
                return;
            }

            showShippingSyncMessage('<?php _e('Deleting all shipping data...', 'yxjto-gateway'); ?>', 'info');

            jQuery.post(ajaxurl, {
                action: 'yxjto_delete_all_shipping_data',
                nonce: '<?php echo wp_create_nonce('yxjto_delete_all_shipping_data'); ?>'
            }).done(function(response) {
                console.log('Delete all shipping data response:', response);
                if (response.success) {
                    showShippingSyncMessage('<?php _e('All shipping data deleted successfully!', 'yxjto-gateway'); ?>' +
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    showShippingSyncMessage('<?php _e('Delete failed:', 'yxjto-gateway'); ?> ' + errorMessage, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.log('Delete all shipping data request failed:', xhr, status, error);
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            });
        }

        // 验证配送数据完整性
        function verifyShippingCompleteness() {
            showShippingSyncMessage('<?php _e('Verifying shipping data completeness...', 'yxjto-gateway'); ?>', 'info');

            jQuery.post(ajaxurl, {
                action: 'yxjto_verify_shipping_completeness',
                nonce: '<?php echo wp_create_nonce('yxjto_verify_shipping_completeness'); ?>'
            }).done(function(response) {
                console.log('Verify shipping completeness response:', response);
                if (response.success) {
                    var report = response.data.report;
                    var message = '<?php _e('Data Completeness Report:', 'yxjto-gateway'); ?>\n\n';

                    // 格式化报告
                    for (var category in report) {
                        message += category.toUpperCase() + ':\n';
                        if (report[category].source_count !== undefined) {
                            message += '  Source: ' + report[category].source_count + '\n';
                            for (var db in report[category].target_counts) {
                                message += '  ' + db + ': ' + report[category].target_counts[db] + '\n';
                            }
                        }
                        message += '\n';
                    }

                    showShippingSyncMessage(message, 'success');
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    showShippingSyncMessage('<?php _e('Verification failed:', 'yxjto-gateway'); ?> ' + errorMessage, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.log('Verify shipping completeness request failed:', xhr, status, error);
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            });
        }

        // 清除失败统计
        function clearFailureStats() {
            if (!confirm('<?php _e('Are you sure you want to clear the failure statistics?', 'yxjto-gateway'); ?>')) {
                return;
            }

            showShippingSyncMessage('<?php _e('Clearing failure statistics...', 'yxjto-gateway'); ?>', 'info');

            jQuery.post(ajaxurl, {
                action: 'yxjto_clear_shipping_failure_stats',
                nonce: '<?php echo wp_create_nonce('yxjto_clear_shipping_failure_stats'); ?>'
            }).done(function(response) {
                console.log('Clear failure stats response:', response);
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Failure statistics cleared successfully!', 'yxjto-gateway'); ?>', 'success');
                    refreshShippingStatus();
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    showShippingSyncMessage('<?php _e('Clear failed:', 'yxjto-gateway'); ?> ' + errorMessage, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.log('Clear failure stats request failed:', xhr, status, error);
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            });
        }

        // 同步特定区域
        function syncSpecificZone() {
            const zoneId = prompt('<?php _e('Please enter the zone ID to sync:', 'yxjto-gateway'); ?>');
            if (!zoneId || isNaN(zoneId)) {
                alert('<?php _e('Please enter a valid zone ID.', 'yxjto-gateway'); ?>');
                return;
            }

            jQuery.post(ajaxurl, {
                action: 'yxjto_sync_specific_zone',
                zone_id: zoneId,
                nonce: '<?php echo wp_create_nonce('yxjto_sync_specific_zone'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Zone synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                } else {
                    showShippingSyncMessage('<?php _e('Zone synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            });
        }

        // 显示同步消息
        function showShippingSyncMessage(message, type) {
            const messageDiv = document.getElementById('shipping-sync-result-message');
            messageDiv.textContent = message;
            messageDiv.className = type;
            messageDiv.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(function() {
                messageDiv.style.display = 'none';
            }, 5000);
        }
        </script>
        <?php
    }
    
    /**
     * Handle form submission
     */
    public static function handle_form_submission() {
        if (!isset($_POST['action']) || $_POST['action'] !== 'save_shipping_sync') {
            return;
        }
        
        if (!wp_verify_nonce($_POST['yxjto_gateway_shipping_sync_nonce'], 'yxjto_gateway_shipping_sync')) {
            wp_die(__('Security check failed', 'yxjto-gateway'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'yxjto-gateway'));
        }
        
        // 收集设置数据
        $shipping_settings = [
            'enable_shipping_replication' => !empty($_POST['enable_shipping_replication']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'manual'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'hourly'),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'newest'),
            'enable_shipping_zones_sync' => !empty($_POST['enable_shipping_zones_sync']),
            'enable_shipping_methods_sync' => !empty($_POST['enable_shipping_methods_sync']),
            'enable_shipping_classes_sync' => !empty($_POST['enable_shipping_classes_sync']),
            'enable_local_pickup_sync' => !empty($_POST['enable_local_pickup_sync']),
            'enable_sync_logging' => !empty($_POST['enable_sync_logging']),
            'exclude_disabled_zones' => !empty($_POST['exclude_disabled_zones']),
            'sync_shipping_rates' => !empty($_POST['sync_shipping_rates']),
            'sync_free_shipping_conditions' => !empty($_POST['sync_free_shipping_conditions']),
            'backup_before_sync' => !empty($_POST['backup_before_sync']),
            'max_retries' => intval($_POST['max_retries'] ?? 3)
        ];

        // 保存设置到配置管理器
        $result = WP_Multi_DB_Config_Manager::update_config_section('shipping_replication', $shipping_settings);

        if ($result !== false) {
            add_settings_error('yxjto_gateway_shipping_sync', 'shipping_sync_updated', __('Shipping synchronization settings saved successfully!', 'yxjto-gateway'), 'updated');
            
            // 如果启用了批量同步，安排 WP Cron 任务
            if (!empty($shipping_settings['enable_shipping_replication']) && ($shipping_settings['sync_mode'] ?? 'realtime') === 'batch') {
                self::schedule_shipping_batch_sync($shipping_settings['batch_interval'] ?? 'hourly');
            } else {
                // 如果禁用了批量同步，清除现有的 Cron 任务
                wp_clear_scheduled_hook('yxjto_shipping_batch_sync');
            }
        } else {
            add_settings_error('yxjto_gateway_shipping_sync', 'shipping_sync_error', __('Failed to save shipping synchronization settings.', 'yxjto-gateway'), 'error');
        }
    }
    
    /**
     * Schedule shipping batch sync
     */
    private static function schedule_shipping_batch_sync($interval = 'hourly') {
        // 清除现有任务
        wp_clear_scheduled_hook('yxjto_shipping_batch_sync');
        
        // 安排新任务
        if (!wp_next_scheduled('yxjto_shipping_batch_sync')) {
            wp_schedule_event(time(), $interval, 'yxjto_shipping_batch_sync');
        }
    }
    
    /**
     * AJAX: Create test shipping data
     */
    public static function ajax_create_test_shipping_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_create_test_shipping_data')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $result = $shipping_replication->create_test_shipping_data();
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => sprintf(__('Created %d test shipping zones and methods.', 'yxjto-gateway'), $result['created_count'])
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error creating test shipping data: ', 'yxjto-gateway') . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Run full shipping sync
     */
    public static function ajax_run_full_shipping_sync() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_run_full_shipping_sync')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $result = $shipping_replication->batch_sync_all_shipping();

                if ($result === true) {
                    wp_send_json_success([
                        'message' => __('Full shipping synchronization completed successfully.', 'yxjto-gateway')
                    ]);
                } else {
                    wp_send_json_error(__('Shipping synchronization failed', 'yxjto-gateway'));
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error running full shipping sync: ', 'yxjto-gateway') . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Debug shipping sync
     */
    public static function ajax_debug_shipping_sync() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_debug_shipping_sync')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $debug_info = $shipping_replication->get_debug_info();
                
                wp_send_json_success([
                    'debug_info' => $debug_info
                ]);
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error debugging shipping sync: ', 'yxjto-gateway') . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Sync specific zone
     */
    public static function ajax_sync_specific_zone() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sync_specific_zone')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $zone_id = intval($_POST['zone_id'] ?? 0);
        if ($zone_id <= 0) {
            wp_send_json_error(__('Invalid zone ID provided.', 'yxjto-gateway'));
        }
        
        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $result = $shipping_replication->sync_specific_zone($zone_id);
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => sprintf(__('Zone %d synchronized successfully.', 'yxjto-gateway'), $zone_id)
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error syncing specific zone: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX: Delete all shipping data from all databases
     */
    public static function ajax_delete_all_shipping_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_delete_all_shipping_data')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $result = $shipping_replication->delete_all_shipping_data();

                if ($result['success']) {
                    $message = sprintf(__('Deleted %d shipping items from all databases successfully.', 'yxjto-gateway'), $result['total_deleted']);

                    // 添加详细结果
                    if (!empty($result['results'])) {
                        $details = [];
                        foreach ($result['results'] as $db_key => $db_result) {
                            if ($db_result['success']) {
                                $details[] = sprintf(
                                    __('%s: %d items (Zones: %d, Methods: %d, Locations: %d, Classes: %d)', 'yxjto-gateway'),
                                    $db_key,
                                    $db_result['deleted_items'],
                                    $db_result['zones'],
                                    $db_result['methods'],
                                    $db_result['locations'],
                                    $db_result['classes']
                                );
                            } else {
                                $details[] = sprintf(__('%s: Failed - %s', 'yxjto-gateway'), $db_key, $db_result['error']);
                            }
                        }
                        $message .= '<br><br>' . __('Details:', 'yxjto-gateway') . '<br>' . implode('<br>', $details);
                    }

                    wp_send_json_success([
                        'message' => $message
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error('Shipping replication class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Delete failed: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Verify shipping data completeness
     */
    public static function ajax_verify_shipping_completeness() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_verify_shipping_completeness')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $report = $shipping_replication->verify_shipping_data_completeness();

                wp_send_json_success([
                    'message' => __('Data completeness verification completed.', 'yxjto-gateway'),
                    'report' => $report
                ]);
            } else {
                wp_send_json_error('Shipping replication class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Verification failed: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Clear shipping failure statistics
     */
    public static function ajax_clear_failure_stats() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_clear_shipping_failure_stats')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            if (class_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication::get_instance();
                $shipping_replication->clear_failure_stats();

                wp_send_json_success([
                    'message' => __('Failure statistics cleared successfully.', 'yxjto-gateway')
                ]);
            } else {
                wp_send_json_error('Shipping replication class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Clear failed: ' . $e->getMessage());
        }
    }
}

// Initialize the admin page
if (class_exists('YXJTO_Shipping_Sync_Admin')) {
    YXJTO_Shipping_Sync_Admin::init();
}
