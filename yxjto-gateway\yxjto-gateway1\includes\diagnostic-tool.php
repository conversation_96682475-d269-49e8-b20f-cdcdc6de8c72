<?php
/**
 * PayPal邮箱支付诊断工具
 * 
 * 用于诊断和验证邮箱支付修复的效果
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    die('Direct access not permitted.');
}

class PayPalEmailPaymentDiagnostic {
    
    private $results = array();
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_run_paypal_diagnostic', array($this, 'run_diagnostic_ajax'));
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'PayPal邮箱支付诊断',
            'PayPal诊断',
            'manage_options',
            'paypal-diagnostic',
            array($this, 'diagnostic_page')
        );
    }
    
    public function diagnostic_page() {
        ?>
        <div class="wrap">
            <h1>PayPal邮箱支付诊断工具</h1>
            
            <div class="notice notice-info">
                <p><strong>说明：</strong>此工具用于诊断PayPal邮箱支付修复的效果，帮助确定是否解决了交易记录缺失的问题。</p>
            </div>
            
            <div id="diagnostic-results"></div>
            
            <button type="button" class="button button-primary" id="run-diagnostic">开始诊断</button>
            
            <h2>诊断项目</h2>
            <ul>
                <li>✓ PayPal账户类型检测</li>
                <li>✓ IPN配置验证</li>
                <li>✓ 支付处理逻辑检查</li>
                <li>✓ 日志记录功能测试</li>
                <li>✓ 订单复制功能验证</li>
            </ul>
            
            <h2>修复内容概述</h2>
            <table class="widefat">
                <thead>
                    <tr>
                        <th>修复项目</th>
                        <th>修复前问题</th>
                        <th>修复后效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>支付类型检测</td>
                        <td>无法正确区分API和邮箱支付</td>
                        <td>自动检测支付类型并采用相应处理逻辑</td>
                    </tr>
                    <tr>
                        <td>邮箱支付执行</td>
                        <td>支付界面正常但无交易记录</td>
                        <td>确保邮箱支付正确执行并生成记录</td>
                    </tr>
                    <tr>
                        <td>IPN处理</td>
                        <td>IPN处理不完善，缺少验证</td>
                        <td>完整的IPN验证和错误处理</td>
                    </tr>
                    <tr>
                        <td>标准支付返回</td>
                        <td>缺少PDT处理机制</td>
                        <td>支持PDT和IPN双重确认</td>
                    </tr>
                    <tr>
                        <td>日志记录</td>
                        <td>调试信息不足</td>
                        <td>详细的调试日志和错误追踪</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#run-diagnostic').click(function() {
                $(this).prop('disabled', true).text('诊断中...');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'run_paypal_diagnostic',
                        nonce: '<?php echo wp_create_nonce("paypal_diagnostic"); ?>'
                    },
                    success: function(response) {
                        $('#diagnostic-results').html(response);
                        $('#run-diagnostic').prop('disabled', false).text('重新诊断');
                    },
                    error: function() {
                        $('#diagnostic-results').html('<div class="notice notice-error"><p>诊断过程中发生错误</p></div>');
                        $('#run-diagnostic').prop('disabled', false).text('重新诊断');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    public function run_diagnostic_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'paypal_diagnostic')) {
            wp_die('Security check failed');
        }
        
        $this->run_diagnostic();
        
        // 输出结果
        echo $this->format_results();
        wp_die();
    }
    
    private function run_diagnostic() {
        // 1. 检查PayPal网关设置
        $this->check_paypal_settings();
        
        // 2. 验证文件修改
        $this->check_file_modifications();
        
        // 3. 测试日志功能
        $this->test_logging_function();
        
        // 4. 检查IPN配置
        $this->check_ipn_configuration();
        
        // 5. 验证账户类型检测
        $this->test_account_type_detection();
    }
    
    private function check_paypal_settings() {
        $gateway = new YXJTO_PayPal_Multi_Gateway_Payment();
        $settings = $gateway->get_option('accounts', array());
        
        if (empty($settings)) {
            $this->results['settings'] = array(
                'status' => 'error',
                'message' => '未找到PayPal账户配置'
            );
            return;
        }
        
        $email_accounts = 0;
        $api_accounts = 0;
        
        foreach ($settings as $account) {
            if (empty($account['client_id']) && empty($account['client_secret'])) {
                $email_accounts++;
            } else {
                $api_accounts++;
            }
        }
        
        $this->results['settings'] = array(
            'status' => 'success',
            'message' => "找到 {$email_accounts} 个邮箱账户和 {$api_accounts} 个API账户"
        );
    }
    
    private function check_file_modifications() {
        $api_file = plugin_dir_path(__FILE__) . '../class-paypal-multi-gateway-api.php';
        $payment_file = plugin_dir_path(__FILE__) . '../class-paypal-multi-gateway-payment.php';
        
        $modifications_found = 0;
        
        // 检查API文件修改
        if (file_exists($api_file)) {
            $api_content = file_get_contents($api_file);
            if (strpos($api_content, 'execute_order_replication') !== false) {
                $modifications_found++;
            }
            if (strpos($api_content, 'is_email_payment') !== false) {
                $modifications_found++;
            }
        }
        
        // 检查支付文件修改
        if (file_exists($payment_file)) {
            $payment_content = file_get_contents($payment_file);
            if (strpos($payment_content, 'handle_standard_payment_return') !== false) {
                $modifications_found++;
            }
            if (strpos($payment_content, 'IPN处理开始') !== false) {
                $modifications_found++;
            }
        }
        
        $this->results['modifications'] = array(
            'status' => $modifications_found >= 3 ? 'success' : 'warning',
            'message' => "检测到 {$modifications_found}/4 个关键修改"
        );
    }
    
    private function test_logging_function() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Payment')) {
            $gateway = new YXJTO_PayPal_Multi_Gateway_Payment();
            
            // 测试日志功能
            if (method_exists($gateway, 'log_debug')) {
                $this->results['logging'] = array(
                    'status' => 'success',
                    'message' => '日志功能正常'
                );
            } else {
                $this->results['logging'] = array(
                    'status' => 'error',
                    'message' => '日志功能缺失'
                );
            }
        } else {
            $this->results['logging'] = array(
                'status' => 'error',
                'message' => 'PayPal网关类不存在'
            );
        }
    }
    
    private function check_ipn_configuration() {
        $home_url = home_url('/');
        $ipn_url = add_query_arg('wc-api', 'paypal_multi_gateway_ipn', $home_url);
        
        // 检查URL是否可访问
        $response = wp_remote_get($ipn_url, array('timeout' => 10));
        
        if (!is_wp_error($response)) {
            $this->results['ipn'] = array(
                'status' => 'success',
                'message' => "IPN URL配置正确: {$ipn_url}"
            );
        } else {
            $this->results['ipn'] = array(
                'status' => 'warning',
                'message' => "IPN URL可能无法访问: {$ipn_url}"
            );
        }
    }
    
    private function test_account_type_detection() {
        // 模拟账户类型检测
        $email_account = array('client_id' => '', 'client_secret' => '');
        $api_account = array('client_id' => 'test_id', 'client_secret' => 'test_secret');
        
        $email_detected = empty($email_account['client_id']) && empty($email_account['client_secret']);
        $api_detected = !empty($api_account['client_id']) && !empty($api_account['client_secret']);
        
        if ($email_detected && $api_detected) {
            $this->results['account_detection'] = array(
                'status' => 'success',
                'message' => '账户类型检测逻辑正常'
            );
        } else {
            $this->results['account_detection'] = array(
                'status' => 'error',
                'message' => '账户类型检测逻辑异常'
            );
        }
    }
    
    private function format_results() {
        $output = '<div class="diagnostic-results">';
        $output .= '<h2>诊断结果</h2>';
        
        $total = count($this->results);
        $success = 0;
        $warnings = 0;
        $errors = 0;
        
        foreach ($this->results as $key => $result) {
            $icon = '';
            $class = 'notice-info';
            
            switch ($result['status']) {
                case 'success':
                    $icon = '✅';
                    $class = 'notice-success';
                    $success++;
                    break;
                case 'warning':
                    $icon = '⚠️';
                    $class = 'notice-warning';
                    $warnings++;
                    break;
                case 'error':
                    $icon = '❌';
                    $class = 'notice-error';
                    $errors++;
                    break;
            }
            
            $output .= "<div class='notice {$class}'>";
            $output .= "<p><strong>{$icon} " . ucfirst($key) . ":</strong> {$result['message']}</p>";
            $output .= "</div>";
        }
        
        // 总结
        $output .= '<div class="notice notice-info">';
        $output .= "<h3>诊断总结</h3>";
        $output .= "<p>总检查项目: {$total}</p>";
        $output .= "<p>✅ 通过: {$success} | ⚠️ 警告: {$warnings} | ❌ 错误: {$errors}</p>";
        
        if ($errors == 0 && $warnings <= 1) {
            $output .= "<p><strong>🎉 邮箱支付修复状态良好！</strong></p>";
        } elseif ($errors > 0) {
            $output .= "<p><strong>🔧 需要修复一些问题才能正常工作。</strong></p>";
        } else {
            $output .= "<p><strong>✅ 基本功能正常，但有一些可以改进的地方。</strong></p>";
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
}

// 初始化诊断工具
if (is_admin()) {
    new PayPalEmailPaymentDiagnostic();
}
?>
