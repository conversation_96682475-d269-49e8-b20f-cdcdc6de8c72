<?php
/**
 * 税收复制功能
 * 
 * 处理所有数据库之间的税收数据复制
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class WP_Multi_DB_Tax_Replication {

    /**
     * 初始化税收复制功能
     */
    public static function init() {
        // 注册AJAX处理器
        add_action('wp_ajax_yxjto_sync_tax_data', [__CLASS__, 'ajax_sync_tax_data']);
        add_action('wp_ajax_yxjto_validate_tax_data', [__CLASS__, 'ajax_validate_tax_data']);
        add_action('wp_ajax_yxjto_cleanup_tax_data', [__CLASS__, 'ajax_cleanup_tax_data']);
        add_action('wp_ajax_yxjto_backup_tax_data', [__CLASS__, 'ajax_backup_tax_data']);
        add_action('wp_ajax_yxjto_restore_tax_data', [__CLASS__, 'ajax_restore_tax_data']);

        // 注册计划任务钩子
        add_action('yxjto_tax_sync_cron', [__CLASS__, 'scheduled_tax_sync']);

        // 如果启用了实时同步，注册相关钩子
        if (class_exists('WP_Multi_DB_Config_Manager') && method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
            if (!empty($tax_settings['enable_tax_replication']) && $tax_settings['sync_mode'] === 'realtime') {
                self::register_realtime_hooks();
            }
        }
    }

    /**
     * 注册实时同步钩子
     */
    private static function register_realtime_hooks() {
        // 税率变更钩子
        add_action('woocommerce_tax_rate_added', [__CLASS__, 'on_tax_rate_added'], 10, 2);
        add_action('woocommerce_tax_rate_updated', [__CLASS__, 'on_tax_rate_updated'], 10, 2);
        add_action('woocommerce_tax_rate_deleted', [__CLASS__, 'on_tax_rate_deleted'], 10, 1);

        // 税率类别变更钩子
        add_action('created_product_tax_class', [__CLASS__, 'on_tax_class_created'], 10, 2);
        add_action('edited_product_tax_class', [__CLASS__, 'on_tax_class_updated'], 10, 2);
        add_action('delete_product_tax_class', [__CLASS__, 'on_tax_class_deleted'], 10, 3);

        // WooCommerce设置变更钩子
        add_action('update_option_woocommerce_tax_based_on', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_shipping_tax_class', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_tax_round_at_subtotal', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_tax_classes', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_tax_total_display', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_tax_display_shop', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_tax_display_cart', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
        add_action('update_option_woocommerce_prices_include_tax', [__CLASS__, 'on_tax_setting_updated'], 10, 3);
    }

    /**
     * 获取需要同步的税率表
     */
    private static function get_tax_tables() {
        global $wpdb;
        return [
            $wpdb->prefix . 'woocommerce_tax_rates',
            $wpdb->prefix . 'woocommerce_tax_rate_locations'
        ];
    }

    /**
     * 获取需要同步的税收相关选项
     */
    private static function get_tax_options() {
        return [
            'woocommerce_tax_based_on',
            'woocommerce_shipping_tax_class', 
            'woocommerce_tax_round_at_subtotal',
            'woocommerce_tax_classes',
            'woocommerce_tax_total_display',
            'woocommerce_tax_display_shop',
            'woocommerce_tax_display_cart',
            'woocommerce_prices_include_tax',
            'woocommerce_calc_taxes',
            'woocommerce_tax_based_on',
            'woocommerce_default_customer_address',
            'woocommerce_store_address',
            'woocommerce_store_address_2',
            'woocommerce_store_city',
            'woocommerce_default_country',
            'woocommerce_store_postcode'
        ];
    }

    /**
     * 执行完整的税收数据同步
     * 
     * @param array $target_databases 目标数据库列表，为空则同步到所有数据库
     * @return array 同步结果
     */
    public static function sync_tax_data($target_databases = []) {
        global $wpdb;
        
        $results = [
            'success' => true,
            'synced_databases' => [],
            'target_databases' => [],
            'synced_items' => [
                'tax_rates' => 0,
                'tax_rate_locations' => 0,
                'tax_options' => 0,
                'tax_classes' => 0
            ],
            'errors' => [],
            'summary' => [
                'tax_rates' => 0,
                'tax_rate_locations' => 0,
                'tax_options' => 0,
                'tax_classes' => 0
            ]
        ];

        try {
            // 获取当前数据库配置
            if (!class_exists('YXJTO_Gateway')) {
                throw new Exception('YXJTO_Gateway 类不可用');
            }
            
            $current_db = YXJTO_Gateway::get_instance()->get_current_database();
            $database_configs = WP_Multi_DB_Config_Manager::get_databases();

            // 如果没有指定目标数据库，则同步到所有数据库
            if (empty($target_databases)) {
                $target_databases = array_keys($database_configs);
            }

            // 移除当前数据库（不需要同步到自己）
            $target_databases = array_filter($target_databases, function($db) use ($current_db) {
                return $db !== $current_db;
            });

            // 获取源数据库的税率数据
            $source_tax_rates = self::get_tax_rates_data();
            $source_tax_rate_locations = self::get_tax_rate_locations_data();
            $source_tax_options = self::get_tax_options_data();
            $source_tax_classes = self::get_tax_classes_data();

            self::log_tax_sync("开始税收数据同步，源数据库: {$current_db}，目标数据库: " . implode(', ', $target_databases));

            // 逐个同步到目标数据库
            foreach ($target_databases as $target_db) {
                if (!isset($database_configs[$target_db]) || empty($database_configs[$target_db]['enabled'])) {
                    continue;
                }

                try {
                    // 切换到目标数据库
                    $original_db = YXJTO_Gateway::get_instance()->get_current_database();
                    YXJTO_Gateway::get_instance()->switch_database($target_db);

                    // 开始事务
                    $wpdb->query('START TRANSACTION');

                    // 同步税率
                    $tax_rates_synced = self::sync_tax_rates_to_database($source_tax_rates, $target_db);
                    $results['summary']['tax_rates'] += $tax_rates_synced;
                    $results['synced_items']['tax_rates'] += $tax_rates_synced;

                    // 同步税率位置数据
                    $tax_locations_synced = self::sync_tax_rate_locations_to_database($source_tax_rate_locations, $target_db);
                    $results['summary']['tax_rate_locations'] += $tax_locations_synced;
                    $results['synced_items']['tax_rate_locations'] += $tax_locations_synced;

                    // 同步税收选项
                    $tax_options_synced = self::sync_tax_options_to_database($source_tax_options, $target_db);
                    $results['summary']['tax_options'] += $tax_options_synced;
                    $results['synced_items']['tax_options'] += $tax_options_synced;

                    // 同步税率类别
                    $tax_classes_synced = self::sync_tax_classes_to_database($source_tax_classes, $target_db);
                    $results['summary']['tax_classes'] += $tax_classes_synced;
                    $results['synced_items']['tax_classes'] += $tax_classes_synced;

                    // 提交事务
                    $wpdb->query('COMMIT');

                    $results['synced_databases'][] = $target_db;
                    $results['target_databases'][] = $target_db;
                    self::log_tax_sync("成功同步到数据库: {$target_db}");

                    // 切换回原数据库
                    YXJTO_Gateway::get_instance()->switch_database($original_db);

                } catch (Exception $e) {
                    // 回滚事务
                    $wpdb->query('ROLLBACK');
                    
                    $error_message = "同步到数据库 {$target_db} 失败: " . $e->getMessage();
                    $results['errors'][] = $error_message;
                    self::log_tax_sync($error_message, 'error');

                    // 切换回原数据库
                    if (isset($original_db)) {
                        YXJTO_Gateway::get_instance()->switch_database($original_db);
                    }
                }
            }

            // 更新最后同步时间
            WP_Multi_DB_Config_Manager::update_tax_replication_setting('last_sync', current_time('mysql'));

        } catch (Exception $e) {
            $results['success'] = false;
            $results['message'] = "税收同步失败: " . $e->getMessage();
            $results['errors'][] = "税收同步失败: " . $e->getMessage();
            self::log_tax_sync("税收同步失败: " . $e->getMessage(), 'error');
        }

        return $results;
    }

    /**
     * 获取税率数据
     */
    private static function get_tax_rates_data() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'woocommerce_tax_rates';
        return $wpdb->get_results("SELECT * FROM {$table_name}", ARRAY_A);
    }

    /**
     * 获取税率位置数据
     */
    private static function get_tax_rate_locations_data() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'woocommerce_tax_rate_locations';
        return $wpdb->get_results("SELECT * FROM {$table_name}", ARRAY_A);
    }

    /**
     * 获取税收选项数据
     */
    private static function get_tax_options_data() {
        $options = [];
        $tax_options = self::get_tax_options();
        
        foreach ($tax_options as $option_name) {
            $options[$option_name] = get_option($option_name);
        }
        
        return $options;
    }

    /**
     * 获取税率类别数据
     */
    private static function get_tax_classes_data() {
        // 检查 WooCommerce 是否激活
        if (!class_exists('WC_Tax')) {
            return [
                'tax_classes' => [],
                'tax_class_slugs' => []
            ];
        }
        
        // 获取产品税率类别
        $tax_classes = WC_Tax::get_tax_classes();
        
        return [
            'tax_classes' => $tax_classes,
            'tax_class_slugs' => array_map('sanitize_title', $tax_classes)
        ];
    }

    /**
     * 同步税率到指定数据库
     */
    private static function sync_tax_rates_to_database($tax_rates, $target_db) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'woocommerce_tax_rates';
        $synced_count = 0;

        // 清空目标表
        $wpdb->query("TRUNCATE TABLE {$table_name}");

        // 插入税率数据
        foreach ($tax_rates as $tax_rate) {
            $result = $wpdb->insert(
                $table_name,
                $tax_rate,
                [
                    '%d', // tax_rate_id
                    '%s', // tax_rate_country
                    '%s', // tax_rate_state
                    '%s', // tax_rate
                    '%s', // tax_rate_name
                    '%d', // tax_rate_priority
                    '%d', // tax_rate_compound
                    '%d', // tax_rate_shipping
                    '%d', // tax_rate_order
                    '%s'  // tax_rate_class
                ]
            );

            if ($result !== false) {
                $synced_count++;
            }
        }

        return $synced_count;
    }

    /**
     * 同步税率位置到指定数据库
     */
    private static function sync_tax_rate_locations_to_database($tax_rate_locations, $target_db) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'woocommerce_tax_rate_locations';
        $synced_count = 0;

        // 清空目标表
        $wpdb->query("TRUNCATE TABLE {$table_name}");

        // 插入税率位置数据
        foreach ($tax_rate_locations as $location) {
            $result = $wpdb->insert(
                $table_name,
                $location,
                [
                    '%d', // location_id
                    '%d', // location_code
                    '%d', // tax_rate_id
                    '%s'  // location_type
                ]
            );

            if ($result !== false) {
                $synced_count++;
            }
        }

        return $synced_count;
    }

    /**
     * 同步税收选项到指定数据库
     */
    private static function sync_tax_options_to_database($tax_options, $target_db) {
        $synced_count = 0;

        foreach ($tax_options as $option_name => $option_value) {
            $result = update_option($option_name, $option_value);
            if ($result) {
                $synced_count++;
            }
        }

        return $synced_count;
    }

    /**
     * 同步税率类别到指定数据库
     */
    private static function sync_tax_classes_to_database($tax_classes_data, $target_db) {
        $synced_count = 0;

        // 同步税率类别选项
        if (isset($tax_classes_data['tax_classes'])) {
            $result = update_option('woocommerce_tax_classes', implode("\n", $tax_classes_data['tax_classes']));
            if ($result) {
                $synced_count++;
            }
        }

        return $synced_count;
    }

    /**
     * 验证税收数据完整性
     */
    public static function validate_tax_data($database = null) {
        global $wpdb;
        
        $validation_results = [
            'success' => true,
            'database' => $database ?: (class_exists('YXJTO_Gateway') ? YXJTO_Gateway::get_instance()->get_current_database() : 'unknown'),
            'checks' => [],
            'validation_results' => [],
            'issues_found' => 0
        ];

        try {
            // 检查税率表
            $tax_rates_table = $wpdb->prefix . 'woocommerce_tax_rates';
            $tax_rates_count = $wpdb->get_var("SELECT COUNT(*) FROM {$tax_rates_table}");
            $validation_results['checks']['tax_rates'] = [
                'table_exists' => $wpdb->get_var("SHOW TABLES LIKE '{$tax_rates_table}'") === $tax_rates_table,
                'record_count' => (int) $tax_rates_count,
                'status' => 'success'
            ];

            // 检查税率位置表
            $tax_locations_table = $wpdb->prefix . 'woocommerce_tax_rate_locations';
            $tax_locations_count = $wpdb->get_var("SELECT COUNT(*) FROM {$tax_locations_table}");
            $validation_results['checks']['tax_rate_locations'] = [
                'table_exists' => $wpdb->get_var("SHOW TABLES LIKE '{$tax_locations_table}'") === $tax_locations_table,
                'record_count' => (int) $tax_locations_count,
                'status' => 'success'
            ];

            // 检查税收选项
            $tax_options = self::get_tax_options();
            $missing_options = [];
            foreach ($tax_options as $option_name) {
                $option_value = get_option($option_name);
                if ($option_value === false && !in_array($option_name, ['woocommerce_store_address_2'])) {
                    $missing_options[] = $option_name;
                }
            }
            
            $validation_results['checks']['tax_options'] = [
                'total_options' => count($tax_options),
                'missing_options' => $missing_options,
                'status' => empty($missing_options) ? 'success' : 'warning'
            ];

            // 检查数据完整性
            $orphaned_locations = $wpdb->get_var("
                SELECT COUNT(*) 
                FROM {$tax_locations_table} trl 
                LEFT JOIN {$tax_rates_table} tr ON trl.tax_rate_id = tr.tax_rate_id 
                WHERE tr.tax_rate_id IS NULL
            ");
            
            $validation_results['checks']['data_integrity'] = [
                'orphaned_locations' => (int) $orphaned_locations,
                'status' => $orphaned_locations > 0 ? 'warning' : 'success'
            ];

            // 设置管理页面期望的字段
            $validation_results['validation_results'] = $validation_results['checks'];
            $validation_results['issues_found'] = 0;
            
            // 计算发现的问题数量
            foreach ($validation_results['checks'] as $check) {
                if (isset($check['status']) && $check['status'] === 'warning') {
                    $validation_results['issues_found']++;
                }
            }

        } catch (Exception $e) {
            $validation_results['success'] = false;
            $validation_results['message'] = $e->getMessage();
            $validation_results['error'] = $e->getMessage();
        }

        return $validation_results;
    }

    /**
     * 备份税收数据
     */
    public static function backup_tax_data($database = null) {
        global $wpdb;
        
        $backup_data = [
            'timestamp' => current_time('mysql'),
            'database' => $database ?: (class_exists('YXJTO_Gateway') ? YXJTO_Gateway::get_instance()->get_current_database() : 'unknown'),
            'tax_rates' => [],
            'tax_rate_locations' => [],
            'tax_options' => [],
            'tax_classes' => []
        ];

        try {
            // 备份税率
            $backup_data['tax_rates'] = self::get_tax_rates_data();

            // 备份税率位置
            $backup_data['tax_rate_locations'] = self::get_tax_rate_locations_data();

            // 备份税收选项
            $backup_data['tax_options'] = self::get_tax_options_data();

            // 备份税率类别
            $backup_data['tax_classes'] = self::get_tax_classes_data();

            // 保存备份文件
            $backup_filename = 'tax_backup_' . $backup_data['database'] . '_' . date('Y-m-d_H-i-s') . '.json';
            $backup_path = YXJTO_GATEWAY_PLUGIN_DIR . 'backups/tax/' . $backup_filename;
            
            // 确保备份目录存在
            wp_mkdir_p(dirname($backup_path));
            
            file_put_contents($backup_path, json_encode($backup_data, JSON_PRETTY_PRINT));

            self::log_tax_sync("税收数据备份完成: {$backup_filename}");

            return [
                'success' => true,
                'backup_file' => $backup_filename,
                'backup_path' => $backup_path,
                'backup_size' => filesize($backup_path),
                'summary' => [
                    'tax_rates' => count($backup_data['tax_rates']),
                    'tax_rate_locations' => count($backup_data['tax_rate_locations']),
                    'tax_options' => count($backup_data['tax_options'])
                ]
            ];

        } catch (Exception $e) {
            self::log_tax_sync("税收数据备份失败: " . $e->getMessage(), 'error');
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 恢复税收数据
     */
    public static function restore_tax_data($backup_file) {
        $backup_path = YXJTO_GATEWAY_PLUGIN_DIR . 'backups/tax/' . $backup_file;
        
        if (!file_exists($backup_path)) {
            return [
                'success' => false,
                'error' => '备份文件不存在'
            ];
        }

        try {
            $backup_data = json_decode(file_get_contents($backup_path), true);
            
            if (!$backup_data) {
                throw new Exception('备份文件格式无效');
            }

            global $wpdb;

            // 开始事务
            $wpdb->query('START TRANSACTION');

            // 恢复税率
            if (!empty($backup_data['tax_rates'])) {
                $table_name = $wpdb->prefix . 'woocommerce_tax_rates';
                $wpdb->query("TRUNCATE TABLE {$table_name}");
                
                foreach ($backup_data['tax_rates'] as $tax_rate) {
                    $wpdb->insert($table_name, $tax_rate);
                }
            }

            // 恢复税率位置
            if (!empty($backup_data['tax_rate_locations'])) {
                $table_name = $wpdb->prefix . 'woocommerce_tax_rate_locations';
                $wpdb->query("TRUNCATE TABLE {$table_name}");
                
                foreach ($backup_data['tax_rate_locations'] as $location) {
                    $wpdb->insert($table_name, $location);
                }
            }

            // 恢复税收选项
            if (!empty($backup_data['tax_options'])) {
                foreach ($backup_data['tax_options'] as $option_name => $option_value) {
                    update_option($option_name, $option_value);
                }
            }

            // 恢复税率类别
            if (!empty($backup_data['tax_classes'])) {
                if (isset($backup_data['tax_classes']['tax_classes'])) {
                    update_option('woocommerce_tax_classes', implode("\n", $backup_data['tax_classes']['tax_classes']));
                }
            }

            // 提交事务
            $wpdb->query('COMMIT');

            self::log_tax_sync("税收数据恢复完成，使用备份: {$backup_file}");

            return [
                'success' => true,
                'restored_from' => $backup_file,
                'summary' => [
                    'tax_rates' => count($backup_data['tax_rates'] ?? []),
                    'tax_rate_locations' => count($backup_data['tax_rate_locations'] ?? []),
                    'tax_options' => count($backup_data['tax_options'] ?? [])
                ]
            ];

        } catch (Exception $e) {
            // 回滚事务
            global $wpdb;
            $wpdb->query('ROLLBACK');
            
            self::log_tax_sync("税收数据恢复失败: " . $e->getMessage(), 'error');
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 清理孤立的税收数据
     */
    public static function cleanup_orphaned_data() {
        global $wpdb;
        
        $cleanup_result = [
            'success' => true,
            'deleted_count' => 0,
            'cleanup_details' => []
        ];

        try {
            // 清理孤立的税率位置数据
            $tax_locations_table = $wpdb->prefix . 'woocommerce_tax_rate_locations';
            $tax_rates_table = $wpdb->prefix . 'woocommerce_tax_rates';
            
            $orphaned_count = $wpdb->query("
                DELETE trl FROM {$tax_locations_table} trl 
                LEFT JOIN {$tax_rates_table} tr ON trl.tax_rate_id = tr.tax_rate_id 
                WHERE tr.tax_rate_id IS NULL
            ");

            if ($orphaned_count !== false) {
                $cleanup_result['deleted_count'] += $orphaned_count;
                $cleanup_result['cleanup_details']['tax_rate_locations'] = $orphaned_count;
            }

            self::log_tax_sync("清理完成，删除了 {$cleanup_result['deleted_count']} 个孤立记录", 'info');
            
            return $cleanup_result;

        } catch (Exception $e) {
            self::log_tax_sync("清理孤立数据时发生错误: " . $e->getMessage(), 'error');
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'deleted_count' => 0,
                'cleanup_details' => []
            ];
        }
    }

    /**
     * 记录税收同步日志
     */
    private static function log_tax_sync($message, $level = 'info') {
        if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            error_log("YXJTO Tax Sync [{$level}]: {$message}");
            return;
        }
        
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        if (!empty($tax_settings['enable_sync_logging'])) {
            $current_db = class_exists('YXJTO_Gateway') ? YXJTO_Gateway::get_instance()->get_current_database() : 'unknown';
            
            $log_entry = [
                'timestamp' => current_time('mysql'),
                'level' => $level,
                'message' => $message,
                'database' => $current_db
            ];

            // 这里可以添加更复杂的日志记录逻辑
            error_log("YXJTO Tax Sync [{$level}]: {$message}");
        }
    }

    /**
     * 实时同步钩子 - 税率添加
     */
    public static function on_tax_rate_added($tax_rate_id, $tax_rate) {
        self::sync_single_tax_rate($tax_rate_id, 'added');
    }

    /**
     * 实时同步钩子 - 税率更新
     */
    public static function on_tax_rate_updated($tax_rate_id, $tax_rate) {
        self::sync_single_tax_rate($tax_rate_id, 'updated');
    }

    /**
     * 实时同步钩子 - 税率删除
     */
    public static function on_tax_rate_deleted($tax_rate_id) {
        self::sync_single_tax_rate($tax_rate_id, 'deleted');
    }

    /**
     * 同步单个税率
     */
    private static function sync_single_tax_rate($tax_rate_id, $action) {
        if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            return;
        }
        
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        if (empty($tax_settings['enable_tax_replication']) || $tax_settings['sync_mode'] !== 'realtime') {
            return;
        }

        // 这里实现单个税率的实时同步逻辑
        self::log_tax_sync("实时同步税率 {$tax_rate_id}: {$action}");
    }

    /**
     * 税率类别创建钩子
     */
    public static function on_tax_class_created($term_id, $tt_id) {
        self::sync_tax_classes_realtime('created', $term_id);
    }

    /**
     * 税率类别更新钩子
     */
    public static function on_tax_class_updated($term_id, $tt_id) {
        self::sync_tax_classes_realtime('updated', $term_id);
    }

    /**
     * 税率类别删除钩子
     */
    public static function on_tax_class_deleted($term, $tt_id, $taxonomy) {
        self::sync_tax_classes_realtime('deleted', $term);
    }

    /**
     * 实时同步税率类别
     */
    private static function sync_tax_classes_realtime($action, $term_data) {
        if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            return;
        }
        
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        if (empty($tax_settings['enable_tax_replication']) || $tax_settings['sync_mode'] !== 'realtime') {
            return;
        }

        // 这里实现税率类别的实时同步逻辑
        self::log_tax_sync("实时同步税率类别: {$action}");
    }

    /**
     * 税收设置更新钩子
     */
    public static function on_tax_setting_updated($old_value, $value, $option) {
        if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            return;
        }
        
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        if (empty($tax_settings['enable_tax_replication']) || $tax_settings['sync_mode'] !== 'realtime') {
            return;
        }

        // 实时同步税收设置
        self::sync_tax_option_realtime($option, $value);
    }

    /**
     * 实时同步单个税收选项
     */
    private static function sync_tax_option_realtime($option_name, $option_value) {
        // 这里实现单个税收选项的实时同步逻辑
        self::log_tax_sync("实时同步税收选项 {$option_name}");
    }

    /**
     * 计划任务同步
     */
    public static function scheduled_tax_sync() {
        if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_tax_replication_settings')) {
            return;
        }
        
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        if (empty($tax_settings['enable_tax_replication']) || $tax_settings['sync_mode'] !== 'scheduled') {
            return;
        }

        self::log_tax_sync("开始计划任务税收同步");
        $result = self::sync_tax_data();
        
        if ($result['success']) {
            self::log_tax_sync("计划任务税收同步完成，同步了 " . count($result['synced_databases']) . " 个数据库");
        } else {
            self::log_tax_sync("计划任务税收同步失败: " . implode(', ', $result['errors']), 'error');
        }
    }

    /**
     * AJAX处理器 - 同步税收数据
     */
    public static function ajax_sync_tax_data() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        $target_databases = isset($_POST['target_databases']) ? (array) $_POST['target_databases'] : [];
        
        $result = self::sync_tax_data($target_databases);
        wp_send_json($result);
    }

    /**
     * AJAX处理器 - 验证税收数据
     */
    public static function ajax_validate_tax_data() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        $database = isset($_POST['database']) ? sanitize_text_field($_POST['database']) : null;
        
        $result = self::validate_tax_data($database);
        wp_send_json($result);
    }

    /**
     * AJAX处理器 - 备份税收数据
     */
    public static function ajax_backup_tax_data() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        $database = isset($_POST['database']) ? sanitize_text_field($_POST['database']) : null;
        
        $result = self::backup_tax_data($database);
        wp_send_json($result);
    }

    /**
     * AJAX处理器 - 恢复税收数据
     */
    public static function ajax_restore_tax_data() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        $backup_file = isset($_POST['backup_file']) ? sanitize_text_field($_POST['backup_file']) : '';
        
        if (empty($backup_file)) {
            wp_send_json([
                'success' => false,
                'error' => '请选择备份文件'
            ]);
        }

        $result = self::restore_tax_data($backup_file);
        wp_send_json($result);
    }

    /**
     * AJAX处理器 - 清理税收数据
     */
    public static function ajax_cleanup_tax_data() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        global $wpdb;
        $cleaned_items = 0;

        try {
            // 清理孤立的税率位置数据
            $tax_locations_table = $wpdb->prefix . 'woocommerce_tax_rate_locations';
            $tax_rates_table = $wpdb->prefix . 'woocommerce_tax_rates';
            
            $orphaned_count = $wpdb->query("
                DELETE trl FROM {$tax_locations_table} trl 
                LEFT JOIN {$tax_rates_table} tr ON trl.tax_rate_id = tr.tax_rate_id 
                WHERE tr.tax_rate_id IS NULL
            ");

            $cleaned_items += $orphaned_count;

            wp_send_json([
                'success' => true,
                'cleaned_items' => $cleaned_items,
                'message' => "清理完成，删除了 {$cleaned_items} 个孤立记录"
            ]);

        } catch (Exception $e) {
            wp_send_json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}

// 初始化税收复制功能 - 只在所需类可用时
if (class_exists('WP_Multi_DB_Config_Manager')) {
    WP_Multi_DB_Tax_Replication::init();
}
