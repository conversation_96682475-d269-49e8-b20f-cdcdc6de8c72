/**
 * PayPal Multi Gateway Admin Statistics & Logs JavaScript
 */

(function($) {
    'use strict';

    // Global variables
    let dailyChart = null;
    let accountChart = null;

    $(document).ready(function() {
        initializeStatistics();
        initializeLogs();
        initializeCharts();
    });

    /**
     * Initialize Statistics functionality
     */
    function initializeStatistics() {
        // Custom date range toggle
        window.toggleCustomDateRange = function(value) {
            const customRange = $('#custom-date-range');
            if (value === 'custom') {
                customRange.slideDown(300);
            } else {
                customRange.slideUp(300);
            }
        };

        // Filter panel toggle
        window.toggleFilterPanel = function() {
            const content = $('.filter-panel-content');
            const toggleBtn = $('.filter-toggle-btn .dashicons');

            if (content.is(':visible')) {
                content.slideUp(300);
                toggleBtn.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
            } else {
                content.slideDown(300);
                toggleBtn.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
            }
        };

        // Refresh statistics with loading state
        window.refreshStatistics = function() {
            const refreshBtn = $('button[onclick="refreshStatistics()"]');
            const originalText = refreshBtn.html();

            // Show loading state
            refreshBtn.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Refreshing...');

            // Add spinning animation
            $('<style>.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>').appendTo('head');

            // Reload after a short delay to show the loading state
            setTimeout(() => {
                location.reload();
            }, 500);
        };

        // Export statistics
        window.exportStatistics = function() {
            const exportBtn = $('button[onclick="exportStatistics()"]');
            const originalText = exportBtn.html();

            exportBtn.prop('disabled', true).html('<span class="dashicons dashicons-download"></span> Exporting...');

            // Simulate export process
            setTimeout(() => {
                // Here you would implement actual export functionality
                showNotice('Export functionality coming soon!', 'info');
                exportBtn.prop('disabled', false).html(originalText);
            }, 1000);
        };

        // Auto-collapse filter panel on mobile
        function handleResponsiveFilter() {
            if ($(window).width() < 768) {
                $('.filter-panel-content').hide();
                $('.filter-toggle-btn .dashicons').removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
            }
        }

        // Initialize responsive behavior
        handleResponsiveFilter();
        $(window).resize(handleResponsiveFilter);

        // Smooth scroll to statistics content after filter apply
        if (window.location.search.includes('tab=statistics') && window.location.search.includes('time_range')) {
            $('html, body').animate({
                scrollTop: $('.statistics-overview').offset().top - 100
            }, 800);
        }

        // Table sorting
        $('.statistics-table .sortable').on('click', function() {
            const $this = $(this);
            const sortBy = $this.data('sort');
            const currentSort = $this.hasClass('asc') ? 'desc' : 'asc';
            
            // Remove sorting from other columns
            $('.sortable').removeClass('asc desc');
            
            // Add sorting to current column
            $this.addClass(currentSort);
            
            // Sort table rows
            sortTable($this.closest('table'), sortBy, currentSort);
        });

        // View account details
        $('.view-details').on('click', function() {
            const accountId = $(this).data('account-id');
            viewAccountDetails(accountId);
        });
    }

    /**
     * Initialize Logs functionality
     */
    function initializeLogs() {
        // Select all logs checkbox
        $('#select-all-logs').on('change', function() {
            $('input[name="log_ids[]"]').prop('checked', this.checked);
        });

        // Individual log checkbox
        $(document).on('change', 'input[name="log_ids[]"]', function() {
            const totalCheckboxes = $('input[name="log_ids[]"]').length;
            const checkedCheckboxes = $('input[name="log_ids[]"]:checked').length;
            
            $('#select-all-logs').prop('checked', totalCheckboxes === checkedCheckboxes);
        });

        // View log details
        $('.view-log-details').on('click', function() {
            const logId = $(this).data('log-id');
            viewLogDetails(logId);
        });

        // Delete single log
        $('.delete-log').on('click', function() {
            const logId = $(this).data('log-id');
            if (confirm(paypalMultiGatewayAdmin.confirmDelete)) {
                deleteLog(logId);
            }
        });

        // Export logs
        window.exportLogs = function() {
            const selectedIds = getSelectedLogIds();
            if (selectedIds.length === 0) {
                if (confirm(paypalMultiGatewayAdmin.confirmExportAll)) {
                    exportLogs([], true);
                }
            } else {
                exportLogs(selectedIds, false);
            }
        };

        // Refresh logs
        window.refreshLogs = function() {
            location.reload();
        };

        // Apply bulk action
        window.applyBulkAction = function() {
            const action = $('#bulk-action').val();
            const selectedIds = getSelectedLogIds();

            if (!action) {
                alert(paypalMultiGatewayAdmin.selectAction);
                return;
            }

            if (selectedIds.length === 0) {
                alert(paypalMultiGatewayAdmin.selectLogs);
                return;
            }

            switch (action) {
                case 'delete':
                    if (confirm(paypalMultiGatewayAdmin.confirmBulkDelete)) {
                        bulkDeleteLogs(selectedIds);
                    }
                    break;
                case 'export':
                    exportLogs(selectedIds, false);
                    break;
            }
        };

        // Close modal
        $('.log-modal-close').on('click', function() {
            $('#log-details-modal').hide();
        });

        // Close modal on background click
        $('#log-details-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        // Close other modals on background click
        $('.log-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        // Auto cleanup settings toggle
        $('#enable-auto-cleanup').on('change', function() {
            const settings = $('#cleanup-settings');
            if (this.checked) {
                settings.show();
            } else {
                settings.hide();
            }
        });
    }

    /**
     * Initialize Charts
     */
    function initializeCharts() {
        if (typeof Chart === 'undefined' || typeof window.paypalStatisticsData === 'undefined') {
            return;
        }

        const data = window.paypalStatisticsData;
        
        // Daily Transaction Chart
        initializeDailyChart(data.dailyStats);
        
        // Account Performance Chart
        initializeAccountChart(data.accountStats);
    }

    /**
     * Initialize Daily Transaction Chart
     */
    function initializeDailyChart(dailyStats) {
        const ctx = document.getElementById('dailyTransactionChart');
        if (!ctx || !dailyStats) return;

        const labels = dailyStats.map(stat => stat.date);
        const transactionData = dailyStats.map(stat => stat.total_transactions);
        const amountData = dailyStats.map(stat => parseFloat(stat.total_amount));

        dailyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: paypalMultiGatewayAdmin.transactions,
                    data: transactionData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    yAxisID: 'y'
                }, {
                    label: paypalMultiGatewayAdmin.amount,
                    data: amountData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: paypalMultiGatewayAdmin.date
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: paypalMultiGatewayAdmin.transactions
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: paypalMultiGatewayAdmin.amount
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    /**
     * Initialize Account Performance Chart
     */
    function initializeAccountChart(accountStats) {
        const ctx = document.getElementById('accountPerformanceChart');
        if (!ctx || !accountStats) return;

        const labels = accountStats.map(stat => stat.account_id);
        const successRates = accountStats.map(stat => parseFloat(stat.success_rate));
        const colors = accountStats.map(() => `hsl(${Math.random() * 360}, 70%, 60%)`);

        accountChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: paypalMultiGatewayAdmin.successRate,
                    data: successRates,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('60%', '40%')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: paypalMultiGatewayAdmin.successRate + ' (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: paypalMultiGatewayAdmin.accounts
                        }
                    }
                }
            }
        });
    }

    /**
     * Sort table
     */
    function sortTable(table, sortBy, direction) {
        const tbody = table.find('tbody');
        const rows = tbody.find('tr').toArray();

        rows.sort(function(a, b) {
            const aVal = $(a).find(`[data-sort="${sortBy}"]`).text().trim();
            const bVal = $(b).find(`[data-sort="${sortBy}"]`).text().trim();

            let comparison = 0;
            if ($.isNumeric(aVal) && $.isNumeric(bVal)) {
                comparison = parseFloat(aVal) - parseFloat(bVal);
            } else {
                comparison = aVal.localeCompare(bVal);
            }

            return direction === 'asc' ? comparison : -comparison;
        });

        tbody.empty().append(rows);
    }

    /**
     * View account details
     */
    function viewAccountDetails(accountId) {
        // Implementation for viewing account details
        console.log('View account details:', accountId);
    }

    /**
     * View log details
     */
    function viewLogDetails(logId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_get_log_details',
                log_id: logId,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            beforeSend: function() {
                $('#log-details-content').html('<div class="loading">Loading...</div>');
                $('#log-details-modal').show();
            },
            success: function(response) {
                if (response.success) {
                    displayLogDetails(response.data);
                } else {
                    $('#log-details-content').html('<div class="error">Error: ' + response.data + '</div>');
                }
            },
            error: function() {
                $('#log-details-content').html('<div class="error">Network error occurred.</div>');
            }
        });
    }

    /**
     * Display log details in modal
     */
    function displayLogDetails(data) {
        const log = data.log;
        const gatewayResponse = data.gateway_response;
        const orderInfo = data.order_info;

        let html = '<div class="log-details">';
        
        // Basic log information
        html += '<div class="log-section">';
        html += '<h4>Transaction Information</h4>';
        html += '<table class="log-details-table">';
        html += `<tr><td><strong>Transaction ID:</strong></td><td>${log.transaction_id}</td></tr>`;
        html += `<tr><td><strong>Order ID:</strong></td><td>${log.order_id}</td></tr>`;
        html += `<tr><td><strong>Account ID:</strong></td><td>${log.account_id}</td></tr>`;
        html += `<tr><td><strong>Amount:</strong></td><td>${log.amount} ${log.currency}</td></tr>`;
        html += `<tr><td><strong>Status:</strong></td><td><span class="status-badge status-${log.status}">${log.status}</span></td></tr>`;
        html += `<tr><td><strong>Created:</strong></td><td>${log.created_at}</td></tr>`;
        html += '</table>';
        html += '</div>';

        // Order information
        if (orderInfo) {
            html += '<div class="log-section">';
            html += '<h4>Order Information</h4>';
            html += '<table class="log-details-table">';
            html += `<tr><td><strong>Order Number:</strong></td><td>${orderInfo.order_number}</td></tr>`;
            html += `<tr><td><strong>Customer:</strong></td><td>${orderInfo.customer_name} (${orderInfo.customer_email})</td></tr>`;
            html += `<tr><td><strong>Order Status:</strong></td><td>${orderInfo.order_status}</td></tr>`;
            html += `<tr><td><strong>Order Total:</strong></td><td>${orderInfo.order_total}</td></tr>`;
            html += `<tr><td><strong>Order Date:</strong></td><td>${orderInfo.order_date}</td></tr>`;
            html += '</table>';
            html += '</div>';
        }

        // Gateway response
        if (gatewayResponse) {
            html += '<div class="log-section">';
            html += '<h4>Gateway Response</h4>';
            html += '<pre class="gateway-response">' + JSON.stringify(gatewayResponse, null, 2) + '</pre>';
            html += '</div>';
        }

        html += '</div>';

        $('#log-details-content').html(html);
    }

    /**
     * Get selected log IDs
     */
    function getSelectedLogIds() {
        return $('input[name="log_ids[]"]:checked').map(function() {
            return $(this).val();
        }).get();
    }

    /**
     * Delete single log
     */
    function deleteLog(logId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_delete_log_entry',
                log_id: logId,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $(`tr[data-log-id="${logId}"]`).fadeOut(function() {
                        $(this).remove();
                    });
                    showNotice(response.data, 'success');
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    }

    /**
     * Bulk delete logs
     */
    function bulkDeleteLogs(logIds) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_bulk_delete_logs',
                log_ids: logIds,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    logIds.forEach(function(id) {
                        $(`tr[data-log-id="${id}"]`).fadeOut(function() {
                            $(this).remove();
                        });
                    });
                    showNotice(response.data, 'success');
                    $('#select-all-logs').prop('checked', false);
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    }

    /**
     * Export logs
     */
    function exportLogs(logIds, exportAll) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_export_logs',
                log_ids: logIds,
                export_all: exportAll,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    downloadCSV(response.data.csv_content, response.data.filename);
                    showNotice('Export completed successfully.', 'success');
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    }

    /**
     * Download CSV file
     */
    function downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * Show notice
     */
    function showNotice(message, type) {
        const notice = $(`<div class="notice notice-${type} is-dismissible"><p>${message}</p></div>`);
        $('.wrap h1').after(notice);

        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Clear Logs Functions
    window.toggleClearLogsMenu = function() {
        const menu = $('#clear-logs-menu');
        menu.toggle();

        // Close menu when clicking outside
        if (menu.is(':visible')) {
            $(document).on('click.clearLogs', function(e) {
                if (!$(e.target).closest('.clear-logs-dropdown').length) {
                    menu.hide();
                    $(document).off('click.clearLogs');
                }
            });
        }
    };

    window.clearOldLogs = function(days) {
        if (!confirm(paypalMultiGatewayAdmin.confirmClearOldLogs.replace('%d', days))) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_clear_old_logs',
                days: days,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    };

    window.clearFailedLogs = function() {
        if (!confirm(paypalMultiGatewayAdmin.confirmClearFailedLogs)) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_clear_failed_logs',
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    };

    window.showClearByStatusDialog = function() {
        $('#clear-logs-menu').hide();
        $('#clear-by-status-modal').show();
    };

    window.closeClearByStatusModal = function() {
        $('#clear-by-status-modal').hide();
    };

    window.toggleDateRangeForClear = function() {
        const checkbox = $('#include-date-range');
        const dateRange = $('#clear-date-range');

        if (checkbox.is(':checked')) {
            dateRange.show();
        } else {
            dateRange.hide();
        }
    };

    window.executeClearByStatus = function() {
        const selectedStatuses = $('input[name="clear_statuses[]"]:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedStatuses.length === 0) {
            alert(paypalMultiGatewayAdmin.selectStatuses);
            return;
        }

        const includeDateRange = $('#include-date-range').is(':checked');
        const dateFrom = includeDateRange ? $('#clear-date-from').val() : '';
        const dateTo = includeDateRange ? $('#clear-date-to').val() : '';

        if (!confirm(paypalMultiGatewayAdmin.confirmClearByStatus)) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_clear_logs_by_status',
                statuses: selectedStatuses,
                date_from: dateFrom,
                date_to: dateTo,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    closeClearByStatusModal();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    };

    window.clearAllLogs = function() {
        if (!confirm(paypalMultiGatewayAdmin.confirmClearAllLogs)) {
            return;
        }

        // Double confirmation for this dangerous action
        if (!confirm(paypalMultiGatewayAdmin.confirmClearAllLogsDouble)) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_clear_all_logs',
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    };

    // Maintenance Functions
    window.showLogMaintenanceDialog = function() {
        $('#log-maintenance-modal').show();
        loadMaintenanceStats();
    };

    window.closeMaintenanceModal = function() {
        $('#log-maintenance-modal').hide();
    };

    function loadMaintenanceStats() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_get_maintenance_stats',
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#maintenance-total-logs').text(data.total_logs);
                    $('#maintenance-db-size').text(data.db_size);
                    $('#maintenance-oldest-log').text(data.oldest_log);
                    $('#enable-auto-cleanup').prop('checked', data.auto_cleanup_enabled);
                    $('#auto-cleanup-days').val(data.auto_cleanup_days);
                    $('#keep-successful-logs').prop('checked', data.keep_successful);

                    // Show/hide cleanup settings
                    if (data.auto_cleanup_enabled) {
                        $('#cleanup-settings').show();
                    } else {
                        $('#cleanup-settings').hide();
                    }
                }
            }
        });
    }

    window.saveMaintenanceSettings = function() {
        const autoCleanupEnabled = $('#enable-auto-cleanup').is(':checked');
        const autoCleanupDays = $('#auto-cleanup-days').val();
        const keepSuccessful = $('#keep-successful-logs').is(':checked');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_save_maintenance_settings',
                auto_cleanup_enabled: autoCleanupEnabled,
                auto_cleanup_days: autoCleanupDays,
                keep_successful: keepSuccessful,
                nonce: paypalMultiGatewayAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            }
        });
    };

    window.optimizeDatabase = function() {
        if (!confirm(paypalMultiGatewayAdmin.confirmOptimizeDb)) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_optimize_database',
                nonce: paypalMultiGatewayAdmin.nonce
            },
            beforeSend: function() {
                $('button[onclick="optimizeDatabase()"]').prop('disabled', true).text('Optimizing...');
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                } else {
                    showNotice(response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error occurred.', 'error');
            },
            complete: function() {
                $('button[onclick="optimizeDatabase()"]').prop('disabled', false).text(paypalMultiGatewayAdmin.optimizeDatabase);
            }
        });
    };

})(jQuery);
