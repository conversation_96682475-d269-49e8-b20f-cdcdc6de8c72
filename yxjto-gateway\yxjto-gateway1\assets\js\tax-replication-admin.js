jQuery(document).ready(function($) {
    // 税收复制设置页面的JavaScript功能
    
    // 同步模式切换
    $('select[name="sync_mode"]').change(function() {
        if ($(this).val() === 'batch') {
            $('#tax-batch-interval-row').show();
        } else {
            $('#tax-batch-interval-row').hide();
        }
    });
    
    // 立即同步税收数据
    $('#tax-sync-now').click(function() {
        var button = $(this);
        var originalText = button.text();
        
        button.prop('disabled', true).text('同步中...');
        showMessage('开始同步税收数据...', 'info');
        
        $.ajax({
            url: taxReplicationAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_tax_sync',
                security: taxReplicationAjax.nonce,
                target_databases: getSelectedDatabases()
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    if (response.data.synced_items) {
                        displaySyncDetails(response.data.synced_items);
                    }
                } else {
                    showMessage('同步失败: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('AJAX请求失败: ' + error, 'error');
            },
            complete: function() {
                button.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // 验证税收数据
    $('#tax-validate-data').click(function() {
        var button = $(this);
        var originalText = button.text();
        
        button.prop('disabled', true).text('验证中...');
        showMessage('开始验证税收数据...', 'info');
        
        $.ajax({
            url: taxReplicationAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_tax_validate',
                security: taxReplicationAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    if (response.data.validation_results) {
                        displayValidationResults(response.data.validation_results);
                    }
                } else {
                    showMessage('验证失败: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('AJAX请求失败: ' + error, 'error');
            },
            complete: function() {
                button.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // 备份税收数据
    $('#tax-backup-data').click(function() {
        var button = $(this);
        var originalText = button.text();
        
        button.prop('disabled', true).text('备份中...');
        showMessage('开始备份税收数据...', 'info');
        
        $.ajax({
            url: taxReplicationAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_tax_backup',
                security: taxReplicationAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    if (response.data.backup_file) {
                        showMessage('备份文件: ' + response.data.backup_file, 'info');
                    }
                } else {
                    showMessage('备份失败: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('AJAX请求失败: ' + error, 'error');
            },
            complete: function() {
                button.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // 清理孤立数据
    $('#tax-cleanup-data').click(function() {
        if (!confirm('确定要清理孤立的税收数据吗？此操作不可逆。')) {
            return;
        }
        
        var button = $(this);
        var originalText = button.text();
        
        button.prop('disabled', true).text('清理中...');
        showMessage('开始清理孤立数据...', 'info');
        
        $.ajax({
            url: taxReplicationAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'yxjto_tax_cleanup',
                security: taxReplicationAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    if (response.data.deleted_count > 0) {
                        showMessage('清理了 ' + response.data.deleted_count + ' 个孤立记录', 'info');
                    }
                } else {
                    showMessage('清理失败: ' + response.data, 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('AJAX请求失败: ' + error, 'error');
            },
            complete: function() {
                button.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // 辅助函数：显示消息
    function showMessage(message, type) {
        var messageDiv = $('#tax-sync-result-message');
        var messageClass = 'notice notice-' + type;
        
        if (type === 'success') {
            messageClass = 'notice notice-success';
        } else if (type === 'error') {
            messageClass = 'notice notice-error';
        } else if (type === 'info') {
            messageClass = 'notice notice-info';
        }
        
        messageDiv.removeClass().addClass(messageClass).html('<p>' + message + '</p>').show();
        
        // 自动隐藏信息消息
        if (type === 'info') {
            setTimeout(function() {
                messageDiv.fadeOut();
            }, 3000);
        }
    }
    
    // 辅助函数：获取选中的数据库
    function getSelectedDatabases() {
        var selected = [];
        $('input[name="target_databases[]"]:checked').each(function() {
            selected.push($(this).val());
        });
        return selected;
    }
    
    // 辅助函数：显示同步详情
    function displaySyncDetails(syncItems) {
        var detailsHtml = '<div class="sync-details"><h4>同步详情:</h4><ul>';
        
        if (syncItems.tax_rates) {
            detailsHtml += '<li>税率: ' + syncItems.tax_rates + '</li>';
        }
        if (syncItems.tax_classes) {
            detailsHtml += '<li>税收类别: ' + syncItems.tax_classes + '</li>';
        }
        if (syncItems.tax_options) {
            detailsHtml += '<li>税收选项: ' + syncItems.tax_options + '</li>';
        }
        if (syncItems.target_databases) {
            detailsHtml += '<li>同步到数据库: ' + syncItems.target_databases.join(', ') + '</li>';
        }
        
        detailsHtml += '</ul></div>';
        
        $('#tax-sync-result-message').append(detailsHtml);
    }
    
    // 辅助函数：显示验证结果
    function displayValidationResults(results) {
        var resultsHtml = '<div class="validation-results"><h4>验证结果:</h4>';
        
        if (results.issues_found && results.issues_found.length > 0) {
            resultsHtml += '<div class="validation-issues"><strong>发现的问题:</strong><ul>';
            results.issues_found.forEach(function(issue) {
                resultsHtml += '<li>' + issue + '</li>';
            });
            resultsHtml += '</ul></div>';
        } else {
            resultsHtml += '<p style="color: green;">✓ 未发现数据问题</p>';
        }
        
        resultsHtml += '</div>';
        
        $('#tax-sync-result-message').append(resultsHtml);
    }
    
    // 表单提交确认
    $('#tax-replication-form').submit(function(e) {
        if (!confirm('确定要保存税收复制设置吗？')) {
            e.preventDefault();
            return false;
        }
    });
});
