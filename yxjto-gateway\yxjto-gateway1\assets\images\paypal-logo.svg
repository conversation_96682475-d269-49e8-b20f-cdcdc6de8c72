<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="26" viewBox="0 0 100 26" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="paypal-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0070ba;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#003087;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- PayPal Logo Background -->
  <rect width="100" height="26" rx="4" fill="url(#paypal-gradient)"/>

  <!-- PayPal Text -->
  <text x="8" y="18" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
    PayPal
  </text>

  <!-- PayPal "P" Icon -->
  <g transform="translate(78, 6)">
    <circle cx="7" cy="7" r="7" fill="white" opacity="0.2"/>
    <path d="M4 3h3c1.5 0 2.5 1 2.5 2.5S8.5 8 7 8H5.5v3H4V3z M5.5 4.5v2H7c0.5 0 1-0.5 1-1s-0.5-1-1-1H5.5z" fill="white"/>
  </g>
</svg>
