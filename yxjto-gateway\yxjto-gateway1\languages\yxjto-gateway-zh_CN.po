# YXJTO Gateway Plugin - Chinese (Simplified)
# Copyright (C) 2024
msgid ""
msgstr ""

# User Management translations
msgid "User Management"
msgstr "用户管理"

msgid "Manage user data across all databases."
msgstr "管理所有数据库中的用户数据。"

msgid "Current User Session"
msgstr "当前用户会话"

msgid "Copy Current User Session to All Databases"
msgstr "复制当前用户会话到所有数据库"

msgid "Copy the current logged-in user session data to all other databases."
msgstr "将当前登录用户的会话数据复制到所有其他数据库。"

msgid "Current User Account"
msgstr "当前用户账号"

msgid "Copy Current User Account to All Databases"
msgstr "复制当前用户账号到所有数据库"

msgid "Copy the current user account data to all other databases."
msgstr "将当前用户账号数据复制到所有其他数据库。"

msgid "Current user session copied to %d out of %d databases."
msgstr "当前用户会话已复制到 %d 个数据库（共 %d 个）。"

msgid "Failed to copy user session to any database. Check error logs for details."
msgstr "复制用户会话到任何数据库都失败了。请查看错误日志了解详情。"

msgid "Current user account copied to %d out of %d databases."
msgstr "当前用户账号已复制到 %d 个数据库（共 %d 个）。"

msgid "Failed to copy user account to any database. Check error logs for details."
msgstr "复制用户账号到任何数据库都失败了。请查看错误日志了解详情。"

msgid "Failed to copy user account to any database. Check error logs for details."
msgstr "复制用户账号到任何数据库都失败了。请查看错误日志了解详情。"

msgid "Plugin enabled in %d out of %d databases."
msgstr "插件已在 %d 个数据库中启用（共 %d 个）。"

msgid "Failed to enable plugin in any database. Check error logs for details."
msgstr "在任何数据库中启用插件都失败了。请查看错误日志了解详情。"

msgid "Plugin disabled in %d out of %d databases."
msgstr "插件已在 %d 个数据库中禁用（共 %d 个）。"

msgid "Failed to disable plugin in any database. Check error logs for details."
msgstr "在任何数据库中禁用插件都失败了。请查看错误日志了解详情。"

msgid "Enable Plugin in All Databases"
msgstr "在所有数据库中启用插件"

msgid "Disable Plugin in All Databases"
msgstr "在所有数据库中禁用插件"

msgid "Current user session copied to %d databases."
msgstr "当前用户会话已复制到 %d 个数据库。"

msgid "No user is currently logged in."
msgstr "当前没有用户登录。"

msgid "Current user account copied to %d databases."
msgstr "当前用户账号已复制到 %d 个数据库。"
"Project-Id-Version: YXJTO Gateway Plugin 1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 00:00+0000\n"
"PO-Revision-Date: 2024-01-01 00:00+0000\n"
"Last-Translator: \n"
"Language-Team: Chinese (Simplified)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "YXJTO Gateway Settings"
msgstr "YXJTO Gateway 设置"

msgid "YXJTO Gateway"
msgstr "YXJTO Gateway"

msgid "Insufficient permissions"
msgstr "权限不足"

msgid "Testing connection..."
msgstr "正在测试连接..."

msgid "Connection successful!"
msgstr "连接成功！"

msgid "Connection failed!"
msgstr "连接失败！"

msgid "Switching database..."
msgstr "正在切换数据库..."

msgid "Database switched successfully!"
msgstr "数据库切换成功！"

msgid "Database switch failed!"
msgstr "数据库切换失败！"

msgid "Databases"
msgstr "数据库"

msgid "IP Rules"
msgstr "IP规则"

msgid "URL Rules"
msgstr "URL规则"

msgid "Status"
msgstr "状态"

msgid "Logs"
msgstr "日志"

msgid "Database Configurations"
msgstr "数据库配置"

msgid "Configure your database connections here."
msgstr "在这里配置您的数据库连接。"

msgid "Current Database"
msgstr "当前数据库"

msgid "Test Connection"
msgstr "测试连接"

msgid "Switch to This"
msgstr "切换到此数据库"

msgid "Remove"
msgstr "删除"

msgid "Name"
msgstr "名称"

msgid "Host"
msgstr "主机"

msgid "Database"
msgstr "数据库"

msgid "Username"
msgstr "用户名"

msgid "Password"
msgstr "密码"

msgid "Charset"
msgstr "字符集"

msgid "Enabled"
msgstr "启用"

msgid "Add New Database"
msgstr "添加新数据库"

msgid "IP-based Database Switching Rules"
msgstr "基于IP的数据库切换规则"

msgid "Configure rules to automatically switch databases based on visitor IP addresses."
msgstr "配置规则以根据访问者IP地址自动切换数据库。"

msgid "Rule %d"
msgstr "规则 %d"

msgid "IP Range"
msgstr "IP范围"

msgid "Examples: ***********, ***********/24, ***********-*************"
msgstr "示例：***********, ***********/24, ***********-*************"

msgid "Add IP Rule"
msgstr "添加IP规则"

msgid "URL Parameter Database Switching Rules"
msgstr "基于URL参数的数据库切换规则"

msgid "Configure rules to switch databases based on URL parameters."
msgstr "配置规则以根据URL参数切换数据库。"

msgid "Parameter Name"
msgstr "参数名称"

msgid "Parameter Value"
msgstr "参数值"

msgid "Add URL Rule"
msgstr "添加URL规则"

msgid "System Status"
msgstr "系统状态"

msgid "Monitor the status of your database connections and system performance."
msgstr "监控您的数据库连接状态和系统性能。"

msgid "Current Status"
msgstr "当前状态"

msgid "Active Database"
msgstr "活动数据库"

msgid "Plugin Version"
msgstr "插件版本"

msgid "WordPress Version"
msgstr "WordPress版本"

msgid "PHP Version"
msgstr "PHP版本"

msgid "Database Connections"
msgstr "数据库连接"

msgid "Disabled"
msgstr "已禁用"

msgid "Switching Rules"
msgstr "切换规则"

msgid "Active IP Rules"
msgstr "活动IP规则"

msgid "Active URL Rules"
msgstr "活动URL规则"

msgid "Performance"
msgstr "性能"

msgid "Memory Usage"
msgstr "内存使用"

msgid "Peak Memory"
msgstr "峰值内存"

msgid "Debug Mode"
msgstr "调试模式"

msgid "Activity Logs"
msgstr "活动日志"

msgid "View database switching activity and system events."
msgstr "查看数据库切换活动和系统事件。"

msgid "Logs table not found. Please deactivate and reactivate the plugin."
msgstr "未找到日志表。请停用并重新激活插件。"

msgid "No logs found."
msgstr "未找到日志。"

msgid "Timestamp"
msgstr "时间戳"

msgid "Action"
msgstr "操作"

msgid "From Database"
msgstr "源数据库"

msgid "To Database"
msgstr "目标数据库"

msgid "IP Address"
msgstr "IP地址"

msgid "Message"
msgstr "消息"

msgid "Success"
msgstr "成功"

msgid "Failed"
msgstr "失败"

msgid "Refresh Logs"
msgstr "刷新日志"

msgid "Clear Logs"
msgstr "清除日志"

msgid "Are you sure you want to clear all logs?"
msgstr "您确定要清除所有日志吗？"

msgid "Failed to clear logs."
msgstr "清除日志失败。"

msgid "Settings saved successfully!"
msgstr "设置保存成功！"

msgid "Logs cleared successfully!"
msgstr "日志清除成功！"

msgid "Failed to clear logs!"
msgstr "清除日志失败！"

msgid "Select Database"
msgstr "选择数据库"

msgid "Are you sure you want to remove this database configuration?"
msgstr "您确定要删除此数据库配置吗？"

msgid "Are you sure you want to remove this IP rule?"
msgstr "您确定要删除此IP规则吗？"

msgid "Are you sure you want to remove this URL rule?"
msgstr "您确定要删除此URL规则吗？"

msgid "Are you sure you want to switch to this database?"
msgstr "您确定要切换到此数据库吗？"

msgid "New Database"
msgstr "新数据库"

msgid "WordPress Multi Database"
msgstr "WordPress多数据库"

msgid "WordPress同域多数据库插件，支持基于IP地址和URL参数的智能数据库切换"
msgstr "WordPress同域多数据库插件，支持基于IP地址和URL参数的智能数据库切换"

msgid "Cannot delete the default database configuration."
msgstr "无法删除默认数据库配置。"

msgid "Database configuration deleted successfully."
msgstr "数据库配置删除成功。"

msgid "Failed to delete database configuration."
msgstr "删除数据库配置失败。"

msgid "URL rule deleted successfully."
msgstr "URL规则删除成功。"

msgid "Failed to delete URL rule."
msgstr "删除URL规则失败。"

msgid "IP rule deleted successfully."
msgstr "IP规则删除成功。"

msgid "Failed to delete IP rule."
msgstr "删除IP规则失败。"

msgid "Crawler Detection"
msgstr "爬虫检测"

msgid "Highest"
msgstr "最高"

msgid "Crawler Detection Settings"
msgstr "爬虫检测设置"

msgid "Configure crawler detection to automatically use the default database when search engine crawlers visit your site. This ensures consistent SEO indexing."
msgstr "配置爬虫检测以在搜索引擎爬虫访问您的网站时自动使用默认数据库。这确保了一致的SEO索引。"

msgid "General Settings"
msgstr "常规设置"

msgid "Enable Crawler Detection"
msgstr "启用爬虫检测"

msgid "Automatically detect crawlers and use default database"
msgstr "自动检测爬虫并使用默认数据库"

msgid "When enabled, detected crawlers will always access the default database regardless of other rules."
msgstr "启用后，检测到的爬虫将始终访问默认数据库，无论其他规则如何。"

msgid "Log Crawler Visits"
msgstr "记录爬虫访问"

msgid "Log crawler visits for monitoring"
msgstr "记录爬虫访问以便监控"

msgid "Keep a log of crawler visits to help with SEO monitoring and debugging."
msgstr "保留爬虫访问日志以帮助SEO监控和调试。"

msgid "Supported Crawlers"
msgstr "支持的爬虫"

msgid "The following crawlers are automatically detected. You can enable/disable detection for specific crawlers."
msgstr "以下爬虫会被自动检测。您可以启用/禁用特定爬虫的检测。"

msgid "Crawler Name"
msgstr "爬虫名称"

msgid "User Agent Patterns"
msgstr "用户代理模式"

msgid "Description"
msgstr "描述"

msgid "Custom Crawler Patterns"
msgstr "自定义爬虫模式"

msgid "Add custom User-Agent patterns to detect additional crawlers. One pattern per line."
msgstr "添加自定义用户代理模式以检测其他爬虫。每行一个模式。"

msgid "Custom Patterns"
msgstr "自定义模式"

msgid "Examples: MyBot, CustomCrawler, SpecialSpider"
msgstr "示例：MyBot, CustomCrawler, SpecialSpider"

msgid "Crawler detection settings saved successfully!"
msgstr "爬虫检测设置保存成功！"

msgid "Failed to save crawler detection settings."
msgstr "保存爬虫检测设置失败。"

msgid "Enable IP Detection"
msgstr "启用IP检测"

msgid "Detect crawlers by IP address ranges"
msgstr "通过IP地址段检测爬虫"

msgid "Use known IP ranges to identify crawlers. This is more reliable than User-Agent detection."
msgstr "使用已知IP段识别爬虫。这比User-Agent检测更可靠。"

msgid "Enable IP Verification"
msgstr "启用IP验证"

msgid "Verify User-Agent claims with IP address"
msgstr "用IP地址验证User-Agent声明"

msgid "When a User-Agent claims to be a crawler, verify that the IP address matches known crawler IP ranges."
msgstr "当User-Agent声称是爬虫时，验证IP地址是否匹配已知的爬虫IP段。"

msgid "Enable Reverse DNS Verification"
msgstr "启用反向DNS验证"

msgid "Verify crawlers using reverse DNS lookup"
msgstr "使用反向DNS查询验证爬虫"

msgid "Perform reverse DNS lookup to verify that the IP truly belongs to the claimed crawler. Most secure but slower."
msgstr "执行反向DNS查询以验证IP确实属于声称的爬虫。最安全但较慢。"

msgid "IP Address Management"
msgstr "IP地址管理"

msgid "Manage IP address ranges for crawler detection. The plugin includes built-in IP ranges for major crawlers."
msgstr "管理用于爬虫检测的IP地址段。插件包含主要爬虫的内置IP段。"

msgid "Built-in IP Ranges Statistics"
msgstr "内置IP段统计"

msgid "IP Ranges Count"
msgstr "IP段数量"

msgid "Sample IP Ranges"
msgstr "示例IP段"

msgid "Custom IP Ranges"
msgstr "自定义IP段"

msgid "Add custom IP ranges for additional crawler detection. Use CIDR notation (e.g., ***********/24) or single IPs."
msgstr "添加自定义IP段以进行额外的爬虫检测。使用CIDR表示法（如***********/24）或单个IP。"

msgid "Examples: ***********/24, ********, ***********/24"
msgstr "示例：***********/24, ********, ***********/24"

msgid "Database configuration not found"
msgstr "未找到数据库配置"

msgid "Database is disabled"
msgstr "数据库已禁用"

msgid "Failed to connect to database"
msgstr "连接数据库失败"

msgid "Database switched from %s to %s"
msgstr "数据库从 %s 切换到 %s"

msgid "Database switch failed"
msgstr "数据库切换失败"

msgid "Successfully switched database"
msgstr "成功切换数据库"

msgid "Connection test"
msgstr "连接测试"

msgid "Manual switch"
msgstr "手动切换"

msgid "IP-based switch"
msgstr "基于IP切换"

msgid "URL-based switch"
msgstr "基于URL切换"

msgid "Auto switch"
msgstr "自动切换"

# Order Manager translations
msgid "Order Manager"
msgstr "订单号管理"

msgid "Manage orders across all databases"
msgstr "管理所有数据库的订单"

msgid "Order Statistics"
msgstr "订单统计"

msgid "Order Search"
msgstr "订单搜索"

msgid "Recent Orders"
msgstr "最近订单"

msgid "Order Actions"
msgstr "订单操作"

msgid "Database Status"
msgstr "数据库状态"

msgid "Total Orders"
msgstr "总订单数"

msgid "Total Revenue"
msgstr "总收入"

msgid "Active Databases"
msgstr "活跃数据库"

msgid "Today's Orders"
msgstr "今日订单"

msgid "Enter order ID, customer email, or phone"
msgstr "输入订单号、客户邮箱或电话"

msgid "Order ID"
msgstr "订单号"

msgid "Customer Email"
msgstr "客户邮箱"

msgid "Customer Phone"
msgstr "客户电话"

msgid "Product Name"
msgstr "产品名称"

msgid "Search Orders"
msgstr "搜索订单"

msgid "Customer"
msgstr "客户"

msgid "Status"
msgstr "状态"

msgid "Total"
msgstr "总计"

msgid "Date"
msgstr "日期"

msgid "Actions"
msgstr "操作"

msgid "View"
msgstr "查看"

msgid "Sync Orders"
msgstr "同步订单"

msgid "Cleanup Old Orders"
msgstr "清理旧订单"

msgid "Order Comparison"
msgstr "订单比较"

msgid "Order Testing"
msgstr "订单测试"

msgid "Online"
msgstr "在线"

msgid "Offline"
msgstr "离线"

msgid "No recent orders found."
msgstr "未找到最近订单。"

msgid "Please enter a search term"
msgstr "请输入搜索关键词"

msgid "Are you sure you want to sync orders across all databases?"
msgstr "确定要同步所有数据库的订单吗？"

msgid "Are you sure you want to cleanup old orders? This action cannot be undone."
msgstr "确定要清理旧订单吗？此操作无法撤销。"

msgid "Orders synced successfully"
msgstr "订单同步成功"

msgid "Orders cleaned up successfully"
msgstr "订单清理成功"

msgid "Failed to sync orders"
msgstr "订单同步失败"

msgid "Failed to cleanup orders"
msgstr "订单清理失败"

# Cache Manager translations
msgid "Cache Manager"
msgstr "缓存管理"

msgid "Manage cache across all databases and systems"
msgstr "管理所有数据库和系统的缓存"

msgid "Cache Statistics"
msgstr "缓存统计"

msgid "Cache Types"
msgstr "缓存类型"

msgid "Cache Monitoring"
msgstr "缓存监控"

msgid "Quick Actions"
msgstr "快速操作"

msgid "Database Cache Status"
msgstr "数据库缓存状态"

msgid "Cache Settings"
msgstr "缓存设置"

msgid "Total Cache Size"
msgstr "总缓存大小"

msgid "Cache Hit Rate"
msgstr "缓存命中率"

msgid "Active Cache Types"
msgstr "活跃缓存类型"

msgid "Last Cache Clear"
msgstr "最后清理缓存"

msgid "ago"
msgstr "前"

msgid "WordPress Cache"
msgstr "WordPress缓存"

msgid "WordPress transient and options cache"
msgstr "WordPress临时数据和选项缓存"

msgid "Database Cache"
msgstr "数据库缓存"

msgid "MySQL query result cache"
msgstr "MySQL查询结果缓存"

msgid "Object Cache"
msgstr "对象缓存"

msgid "PHP object cache (Redis/Memcached)"
msgstr "PHP对象缓存(Redis/Memcached)"

msgid "Page Cache"
msgstr "页面缓存"

msgid "Full page HTML cache"
msgstr "完整页面HTML缓存"

msgid "Enabled"
msgstr "已启用"

msgid "Disabled"
msgstr "已禁用"

msgid "Enable"
msgstr "启用"

msgid "Clear"
msgstr "清理"

msgid "Cache Type"
msgstr "缓存类型"

msgid "Hit Rate"
msgstr "命中率"

msgid "Size"
msgstr "大小"

msgid "Items"
msgstr "项目数"

msgid "Last Updated"
msgstr "最后更新"

msgid "Clear All Cache"
msgstr "清理所有缓存"

msgid "Clear WordPress Cache"
msgstr "清理WordPress缓存"

msgid "Clear Database Cache"
msgstr "清理数据库缓存"

msgid "Clear Object Cache"
msgstr "清理对象缓存"

msgid "Clear Page Cache"
msgstr "清理页面缓存"

msgid "Warm Cache"
msgstr "预热缓存"

msgid "Auto Clear Cache"
msgstr "自动清理缓存"

msgid "Cache Expiration (hours)"
msgstr "缓存过期时间（小时）"

msgid "Cache Compression"
msgstr "缓存压缩"

msgid "Cache Debug Mode"
msgstr "缓存调试模式"

msgid "Save Settings"
msgstr "保存设置"

msgid "Are you sure you want to clear all cache? This may affect site performance temporarily."
msgstr "确定要清理所有缓存吗？这可能会暂时影响网站性能。"

msgid "Clear WordPress cache?"
msgstr "清理WordPress缓存？"

msgid "Clear database cache?"
msgstr "清理数据库缓存？"

msgid "Clear object cache?"
msgstr "清理对象缓存？"

msgid "Clear page cache?"
msgstr "清理页面缓存？"

msgid "Start cache warming? This may take some time."
msgstr "开始预热缓存？这可能需要一些时间。"

msgid "Clearing all cache..."
msgstr "正在清理所有缓存..."

msgid "Warming cache..."
msgstr "正在预热缓存..."

msgid "All cache cleared successfully"
msgstr "所有缓存清理成功"

msgid "WordPress cache cleared successfully"
msgstr "WordPress缓存清理成功"

msgid "Database cache cleared successfully"
msgstr "数据库缓存清理成功"

msgid "Object cache cleared successfully"
msgstr "对象缓存清理成功"

msgid "Page cache cleared successfully"
msgstr "页面缓存清理成功"

msgid "Cache warming completed successfully"
msgstr "缓存预热完成"

msgid "Cache type toggled successfully"
msgstr "缓存类型切换成功"

msgid "Cache settings saved successfully"
msgstr "缓存设置保存成功"

msgid "Failed to clear all cache"
msgstr "清理所有缓存失败"

msgid "Failed to clear WordPress cache"
msgstr "清理WordPress缓存失败"

msgid "Failed to clear database cache"
msgstr "清理数据库缓存失败"

msgid "Failed to clear object cache"
msgstr "清理对象缓存失败"

msgid "Failed to clear page cache"
msgstr "清理页面缓存失败"

msgid "Failed to warm cache"
msgstr "预热缓存失败"

msgid "Failed to toggle cache type"
msgstr "切换缓存类型失败"

# Order Start ID Manager translations
msgid "Order Start ID Manager"
msgstr "订单起始ID管理"

msgid "Manage order starting IDs across all databases"
msgstr "管理所有数据库的订单起始ID"

msgid "Database Order Start IDs"
msgstr "数据库订单起始ID"

msgid "Batch Update"
msgstr "批量更新"

msgid "Database Name"
msgstr "数据库名称"

msgid "Status"
msgstr "状态"

msgid "Current Start ID"
msgstr "当前起始ID"

msgid "Current Max ID"
msgstr "当前最大ID"

msgid "New Start ID"
msgstr "新起始ID"

msgid "Actions"
msgstr "操作"

msgid "Enabled"
msgstr "已启用"

msgid "Update"
msgstr "更新"

msgid "No databases configured."
msgstr "未配置数据库。"

msgid "Apply the same starting ID to all databases at once."
msgstr "一次性为所有数据库应用相同的起始ID。"

msgid "New Start ID:"
msgstr "新起始ID："

msgid "e.g., 10000"
msgstr "例如：10000"

msgid "Update All"
msgstr "全部更新"

msgid "Increment by:"
msgstr "递增："

msgid "e.g., 1000"
msgstr "例如：1000"

msgid "Increment All"
msgstr "全部递增"

msgid "Please enter a valid start ID"
msgstr "请输入有效的起始ID"

msgid "Updating..."
msgstr "正在更新..."

msgid "Updated successfully"
msgstr "更新成功"

msgid "No changes"
msgstr "无更改"

msgid "Network error"
msgstr "网络错误"

msgid "All values updated, click individual Update buttons to save"
msgstr "所有值已更新，点击各自的更新按钮保存"

msgid "Please enter a valid increment value"
msgstr "请输入有效的递增值"

msgid "All values incremented, click individual Update buttons to save"
msgstr "所有值已递增，点击各自的更新按钮保存"

msgid "Order start ID updated successfully"
msgstr "订单起始ID更新成功"

msgid "Failed to update order start ID"
msgstr "更新订单起始ID失败"
