<?php
/**
 * 跨数据库订单比较测试页面
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Order_Comparison {
    
    private $gateway;
    
    public function __construct() {
        // 检查依赖类是否存在
        if (!class_exists('YXJTO_Gateway')) {
            error_log('YXJTO_Order_Comparison: YXJTO_Gateway class not found');
            return;
        }
        
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            error_log('YXJTO_Order_Comparison: WP_Multi_DB_Config_Manager class not found');
            return;
        }
        
        $this->gateway = YXJTO_Gateway::get_instance();
        
        if (!$this->gateway) {
            error_log('YXJTO_Order_Comparison: Failed to get YXJTO_Gateway instance');
            return;
        }
        
        // AJAX处理器现在在主类中注册，这里不需要重复注册
    }
    
    /**
     * 渲染订单比较页面
     */
    public function render_comparison_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        // 确保jQuery已加载
        wp_enqueue_script('jquery');
        
        // 检查依赖类
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            echo '<div class="wrap">';
            echo '<h1>跨数据库订单比较测试</h1>';
            echo '<div class="notice notice-error"><p>错误: WP_Multi_DB_Config_Manager 类未找到。请确保相关组件已正确加载。</p></div>';
            echo '</div>';
            return;
        }
        
        if (!$this->gateway) {
            echo '<div class="wrap">';
            echo '<h1>跨数据库订单比较测试</h1>';
            echo '<div class="notice notice-error"><p>错误: YXJTO_Gateway 实例未找到。请确保插件已正确初始化。</p></div>';
            echo '</div>';
            return;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        if (!$databases || !is_array($databases)) {
            echo '<div class="wrap">';
            echo '<h1>跨数据库订单比较测试</h1>';
            echo '<div class="notice notice-error"><p>错误: 未找到数据库配置。请先配置数据库。</p></div>';
            echo '</div>';
            return;
        }
        
        $current_database = $this->gateway->get_current_database();
        
        ?>
        <div class="wrap">
            <h1><?php _e('跨数据库订单比较测试', 'yxjto-gateway'); ?></h1>
            
            <div class="yxjto-order-comparison">
                <!-- 数据库状态概览 -->
                <div class="database-status-overview">
                    <h2><?php _e('数据库连接状态', 'yxjto-gateway'); ?></h2>
                    <div class="database-grid">
                        <?php foreach ($databases as $db_name => $db_config): ?>
                            <div class="database-card <?php echo $db_name === $current_database ? 'current' : ''; ?>">
                                <h3><?php echo esc_html($db_name); ?></h3>
                                <div class="database-info">
                                    <p><strong>Host:</strong> <?php echo esc_html($db_config['host']); ?></p>
                                    <p><strong>Database:</strong> <?php echo esc_html($db_config['name']); ?></p>
                                    <p><strong>Status:</strong> 
                                        <span class="status-indicator" data-database="<?php echo esc_attr($db_name); ?>">
                                            <span class="spinner"></span> 检测中...
                                        </span>
                                    </p>
                                    <?php if ($db_name === $current_database): ?>
                                        <span class="current-badge">当前数据库</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- 订单搜索和比较 -->
                <div class="order-comparison-section">
                    <h2><?php _e('订单比较', 'yxjto-gateway'); ?></h2>
                    
                    <div class="search-controls">
                        <div class="search-input-group">
                            <label for="order-search">搜索订单（订单ID或客户邮箱）:</label>
                            <input type="text" id="order-search" placeholder="输入订单ID（如: 12345）或客户邮箱">
                            <button id="search-orders" class="button button-primary">搜索订单</button>
                        </div>
                        
                        <div class="search-options">
                            <label>
                                <input type="checkbox" id="search-recent" checked>
                                同时显示最近10个订单
                            </label>
                            <label>
                                <input type="checkbox" id="include-meta" checked>
                                包含详细元数据比较
                            </label>
                        </div>
                        
                        <!-- 调试测试按钮 -->
                        <div class="debug-controls" style="margin-top: 15px; padding: 10px; border: 1px dashed #ccc;">
                            <h4>调试工具</h4>
                            <label for="test-order-id">测试订单ID:</label>
                            <input type="number" id="test-order-id" placeholder="输入订单ID进行测试" style="width: 150px;">
                            <button id="test-save-html" class="button" onclick="testSaveFunction()">🧪 测试保存功能</button>
                        </div>
                    </div>
                    
                    <div id="search-results" class="search-results">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
                
                <!-- 数据库切换测试 -->
                <div class="database-switch-test">
                    <h2><?php _e('数据库切换测试', 'yxjto-gateway'); ?></h2>
                    <p>测试支付跳转时的数据库切换功能</p>
                    
                    <div class="switch-test-controls">
                        <select id="test-target-database">
                            <option value="">选择目标数据库</option>
                            <?php foreach ($databases as $db_name => $db_config): ?>
                                <?php if ($db_config['enabled']): ?>
                                    <option value="<?php echo esc_attr($db_name); ?>">
                                        <?php echo esc_html($db_name); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                        <button id="test-database-switch" class="button">测试切换</button>
                    </div>
                    
                    <div id="switch-test-results" class="switch-test-results">
                        <!-- 切换测试结果将在这里显示 -->
                    </div>
                </div>
                
                <!-- 实时日志监控 -->
                <div class="log-monitor">
                    <h2><?php _e('实时日志监控', 'yxjto-gateway'); ?></h2>
                    <div class="log-controls">
                        <button id="start-log-monitor" class="button">开始监控</button>
                        <button id="stop-log-monitor" class="button">停止监控</button>
                        <button id="clear-log-display" class="button">清空显示</button>
                    </div>
                    <div id="log-display" class="log-display">
                        <!-- 实时日志将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .yxjto-order-comparison {
            max-width: 1200px;
        }
        
        .database-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .database-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .database-card.current {
            border-color: #0073aa;
            background: #e5f5ff;
        }
        
        .database-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .database-info p {
            margin: 5px 0;
            font-size: 13px;
        }
        
        .current-badge {
            background: #0073aa;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .status-indicator {
            display: inline-block;
        }
        
        .status-indicator.connected {
            color: #46b450;
        }
        
        .status-indicator.disconnected {
            color: #dc3232;
        }
        
        .status-indicator .spinner {
            width: 16px;
            height: 16px;
            margin-right: 5px;
        }
        
        .search-controls {
            background: #fff;
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .search-input-group {
            margin-bottom: 15px;
        }
        
        .search-input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .search-input-group input[type="text"] {
            width: 300px;
            margin-right: 10px;
        }
        
        .search-options label {
            margin-right: 20px;
        }
        
        .search-results {
            margin: 20px 0;
        }
        
        .order-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .order-comparison-table th,
        .order-comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .order-comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .comparison-match {
            background-color: #d4edda;
        }
        
        .comparison-mismatch {
            background-color: #f8d7da;
        }
        
        .comparison-missing {
            background-color: #fff3cd;
        }
        
        .switch-test-controls {
            margin: 15px 0;
        }
        
        .switch-test-controls select {
            margin-right: 10px;
            width: 200px;
        }
        
        .switch-test-results {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            min-height: 100px;
        }
        
        .log-monitor {
            margin: 30px 0;
        }
        
        .log-controls {
            margin: 15px 0;
        }
        
        .log-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .log-entry {
            margin: 2px 0;
        }
        
        .log-entry.error {
            color: #e74c3c;
        }
        
        .log-entry.warning {
            color: #f39c12;
        }
        
        .log-entry.info {
            color: #3498db;
        }
        
        .log-entry.debug {
            color: #95a5a6;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // 检测数据库连接状态
            checkDatabaseStatus();
            
            // 搜索订单
            $('#search-orders').click(function() {
                var searchTerm = $('#order-search').val();
                var includeRecent = $('#search-recent').is(':checked');
                var includeMeta = $('#include-meta').is(':checked');
                
                if (!searchTerm && !includeRecent) {
                    alert('请输入订单ID/邮箱或选择显示最近订单');
                    return;
                }
                
                searchOrders(searchTerm, includeRecent, includeMeta);
            });
            
            // 测试数据库切换
            $('#test-database-switch').click(function() {
                var targetDb = $('#test-target-database').val();
                if (!targetDb) {
                    alert('请选择目标数据库');
                    return;
                }
                
                testDatabaseSwitch(targetDb);
            });
            
            // 日志监控
            var logMonitorInterval;
            $('#start-log-monitor').click(function() {
                startLogMonitor();
            });
            
            $('#stop-log-monitor').click(function() {
                stopLogMonitor();
            });
            
            $('#clear-log-display').click(function() {
                $('#log-display').empty();
            });
            
            // 检测数据库连接状态
            function checkDatabaseStatus() {
                <?php foreach ($databases as $db_name => $db_config): ?>
                checkSingleDatabase('<?php echo esc_js($db_name); ?>');
                <?php endforeach; ?>
            }
            
            function checkSingleDatabase(dbName) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'yxjto_test_database_connection',
                        database: dbName,
                        nonce: '<?php echo wp_create_nonce('yxjto_test_db'); ?>'
                    },
                    success: function(response) {
                        var $indicator = $('.status-indicator[data-database="' + dbName + '"]');
                        if (response.success) {
                            $indicator.html('<span style="color: #46b450;">● 已连接</span>');
                        } else {
                            $indicator.html('<span style="color: #dc3232;">● 连接失败: ' + response.data + '</span>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Database connection test failed:', {
                            database: dbName,
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                        var $indicator = $('.status-indicator[data-database="' + dbName + '"]');
                        $indicator.html('<span style="color: #dc3232;">● 检测失败 (' + status + ')</span>');
                    }
                });
            }
            
            // 搜索订单
            function searchOrders(searchTerm, includeRecent, includeMeta) {
                $('#search-results').html('<div class="spinner is-active"></div> 搜索中...');
                
                console.log('Starting order search:', {
                    searchTerm: searchTerm,
                    includeRecent: includeRecent,
                    includeMeta: includeMeta
                });
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'yxjto_comparison_search_orders',
                        search_term: searchTerm,
                        include_recent: includeRecent,
                        include_meta: includeMeta,
                        nonce: '<?php echo wp_create_nonce('yxjto_comparison_search_orders'); ?>'
                    },
                    success: function(response) {
                        console.log('Search response:', response);
                        if (response.success) {
                            $('#search-results').html(response.data);
                        } else {
                            $('#search-results').html('<div class="notice notice-error"><p>搜索失败: ' + response.data + '</p></div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Search request failed:', {
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                        $('#search-results').html('<div class="notice notice-error"><p>搜索请求失败: ' + status + ' - ' + error + '</p></div>');
                    }
                });
            }
            
            // 测试数据库切换
            function testDatabaseSwitch(targetDb) {
                $('#switch-test-results').html('<div class="spinner is-active"></div> 测试中...');
                
                console.log('Starting database switch test:', targetDb);
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'yxjto_test_database_switch',
                        target_database: targetDb,
                        nonce: '<?php echo wp_create_nonce('yxjto_test_switch'); ?>'
                    },
                    success: function(response) {
                        console.log('Switch test response:', response);
                        if (response.success) {
                            $('#switch-test-results').html(response.data);
                        } else {
                            $('#switch-test-results').html('<div class="notice notice-error"><p>测试失败: ' + response.data + '</p></div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Switch test failed:', {
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                        $('#switch-test-results').html('<div class="notice notice-error"><p>测试请求失败: ' + status + ' - ' + error + '</p></div>');
                    }
                });
            }
            
            // 开始日志监控
            function startLogMonitor() {
                $('#start-log-monitor').prop('disabled', true);
                $('#stop-log-monitor').prop('disabled', false);
                
                logMonitorInterval = setInterval(function() {
                    // 这里实现日志获取逻辑
                    // 暂时模拟日志输出
                    var now = new Date();
                    var timestamp = now.toLocaleTimeString();
                    var logEntry = timestamp + ' [INFO] 监控运行中...\n';
                    $('#log-display').append(logEntry);
                    
                    // 保持滚动到底部
                    var logDisplay = document.getElementById('log-display');
                    logDisplay.scrollTop = logDisplay.scrollHeight;
                }, 2000);
            }
            
            // 停止日志监控
            function stopLogMonitor() {
                if (logMonitorInterval) {
                    clearInterval(logMonitorInterval);
                    logMonitorInterval = null;
                }
                
                $('#start-log-monitor').prop('disabled', false);
                $('#stop-log-monitor').prop('disabled', true);
            }
        });
        
        // 保存比较结果为HTML文件
        function saveComparisonToHtml(orderId) {
            console.log('saveComparisonToHtml called with orderId:', orderId);
            
            if (!orderId) {
                alert('订单ID无效');
                return;
            }
            
            // 检查ajaxurl是否定义
            if (typeof ajaxurl === 'undefined') {
                console.error('ajaxurl is not defined');
                alert('❌ AJAX URL未定义，请刷新页面重试');
                return;
            }
            
            // 检查jQuery是否可用
            if (typeof jQuery === 'undefined') {
                console.error('jQuery is not available');
                alert('❌ jQuery未加载，请刷新页面重试');
                return;
            }
            
            var $ = jQuery; // 确保使用jQuery
            
            var $button = $('#save-comparison-html');
            if ($button.length === 0) {
                console.error('Save button not found');
                alert('❌ 保存按钮未找到');
                return;
            }
            
            var originalText = $button.text();
            
            $button.prop('disabled', true).text('💾 保存中...');
            
            console.log('Starting save comparison to HTML:', orderId);
            console.log('AJAX URL:', ajaxurl);
            
            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'yxjto_save_comparison_html',
                    order_id: orderId,
                    nonce: '<?php echo wp_create_nonce('yxjto_save_comparison'); ?>'
                },
                timeout: 30000, // 30 seconds timeout
                success: function(response) {
                    console.log('Save response:', response);
                    
                    $button.prop('disabled', false).text(originalText);
                    
                    if (response && response.success) {
                        var data = response.data;
                        var message = '✅ ' + data.message + '\\n\\n';
                        message += '📁 文件名: ' + data.filename + '\\n';
                        message += '📊 文件大小: ' + data.file_size + '\\n\\n';
                        message += '是否现在下载文件？';
                        
                        if (confirm(message)) {
                            // 创建下载链接
                            var link = document.createElement('a');
                            link.href = data.file_url;
                            link.download = data.filename;
                            link.target = '_blank';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                        
                        // 显示成功消息
                        var $container = $('.order-comparison-container');
                        if ($container.length) {
                            var successMsg = '<div class="notice notice-success is-dismissible" style="margin: 10px 0;"><p><strong>保存成功!</strong> 文件已保存为: <a href="' + data.file_url + '" target="_blank">' + data.filename + '</a></p><button type="button" class="notice-dismiss" onclick="this.parentElement.remove()"><span class="screen-reader-text">忽略此通知</span></button></div>';
                            $container.prepend(successMsg);
                        }
                    } else {
                        var errorMsg = response && response.data ? response.data : '未知错误';
                        console.error('Save failed:', errorMsg);
                        alert('❌ 保存失败: ' + errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Save request failed:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        readyState: xhr.readyState,
                        statusText: xhr.statusText
                    });
                    
                    $button.prop('disabled', false).text(originalText);
                    
                    var errorMessage = '❌ 保存请求失败: ' + status;
                    
                    if (status === 'timeout') {
                        errorMessage += ' (请求超时，请重试)';
                    } else if (status === 'error') {
                        errorMessage += ' (网络错误)';
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response && response.data) {
                                errorMessage += ' - ' + response.data;
                            }
                        } catch (e) {
                            errorMessage += ' - ' + xhr.responseText.substring(0, 100);
                        }
                    }
                    
                    alert(errorMessage);
                }
            });
        }
        
        // 打印比较结果
        function printComparison() {
            var printContent = document.querySelector('.order-comparison-container');
            if (!printContent) {
                alert('没有找到可打印的内容');
                return;
            }
            
            var printWindow = window.open('', '_blank');
            printWindow.document.write('<!DOCTYPE html><html><head>');
            printWindow.document.write('<title>订单比较报告</title>');
            printWindow.document.write('<style>');
            printWindow.document.write('body { font-family: Arial, sans-serif; margin: 20px; }');
            printWindow.document.write('.comparison-actions { display: none; }');
            printWindow.document.write('table { border-collapse: collapse; width: 100%; margin: 10px 0; }');
            printWindow.document.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }');
            printWindow.document.write('th { background-color: #f5f5f5; }');
            printWindow.document.write('.comparison-match { background-color: #d4edda; }');
            printWindow.document.write('.comparison-mismatch { background-color: #f8d7da; }');
            printWindow.document.write('.comparison-missing { background-color: #fff3cd; }');
            printWindow.document.write('</style>');
            printWindow.document.write('</head><body>');
            printWindow.document.write(printContent.innerHTML);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }
        
        // 测试保存功能
        function testSaveFunction() {
            var testOrderId = document.getElementById('test-order-id').value;
            
            if (!testOrderId) {
                alert('请输入测试订单ID');
                return;
            }
            
            console.log('Testing save function with order ID:', testOrderId);
            
            // 检查环境
            console.log('Environment check:', {
                'ajaxurl defined': typeof ajaxurl !== 'undefined',
                'ajaxurl value': typeof ajaxurl !== 'undefined' ? ajaxurl : 'undefined',
                'jQuery available': typeof jQuery !== 'undefined',
                'saveComparisonToHtml function': typeof saveComparisonToHtml === 'function'
            });
            
            // 直接调用保存函数
            saveComparisonToHtml(parseInt(testOrderId));
        }
        </script>
        <?php
    }
    
    /**
     * AJAX: 测试数据库连接
     */
    public function ajax_test_database_connection() {
        check_ajax_referer('yxjto_test_db', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(-1);
        }
        
        $database = sanitize_text_field($_POST['database']);
        
        $test_result = $this->test_database_connection($database);
        
        if ($test_result['success']) {
            wp_send_json_success($test_result['message']);
        } else {
            wp_send_json_error($test_result['message']);
        }
    }
    
    /**
     * AJAX: 比较订单
     */
    public function ajax_compare_order() {
        check_ajax_referer('yxjto_compare_order', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(-1);
        }
        
        $order_id = intval($_POST['order_id']);
        if (!$order_id) {
            wp_send_json_error('无效的订单ID');
        }
        
        $comparison_result = $this->compare_order_across_databases($order_id);
        
        wp_send_json_success($comparison_result);
    }
    
    /**
     * AJAX: 搜索订单
     */
    public function ajax_search_orders() {
        try {
            check_ajax_referer('yxjto_comparison_search_orders', 'nonce');
            
            if (!current_user_can('manage_options')) {
                wp_die(-1);
            }
            
            // 检查依赖类
            if (!class_exists('WP_Multi_DB_Config_Manager')) {
                wp_send_json_error('WP_Multi_DB_Config_Manager 类未找到');
                return;
            }
            
            if (!$this->gateway) {
                wp_send_json_error('YXJTO_Gateway 实例未找到');
                return;
            }
            
            if (!function_exists('wc_get_orders')) {
                wp_send_json_error('WooCommerce 未安装或未激活');
                return;
            }
            
            $search_term = sanitize_text_field($_POST['search_term']);
            $include_recent = isset($_POST['include_recent']) && $_POST['include_recent'];
            $include_meta = isset($_POST['include_meta']) && $_POST['include_meta'];
            
            $results = $this->search_orders_across_databases($search_term, $include_recent, $include_meta);
            
            wp_send_json_success($results);
            
        } catch (Exception $e) {
            error_log('YXJTO Order Comparison - Search Error: ' . $e->getMessage());
            wp_send_json_error('搜索时发生错误: ' . $e->getMessage());
        }
    }
    
    /**
     * AJAX: 测试数据库切换
     */
    public function ajax_test_database_switch() {
        check_ajax_referer('yxjto_test_switch', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(-1);
        }
        
        $target_database = sanitize_text_field($_POST['target_database']);
        
        $test_result = $this->test_database_switch($target_database);
        
        wp_send_json_success($test_result);
    }
    
    /**
     * AJAX: 保存比较结果为HTML文件
     */
    public function ajax_save_comparison_html() {
        try {
            // 调试信息
            error_log('YXJTO: Starting ajax_save_comparison_html');
            
            check_ajax_referer('yxjto_save_comparison', 'nonce');
            
            if (!current_user_can('manage_options')) {
                error_log('YXJTO: User lacks manage_options capability');
                wp_die(-1);
            }
            
            $order_id = intval($_POST['order_id']);
            if (!$order_id) {
                error_log('YXJTO: Invalid order ID: ' . $_POST['order_id']);
                wp_send_json_error('无效的订单ID');
                return;
            }
            
            error_log('YXJTO: Processing order ID: ' . $order_id);
            
            // 检查依赖类
            if (!class_exists('WP_Multi_DB_Config_Manager')) {
                error_log('YXJTO: WP_Multi_DB_Config_Manager class not found');
                wp_send_json_error('WP_Multi_DB_Config_Manager 类未找到');
                return;
            }
            
            if (!$this->gateway) {
                error_log('YXJTO: Gateway instance not found');
                wp_send_json_error('YXJTO_Gateway 实例未找到');
                return;
            }
            
            // 生成比较数据
            error_log('YXJTO: Generating comparison data');
            $comparison_data = $this->get_comparison_data_for_order($order_id);
            
            if (empty($comparison_data)) {
                error_log('YXJTO: Comparison data is empty');
                wp_send_json_error('无法生成比较数据');
                return;
            }
            
            // 生成比较结果HTML
            error_log('YXJTO: Generating comparison HTML');
            $comparison_html = $this->generate_comparison_html($comparison_data, $order_id);
            
            if (empty($comparison_html)) {
                error_log('YXJTO: Comparison HTML is empty');
                wp_send_json_error('无法生成比较HTML');
                return;
            }
            
            // 生成完整的HTML文件内容
            error_log('YXJTO: Generating standalone HTML');
            $html_content = $this->generate_standalone_html($comparison_html, $order_id);
            
            if (empty($html_content)) {
                error_log('YXJTO: HTML content is empty');
                wp_send_json_error('无法生成HTML内容');
                return;
            }
            
            // 创建文件名
            $filename = 'order-comparison-' . $order_id . '-' . date('Y-m-d-H-i-s') . '.html';
            
            // 确保uploads目录存在
            $upload_dir = wp_upload_dir();
            $comparison_dir = $upload_dir['basedir'] . '/order-comparisons';
            
            error_log('YXJTO: Creating directory: ' . $comparison_dir);
            
            if (!file_exists($comparison_dir)) {
                $mkdir_result = wp_mkdir_p($comparison_dir);
                if (!$mkdir_result) {
                    error_log('YXJTO: Failed to create directory: ' . $comparison_dir);
                    wp_send_json_error('无法创建保存目录');
                    return;
                }
            }
            
            $file_path = $comparison_dir . '/' . $filename;
            
            error_log('YXJTO: Writing file to: ' . $file_path);
            error_log('YXJTO: HTML content length: ' . strlen($html_content));
            
            // 写入文件
            $result = file_put_contents($file_path, $html_content);
            
            if ($result === false) {
                error_log('YXJTO: Failed to write file: ' . $file_path);
                wp_send_json_error('文件保存失败，请检查目录权限');
                return;
            }
            
            error_log('YXJTO: File written successfully, bytes: ' . $result);
            
            // 验证文件是否存在
            if (!file_exists($file_path)) {
                error_log('YXJTO: File does not exist after writing: ' . $file_path);
                wp_send_json_error('文件保存验证失败');
                return;
            }
            
            // 返回下载链接
            $file_url = $upload_dir['baseurl'] . '/order-comparisons/' . $filename;
            
            error_log('YXJTO: Success - File URL: ' . $file_url);
            
            wp_send_json_success([
                'message' => '比较结果已保存为HTML文件',
                'filename' => $filename,
                'file_url' => $file_url,
                'file_path' => $file_path,
                'file_size' => size_format(filesize($file_path))
            ]);
            
        } catch (Exception $e) {
            error_log('YXJTO Order Comparison - Save HTML Error: ' . $e->getMessage());
            error_log('YXJTO Order Comparison - Stack trace: ' . $e->getTraceAsString());
            wp_send_json_error('保存时发生错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 跨数据库比较订单
     */
    private function compare_order_across_databases($order_id) {
        $comparison_data = $this->get_comparison_data_for_order($order_id);
        return $this->generate_comparison_html($comparison_data, $order_id);
    }
    
    /**
     * 获取订单的跨数据库比较数据
     */
    private function get_comparison_data_for_order($order_id) {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_database = $this->gateway->get_current_database();
        $comparison_data = [];
        
        foreach ($databases as $db_name => $db_config) {
            if (!$db_config['enabled']) {
                continue;
            }
            
            // 切换到目标数据库
            $this->gateway->switch_database($db_name);
            
            // 获取订单数据
            $order_data = $this->get_order_data($order_id);
            $comparison_data[$db_name] = $order_data;
        }
        
        // 切换回原数据库
        $this->gateway->switch_database($current_database);
        
        return $comparison_data;
    }
    
    /**
     * 搜索跨数据库订单
     */
    private function search_orders_across_databases($search_term, $include_recent, $include_meta) {
        try {
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            if (!$databases || !is_array($databases)) {
                return '<p>未找到数据库配置</p>';
            }
            
            $current_database = $this->gateway->get_current_database();
            $search_results = [];
            
            foreach ($databases as $db_name => $db_config) {
                if (!$db_config['enabled']) {
                    continue;
                }
                
                try {
                    // 切换到目标数据库
                    $switch_result = $this->gateway->switch_database($db_name);
                    if (!$switch_result) {
                        $search_results[$db_name] = [];
                        continue;
                    }
                    
                    $orders = [];
                    
                    // 搜索特定订单
                    if (!empty($search_term)) {
                        if (is_numeric($search_term)) {
                            // 按订单ID搜索
                            $order = wc_get_order($search_term);
                            if ($order && !is_wp_error($order)) {
                                $orders[] = $this->get_order_summary($order);
                            }
                        } else {
                            // 按邮箱搜索
                            $customer_orders = wc_get_orders([
                                'billing_email' => $search_term,
                                'limit' => 10
                            ]);
                            
                            if ($customer_orders && !is_wp_error($customer_orders)) {
                                foreach ($customer_orders as $order) {
                                    $orders[] = $this->get_order_summary($order);
                                }
                            }
                        }
                    }
                    
                    // 包含最近订单
                    if ($include_recent) {
                        $recent_orders = wc_get_orders([
                            'limit' => 10,
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ]);
                        
                        if ($recent_orders && !is_wp_error($recent_orders)) {
                            foreach ($recent_orders as $order) {
                                $summary = $this->get_order_summary($order);
                                // 避免重复
                                $found = false;
                                foreach ($orders as $existing) {
                                    if ($existing['id'] == $summary['id']) {
                                        $found = true;
                                        break;
                                    }
                                }
                                if (!$found) {
                                    $orders[] = $summary;
                                }
                            }
                        }
                    }
                    
                    $search_results[$db_name] = $orders;
                    
                } catch (Exception $e) {
                    error_log("Database {$db_name} search error: " . $e->getMessage());
                    $search_results[$db_name] = [];
                }
            }
            
            // 切换回原数据库
            $this->gateway->switch_database($current_database);
            
            return $this->generate_search_results_html($search_results, $include_meta);
            
        } catch (Exception $e) {
            error_log('Search orders across databases error: ' . $e->getMessage());
            return '<p>搜索时发生错误: ' . esc_html($e->getMessage()) . '</p>';
        }
    }
    
    /**
     * 测试数据库切换
     */
    private function test_database_switch($target_database) {
        $current_database = $this->gateway->get_current_database();
        $test_log = [];
        
        $test_log[] = "开始数据库切换测试";
        $test_log[] = "当前数据库: {$current_database}";
        $test_log[] = "目标数据库: {$target_database}";
        
        try {
            // 测试切换
            $switch_result = $this->gateway->switch_database($target_database);
            $test_log[] = "切换结果: " . ($switch_result ? '成功' : '失败');
            
            if ($switch_result) {
                // 验证当前数据库
                $new_current = $this->gateway->get_current_database();
                $test_log[] = "验证当前数据库: {$new_current}";
                
                if ($new_current === $target_database) {
                    $test_log[] = "✅ 数据库切换验证成功";
                } else {
                    $test_log[] = "❌ 数据库切换验证失败";
                }
                
                // 测试数据库连接
                global $wpdb;
                $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'shop_order'");
                $test_log[] = "测试查询结果: 找到 {$test_query} 个订单";
                
                // 切换回原数据库
                $this->gateway->switch_database($current_database);
                $test_log[] = "已切换回原数据库: {$current_database}";
            }
            
        } catch (Exception $e) {
            $test_log[] = "❌ 切换异常: " . $e->getMessage();
            // 确保切换回原数据库
            $this->gateway->switch_database($current_database);
        }
        
        return '<pre>' . implode("\n", $test_log) . '</pre>';
    }
    
    /**
     * 测试数据库连接
     */
    private function test_database_connection($database_name) {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        
        if (!isset($databases[$database_name])) {
            return [
                'success' => false,
                'message' => '数据库配置不存在'
            ];
        }
        
        $db_config = $databases[$database_name];
        $current_database = $this->gateway->get_current_database();
        
        try {
            // 测试切换
            $switch_result = $this->gateway->switch_database($database_name);
            
            if (!$switch_result) {
                return [
                    'success' => false,
                    'message' => '数据库切换失败'
                ];
            }
            
            // 测试查询
            global $wpdb;
            $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'shop_order' LIMIT 1");
            
            // 切换回原数据库
            $this->gateway->switch_database($current_database);
            
            if ($test_query !== null) {
                return [
                    'success' => true,
                    'message' => "连接成功，找到 {$test_query} 个订单"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '查询测试失败'
                ];
            }
            
        } catch (Exception $e) {
            // 确保切换回原数据库
            $this->gateway->switch_database($current_database);
            
            return [
                'success' => false,
                'message' => '连接异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取订单详细数据
     */
    private function get_order_data($order_id) {
        $order = wc_get_order($order_id);
        
        if (!$order || is_wp_error($order) || !is_object($order)) {
            return [
                'exists' => false,
                'error' => '订单不存在或无效'
            ];
        }
        
        global $wpdb;
        $current_db = $wpdb->dbname;
        
        // 安全获取创建日期和修改日期
        $date_created = $order->get_date_created();
        $date_modified = $order->get_date_modified();
        $date_completed = $order->get_date_completed();
        $date_paid = $order->get_date_paid();
        
        $formatted_date_created = $date_created ? $date_created->format('Y-m-d H:i:s') : '未知日期';
        $formatted_date_modified = $date_modified ? $date_modified->format('Y-m-d H:i:s') : '未知日期';
        $formatted_date_completed = $date_completed ? $date_completed->format('Y-m-d H:i:s') : '未完成';
        $formatted_date_paid = $date_paid ? $date_paid->format('Y-m-d H:i:s') : '未支付';
        
        // 获取订单备注
        $customer_note = $order->get_customer_note();
        $order_notes = $this->get_order_notes($order_id);
        
        // 获取地址信息
        $billing_address = $order->get_formatted_billing_address();
        $shipping_address = $order->get_formatted_shipping_address();
        
        // 获取优惠券信息
        $coupons = $order->get_coupon_codes();
        
        // 获取费用信息
        $fees = [];
        foreach ($order->get_fees() as $fee) {
            $fees[] = [
                'name' => $fee->get_name(),
                'amount' => $fee->get_amount(),
                'total' => $fee->get_total()
            ];
        }
        
        // 获取运费信息
        $shipping_methods = [];
        foreach ($order->get_shipping_methods() as $shipping) {
            $shipping_methods[] = [
                'method_title' => $shipping->get_method_title(),
                'method_id' => $shipping->get_method_id(),
                'total' => $shipping->get_total()
            ];
        }
        
        // 获取税费信息
        $tax_totals = $order->get_tax_totals();
        
        return [
            'exists' => true,
            'database' => $current_db,
            
            // 基本信息
            'id' => $order->get_id() ?: 0,
            'order_key' => $order->get_order_key() ?: '无',
            'status' => $order->get_status() ?: '未知',
            'parent_id' => $order->get_parent_id() ?: 0,
            'type' => $order->get_type() ?: 'shop_order',
            
            // 日期信息
            'date_created' => $formatted_date_created,
            'date_modified' => $formatted_date_modified,
            'date_completed' => $formatted_date_completed,
            'date_paid' => $formatted_date_paid,
            
            // 金额信息
            'total' => $order->get_total() ?: '0',
            'subtotal' => $order->get_subtotal() ?: '0',
            'tax_total' => $order->get_total_tax() ?: '0',
            'shipping_total' => $order->get_shipping_total() ?: '0',
            'discount_total' => $order->get_discount_total() ?: '0',
            'discount_tax' => $order->get_discount_tax() ?: '0',
            'cart_tax' => $order->get_cart_tax() ?: '0',
            'shipping_tax' => $order->get_shipping_tax() ?: '0',
            'total_refunded' => $order->get_total_refunded() ?: '0',
            'currency' => $order->get_currency() ?: 'CNY',
            
            // 客户信息
            'customer_id' => $order->get_customer_id() ?: 0,
            'customer_ip_address' => $order->get_customer_ip_address() ?: '未知',
            'customer_user_agent' => $order->get_customer_user_agent() ?: '未知',
            'customer_note' => $customer_note ?: '无',
            
            // 账单地址
            'billing_first_name' => $order->get_billing_first_name() ?: '未知',
            'billing_last_name' => $order->get_billing_last_name() ?: '未知',
            'billing_company' => $order->get_billing_company() ?: '无',
            'billing_address_1' => $order->get_billing_address_1() ?: '未知',
            'billing_address_2' => $order->get_billing_address_2() ?: '无',
            'billing_city' => $order->get_billing_city() ?: '未知',
            'billing_state' => $order->get_billing_state() ?: '未知',
            'billing_postcode' => $order->get_billing_postcode() ?: '未知',
            'billing_country' => $order->get_billing_country() ?: '未知',
            'billing_email' => $order->get_billing_email() ?: '未知',
            'billing_phone' => $order->get_billing_phone() ?: '未知',
            'billing_address_formatted' => $billing_address ?: '未知',
            
            // 收货地址
            'shipping_first_name' => $order->get_shipping_first_name() ?: '未知',
            'shipping_last_name' => $order->get_shipping_last_name() ?: '未知',
            'shipping_company' => $order->get_shipping_company() ?: '无',
            'shipping_address_1' => $order->get_shipping_address_1() ?: '未知',
            'shipping_address_2' => $order->get_shipping_address_2() ?: '无',
            'shipping_city' => $order->get_shipping_city() ?: '未知',
            'shipping_state' => $order->get_shipping_state() ?: '未知',
            'shipping_postcode' => $order->get_shipping_postcode() ?: '未知',
            'shipping_country' => $order->get_shipping_country() ?: '未知',
            'shipping_address_formatted' => $shipping_address ?: '未知',
            
            // 支付信息
            'payment_method' => $order->get_payment_method() ?: '未知',
            'payment_method_title' => $order->get_payment_method_title() ?: '未知',
            'transaction_id' => $order->get_transaction_id() ?: '无',
            
            // 商品信息 
            'item_count' => count($order->get_items()),
            'items' => $this->get_order_items_data($order),
            
            // 优惠券信息
            'coupons' => $coupons,
            'coupon_count' => count($coupons),
            
            // 费用信息
            'fees' => $fees,
            'fee_count' => count($fees),
            
            // 运费信息
            'shipping_methods' => $shipping_methods,
            'shipping_method_count' => count($shipping_methods),
            
            // 税费信息
            'tax_totals' => $tax_totals,
            'tax_count' => count($tax_totals),
            
            // 订单备注
            'order_notes' => $order_notes,
            'note_count' => count($order_notes),
            
            // 元数据
            'meta_data' => $this->get_order_meta_data($order),
            
            // 订单统计
            'downloadable_items' => $order->get_downloadable_items(),
            'needs_payment' => $order->needs_payment() ? '是' : '否',
            'needs_processing' => $order->needs_processing() ? '是' : '否',
            'has_status' => $order->has_status(['completed', 'processing']) ? '已处理' : '未处理',
            'is_editable' => $order->is_editable() ? '可编辑' : '不可编辑',
            'is_paid' => $order->is_paid() ? '已支付' : '未支付'
        ];
    }
    
    /**
     * 获取订单概要信息
     */
    private function get_order_summary($order) {
        // 验证订单对象
        if (!$order || !is_object($order)) {
            return [
                'id' => 0,
                'status' => '未知',
                'total' => '0',
                'date_created' => '未知日期',
                'billing_email' => '未知',
                'payment_method' => '未知'
            ];
        }
        
        // 安全获取创建日期
        $date_created = $order->get_date_created();
        $formatted_date = $date_created ? $date_created->format('Y-m-d H:i:s') : '未知日期';
        
        return [
            'id' => $order->get_id() ?: 0,
            'status' => $order->get_status() ?: '未知',
            'total' => $order->get_total() ?: '0',
            'date_created' => $formatted_date,
            'billing_email' => $order->get_billing_email() ?: '未知',
            'payment_method' => $order->get_payment_method_title() ?: '未知'
        ];
    }
    
    /**
     * 获取订单项目数据
     */
    private function get_order_items_data($order) {
        $items_data = [];
        
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $items_data[] = [
                'id' => $item_id,
                'name' => $item->get_name(),
                'product_id' => $item->get_product_id(),
                'variation_id' => $item->get_variation_id(),
                'quantity' => $item->get_quantity(),
                'total' => $item->get_total(),
                'subtotal' => $item->get_subtotal(),
                'tax_total' => $item->get_total_tax(),
                'sku' => $product ? $product->get_sku() : '无',
                'weight' => $product ? $product->get_weight() : '0',
                'meta_data' => $item->get_formatted_meta_data()
            ];
        }
        
        return $items_data;
    }
    
    /**
     * 获取订单备注
     */
    private function get_order_notes($order_id) {
        $notes = wc_get_order_notes([
            'order_id' => $order_id,
            'limit' => 20
        ]);
        
        $notes_data = [];
        foreach ($notes as $note) {
            $notes_data[] = [
                'id' => $note->id,
                'content' => $note->content,
                'customer_note' => $note->customer_note,
                'added_by' => $note->added_by,
                'date_created' => $note->date_created->format('Y-m-d H:i:s')
            ];
        }
        
        return $notes_data;
    }
    
    /**
     * 获取订单元数据
     */
    private function get_order_meta_data($order) {
        $meta_data = [];
        
        foreach ($order->get_meta_data() as $meta) {
            $meta_data[$meta->key] = $meta->value;
        }
        
        return $meta_data;
    }
    
    /**
     * 生成比较结果HTML
     */
    private function generate_comparison_html($comparison_data, $order_id) {
        $html = "<div class='order-comparison-container'>";
        $html .= "<div class='comparison-header'>";
        $html .= "<h3>📊 订单 #{$order_id} 跨数据库详细比较结果</h3>";
        $html .= "<div class='comparison-actions'>";
        $html .= "<button id='save-comparison-html' class='button button-primary' data-order-id='{$order_id}' onclick='saveComparisonToHtml({$order_id})'>💾 保存为HTML文件</button>";
        $html .= "<button id='print-comparison' class='button' onclick='printComparison()'>🖨️ 打印报告</button>";
        $html .= "</div>";
        $html .= "</div>";
        
        if (empty($comparison_data)) {
            return $html . "<p>❌ 没有找到任何数据。</p></div>";
        }
        
        // 统计信息
        $total_dbs = count($comparison_data);
        $found_dbs = 0;
        $missing_dbs = [];
        
        foreach ($comparison_data as $db_name => $order_data) {
            if ($order_data && $order_data['exists']) {
                $found_dbs++;
            } else {
                $missing_dbs[] = $db_name;
            }
        }
        
        $html .= "<div class='comparison-summary'>";
        $html .= "<h4>📈 统计概览</h4>";
        $html .= "<p><strong>总数据库数:</strong> {$total_dbs}</p>";
        $html .= "<p><strong>找到订单数:</strong> {$found_dbs}</p>";
        $html .= "<p><strong>缺失订单数:</strong> " . count($missing_dbs) . "</p>";
        if (!empty($missing_dbs)) {
            $html .= "<p><strong>缺失数据库:</strong> " . implode(', ', $missing_dbs) . "</p>";
        }
        $html .= "</div>";
        
        // 基本信息比较
        $html .= $this->generate_comparison_section('基本信息', [
            'exists' => '订单存在',
            'id' => '订单ID',
            'order_key' => '订单密钥',
            'status' => '订单状态',
            'type' => '订单类型',
            'parent_id' => '父订单ID'
        ], $comparison_data);
        
        // 日期信息比较
        $html .= $this->generate_comparison_section('日期信息', [
            'date_created' => '创建时间',
            'date_modified' => '修改时间',
            'date_completed' => '完成时间',
            'date_paid' => '支付时间'
        ], $comparison_data);
        
        // 金额信息比较
        $html .= $this->generate_comparison_section('金额信息', [
            'total' => '订单总额',
            'subtotal' => '小计',
            'tax_total' => '税费总额',
            'shipping_total' => '运费总额',
            'discount_total' => '折扣总额',
            'discount_tax' => '折扣税费',
            'cart_tax' => '购物车税费',
            'shipping_tax' => '运费税费',
            'total_refunded' => '退款总额',
            'currency' => '货币'
        ], $comparison_data);
        
        // 客户信息比较
        $html .= $this->generate_comparison_section('客户信息', [
            'customer_id' => '客户ID',
            'customer_ip_address' => 'IP地址',
            'customer_user_agent' => '用户代理',
            'customer_note' => '客户备注'
        ], $comparison_data);
        
        // 账单地址比较
        $html .= $this->generate_comparison_section('账单地址', [
            'billing_first_name' => '名',
            'billing_last_name' => '姓',
            'billing_company' => '公司',
            'billing_address_1' => '地址1',
            'billing_address_2' => '地址2',
            'billing_city' => '城市',
            'billing_state' => '省/州',
            'billing_postcode' => '邮编',
            'billing_country' => '国家',
            'billing_email' => '邮箱',
            'billing_phone' => '电话'
        ], $comparison_data);
        
        // 收货地址比较
        $html .= $this->generate_comparison_section('收货地址', [
            'shipping_first_name' => '收货人名',
            'shipping_last_name' => '收货人姓',
            'shipping_company' => '收货公司',
            'shipping_address_1' => '收货地址1',
            'shipping_address_2' => '收货地址2',
            'shipping_city' => '收货城市',
            'shipping_state' => '收货省/州',
            'shipping_postcode' => '收货邮编',
            'shipping_country' => '收货国家'
        ], $comparison_data);
        
        // 支付信息比较
        $html .= $this->generate_comparison_section('支付信息', [
            'payment_method' => '支付方式',
            'payment_method_title' => '支付方式标题',
            'transaction_id' => '交易ID'
        ], $comparison_data);
        
        // 订单状态比较
        $html .= $this->generate_comparison_section('订单状态', [
            'needs_payment' => '需要支付',
            'needs_processing' => '需要处理',
            'has_status' => '处理状态',
            'is_editable' => '是否可编辑',
            'is_paid' => '是否已支付'
        ], $comparison_data);
        
        // 商品统计比较
        $html .= $this->generate_comparison_section('商品统计', [
            'item_count' => '商品数量',
            'coupon_count' => '优惠券数量',
            'fee_count' => '费用项数量',
            'shipping_method_count' => '运输方式数量',
            'tax_count' => '税费项数量',
            'note_count' => '备注数量'
        ], $comparison_data);
        
        // 商品详情比较
        $html .= $this->generate_items_comparison($comparison_data);
        
        // 优惠券比较
        $html .= $this->generate_coupons_comparison($comparison_data);
        
        // 运费方式比较
        $html .= $this->generate_shipping_comparison($comparison_data);
        
        // 备注比较
        $html .= $this->generate_notes_comparison($comparison_data);
        
        // 元数据比较
        $html .= $this->generate_metadata_comparison($comparison_data);
        
        $html .= "</div>";
        
        // 添加样式
        $html .= $this->get_comparison_styles();
        
        return $html;
    }
    
    /**
     * 生成比较区段
     */
    private function generate_comparison_section($section_title, $fields, $comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>📋 {$section_title}</h4>";
        $html .= '<table class="order-comparison-table">';
        $html .= '<thead><tr>';
        $html .= '<th class="field-column">字段</th>';
        
        foreach ($comparison_data as $db_name => $order_data) {
            $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
            $html .= '<th class="db-column">' . $status_icon . ' ' . esc_html($db_name) . '</th>';
        }
        
        $html .= '</tr></thead><tbody>';
        
        foreach ($fields as $field => $label) {
            $html .= '<tr>';
            $html .= '<td class="field-label"><strong>' . esc_html($label) . '</strong></td>';
            
            $values = [];
            $all_same = true;
            $first_value = null;
            
            // 收集所有值
            foreach ($comparison_data as $db_name => $order_data) {
                if ($order_data && $order_data['exists']) {
                    $value = $order_data[$field] ?? '不存在';
                    if ($first_value === null) {
                        $first_value = $value;
                    } elseif ($first_value !== $value) {
                        $all_same = false;
                    }
                } else {
                    $value = '❌ 订单不存在';
                    $all_same = false;
                }
                $values[$db_name] = $value;
            }
            
            // 显示值
            foreach ($comparison_data as $db_name => $order_data) {
                $value = $values[$db_name];
                
                // 确定样式类
                $class = '';
                if (!$order_data || !$order_data['exists']) {
                    $class = 'comparison-missing';
                } elseif ($all_same) {
                    $class = 'comparison-match';
                } else {
                    $class = 'comparison-mismatch';
                }
                
                $html .= '<td class="' . $class . '">' . esc_html($value) . '</td>';
            }
            
            $html .= '</tr>';
        }
        
        $html .= '</tbody></table>';
        $html .= "</div>";
        
        return $html;
    }
    
    /**
     * 生成商品详情比较
     */
    private function generate_items_comparison($comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>🛍️ 商品详情比较</h4>";
        
        // 收集所有商品
        $all_items = [];
        foreach ($comparison_data as $db_name => $order_data) {
            if ($order_data && $order_data['exists'] && !empty($order_data['items'])) {
                foreach ($order_data['items'] as $item) {
                    $key = $item['product_id'] . '_' . $item['variation_id'];
                    if (!isset($all_items[$key])) {
                        $all_items[$key] = [
                            'name' => $item['name'],
                            'product_id' => $item['product_id'],
                            'variation_id' => $item['variation_id'],
                            'sku' => $item['sku']
                        ];
                    }
                }
            }
        }
        
        if (empty($all_items)) {
            $html .= "<p>没有找到商品信息。</p>";
        } else {
            // 商品详情对比表
            $html .= "<h5>📋 商品详情对比</h5>";
            $html .= '<table class="order-comparison-table items-comparison">';
            $html .= '<thead><tr>';
            $html .= '<th class="item-info-column">商品信息</th>';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<th class="db-column">' . $status_icon . ' ' . esc_html($db_name) . '</th>';
            }
            
            $html .= '</tr></thead><tbody>';
            
            foreach ($all_items as $key => $item_info) {
                $html .= '<tr>';
                $html .= '<td class="item-info"><strong>' . esc_html($item_info['name']) . '</strong><br>';
                $html .= '<span class="item-details">SKU: ' . esc_html($item_info['sku']) . '<br>';
                $html .= 'ID: ' . $item_info['product_id'];
                if ($item_info['variation_id']) {
                    $html .= ' (变体: ' . $item_info['variation_id'] . ')';
                }
                $html .= '</span></td>';
                
                foreach ($comparison_data as $db_name => $order_data) {
                    $found_item = null;
                    if ($order_data && $order_data['exists'] && !empty($order_data['items'])) {
                        foreach ($order_data['items'] as $item) {
                            if (($item['product_id'] . '_' . $item['variation_id']) === $key) {
                                $found_item = $item;
                                break;
                            }
                        }
                    }
                    
                    if ($found_item) {
                        $html .= '<td class="comparison-match item-details-cell">';
                        $html .= '<div class="item-stat">数量: <span class="value">' . $found_item['quantity'] . '</span></div>';
                        $html .= '<div class="item-stat">单价: <span class="value">¥' . number_format($found_item['subtotal']/$found_item['quantity'], 2) . '</span></div>';
                        $html .= '<div class="item-stat">小计: <span class="value">¥' . number_format($found_item['subtotal'], 2) . '</span></div>';
                        $html .= '<div class="item-stat">总价: <span class="value">¥' . number_format($found_item['total'], 2) . '</span></div>';
                        $html .= '<div class="item-stat">税费: <span class="value">¥' . number_format($found_item['tax_total'], 2) . '</span></div>';
                        if (!empty($found_item['meta_data'])) {
                            $html .= '<div class="item-meta">附加信息: ' . count($found_item['meta_data']) . ' 项</div>';
                        }
                        $html .= '</td>';
                    } else {
                        $html .= '<td class="comparison-missing">❌ 商品不存在</td>';
                    }
                }
                
                $html .= '</tr>';
            }
            
            $html .= '</tbody></table>';
            
            // 各数据库商品完整列表
            $html .= "<h5>📦 各数据库商品完整列表</h5>";
            $html .= '<div class="database-items-grid">';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $html .= '<div class="database-items-card">';
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<h6>' . $status_icon . ' ' . esc_html($db_name) . '</h6>';
                
                if (!$order_data || !$order_data['exists']) {
                    $html .= '<p class="no-order">❌ 订单不存在</p>';
                } elseif (empty($order_data['items'])) {
                    $html .= '<p class="no-items">📝 无商品</p>';
                } else {
                    $html .= '<div class="items-list">';
                    $total_quantity = 0;
                    $total_amount = 0;
                    
                    foreach ($order_data['items'] as $index => $item) {
                        $total_quantity += $item['quantity'];
                        $total_amount += $item['total'];
                        
                        $html .= '<div class="item-row">';
                        $html .= '<div class="item-number">#' . ($index + 1) . '</div>';
                        $html .= '<div class="item-content">';
                        $html .= '<div class="item-name"><strong>' . esc_html($item['name']) . '</strong></div>';
                        $html .= '<div class="item-info-grid">';
                        $html .= '<span class="info-item">SKU: ' . esc_html($item['sku']) . '</span>';
                        $html .= '<span class="info-item">数量: ' . $item['quantity'] . '</span>';
                        $html .= '<span class="info-item">单价: ¥' . number_format($item['subtotal']/$item['quantity'], 2) . '</span>';
                        $html .= '<span class="info-item">总价: ¥' . number_format($item['total'], 2) . '</span>';
                        $html .= '</div>';
                        
                        if ($item['product_id']) {
                            $html .= '<div class="item-ids">ID: ' . $item['product_id'];
                            if ($item['variation_id']) {
                                $html .= ' (变体: ' . $item['variation_id'] . ')';
                            }
                            $html .= '</div>';
                        }
                        
                        if (!empty($item['meta_data'])) {
                            $html .= '<div class="item-meta-info">🏷️ 附加信息: ' . count($item['meta_data']) . ' 项</div>';
                        }
                        
                        $html .= '</div></div>';
                    }
                    
                    $html .= '</div>';
                    
                    // 数据库商品统计
                    $html .= '<div class="items-summary">';
                    $html .= '<div class="summary-stat">总商品种类: <strong>' . count($order_data['items']) . '</strong></div>';
                    $html .= '<div class="summary-stat">总数量: <strong>' . $total_quantity . '</strong></div>';
                    $html .= '<div class="summary-stat">商品总价: <strong>¥' . number_format($total_amount, 2) . '</strong></div>';
                    $html .= '</div>';
                }
                
                $html .= '</div>';
            }
            
            $html .= '</div>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * 生成优惠券比较
     */
    private function generate_coupons_comparison($comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>🎫 优惠券比较</h4>";
        
        $all_coupons = [];
        foreach ($comparison_data as $db_name => $order_data) {
            if ($order_data && $order_data['exists'] && !empty($order_data['coupons'])) {
                foreach ($order_data['coupons'] as $coupon) {
                    $all_coupons[$coupon] = $coupon;
                }
            }
        }
        
        if (empty($all_coupons)) {
            $html .= "<p>没有使用优惠券。</p>";
        } else {
            $html .= '<table class="order-comparison-table">';
            $html .= '<thead><tr>';
            $html .= '<th>优惠券代码</th>';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<th>' . $status_icon . ' ' . esc_html($db_name) . '</th>';
            }
            
            $html .= '</tr></thead><tbody>';
            
            foreach ($all_coupons as $coupon_code) {
                $html .= '<tr>';
                $html .= '<td><strong>' . esc_html($coupon_code) . '</strong></td>';
                
                foreach ($comparison_data as $db_name => $order_data) {
                    if ($order_data && $order_data['exists'] && in_array($coupon_code, $order_data['coupons'])) {
                        $html .= '<td class="comparison-match">✅ 已使用</td>';
                    } else {
                        $html .= '<td class="comparison-missing">❌ 未使用</td>';
                    }
                }
                
                $html .= '</tr>';
            }
            
            $html .= '</tbody></table>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * 生成运费方式比较
     */
    private function generate_shipping_comparison($comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>🚚 运费方式比较</h4>";
        
        $has_shipping = false;
        foreach ($comparison_data as $order_data) {
            if ($order_data && $order_data['exists'] && !empty($order_data['shipping_methods'])) {
                $has_shipping = true;
                break;
            }
        }
        
        if (!$has_shipping) {
            $html .= "<p>没有运费信息。</p>";
        } else {
            $html .= '<table class="order-comparison-table">';
            $html .= '<thead><tr>';
            $html .= '<th>字段</th>';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<th>' . $status_icon . ' ' . esc_html($db_name) . '</th>';
            }
            
            $html .= '</tr></thead><tbody>';
            
            $html .= '<tr>';
            $html .= '<td><strong>运费总额</strong></td>';
            foreach ($comparison_data as $db_name => $order_data) {
                if ($order_data && $order_data['exists']) {
                    $html .= '<td class="comparison-match">¥' . esc_html($order_data['shipping_total']) . '</td>';
                } else {
                    $html .= '<td class="comparison-missing">❌ 无数据</td>';
                }
            }
            $html .= '</tr>';
            
            $html .= '<tr>';
            $html .= '<td><strong>运费方式</strong></td>';
            foreach ($comparison_data as $db_name => $order_data) {
                if ($order_data && $order_data['exists'] && !empty($order_data['shipping_methods'])) {
                    $methods = [];
                    foreach ($order_data['shipping_methods'] as $method) {
                        $methods[] = $method['method_title'] . ' (¥' . $method['total'] . ')';
                    }
                    $html .= '<td class="comparison-match">' . implode('<br>', $methods) . '</td>';
                } else {
                    $html .= '<td class="comparison-missing">❌ 无运费方式</td>';
                }
            }
            $html .= '</tr>';
            
            $html .= '</tbody></table>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * 生成备注比较
     */
    private function generate_notes_comparison($comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>📝 订单备注比较</h4>";
        
        $has_notes = false;
        foreach ($comparison_data as $order_data) {
            if ($order_data && $order_data['exists'] && !empty($order_data['order_notes'])) {
                $has_notes = true;
                break;
            }
        }
        
        if (!$has_notes) {
            $html .= "<p>没有订单备注。</p>";
        } else {
            $html .= '<table class="order-comparison-table">';
            $html .= '<thead><tr>';
            $html .= '<th>数据库</th>';
            $html .= '<th>备注数量</th>';
            $html .= '<th>最新备注</th>';
            $html .= '</tr></thead><tbody>';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $html .= '<tr>';
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<td>' . $status_icon . ' ' . esc_html($db_name) . '</td>';
                
                if ($order_data && $order_data['exists'] && !empty($order_data['order_notes'])) {
                    $notes = $order_data['order_notes'];
                    $html .= '<td class="comparison-match">' . count($notes) . ' 条备注</td>';
                    $latest_note = reset($notes);
                    $html .= '<td class="comparison-match">';
                    $html .= '<strong>时间:</strong> ' . $latest_note['date_created'] . '<br>';
                    $html .= '<strong>内容:</strong> ' . esc_html(substr($latest_note['content'], 0, 100));
                    if (strlen($latest_note['content']) > 100) {
                        $html .= '...';
                    }
                    $html .= '</td>';
                } else {
                    $html .= '<td class="comparison-missing">0 条备注</td>';
                    $html .= '<td class="comparison-missing">❌ 无备注</td>';
                }
                
                $html .= '</tr>';
            }
            
            $html .= '</tbody></table>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * 生成元数据比较
     */
    private function generate_metadata_comparison($comparison_data) {
        $html = "<div class='comparison-section'>";
        $html .= "<h4>🔧 元数据比较</h4>";
        
        // 收集所有元数据键
        $all_meta_keys = [];
        foreach ($comparison_data as $order_data) {
            if ($order_data && $order_data['exists'] && !empty($order_data['meta_data'])) {
                $all_meta_keys = array_merge($all_meta_keys, array_keys($order_data['meta_data']));
            }
        }
        $all_meta_keys = array_unique($all_meta_keys);
        
        if (empty($all_meta_keys)) {
            $html .= "<p>没有元数据。</p>";
        } else {
            $html .= '<table class="order-comparison-table">';
            $html .= '<thead><tr>';
            $html .= '<th>元数据键</th>';
            
            foreach ($comparison_data as $db_name => $order_data) {
                $status_icon = ($order_data && $order_data['exists']) ? '✅' : '❌';
                $html .= '<th>' . $status_icon . ' ' . esc_html($db_name) . '</th>';
            }
            
            $html .= '</tr></thead><tbody>';
            
            foreach ($all_meta_keys as $meta_key) {
                $html .= '<tr>';
                $html .= '<td><strong>' . esc_html($meta_key) . '</strong></td>';
                
                $values = [];
                foreach ($comparison_data as $db_name => $order_data) {
                    if ($order_data && $order_data['exists'] && isset($order_data['meta_data'][$meta_key])) {
                        $value = $order_data['meta_data'][$meta_key];
                        $values[] = $value;
                    } else {
                        $value = null;
                        $values[] = null;
                    }
                }
                
                // 检查是否所有值相同
                $unique_values = array_filter(array_unique($values), function($v) { return $v !== null; });
                $all_same = count($unique_values) <= 1;
                
                foreach ($values as $index => $value) {
                    if ($value !== null) {
                        $class = $all_same ? 'comparison-match' : 'comparison-mismatch';
                        $display_value = is_array($value) ? json_encode($value) : $value;
                        $display_value = strlen($display_value) > 50 ? substr($display_value, 0, 50) . '...' : $display_value;
                        $html .= '<td class="' . $class . '">' . esc_html($display_value) . '</td>';
                    } else {
                        $html .= '<td class="comparison-missing">❌ 不存在</td>';
                    }
                }
                
                $html .= '</tr>';
            }
            
            $html .= '</tbody></table>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * 获取比较样式
     */
    private function get_comparison_styles() {
        return '<style>
        .order-comparison-container {
            max-width: 100%;
            margin: 20px 0;
        }
        
        .comparison-summary {
            background: #f0f8ff;
            border: 1px solid #0073aa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .comparison-section {
            margin: 25px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #fff;
        }
        
        .comparison-section h4 {
            margin: 0 0 15px 0;
            color: #0073aa;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 5px;
        }
        
        .order-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .order-comparison-table th,
        .order-comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        .order-comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .field-column {
            width: 200px;
            background-color: #f9f9f9 !important;
        }
        
        .field-label {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        
        .db-column {
            min-width: 150px;
        }
        
        .comparison-match {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .comparison-mismatch {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .comparison-missing {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        
        .order-comparison-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .order-comparison-table td:first-child {
            font-weight: bold;
        }
        
        /* 商品比较专用样式 */
        .items-comparison .item-info-column {
            width: 250px;
            background-color: #f0f8ff !important;
        }
        
        .item-info {
            background-color: #f0f8ff;
        }
        
        .item-details {
            font-size: 12px;
            color: #666;
        }
        
        .item-details-cell {
            font-size: 12px;
        }
        
        .item-stat {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
        }
        
        .item-stat .value {
            font-weight: bold;
            color: #0073aa;
        }
        
        .item-meta {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }
        
        /* 数据库商品网格布局 */
        .database-items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .database-items-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .database-items-card h6 {
            margin: 0 0 15px 0;
            padding: 10px;
            background: #0073aa;
            color: white;
            border-radius: 4px;
            text-align: center;
        }
        
        .no-order, .no-items {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        
        .items-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .item-row {
            display: flex;
            align-items: flex-start;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #0073aa;
        }
        
        .item-number {
            background: #0073aa;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .item-content {
            flex: 1;
        }
        
        .item-name {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .item-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            margin: 5px 0;
        }
        
        .info-item {
            font-size: 11px;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .item-ids {
            font-size: 11px;
            color: #666;
            margin: 5px 0;
        }
        
        .item-meta-info {
            font-size: 11px;
            color: #0073aa;
            margin-top: 5px;
        }
        
        .items-summary {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        
        .summary-stat {
            margin: 3px 0;
            font-size: 12px;
        }
        
        .comparison-section h5 {
            margin: 20px 0 10px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        /* 比较结果头部样式 */
        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #0073aa;
        }
        
        .comparison-header h3 {
            margin: 0;
            color: #0073aa;
        }
        
        .comparison-actions {
            display: flex;
            gap: 10px;
        }
        
        .comparison-actions .button {
            font-size: 13px;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .comparison-actions .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .comparison-actions .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 成功消息样式 */
        .notice {
            border-left: 4px solid #ddd;
            padding: 12px;
            margin: 10px 0;
            background: #fff;
            border-radius: 0 4px 4px 0;
        }
        
        .notice-success {
            border-left-color: #46b450;
            background-color: #f7fcf0;
        }
        
        .notice-error {
            border-left-color: #dc3232;
            background-color: #fbeaea;
        }
        
        .notice-dismiss {
            float: right;
            padding: 9px 15px;
            margin: -9px -15px;
            background: none;
            border: none;
            cursor: pointer;
            color: #72777c;
        }
        
        .notice-dismiss:hover {
            color: #c00;
        }
        </style>';
    }
    private function generate_search_results_html($search_results, $include_meta) {
        $html = "<h3>跨数据库搜索结果</h3>";
        
        foreach ($search_results as $db_name => $orders) {
            $html .= "<h4>数据库: {$db_name} (" . count($orders) . " 个订单)</h4>";
            
            if (empty($orders)) {
                $html .= "<p>未找到订单。</p>";
                continue;
            }
            
            $html .= '<table class="widefat">';
            $html .= '<thead><tr>';
            $html .= '<th>订单ID</th><th>状态</th><th>总额</th><th>创建时间</th><th>客户邮箱</th><th>支付方式</th><th>操作</th>';
            $html .= '</tr></thead><tbody>';
            
            foreach ($orders as $order) {
                $html .= '<tr>';
                $html .= '<td>#' . $order['id'] . '</td>';
                $html .= '<td>' . esc_html($order['status']) . '</td>';
                $html .= '<td>' . esc_html($order['total']) . '</td>';
                $html .= '<td>' . esc_html($order['date_created']) . '</td>';
                $html .= '<td>' . esc_html($order['billing_email']) . '</td>';
                $html .= '<td>' . esc_html($order['payment_method']) . '</td>';
                $html .= '<td><button class="button button-small compare-order" data-order-id="' . $order['id'] . '" onclick="compareOrder(' . $order['id'] . ')">比较</button></td>';
                $html .= '</tr>';
            }
            
            $html .= '</tbody></table>';
        }
        
        // 添加比较功能的JavaScript
        $html .= '<script>
        function compareOrder(orderId) {
            if (!orderId) {
                alert("订单ID无效");
                return;
            }
            
            console.log("Starting order comparison for order:", orderId);
            
            // 创建比较结果显示区域
            var resultsDiv = document.getElementById("comparison-results");
            if (!resultsDiv) {
                resultsDiv = document.createElement("div");
                resultsDiv.id = "comparison-results";
                resultsDiv.style.marginTop = "20px";
                resultsDiv.style.border = "1px solid #ddd";
                resultsDiv.style.padding = "15px";
                resultsDiv.style.borderRadius = "5px";
                resultsDiv.style.backgroundColor = "#f9f9f9";
                document.querySelector(".search-results").appendChild(resultsDiv);
            }
            
            resultsDiv.innerHTML = "<div class=\\"spinner is-active\\"></div> 正在比较订单 #" + orderId + "...";
            
            jQuery.ajax({
                url: ajaxurl,
                type: "POST",
                data: {
                    action: "yxjto_compare_order",
                    order_id: orderId,
                    nonce: "' . wp_create_nonce('yxjto_compare_order') . '"
                },
                success: function(response) {
                    console.log("Order comparison response:", response);
                    if (response.success) {
                        resultsDiv.innerHTML = response.data;
                    } else {
                        resultsDiv.innerHTML = "<div class=\\"notice notice-error\\"><p>比较失败: " + (response.data || "未知错误") + "</p></div>";
                    }
                },
                error: function(xhr, status, error) {
                    console.log("Order comparison failed:", {
                        orderId: orderId,
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    resultsDiv.innerHTML = "<div class=\\"notice notice-error\\"><p>比较请求失败: " + status + " - " + error + "</p></div>";
                }
            });
        }
        </script>';
        
        return $html;
    }
    
    /**
     * 生成独立的HTML文件内容
     */
    private function generate_standalone_html($comparison_html, $order_id) {
        $current_time = current_time('Y-m-d H:i:s');
        $site_name = get_bloginfo('name');
        $site_url = get_site_url();
        
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单比较报告 #' . $order_id . ' - ' . esc_html($site_name) . '</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f1f1f1;
            color: #333;
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .report-header {
            background: linear-gradient(135deg, #0073aa, #005a87);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .report-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .report-meta {
            margin-top: 15px;
            opacity: 0.9;
        }
        
        .report-content {
            padding: 30px;
        }
        
        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #0073aa;
        }
        
        .comparison-actions {
            display: flex;
            gap: 10px;
        }
        
        .button {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            border: 1px solid #ccc;
            background: #f7f7f7;
            color: #333;
            cursor: pointer;
            font-size: 13px;
        }
        
        .button-primary {
            background: #0073aa;
            border-color: #0073aa;
            color: white;
        }
        
        .comparison-summary {
            background: #f0f8ff;
            border: 1px solid #0073aa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .comparison-section {
            margin: 25px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #fff;
        }
        
        .comparison-section h4 {
            margin: 0 0 15px 0;
            color: #0073aa;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 5px;
        }
        
        .comparison-section h5 {
            margin: 20px 0 10px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .order-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .order-comparison-table th,
        .order-comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        .order-comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .field-column {
            width: 200px;
            background-color: #f9f9f9 !important;
        }
        
        .field-label {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        
        .db-column {
            min-width: 150px;
        }
        
        .comparison-match {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .comparison-mismatch {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .comparison-missing {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        
        .items-comparison .item-info-column {
            width: 250px;
            background-color: #f0f8ff !important;
        }
        
        .item-info {
            background-color: #f0f8ff;
        }
        
        .item-details {
            font-size: 12px;
            color: #666;
        }
        
        .item-details-cell {
            font-size: 12px;
        }
        
        .item-stat {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
        }
        
        .item-stat .value {
            font-weight: bold;
            color: #0073aa;
        }
        
        .item-meta {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }
        
        .database-items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .database-items-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .database-items-card h6 {
            margin: 0 0 15px 0;
            padding: 10px;
            background: #0073aa;
            color: white;
            border-radius: 4px;
            text-align: center;
        }
        
        .no-order, .no-items {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        
        .items-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .item-row {
            display: flex;
            align-items: flex-start;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #0073aa;
        }
        
        .item-number {
            background: #0073aa;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .item-content {
            flex: 1;
        }
        
        .item-name {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .item-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            margin: 5px 0;
        }
        
        .info-item {
            font-size: 11px;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .item-ids {
            font-size: 11px;
            color: #666;
            margin: 5px 0;
        }
        
        .item-meta-info {
            font-size: 11px;
            color: #0073aa;
            margin-top: 5px;
        }
        
        .items-summary {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        
        .summary-stat {
            margin: 3px 0;
            font-size: 12px;
        }
        
        .report-footer {
            background: #f8f9fa;
            padding: 20px 30px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .report-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .comparison-actions {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>🔍 订单跨数据库比较报告</h1>
            <div class="report-meta">
                <div>📝 订单编号: #' . $order_id . '</div>
                <div>🕒 生成时间: ' . $current_time . '</div>
                <div>🌐 网站: ' . esc_html($site_name) . ' (' . $site_url . ')</div>
            </div>
        </div>
        
        <div class="report-content">
            ' . $comparison_html . '
        </div>
        
        <div class="report-footer">
            <p>此报告由 YXJTO Gateway 插件自动生成 | 生成时间: ' . $current_time . '</p>
            <p>如需技术支持，请联系系统管理员</p>
        </div>
    </div>
    
    <script>
        function printComparison() {
            window.print();
        }
        
        // 移除保存按钮的onclick事件，因为在独立HTML中不需要
        document.addEventListener("DOMContentLoaded", function() {
            var saveBtn = document.getElementById("save-comparison-html");
            if (saveBtn) {
                saveBtn.onclick = function() {
                    alert("此文件已经是保存的HTML版本");
                    return false;
                };
                saveBtn.style.opacity = "0.5";
                saveBtn.style.cursor = "not-allowed";
            }
        });
    </script>
</body>
</html>';
        
        return $html;
    }
}

// 在管理页面时初始化AJAX处理器
if (is_admin()) {
    // 创建全局实例以注册AJAX处理器
    global $yxjto_order_comparison;
    if (!isset($yxjto_order_comparison)) {
        $yxjto_order_comparison = new YXJTO_Order_Comparison();
    }
}
