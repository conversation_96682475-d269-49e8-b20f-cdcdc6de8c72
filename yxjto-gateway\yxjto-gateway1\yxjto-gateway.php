<?php
/**
 * Plugin Name: YXJTO Gateway
 * Plugin URI: https://github.com/yxjto/yxjto-gateway
 * Description: YXJTO Gateway - 统一支付网关插件，支持多数据库和PayPal集成
 * Version: 1.0.0
 * Author: yxjto
 * Author URI: https://yxjto.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: yxjto-gateway
 * Domain Path: /languages
 * Requires at least: 6.8
 * Tested up to: 6.8
 * Requires PHP: 8.2
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('YXJTO_GATEWAY_VERSION', '1.0.0');
define('YXJTO_GATEWAY_PLUGIN_FILE', __FILE__);

// 兼容非WordPress环境
if (function_exists('plugin_dir_path')) {
    define('YXJTO_GATEWAY_PLUGIN_DIR', plugin_dir_path(__FILE__));
} else {
    define('YXJTO_GATEWAY_PLUGIN_DIR', dirname(__FILE__) . '/');
}

if (function_exists('plugin_dir_url')) {
    define('YXJTO_GATEWAY_PLUGIN_URL', plugin_dir_url(__FILE__));
} else {
    define('YXJTO_GATEWAY_PLUGIN_URL', 'http://example.com/wp-content/plugins/' . basename(dirname(__FILE__)) . '/');
}

if (function_exists('plugin_basename')) {
    define('YXJTO_GATEWAY_PLUGIN_BASENAME', plugin_basename(__FILE__));
} else {
    define('YXJTO_GATEWAY_PLUGIN_BASENAME', basename(dirname(__FILE__)) . '/' . basename(__FILE__));
}

// 调试模式
define('YXJTO_GATEWAY_DEBUG', true);

// PayPal Multi Gateway 常量定义
define('YXJTO_PAYPAL_MULTI_GATEWAY_VERSION', '1.0.0');

/**
 * 安全包含文件函数
 * 
 * @param string $file_path 文件路径
 * @param bool $required 是否必需文件（true: require_once, false: include_once）
 * @return bool 是否成功包含文件
 */
function yxjto_safe_include_file($file_path, $required = true) {
    if (file_exists($file_path)) {
        if ($required) {
            require_once $file_path;
        } else {
            include_once $file_path;
        }
        return true;
    } else {
        $error_message = sprintf('YXJTO Gateway: File not found - %s', $file_path);
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log($error_message);
        }
        
        // 如果是必需文件且不存在，显示管理员通知
        if ($required && is_admin()) {
            add_action('admin_notices', function() use ($file_path) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(__('YXJTO Gateway: 必需文件不存在 - %s', 'yxjto-gateway'), esc_html($file_path));
                echo '</p></div>';
            });
        }
        
        return false;
    }
}
define('YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_DIR', YXJTO_GATEWAY_PLUGIN_DIR);
define('YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL', YXJTO_GATEWAY_PLUGIN_URL);
define('YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_BASENAME', YXJTO_GATEWAY_PLUGIN_BASENAME);

/**
 * 主插件类
 */
class YXJTO_Gateway {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 当前数据库配置
     */
    private $current_database = 'default';
    
    /**
     * 数据库配置
     */
    private $database_configs = [];
    
    /**
     * 数据库连接池
     */
    private $connections = [];
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * 初始化插件
     */
    private function init() {
        // 加载配置管理器
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/config-manager.php';

        // 初始化配置管理器
        WP_Multi_DB_Config_Manager::init();

        // 加载语言管理器
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/language-manager.php';

        // 加载爬虫检测器
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/crawler-detector.php';

        // 加载税收复制功能
        if (file_exists(YXJTO_GATEWAY_PLUGIN_DIR . 'includes/tax-replication.php')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/tax-replication.php';
        }

        // 加载税务复制管理类（AJAX支持需要）
        if (file_exists(YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-tax-replication-admin.php')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-tax-replication-admin.php';
        }

        // 初始化PayPal Multi Gateway
        $this->init_paypal_multi_gateway();

        // 加载配置
        $this->load_database_configs();

        // 注册钩子
        add_action('init', [$this, 'early_init']);
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);


        // 在管理后台时初始化管理页面AJAX处理器
        if (is_admin()) {
            add_action('admin_init', [$this, 'init_admin_handlers']);
        }

        // 注册激活和停用钩子
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        // 加载文本域（延迟到plugins_loaded之后）
        add_action('init', [$this, 'load_textdomain'], 10);

        // 早期初始化数据库切换
        $this->maybe_switch_database();

        // 注册AJAX处理器（需要在早期注册）
        $this->register_ajax_handlers();

        // 注册用户管理钩子
        $this->register_user_management_hooks();

        // 初始化订单复制功能
        $this->init_order_replication();
    }
    
    /**
     * 注册AJAX处理器
     */
    private function register_ajax_handlers() {
        // 基础AJAX处理器
        add_action('wp_ajax_yxjto_gateway_test_connection', [$this, 'ajax_test_connection']);
        add_action('wp_ajax_yxjto_gateway_switch_database', [$this, 'ajax_switch_database']);
        add_action('wp_ajax_yxjto_gateway_clear_logs', [$this, 'ajax_clear_logs']);

        // PayPal相关AJAX处理器
        add_action('wp_ajax_yxjto_enable_payment_gateway', [$this, 'ajax_enable_payment_gateway']);
        add_action('wp_ajax_yxjto_add_test_account', [$this, 'ajax_add_test_account']);
        add_action('wp_ajax_yxjto_cleanup_config', [$this, 'ajax_cleanup_config']);

        // 测试AJAX处理器
        add_action('wp_ajax_yxjto_test_simple', [$this, 'ajax_test_simple']);
        add_action('wp_ajax_yxjto_check_ajax_class', [$this, 'ajax_check_ajax_class']);

        // 系统操作AJAX处理器
        add_action('wp_ajax_yxjto_system_operation', [$this, 'handle_system_operation_ajax']);
        
        // 综合测试AJAX处理器
        add_action('wp_ajax_yxjto_comprehensive_test', [$this, 'handle_comprehensive_test_ajax']);
        
        // 配送复制AJAX处理器
        add_action('wp_ajax_yxjto_run_shipping_sync', [$this, 'handle_shipping_sync_ajax']);
        add_action('wp_ajax_yxjto_test_shipping_connections', [$this, 'handle_shipping_test_ajax']);
        add_action('wp_ajax_yxjto_clear_shipping_logs', [$this, 'handle_shipping_logs_ajax']);
        add_action('wp_ajax_yxjto_download_shipping_logs', [$this, 'handle_shipping_logs_download']);
        
        // 税务复制AJAX处理器
        add_action('wp_ajax_yxjto_tax_sync', 'YXJTO_Tax_Replication_Admin::ajax_tax_sync_now');
        add_action('wp_ajax_yxjto_tax_validate', 'YXJTO_Tax_Replication_Admin::ajax_tax_validate_data');
        add_action('wp_ajax_yxjto_tax_backup', 'YXJTO_Tax_Replication_Admin::ajax_tax_backup_data');
        add_action('wp_ajax_yxjto_tax_cleanup', 'YXJTO_Tax_Replication_Admin::ajax_tax_cleanup_data');
        
        // 订单管理AJAX处理器
        add_action('wp_ajax_yxjto_search_orders', [$this, 'ajax_search_orders']);
        add_action('wp_ajax_yxjto_sync_orders', [$this, 'ajax_sync_orders']);
        add_action('wp_ajax_yxjto_cleanup_orders', [$this, 'ajax_cleanup_orders']);
        add_action('wp_ajax_yxjto_update_order_start_id', [$this, 'ajax_update_order_start_id']);
        
        // 订单比较AJAX处理器
        add_action('wp_ajax_yxjto_compare_order', [$this, 'ajax_compare_order_handler']);
        add_action('wp_ajax_yxjto_comparison_search_orders', [$this, 'ajax_comparison_search_orders_handler']);
        add_action('wp_ajax_yxjto_test_database_switch', [$this, 'ajax_test_database_switch_handler']);
        add_action('wp_ajax_yxjto_test_database_connection', [$this, 'ajax_test_database_connection_handler']);
        add_action('wp_ajax_yxjto_save_comparison_html', [$this, 'ajax_save_comparison_html_handler']);
    }

    /**
     * 注册用户管理钩子
     */
    private function register_user_management_hooks() {
        // 从config-manager获取用户管理设置
        $user_management_settings = WP_Multi_DB_Config_Manager::get_user_management_settings();

        // 如果启用了自动复制用户创建
        if (!empty($user_management_settings['auto_copy_user_creation'])) {
            add_action('user_register', [$this, 'auto_copy_user_creation'], 10, 1);
        }

        // 如果启用了自动复制用户会话
        if (!empty($user_management_settings['auto_copy_user_sessions'])) {
            add_action('wp_login', [$this, 'auto_copy_user_session'], 10, 2);
        }

        // 如果启用了自动复制WooCommerce会话
        if (!empty($user_management_settings['auto_copy_woocommerce_sessions']) && class_exists('WooCommerce')) {
            // 监听WooCommerce会话创建和更新
            // add_action('woocommerce_set_cart_cookies', [$this, 'auto_copy_woocommerce_session'], 10, 1);
            // add_action('woocommerce_cart_updated', [$this, 'auto_copy_woocommerce_session_on_cart_update'], 10);
            add_action('woocommerce_checkout_update_user_meta', [$this, 'auto_copy_woocommerce_session_on_checkout'], 10, 2);
        }

        // 注册钩子（保留用于手动调用）
        add_action('yxjto_gateway_delayed_user_copy', [$this, 'delayed_user_copy']);
        add_action('yxjto_gateway_delayed_session_copy', [$this, 'delayed_session_copy']);
    }

    /**
     * 初始化订单复制功能
     */
    private function init_order_replication() {
        // 检查WooCommerce是否激活
        if (!class_exists('WooCommerce')) {
            return;
        }

        // 加载订单复制类
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-order-replication.php';

        // 加载支付数据库切换配置类
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-payment-database-switch-config.php';

        // 初始化订单复制
        YXJTO_Order_Replication::get_instance();

        // 初始化支付数据库切换配置
        YXJTO_Payment_Database_Switch_Config::get_instance();
    }

    /**
     * 初始化管理页面处理器
     */
    public function init_admin_handlers() {
        // 包含管理页面文件
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin-page.php';

        // 包含测试运行器
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-test-runner.php';

        // 包含订单比较功能
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-order-comparison.php';

        // 初始化管理页面AJAX处理器
        if (class_exists('WP_Multi_Database_Admin')) {
            WP_Multi_Database_Admin::init();
        }
    }

    /**
     * 初始化PayPal Multi Gateway
     */
    private function init_paypal_multi_gateway() {
        // 检查WooCommerce是否激活
        if (!class_exists('WooCommerce')) {
            return;
        }

        // 加载PayPal Multi Gateway核心文件
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-core.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-accounts.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-config.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-api.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-payment.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-multi-gateway-ajax.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-transaction-logger.php';
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-paypal-order-record.php';
        
        // 加载优惠券同步功能
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-coupon-sync.php';
        
        // 加载配送复制功能
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-shipping-replication.php';

        // 加载管理页面
        if (is_admin()) {
            $admin_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-paypal-multi-gateway-admin.php';
            $diagnostic_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/diagnostic-tool.php';

            if (file_exists($admin_file)) {
                require_once $admin_file;

                if (file_exists($diagnostic_file)) {
                    require_once $diagnostic_file;
                }

                YXJTO_PayPal_Multi_Gateway_Admin::get_instance();
            } else {
                error_log("YXJTO Gateway: Admin class file not found: " . $admin_file);
                // 添加管理员通知
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>YXJTO Gateway: 管理页面文件缺失，请检查插件文件完整性。</p></div>';
                });
            }

            // 加载并初始化Coupon Sync Admin
            $coupon_admin_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-coupon-sync-admin.php';
            if (file_exists($coupon_admin_file)) {
                require_once $coupon_admin_file;
                YXJTO_Coupon_Sync_Admin::init();
            }

            // 加载并初始化Shipping Sync Admin
            $shipping_admin_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-shipping-sync-admin.php';
            if (file_exists($shipping_admin_file)) {
                require_once $shipping_admin_file;
                YXJTO_Shipping_Sync_Admin::init();
            }
        }

        // 加载WooCommerce Blocks支持
        add_action('woocommerce_blocks_loaded', array($this, 'init_paypal_multi_gateway_blocks'));

        // 初始化核心功能
        YXJTO_PayPal_Multi_Gateway_Core::get_instance();
        
        // 初始化优惠券同步功能
        YXJTO_Coupon_Sync::get_instance();

        // 注册支付网关
        add_filter('woocommerce_payment_gateways', array($this, 'add_paypal_multi_gateway'));

        // 初始化AJAX处理器
        add_action('init', array($this, 'init_ajax_handlers'), 20);

        // 创建数据库表
        add_action('init', array($this, 'create_paypal_multi_gateway_tables'));

        // 创建配置目录
        add_action('init', array($this, 'create_paypal_multi_gateway_config_dir'));
    }

    /**
     * 初始化AJAX处理器
     */
    public function init_ajax_handlers() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Ajax')) {
            // 初始化专门的AJAX处理器
            YXJTO_PayPal_Multi_Gateway_Ajax::get_instance();
        }
    }

    /**
     * 添加PayPal Multi Gateway到WooCommerce支付网关列表
     */
    public function add_paypal_multi_gateway($gateways) {
        $gateways[] = 'YXJTO_PayPal_Multi_Gateway_Payment';
        return $gateways;
    }

    /**
     * 初始化PayPal Multi Gateway WooCommerce Blocks支持
     */
    public function init_paypal_multi_gateway_blocks() {
        if (!class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
            return;
        }

        // 加载Blocks支持类
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/blocks/class-paypal-multi-gateway-blocks.php';

        // 注册Blocks支付方式
        add_action(
            'woocommerce_blocks_payment_method_type_registration',
            function(Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry $payment_method_registry) {
                $payment_method_registry->register(new YXJTO_PayPal_Multi_Gateway_Blocks());
            }
        );
    }

    /**
     * 创建PayPal Multi Gateway数据库表
     */
    public function create_paypal_multi_gateway_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // PayPal账户配置现在存储在配置文件中，不再使用数据库表

        // 交易日志表（保留用于记录交易记录）
        $logs_table = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';
        $logs_sql = "CREATE TABLE $logs_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            transaction_id varchar(255) NOT NULL,
            account_id varchar(255) NOT NULL,
            order_id int(11) NOT NULL,
            amount decimal(10,2) NOT NULL,
            currency varchar(10) NOT NULL,
            status varchar(50) NOT NULL,
            gateway_response longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY transaction_id (transaction_id),
            KEY account_id (account_id),
            KEY order_id (order_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($logs_sql);
    }

    /**
     * 创建PayPal Multi Gateway配置目录
     */
    public function create_paypal_multi_gateway_config_dir() {
        $config_dir = YXJTO_GATEWAY_PLUGIN_DIR . 'config/';

        if (!file_exists($config_dir)) {
            wp_mkdir_p($config_dir);

            // 创建.htaccess文件保护配置目录
            $htaccess_content = "# YXJTO PayPal Multi Gateway Config Protection\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";

            file_put_contents($config_dir . '.htaccess', $htaccess_content);

            // 创建index.php文件防止目录浏览
            $index_content = "<?php\n// Silence is golden.\n";
            file_put_contents($config_dir . 'index.php', $index_content);
        }
    }

    /**
     * 早期初始化
     */
    public function early_init() {
        // 检查是否已经通过wp-config hook切换了数据库
        if (defined('WP_MULTI_DB_SWITCHED') && WP_MULTI_DB_SWITCHED) {
            $this->current_database = WP_MULTI_DB_CURRENT_DATABASE;
            $switch_rule = defined('WP_MULTI_DB_SWITCH_RULE') ? WP_MULTI_DB_SWITCH_RULE : 'Unknown';
            // $this->log_info("Database already switched by wp-config hook to: {$this->current_database} (Rule: {$switch_rule})");
        } else {
            // 处理数据库切换逻辑（兼容模式）
            $this->handle_database_switching();
        }
    }
    
    /**
     * 加载数据库配置
     */
    private function load_database_configs() {
        // 从配置文件加载数据库配置
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        // 如果配置文件中没有数据库配置，使用当前WordPress数据库作为默认配置
        if (empty($databases)) {
            $db_name = defined('DB_NAME') ? DB_NAME : 'wordpress';
            $databases = [
                'default' => [
                    'name' => 'Current WordPress Database (' . $db_name . ')',
                    'host' => defined('DB_HOST') ? DB_HOST : 'localhost',
                    'database' => $db_name,
                    'username' => defined('DB_USER') ? DB_USER : '',
                    'password' => defined('DB_PASSWORD') ? DB_PASSWORD : '',
                    'charset' => defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4',
                    'collate' => defined('DB_COLLATE') ? DB_COLLATE : '',
                    'enabled' => true
                ]
            ];
            // 保存默认配置到文件
            WP_Multi_DB_Config_Manager::save_databases($databases);
        }

        $this->database_configs = $databases;
    }
    
    /**
     * 可能的数据库切换
     * 优先级：爬虫检测 > URL参数规则 > cookies中的URL参数 > IP地址规则
     */
    private function maybe_switch_database() {
        // 调试：测试 cookies 功能
        if (isset($_GET['test_cookies'])) {
            $this->test_cookie_functionality();
        }

        // 获取访客信息
        $client_ip = $this->get_client_ip();
        $current_url = $this->get_current_url();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        // 首先检查爬虫检测（优先级最高）
        $crawler_detector = WP_Multi_Database_Crawler_Detector::get_instance();
        if ($crawler_detector->is_crawler()) {
            // 爬虫始终使用默认数据库
            $this->switch_database('default');
            // $this->log_info("Database switched to default for crawler: " . substr($user_agent, 0, 100));
            return;
        }

        // 然后检查URL参数切换（优先级最高）
        $url_database = $this->check_url_based_switching();
        if ($url_database) {
            // URL参数匹配，设置到cookies中
            $this->set_url_parameter_cookie($url_database);
            $this->switch_database($url_database);
            return;
        }

        // 然后检查cookies中的URL参数（优先级中等）
        $cookie_database = $this->check_cookie_based_switching();
        if ($cookie_database) {
            $this->switch_database($cookie_database);
            return;
        }

        // 最后检查IP地址切换（优先级最低）
        $ip_database = $this->check_ip_based_switching();
        if ($ip_database) {
            $this->switch_database($ip_database);
            return;
        }

        // 输出 cookies 调试信息到控制台（仅在前端）
        // 注释掉此功能以避免潜在的访问级别冲突
        // if (!is_admin() && !$this->is_console_output_disabled()) {
        //     add_action('wp_footer', [$this, 'output_cookie_debug_info']);
        // }
    }
    
    /**
     * 检查基于IP的数据库切换
     * 优先级：最低（低于URL参数规则和cookies规则）
     */
    private function check_ip_based_switching() {
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();
        $client_ip = $this->get_client_ip();

        if (empty($ip_rules)) {
            // $this->log_info("No IP rules configured");
            return false;
        }

        // $this->log_info("Checking IP rules for client IP: {$client_ip}, total rules: " . count($ip_rules));

        foreach ($ip_rules as $index => $rule) {
            if (!$rule['enabled']) {
                // $this->log_info("IP rule {$index} is disabled, skipping");
                continue;
            }

            // $this->log_info("Checking IP rule {$index}: range '{$rule['ip_range']}' against client IP '{$client_ip}'");

            if ($this->ip_matches_rule($client_ip, $rule)) {
                // $this->log_info("IP rule {$index} matched! Switching to database: {$rule['database']}");
                return $rule['database'];
            }
        }

        // $this->log_info("No IP rules matched for client IP: {$client_ip}");
        return false;
    }
    
    /**
     * 检查基于URL参数的数据库切换
     * 优先级：最高（高于cookies和IP规则）
     */
    private function check_url_based_switching() {
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();

        if (empty($url_rules)) {
            $this->log_info("No URL rules configured");
            return false;
        }

        $this->log_info("Checking URL parameter rules, total rules: " . count($url_rules));

        foreach ($url_rules as $index => $rule) {
            if (!$rule['enabled']) {
                // $this->log_info("URL rule {$index} is disabled, skipping");
                continue;
            }
            
            $this->log_info(home_url(add_query_arg(array())));
            $param_name = $rule['parameter'];
            $expected_value = $rule['value'];
            $param_value = isset($_GET[$param_name]) ? sanitize_text_field($_GET[$param_name]) : '';

            $this->log_info("Checking URL rule {$index}: parameter '{$param_name}' = '{$param_value}', expected '{$expected_value}'");

            if ($param_value && $param_value === $expected_value) {
                // 安全验证
                if ($this->validate_url_parameter($rule, $param_value)) {
                    $this->log_info("URL rule {$index} matched! Switching to database: {$rule['database']}");
                    return $rule['database'];
                } else {
                    $this->log_error("URL rule {$index} matched but failed security validation");
                }
            }
        }

        // $this->log_info("No URL rules matched");
        return false;
    }

    /**
     * 检查基于cookies的数据库切换
     * 优先级：高于IP规则，低于URL参数规则
     */
    private function check_cookie_based_switching() {
        // 首先检查是否有全局清除标记
        $this->check_and_clear_expired_cookies();

        // 检查是否存在数据库切换相关的cookie
        $cookie_name = 'yxjto_gateway_param';

        if (!isset($_COOKIE[$cookie_name])) {
            $this->log_info("No database parameter cookie found");
            return false;
        }

        $cookie_data = $this->parse_cookie_data($_COOKIE[$cookie_name]);
        if (!$cookie_data) {
            $this->log_info("Invalid cookie data format");
            return false;
        }

        // 检查cookie是否过期
        if ($this->is_cookie_expired($cookie_data)) {
            // 清除过期的cookie
            $this->clear_url_parameter_cookie();
            // $this->log_info("Database parameter cookie expired and cleared");
            return false;
        }

        // 验证cookie中的数据库参数
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
        if (empty($url_rules)) {
            // $this->log_info("No URL rules configured for cookie validation");
            return false;
        }

        foreach ($url_rules as $index => $rule) {
            if (!$rule['enabled']) {
                continue;
            }

            // 检查cookie中的参数是否匹配某个规则
            if (isset($cookie_data['param']) && isset($cookie_data['value']) &&
                $cookie_data['param'] === $rule['parameter'] &&
                $cookie_data['value'] === $rule['value']) {

                // 验证安全性
                if ($this->validate_url_parameter($rule, $cookie_data['value'])) {
                    $this->log_info("Cookie-based database switching matched rule {$index}: {$rule['database']}");
                    return $rule['database'];
                } else {
                    $this->log_error("Cookie-based rule {$index} matched but failed security validation");
                }
            }
        }

        // $this->log_info("No cookie-based rules matched");
        return false;
    }

    /**
     * 设置URL参数到cookie
     */
    private function set_url_parameter_cookie($database_name) {
        $this->log_info("Setting URL parameter cookie for database: {$database_name}");

        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
        $this->log_info("Found " . count($url_rules) . " URL rules for cookie setting");

        // 找到匹配的URL规则
        foreach ($url_rules as $rule) {
            if (!$rule['enabled']) {
                continue;
            }

            $param_name = $rule['parameter'];
            $expected_value = $rule['value'];
            $param_value = isset($_GET[$param_name]) ? sanitize_text_field($_GET[$param_name]) : '';

            $this->log_info("Checking rule for cookie: param={$param_name}, expected={$expected_value}, actual={$param_value}, database={$rule['database']}");

            if ($param_value && $param_value === $expected_value && $rule['database'] === $database_name) {
                // 获取配置的过期时间
                $expire_hours = $this->get_cookie_expire_hours();
                $expire_seconds = $this->get_cookie_expire_seconds();

                // 创建cookie数据
                $cookie_data = array(
                    'param' => $param_name,
                    'value' => $param_value,
                    'database' => $database_name,
                    'timestamp' => time(),
                    'expires' => time() + $expire_seconds,
                    'expire_hours' => $expire_hours // 记录过期小时数，用于调试
                );

                $cookie_value = $this->encode_cookie_data($cookie_data);
                $cookie_name = 'yxjto_gateway_param';

                // 设置cookie
                $expire_time = time() + $expire_seconds;
                $cookie_path = '/';
                $cookie_domain = '';
                $secure = is_ssl();
                $httponly = true;

                setcookie($cookie_name, $cookie_value, $expire_time, $cookie_path, $cookie_domain, $secure, $httponly);

                $this->log_info("Set database parameter cookie: {$param_name}={$param_value} -> {$database_name} (expires in {$expire_hours} hours)");
                break;
            }
        }
    }

    /**
     * 清除URL参数cookie
     */
    private function clear_url_parameter_cookie() {
        $cookie_name = 'yxjto_gateway_param';
        setcookie($cookie_name, '', time() - 3600, '/');
        // $this->log_info("Cleared database parameter cookie");
    }

    /**
     * 编码cookie数据
     */
    private function encode_cookie_data($data) {
        // 使用base64编码和简单的校验和
        $json_data = json_encode($data);
        $checksum = md5($json_data . 'yxjto_gateway_salt');
        $encoded_data = base64_encode($json_data . '|' . $checksum);
        return $encoded_data;
    }

    /**
     * 解析cookie数据
     */
    private function parse_cookie_data($cookie_value) {
        try {
            // 首先尝试base64解码
            $decoded_data = base64_decode($cookie_value);
            if (!$decoded_data) {
                $this->log_error("Cookie base64 decode failed");
                return false;
            }

            // 检查是否包含管道符（带哈希的格式）
            if (strpos($decoded_data, '|') !== false) {
                $parts = explode('|', $decoded_data);

                if (count($parts) === 2) {
                    // 格式: json_data|hash
                    $json_data = $parts[0];
                    $checksum = $parts[1];

                    // 验证校验和
                    $expected_checksum = md5($json_data . 'yxjto_gateway_salt');
                    if ($checksum !== $expected_checksum) {
                        $this->log_error("Cookie data checksum validation failed");
                        // 尝试不验证校验和，直接解析JSON
                        $data = json_decode($json_data, true);
                        if ($data && is_array($data)) {
                            $this->log_info("Cookie parsed without checksum validation");
                            return $this->validate_cookie_fields($data);
                        }
                        return false;
                    }

                    $data = json_decode($json_data, true);
                    if (!$data || !is_array($data)) {
                        $this->log_error("Cookie JSON decode failed");
                        return false;
                    }

                    return $this->validate_cookie_fields($data);
                } else {
                    // 可能是其他格式，尝试直接解析为JSON
                    $data = json_decode($decoded_data, true);
                    if ($data && is_array($data)) {
                        $this->log_info("Cookie parsed as direct JSON");
                        return $this->validate_cookie_fields($data);
                    }
                    return false;
                }
            } else {
                // 没有管道符，尝试直接解析为JSON
                $data = json_decode($decoded_data, true);
                if ($data && is_array($data)) {
                    $this->log_info("Cookie parsed as simple JSON");
                    return $this->validate_cookie_fields($data);
                }
                return false;
            }
        } catch (Exception $e) {
            $this->log_error("Error parsing cookie data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证Cookie字段
     */
    private function validate_cookie_fields($data) {
        if (!$data || !is_array($data)) {
            return false;
        }

        // 验证必要的字段
        $required_fields = ['param', 'value', 'database', 'timestamp'];
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                $this->log_error("Cookie missing required field: {$field}");
                return false;
            }
        }

        // 如果没有expires字段，根据timestamp计算
        if (!isset($data['expires'])) {
            $data['expires'] = $data['timestamp'] + (24 * 60 * 60); // 默认24小时
            $this->log_info("Cookie expires field added based on timestamp");
        }

        return $data;
    }

    /**
     * 检查cookie是否过期
     */
    private function is_cookie_expired($cookie_data) {
        $current_time = time();
        return $current_time > $cookie_data['expires'];
    }

    /**
     * 检查并清除过期的cookies
     */
    private function check_and_clear_expired_cookies() {
        // 检查是否有全局清除标记
        $clear_timestamp = get_option('yxjto_gateway_clear_cookies_timestamp', 0);

        if ($clear_timestamp > 0) {
            $cookie_name = 'yxjto_gateway_param';

            // 检查当前用户的cookie是否在清除时间戳之前创建
            if (isset($_COOKIE[$cookie_name])) {
                $cookie_data = $this->parse_cookie_data($_COOKIE[$cookie_name]);

                if ($cookie_data && isset($cookie_data['timestamp'])) {
                    // 如果cookie创建时间早于清除时间戳，则清除它
                    if ($cookie_data['timestamp'] < $clear_timestamp) {
                        $this->clear_url_parameter_cookie();
                        $this->log_info("Cookie cleared due to global clear timestamp");
                    }
                } else {
                    // 如果无法解析cookie数据，也清除它
                    $this->clear_url_parameter_cookie();
                    $this->log_info("Invalid cookie cleared due to global clear timestamp");
                }
            }
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * 获取当前完整URL
     */
    private function get_current_url() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';

        return $protocol . '://' . $host . $uri;
    }












    
    /**
     * 检查IP是否匹配规则
     */
    private function ip_matches_rule($ip, $rule) {
        $rule_ip = $rule['ip_range'];
        
        // 单个IP
        if (filter_var($rule_ip, FILTER_VALIDATE_IP)) {
            return $ip === $rule_ip;
        }
        
        // CIDR格式
        if (strpos($rule_ip, '/') !== false) {
            return $this->ip_in_cidr($ip, $rule_ip);
        }
        
        // IP范围 (***********-*************)
        if (strpos($rule_ip, '-') !== false) {
            list($start_ip, $end_ip) = explode('-', $rule_ip);
            return $this->ip_in_range($ip, trim($start_ip), trim($end_ip));
        }
        
        return false;
    }
    
    /**
     * 检查IP是否在CIDR范围内
     */
    private function ip_in_cidr($ip, $cidr) {
        list($subnet, $mask) = explode('/', $cidr);
        return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
    }
    
    /**
     * 检查IP是否在范围内
     */
    private function ip_in_range($ip, $start_ip, $end_ip) {
        return ip2long($ip) >= ip2long($start_ip) && ip2long($ip) <= ip2long($end_ip);
    }
    
    /**
     * 验证URL参数
     * 增强安全检查，防止恶意参数
     */
    private function validate_url_parameter($rule, $value) {
        // 基本安全检查
        if (strlen($value) > 50) {
            $this->log_error("URL parameter value too long: " . strlen($value) . " characters");
            return false;
        }

        // 只允许字母、数字、下划线和连字符
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $value)) {
            $this->log_error("URL parameter contains invalid characters: {$value}");
            return false;
        }

        // 检查数据库是否存在且已启用
        $database_configs = WP_Multi_DB_Config_Manager::get_databases();
        if (!isset($database_configs[$rule['database']])) {
            $this->log_error("Target database not found in configuration: {$rule['database']}");
            return false;
        }

        if (!$database_configs[$rule['database']]['enabled']) {
            $this->log_error("Target database is disabled: {$rule['database']}");
            return false;
        }

        // 检查是否在允许的值列表中（如果配置了）
        if (!empty($rule['allowed_values'])) {
            if (!in_array($value, $rule['allowed_values'])) {
                $this->log_error("URL parameter value not in allowed list: {$value}");
                return false;
            }
        }

        // 防止SQL注入等攻击
        $dangerous_patterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+set/i',
            '/<script/i',
            '/javascript:/i'
        ];

        foreach ($dangerous_patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                $this->log_error("URL parameter contains dangerous pattern: {$value}");
                return false;
            }
        }

        $this->log_info("URL parameter validation passed for value: {$value}");
        return true;
    }
    
    /**
     * 切换数据库
     */
    public function switch_database($database_name) {
        if (!isset($this->database_configs[$database_name])) {
            $this->log_error("Database configuration not found: {$database_name}");
            return false;
        }
        
        $config = $this->database_configs[$database_name];
        
        if (!$config['enabled']) {
            $this->log_error("Database is disabled: {$database_name}");
            return false;
        }
        
        // 触发切换前钩子
        do_action('wp_multi_db_before_switch', $this->current_database, $database_name);
        
        try {
            // 测试数据库连接
            if (!$this->test_database_connection($config)) {
                throw new Exception("Failed to connect to database: {$database_name}");
            }
            
            // 更新WordPress数据库配置
            $this->update_wp_database_config($config);
            
            $old_database = $this->current_database;
            $this->current_database = $database_name;

            // 如果存在wp-config常量，记录状态信息（仅用于调试）
            if (defined('WP_MULTI_DB_SWITCHED') && defined('YXJTO_GATEWAY_DEBUG') && YXJTO_GATEWAY_DEBUG) {
                // 注意：这些常量在运行时不能重新定义，状态不同是正常的
                if (defined('WP_MULTI_DB_CURRENT_DATABASE') && WP_MULTI_DB_CURRENT_DATABASE !== $database_name) {
                    $this->log_info("Runtime database switch to {$database_name}, wp-config shows " . WP_MULTI_DB_CURRENT_DATABASE . " (this is normal for dynamic switching)");
                }
            }

            // 触发切换后钩子
            do_action('wp_multi_db_after_switch', $old_database, $database_name);

            // 验证切换是否成功
            $verification = $this->verify_database_switch($database_name);
            if (!$verification['success']) {
                $this->log_error("Database switch verification failed: " . $verification['message']);
            }

            // $this->log_info("Database switched from {$old_database} to {$database_name}");
            $this->log_database_operation('switch', $old_database, $database_name, true, "Successfully switched database");

            return true;

        } catch (Exception $e) {
            $this->log_error("Database switch failed: " . $e->getMessage());
            $this->log_database_operation('switch', $this->current_database, $database_name, false, $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function test_database_connection($config, &$error_message = null) {
        try {
            // 验证配置参数
            $host = $config['host'] ?? 'localhost';
            $username = $config['username'] ?? '';
            $password = $config['password'] ?? '';
            $database = $config['database'] ?? '';

            // 检查必填字段
            if (empty($host)) {
                $error_message = __('Host is required', 'yxjto-gateway');
                return false;
            }
            if (empty($username)) {
                $error_message = __('Username is required', 'yxjto-gateway');
                return false;
            }
            if (empty($database)) {
                $error_message = __('Database name is required', 'yxjto-gateway');
                return false;
            }

            // 尝试连接数据库
            $connection = new mysqli($host, $username, $password, $database);

            if ($connection->connect_error) {
                $error_message = sprintf(
                    __('Connection failed: %s (Error %d)', 'yxjto-gateway'),
                    $connection->connect_error,
                    $connection->connect_errno
                );
                return false;
            }

            // 测试基本查询
            $result = $connection->query("SELECT 1");
            if (!$result) {
                $error_message = sprintf(
                    __('Query test failed: %s', 'yxjto-gateway'),
                    $connection->error
                );
                $connection->close();
                return false;
            }

            $connection->close();
            return true;

        } catch (Exception $e) {
            $error_message = sprintf(
                __('Exception occurred: %s', 'yxjto-gateway'),
                $e->getMessage()
            );
            return false;
        }
    }
    
    /**
     * 更新WordPress数据库配置
     */
    private function update_wp_database_config($config) {
        global $wpdb;
        
        // 保存原始表前缀
        $original_prefix = $wpdb->prefix;
        
        // 重新初始化wpdb连接
        $wpdb->__construct(
            $config['username'],
            $config['password'],
            $config['database'],
            $config['host']
        );
        
        // 设置正确的表前缀
        $prefix = isset($config['prefix']) ? $config['prefix'] : 'wp_';
        $wpdb->set_prefix($prefix);
        
        // 记录表前缀变化
        // $this->log_info("Database prefix changed from '{$original_prefix}' to '{$wpdb->prefix}'");
        
        // 确保WordPress使用新的数据库连接
        wp_cache_flush();
    }
    
    /**
     * 记录信息日志
     */
    private function log_info($message) {
        if (YXJTO_GATEWAY_DEBUG) {
            error_log("[YXJTO Gateway INFO] " . $message);
        }
    }

    /**
     * 记录警告日志
     */
    private function log_warning($message) {
        if (YXJTO_GATEWAY_DEBUG) {
            error_log("[YXJTO Gateway WARNING] " . $message);
        }
    }

    /**
     * 记录错误日志
     */
    private function log_error($message) {
        error_log("[YXJTO Gateway ERROR] " . $message);
    }

    /**
     * 记录数据库操作日志
     */
    private function log_database_operation($action, $from_database = null, $to_database = null, $success = true, $message = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_gateway_logs';

        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return;
        }

        $wpdb->insert(
            $table_name,
            [
                'action' => $action,
                'from_database' => $from_database,
                'to_database' => $to_database,
                'ip_address' => $this->get_client_ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'success' => $success ? 1 : 0,
                'message' => $message,
                'timestamp' => current_time('mysql')
            ],
            [
                '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s'
            ]
        );
    }
    
    /**
     * 处理数据库切换逻辑
     */
    private function handle_database_switching() {
        // 应用过滤器允许自定义数据库选择逻辑
        $selected_database = apply_filters('wp_multi_db_select', $this->current_database);

        if ($selected_database !== $this->current_database) {
            $this->switch_database($selected_database);
        }
    }

    /**
     * 验证数据库切换是否成功
     */
    private function verify_database_switch($expected_database) {
        global $wpdb;

        if (!$wpdb || !method_exists($wpdb, 'get_var')) {
            return [
                'success' => false,
                'message' => 'WordPress database connection not available for verification'
            ];
        }

        try {
            // 获取实际连接的数据库名称
            $actual_db = $wpdb->get_var("SELECT DATABASE()");

            if (!$actual_db) {
                return [
                    'success' => false,
                    'message' => 'Could not retrieve actual database name'
                ];
            }

            // 获取期望的数据库配置
            if (!isset($this->database_configs[$expected_database])) {
                return [
                    'success' => false,
                    'message' => "Expected database configuration not found: {$expected_database}"
                ];
            }

            $expected_config = $this->database_configs[$expected_database];
            $expected_db_name = $expected_config['database'];

            if ($actual_db === $expected_db_name) {
                return [
                    'success' => true,
                    'message' => "Database switch verified successfully: {$expected_database} ({$actual_db})"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Database switch verification failed. Expected: {$expected_db_name}, Actual: {$actual_db}"
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Database verification failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取当前数据库
     */
    public function get_current_database() {
        // 优先返回内部状态，因为它反映了实际的数据库切换操作
        // wp-config 常量在运行时是不可变的，不能反映动态切换

        // 检查是否有实际的数据库连接信息来验证当前数据库
        global $wpdb;
        if ($wpdb && method_exists($wpdb, 'get_var')) {
            try {
                // 获取实际连接的数据库名称
                $actual_db = $wpdb->get_var("SELECT DATABASE()");
                if ($actual_db) {
                    // 查找匹配的数据库配置
                    foreach ($this->database_configs as $db_key => $config) {
                        if (isset($config['database']) && $config['database'] === $actual_db) {
                            // 如果找到匹配的配置，但内部状态不一致，更新内部状态
                            if ($this->current_database !== $db_key) {
                                $this->log_info("Updated internal database state based on actual connection: {$db_key} ({$actual_db})");
                                $this->current_database = $db_key;
                            }
                            return $db_key;
                        }
                    }

                    // 如果没有找到匹配的配置，记录警告但仍返回内部状态
                    $this->log_error("Current database connection ({$actual_db}) does not match any configured database. Internal state: {$this->current_database}");
                }
            } catch (Exception $e) {
                $this->log_error("Failed to get actual database name: " . $e->getMessage());
            }
        }

        // 检查wp-config状态是否与内部状态不一致（仅用于警告）
        if (defined('WP_MULTI_DB_SWITCHED') && WP_MULTI_DB_SWITCHED && defined('WP_MULTI_DB_CURRENT_DATABASE')) {
            if ($this->current_database !== WP_MULTI_DB_CURRENT_DATABASE) {
                // 这是正常情况：wp-config在启动时设置，但运行时可能会切换到其他数据库
                // 只在调试模式下记录这个信息
                if (defined('YXJTO_GATEWAY_DEBUG') && YXJTO_GATEWAY_DEBUG) {
                    $this->log_info("Runtime database ({$this->current_database}) differs from wp-config database (" . WP_MULTI_DB_CURRENT_DATABASE . ") - this is normal for dynamic switching");
                }
            }
        }

        return $this->current_database;
    }

    /**
     * 获取数据库列表
     */
    public function get_database_list() {
        return array_keys($this->database_configs);
    }

    /**
     * 获取实际连接的数据库信息
     */
    public function get_actual_database_info() {
        global $wpdb;

        if (!$wpdb || !method_exists($wpdb, 'get_var')) {
            return [
                'success' => false,
                'message' => 'WordPress database connection not available'
            ];
        }

        try {
            $actual_db = $wpdb->get_var("SELECT DATABASE()");
            $host_info = $wpdb->get_var("SELECT @@hostname");
            $user_info = $wpdb->get_var("SELECT USER()");

            return [
                'success' => true,
                'database' => $actual_db,
                'host' => $host_info,
                'user' => $user_info,
                'internal_state' => $this->current_database,
                'wp_config_state' => defined('WP_MULTI_DB_CURRENT_DATABASE') ? WP_MULTI_DB_CURRENT_DATABASE : null,
                'wp_config_switched' => defined('WP_MULTI_DB_SWITCHED') ? WP_MULTI_DB_SWITCHED : false
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get database info: ' . $e->getMessage(),
                'internal_state' => $this->current_database,
                'wp_config_state' => defined('WP_MULTI_DB_CURRENT_DATABASE') ? WP_MULTI_DB_CURRENT_DATABASE : null,
                'wp_config_switched' => defined('WP_MULTI_DB_SWITCHED') ? WP_MULTI_DB_SWITCHED : false
            ];
        }
    }

    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        // 添加顶级菜单 YXJTO
        add_menu_page(
            __('YXJTO', 'yxjto-gateway'),
            __('YXJTO', 'yxjto-gateway'),
            'manage_options',
            'yxjto',
            [$this, 'yxjto_main_page'],
            'dashicons-admin-tools',
            30
        );

        // 添加子菜单 - 多数据库设置
        add_submenu_page(
            'yxjto',
            __('Multi Database Settings', 'yxjto-gateway'),
            __('多数据库设置', 'yxjto-gateway'),
            'manage_options',
            'yxjto-gateway',
            [$this, 'admin_page']
        );

        // 添加子菜单 - PayPal Multi Gateway
        if (class_exists('WooCommerce')) {
            add_submenu_page(
                'yxjto',
                __('PayPal Multi Gateway', 'yxjto-gateway'),
                __('PayPal支付网关', 'yxjto-gateway'),
                'manage_woocommerce',
                'yxjto-paypal-multi-gateway',
                [$this, 'paypal_multi_gateway_page']
            );

        }
        // 添加子菜单 - 系统设置
        add_submenu_page(
            'yxjto',
            __('System Settings', 'yxjto-gateway'),
            __('系统设置', 'yxjto-gateway'),
            'manage_options',
            'yxjto-system',
            [$this, 'system_settings_page']
        );

        // 添加子菜单 - 配送复制设置
        add_submenu_page(
            'yxjto',
            __('Shipping Replication Settings', 'yxjto-gateway'),
            __('配送复制设置', 'yxjto-gateway'),
            'manage_options',
            'yxjto-shipping-replication',
            [$this, 'shipping_replication_settings_page']
        );

        // 添加子菜单 - 税收复制设置
        add_submenu_page(
            'yxjto',
            __('Tax Replication Settings', 'yxjto-gateway'),
            __('税收复制设置', 'yxjto-gateway'),
            'manage_options',
            'yxjto-tax-replication',
            [$this, 'tax_replication_settings_page']
        );

        // 添加子菜单 - Coupon Sync
        add_submenu_page(
            'yxjto',
            __('Coupon Sync', 'yxjto-gateway'),
            __('Coupon Sync', 'yxjto-gateway'),
            'manage_options',
            'yxjto-coupon-sync',
            [$this, 'coupon_sync_page']
        );

        // 添加子菜单 - Shipping Sync
        add_submenu_page(
            'yxjto',
            __('Shipping Sync', 'yxjto-gateway'),
            __('Shipping Sync', 'yxjto-gateway'),
            'manage_options',
            'yxjto-shipping-sync',
            [$this, 'shipping_sync_page']
        );

        // 添加子菜单 - 综合订单测试
        add_submenu_page(
            'yxjto',
            __('Comprehensive Order Test', 'yxjto-gateway'),
            __('综合订单测试', 'yxjto-gateway'),
            'manage_options',
            'yxjto-comprehensive-order-test',
            [$this, 'comprehensive_order_test_page']
        );



        // 添加子菜单 - 订单比较测试
        add_submenu_page(
            'yxjto',
            __('Order Comparison Test', 'yxjto-gateway'),
            __('订单比较测试', 'yxjto-gateway'),
            'manage_options',
            'yxjto-order-comparison',
            [$this, 'order_comparison_page']
        );

        // 添加子菜单 - 订单号管理
        add_submenu_page(
            'yxjto',
            __('Order ID Manager', 'yxjto-gateway'),
            __('订单ID管理', 'yxjto-gateway'),
            'manage_options',
            'yxjto-order-manager',
            [$this, 'order_manager_page']
        );

    }

    /**
     * 管理页面
     */
    public function admin_page() {
        // 确保管理页面文件已加载
        if (!class_exists('WP_Multi_Database_Admin')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin-page.php';
        }

        if (class_exists('WP_Multi_Database_Admin')) {
            WP_Multi_Database_Admin::render_admin_page();
        } else {
            echo '<div class="notice notice-error"><p>' . __('Admin page class could not be loaded. Please check plugin files.', 'yxjto-gateway') . '</p></div>';
        }
    }

    /**
     * PayPal Multi Gateway 管理页面
     */
    public function paypal_multi_gateway_page() {
        // 检查WooCommerce是否激活
        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for PayPal Multi Gateway functionality.', 'yxjto-gateway') . '</p></div>';
            return;
        }

        // 确保PayPal Multi Gateway管理类已加载
        if (!class_exists('YXJTO_PayPal_Multi_Gateway_Admin')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-paypal-multi-gateway-admin.php';
        }

        if (class_exists('YXJTO_PayPal_Multi_Gateway_Admin')) {
            $admin = YXJTO_PayPal_Multi_Gateway_Admin::get_instance();
            $admin->admin_page();
        } else {
            echo '<div class="notice notice-error"><p>' . __('PayPal Multi Gateway admin class could not be loaded. Please check plugin files.', 'yxjto-gateway') . '</p></div>';
        }
    }

    /**
     * Blocks测试页面
     */
    public function blocks_test_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // 检查环境
        $woocommerce_active = class_exists('WooCommerce');
        $blocks_available = class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType');
        $blocks_class_exists = class_exists('YXJTO_PayPal_Multi_Gateway_Blocks');
        $payment_class_exists = class_exists('YXJTO_PayPal_Multi_Gateway_Payment');

        // 检查文件
        $required_files = array(
            'includes/blocks/class-paypal-multi-gateway-blocks.php' => 'Blocks支持类',
            'assets/js/blocks/paypal-multi-gateway-blocks.js' => 'Blocks JavaScript文件',
            'assets/js/blocks/paypal-multi-gateway-blocks.asset.php' => 'Blocks资源依赖文件',
            'assets/images/icons.svg' => '图标文件',
            'assets/images/paypal-logo.svg' => 'PayPal Logo文件'
        );

        echo '<div class="wrap">';
        echo '<h1>' . __('YXJTO PayPal Multi Gateway Blocks Test', 'yxjto-gateway') . '</h1>';

        // 环境检查
        echo '<div class="card">';
        echo '<h2>' . __('Environment Check', 'yxjto-gateway') . '</h2>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>检查项</th><th>状态</th><th>说明</th></tr></thead>';
        echo '<tbody>';

        echo '<tr>';
        echo '<td>WooCommerce</td>';
        echo '<td>' . ($woocommerce_active ? '<span style="color: green;">✅ 已激活</span>' : '<span style="color: red;">❌ 未激活</span>') . '</td>';
        echo '<td>' . ($woocommerce_active ? 'WooCommerce插件正常运行' : '需要安装并激活WooCommerce插件') . '</td>';
        echo '</tr>';

        echo '<tr>';
        echo '<td>WooCommerce Blocks</td>';
        echo '<td>' . ($blocks_available ? '<span style="color: green;">✅ 可用</span>' : '<span style="color: red;">❌ 不可用</span>') . '</td>';
        echo '<td>' . ($blocks_available ? 'WooCommerce Blocks支持可用' : '需要更新WooCommerce到支持Blocks的版本') . '</td>';
        echo '</tr>';

        echo '<tr>';
        echo '<td>Blocks类</td>';
        echo '<td>' . ($blocks_class_exists ? '<span style="color: green;">✅ 已加载</span>' : '<span style="color: red;">❌ 未加载</span>') . '</td>';
        echo '<td>' . ($blocks_class_exists ? 'YXJTO PayPal Blocks类已正确加载' : 'Blocks类文件可能缺失或有错误') . '</td>';
        echo '</tr>';

        echo '<tr>';
        echo '<td>支付网关类</td>';
        echo '<td>' . ($payment_class_exists ? '<span style="color: green;">✅ 已加载</span>' : '<span style="color: red;">❌ 未加载</span>') . '</td>';
        echo '<td>' . ($payment_class_exists ? 'YXJTO PayPal支付网关类已正确加载' : '支付网关类文件可能缺失或有错误') . '</td>';
        echo '</tr>';

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        // 文件检查
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('File Check', 'yxjto-gateway') . '</h2>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>文件</th><th>状态</th><th>大小</th></tr></thead>';
        echo '<tbody>';

        foreach ($required_files as $file => $description) {
            $file_path = YXJTO_GATEWAY_PLUGIN_DIR . $file;
            $file_exists = file_exists($file_path);
            $file_size = $file_exists ? filesize($file_path) : 0;

            echo '<tr>';
            echo '<td>' . $description . '<br><small>' . $file . '</small></td>';
            echo '<td>' . ($file_exists ? '<span style="color: green;">✅ 存在</span>' : '<span style="color: red;">❌ 缺失</span>') . '</td>';
            echo '<td>' . ($file_exists ? number_format($file_size) . ' bytes' : 'N/A') . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        // 支付网关状态检查
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('Payment Gateway Status', 'yxjto-gateway') . '</h2>';

        if ($payment_class_exists) {
            $gateway = new YXJTO_PayPal_Multi_Gateway_Payment();
            $is_enabled = $gateway->enabled === 'yes';
            $is_available = $gateway->is_available();

            echo '<table class="widefat">';
            echo '<thead><tr><th>项目</th><th>状态</th><th>说明</th></tr></thead>';
            echo '<tbody>';
            echo '<tr>';
            echo '<td>支付网关启用状态</td>';
            echo '<td>' . ($is_enabled ? '<span style="color: green;">✅ 已启用</span>' : '<span style="color: red;">❌ 未启用</span>') . '</td>';
            echo '<td>' . ($is_enabled ? '支付网关已在WooCommerce中启用' : '需要在WooCommerce设置中启用支付网关') . '</td>';
            echo '</tr>';
            echo '<tr>';
            echo '<td>支付网关可用性</td>';
            echo '<td>' . ($is_available ? '<span style="color: green;">✅ 可用</span>' : '<span style="color: red;">❌ 不可用</span>') . '</td>';
            echo '<td>' . ($is_available ? '支付网关通过所有检查' : '支付网关未通过可用性检查') . '</td>';
            echo '</tr>';
            echo '</tbody>';
            echo '</table>';

            if (!$is_enabled) {
                echo '<div style="background: #f8d7da; padding: 15px; border-radius: 4px; color: #721c24; margin-top: 10px;">';
                echo '<strong>❌ 支付网关未启用</strong><br>';
                echo '请前往 <a href="' . admin_url('admin.php?page=wc-settings&tab=checkout&section=yxjto_paypal_multi_gateway') . '" target="_blank">WooCommerce → 设置 → 支付 → YXJTO PayPal Multi Gateway</a> 启用支付网关。<br><br>';
                echo '<button type="button" class="button button-primary" onclick="enablePaymentGateway()">快速启用支付网关</button>';
                echo '</div>';
            }
        }
        echo '</div>';

        // 账户状态检查
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('Account Status', 'yxjto-gateway') . '</h2>';

        if (class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            $accounts_manager = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            $all_accounts = $accounts_manager->get_all_accounts();
            $active_accounts = $accounts_manager->get_active_accounts();

            echo '<table class="widefat">';
            echo '<thead><tr><th>项目</th><th>数量</th><th>详情</th></tr></thead>';
            echo '<tbody>';
            echo '<tr><td>总账户数</td><td>' . count($all_accounts) . '</td><td>所有配置的账户</td></tr>';
            echo '<tr><td>活跃账户数</td><td>' . count($active_accounts) . '</td><td>状态为active的账户</td></tr>';
            echo '</tbody>';
            echo '</table>';

            if (!empty($all_accounts)) {
                echo '<h3>账户详情</h3>';
                echo '<table class="widefat">';
                echo '<thead><tr><th>ID</th><th>类型</th><th>状态</th><th>数据</th></tr></thead>';
                echo '<tbody>';
                foreach ($all_accounts as $account) {
                    // 安全地获取属性，避免undefined property错误
                    $account_id = isset($account->account_id) ? $account->account_id : 'N/A';
                    $account_type = isset($account->account_type) ? $account->account_type : 'N/A';
                    $status = isset($account->status) ? $account->status : 'N/A';

                    // 安全地处理account_data
                    $account_data = 'N/A';
                    if (isset($account->account_data)) {
                        if (is_string($account->account_data)) {
                            $decoded = json_decode($account->account_data, true);
                            $account_data = $decoded ? $decoded : $account->account_data;
                        } elseif (is_array($account->account_data)) {
                            $account_data = $account->account_data;
                        }
                    }

                    // 跳过无效的账户（所有关键属性都是N/A的）
                    if ($account_id === 'N/A' && $account_type === 'N/A' && $status === 'N/A') {
                        continue;
                    }

                    echo '<tr>';
                    echo '<td>' . esc_html($account_id) . '</td>';
                    echo '<td>' . esc_html($account_type) . '</td>';
                    echo '<td>' . esc_html($status) . '</td>';
                    echo '<td><pre style="font-size: 11px; max-width: 300px; overflow: auto;">' . esc_html(print_r($account_data, true)) . '</pre></td>';
                    echo '</tr>';
                }
                echo '</tbody>';
                echo '</table>';
            } else {
                echo '<div style="background: #fff3cd; padding: 15px; border-radius: 4px; color: #856404;">';
                echo '<strong>⚠️ 没有配置任何PayPal账户</strong><br>';
                echo '请先在 <a href="' . admin_url('admin.php?page=yxjto-paypal-multi-gateway') . '">YXJTO → PayPal Multi Gateway</a> 中添加账户。<br><br>';
                echo '<button type="button" class="button button-secondary" onclick="addTestAccount()">添加测试账户</button>';
                echo '</div>';
            }

            // 添加配置清理按钮
            echo '<div style="margin-top: 15px;">';
            echo '<button type="button" class="button button-secondary" onclick="cleanupConfig()">清理无效配置</button>';
            echo '<small style="margin-left: 10px; color: #666;">删除配置文件中的无效账户和旧格式数据</small>';
            echo '</div>';
        }
        echo '</div>';

        // AJAX钩子检查
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('AJAX Hooks Check', 'yxjto-gateway') . '</h2>';

        global $wp_filter;
        $ajax_hooks = array(
            'wp_ajax_yxjto_paypal_multi_gateway_check_status' => 'PayPal状态检查（登录用户）',
            'wp_ajax_nopriv_yxjto_paypal_multi_gateway_check_status' => 'PayPal状态检查（未登录用户）',
            'wp_ajax_yxjto_paypal_multi_gateway_select_account' => '账户选择（登录用户）',
            'wp_ajax_nopriv_yxjto_paypal_multi_gateway_select_account' => '账户选择（未登录用户）',
            'wp_ajax_yxjto_paypal_multi_gateway_switch_account' => '账户切换（登录用户）',
            'wp_ajax_nopriv_yxjto_paypal_multi_gateway_switch_account' => '账户切换（未登录用户）',
            'wp_ajax_yxjto_paypal_multi_gateway_simulate_checkout' => '结账模拟（登录用户）',
            'wp_ajax_yxjto_test_simple' => '简单测试（登录用户）'
        );

        echo '<table class="widefat">';
        echo '<thead><tr><th>AJAX钩子</th><th>状态</th><th>回调函数</th></tr></thead>';
        echo '<tbody>';

        foreach ($ajax_hooks as $hook => $description) {
            if (isset($wp_filter[$hook])) {
                echo '<tr>';
                echo '<td>' . $description . '<br><small>' . $hook . '</small></td>';
                echo '<td><span style="color: green;">✅ 已注册</span></td>';

                $callbacks = array();
                foreach ($wp_filter[$hook]->callbacks as $priority => $callback_group) {
                    foreach ($callback_group as $callback) {
                        if (is_array($callback['function'])) {
                            $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                            $method = $callback['function'][1];
                            $callbacks[] = $class . '::' . $method;
                        } else {
                            $callbacks[] = $callback['function'];
                        }
                    }
                }
                echo '<td><small>' . implode('<br>', $callbacks) . '</small></td>';
                echo '</tr>';
            } else {
                echo '<tr>';
                echo '<td>' . $description . '<br><small>' . $hook . '</small></td>';
                echo '<td><span style="color: red;">❌ 未注册</span></td>';
                echo '<td>-</td>';
                echo '</tr>';
            }
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        // AJAX测试
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('AJAX Test', 'yxjto-gateway') . '</h2>';
        echo '<button type="button" class="button button-secondary" onclick="testSimpleAjax()">测试简单AJAX</button>';
        echo '<button type="button" class="button button-secondary" onclick="checkAjaxClass()">检查AJAX类状态</button>';
        echo '<button type="button" class="button button-secondary" onclick="testAjaxSelfCheck()">AJAX类自检</button>';
        echo '<button type="button" class="button button-secondary" onclick="testMainPluginStatus()">检查主插件状态</button>';
        if ($payment_class_exists) {
            echo '<button type="button" class="button button-primary" onclick="testAjaxHandler()">测试PayPal AJAX状态检查</button>';
            echo '<button type="button" class="button button-primary" onclick="testAccountSelection()">测试账户选择（负载均衡）</button>';
            echo '<button type="button" class="button button-secondary" onclick="testAccountSwitch()">测试账户切换</button>';
            echo '<button type="button" class="button button-secondary" onclick="testCheckoutSimulation()">测试结账模拟</button>';
        }
        echo '<div id="ajax-results" style="margin-top: 10px;"></div>';
        echo '</div>';

            // 添加JavaScript
            echo '<script>
            function enablePaymentGateway() {
                if (!confirm("确定要启用YXJTO PayPal Multi Gateway支付网关吗？")) {
                    return;
                }

                const formData = new FormData();
                formData.append("action", "yxjto_enable_payment_gateway");
                formData.append("nonce", "' . wp_create_nonce('yxjto_enable_gateway') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("✅ 支付网关已成功启用！页面将刷新。");
                        location.reload();
                    } else {
                        alert("❌ 启用失败: " + (data.data || "未知错误"));
                    }
                })
                .catch(error => {
                    alert("❌ 请求失败: " + error.message);
                });
            }

            function addTestAccount() {
                if (!confirm("确定要添加一个测试PayPal账户吗？\\n\\n这将添加一个邮箱类型的测试账户：<EMAIL>")) {
                    return;
                }

                const formData = new FormData();
                formData.append("action", "yxjto_add_test_account");
                formData.append("nonce", "' . wp_create_nonce('yxjto_add_test_account') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("✅ 测试账户已成功添加！页面将刷新。");
                        location.reload();
                    } else {
                        alert("❌ 添加失败: " + (data.data || "未知错误"));
                    }
                })
                .catch(error => {
                    alert("❌ 请求失败: " + error.message);
                });
            }

            function cleanupConfig() {
                if (!confirm("确定要清理无效的配置数据吗？\\n\\n这将删除：\\n- 缺少必要属性的无效账户\\n- 旧格式的配置数据\\n\\n此操作不可撤销！")) {
                    return;
                }

                const formData = new FormData();
                formData.append("action", "yxjto_cleanup_config");
                formData.append("nonce", "' . wp_create_nonce('yxjto_cleanup_config') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("✅ 配置清理完成！\\n\\n" + data.data);
                        location.reload();
                    } else {
                        alert("❌ 清理失败: " + (data.data || "未知错误"));
                    }
                })
                .catch(error => {
                    alert("❌ 请求失败: " + error.message);
                });
            }

            function testSimpleAjax() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试简单AJAX...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_test_simple");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => {
                    console.log("Simple AJAX Response status:", response.status);
                    return response.text();
                })
                .then(text => {
                    console.log("Simple AJAX Raw response:", text);

                    try {
                        const data = JSON.parse(text);
                        console.log("Simple AJAX Parsed data:", data);

                        if (data.success) {
                            resultsDiv.innerHTML = `
                                <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                    <h4>✅ 简单AJAX测试成功</h4>
                                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                                </div>
                            `;
                        } else {
                            resultsDiv.innerHTML = `
                                <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                    <h4>❌ 简单AJAX返回错误</h4>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 响应不是有效JSON</h4>
                                <p>原始响应:</p>
                                <pre>${text}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error("Simple AJAX Error:", error);
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ 简单AJAX请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function checkAjaxClass() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在检查AJAX类状态...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_check_ajax_class");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => {
                    console.log("AJAX Class Check Response status:", response.status);
                    return response.text();
                })
                .then(text => {
                    console.log("AJAX Class Check Raw response:", text);

                    try {
                        const data = JSON.parse(text);
                        console.log("AJAX Class Check Parsed data:", data);

                        if (data.success) {
                            resultsDiv.innerHTML = `
                                <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                    <h4>✅ AJAX类状态检查成功</h4>
                                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                                </div>
                            `;
                        } else {
                            resultsDiv.innerHTML = `
                                <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                    <h4>❌ AJAX类状态检查失败</h4>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 响应不是有效JSON</h4>
                                <p>原始响应:</p>
                                <pre>${text}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error("AJAX Class Check Error:", error);
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ AJAX类检查请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testAjaxSelfCheck() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试AJAX类自检...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_ajax_class_self_check");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => {
                    console.log("AJAX Self Check Response status:", response.status);
                    return response.text();
                })
                .then(text => {
                    console.log("AJAX Self Check Raw response:", text);

                    try {
                        const data = JSON.parse(text);
                        console.log("AJAX Self Check Parsed data:", data);

                        if (data.success) {
                            resultsDiv.innerHTML = `
                                <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                    <h4>✅ AJAX类自检成功</h4>
                                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                                </div>
                            `;
                        } else {
                            resultsDiv.innerHTML = `
                                <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                    <h4>❌ AJAX类自检失败</h4>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 响应不是有效JSON</h4>
                                <p>原始响应:</p>
                                <pre>${text}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error("AJAX Self Check Error:", error);
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ AJAX类自检请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testMainPluginStatus() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在检查主插件状态...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_main_plugin_status");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => {
                    console.log("Main Plugin Status Response status:", response.status);
                    return response.text();
                })
                .then(text => {
                    console.log("Main Plugin Status Raw response:", text);

                    try {
                        const data = JSON.parse(text);
                        console.log("Main Plugin Status Parsed data:", data);

                        if (data.success) {
                            resultsDiv.innerHTML = `
                                <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                    <h4>✅ 主插件状态检查成功</h4>
                                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                                </div>
                            `;
                        } else {
                            resultsDiv.innerHTML = `
                                <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                    <h4>❌ 主插件状态检查失败</h4>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 响应不是有效JSON</h4>
                                <p>原始响应:</p>
                                <pre>${text}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error("Main Plugin Status Error:", error);
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ 主插件状态检查请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testAjaxHandler() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试AJAX处理器...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_paypal_multi_gateway_check_status");
                formData.append("nonce", "' . wp_create_nonce('yxjto_paypal_multi_gateway_checkout') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log("AJAX Response:", data);

                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                <h4>✅ AJAX处理器工作正常</h4>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ AJAX处理器返回错误</h4>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error("AJAX Error:", error);
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ AJAX请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testAccountSelection() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试账户选择（负载均衡）...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_paypal_multi_gateway_select_account");
                formData.append("nonce", "' . wp_create_nonce('yxjto_paypal_multi_gateway_checkout') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                <h4>✅ 账户选择成功</h4>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 账户选择失败</h4>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ 请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testAccountSwitch() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试账户切换...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_paypal_multi_gateway_switch_account");
                formData.append("nonce", "' . wp_create_nonce('yxjto_paypal_multi_gateway_checkout') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                <h4>✅ 账户切换成功</h4>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 账户切换失败</h4>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ 请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }

            function testCheckoutSimulation() {
                const resultsDiv = document.getElementById("ajax-results");
                resultsDiv.innerHTML = "<p style=\"color: blue;\">正在测试结账模拟...</p>";

                const formData = new FormData();
                formData.append("action", "yxjto_paypal_multi_gateway_simulate_checkout");
                formData.append("nonce", "' . wp_create_nonce('yxjto_paypal_multi_gateway_checkout') . '");

                fetch("' . admin_url('admin-ajax.php') . '", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div style="background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;">
                                <h4>✅ 结账模拟成功</h4>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                                <h4>❌ 结账模拟失败</h4>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;">
                            <h4>❌ 请求失败</h4>
                            <p>错误: ${error.message}</p>
                        </div>
                    `;
                });
            }
            </script>';

        // 总结
        $all_tests_passed = $woocommerce_active && $blocks_available && $blocks_class_exists && $payment_class_exists;
        $files_exist = true;
        foreach ($required_files as $file => $description) {
            if (!file_exists(YXJTO_GATEWAY_PLUGIN_DIR . $file)) {
                $files_exist = false;
                break;
            }
        }
        $all_tests_passed = $all_tests_passed && $files_exist;

        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>' . __('Test Summary', 'yxjto-gateway') . '</h2>';
        if ($all_tests_passed) {
            echo '<div style="background: #d4edda; padding: 15px; border-radius: 4px; color: #155724;">';
            echo '<h3>✅ 所有测试通过！</h3>';
            echo '<p>YXJTO PayPal Multi Gateway Blocks集成已成功配置！</p>';
            echo '</div>';
        } else {
            echo '<div style="background: #f8d7da; padding: 15px; border-radius: 4px; color: #721c24;">';
            echo '<h3>❌ 部分测试失败</h3>';
            echo '<p>请修复上述问题后重新测试。</p>';
            echo '</div>';
        }
        echo '</div>';

        echo '</div>';
    }

    /**
     * AJAX处理器：启用支付网关
     */
    public function ajax_enable_payment_gateway() {
        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-gateway'));
            return;
        }

        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_enable_gateway')) {
            wp_send_json_error(__('Security check failed.', 'yxjto-gateway'));
            return;
        }

        try {
            // 获取当前支付网关设置
            $gateway_settings = get_option('woocommerce_yxjto_paypal_multi_gateway_settings', array());

            // 启用支付网关
            $gateway_settings['enabled'] = 'yes';

            // 如果没有设置标题，设置默认标题
            if (empty($gateway_settings['title'])) {
                $gateway_settings['title'] = 'PayPal';
            }

            // 如果没有设置描述，设置默认描述
            if (empty($gateway_settings['description'])) {
                $gateway_settings['description'] = __('Pay securely with PayPal. You can pay with your PayPal account or credit card.', 'yxjto-gateway');
            }

            // 保存设置
            $result = update_option('woocommerce_yxjto_paypal_multi_gateway_settings', $gateway_settings);

            if ($result) {
                wp_send_json_success(__('Payment gateway enabled successfully.', 'yxjto-gateway'));
            } else {
                wp_send_json_error(__('Failed to save settings.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理器：添加测试账户
     */
    public function ajax_add_test_account() {
        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-gateway'));
            return;
        }

        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_add_test_account')) {
            wp_send_json_error(__('Security check failed.', 'yxjto-gateway'));
            return;
        }

        try {
            // 检查账户管理器是否可用
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
                wp_send_json_error(__('Account manager not available.', 'yxjto-gateway'));
                return;
            }

            $accounts_manager = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();

            // 创建测试账户数据
            $test_account_data = array(
                'email' => '<EMAIL>'
            );

            // 添加测试账户
            $account_id = $accounts_manager->add_account('email', $test_account_data, 100);

            if ($account_id) {
                wp_send_json_success(__('Test account added successfully. Account ID: ', 'yxjto-gateway') . $account_id);
            } else {
                wp_send_json_error(__('Failed to add test account.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理器：清理配置
     */
    public function ajax_cleanup_config() {
        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-gateway'));
            return;
        }

        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_cleanup_config')) {
            wp_send_json_error(__('Security check failed.', 'yxjto-gateway'));
            return;
        }

        try {
            // 检查配置管理器是否可用
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Config')) {
                wp_send_json_error(__('Config manager not available.', 'yxjto-gateway'));
                return;
            }

            $config_manager = YXJTO_PayPal_Multi_Gateway_Config::get_instance();

            // 获取当前配置
            $config = $config_manager->get_config();
            if (!$config) {
                wp_send_json_error(__('No configuration found.', 'yxjto-gateway'));
                return;
            }

            $cleanup_count = 0;
            $cleanup_details = array();

            // 清理账户数据
            if (isset($config['accounts'])) {
                $valid_accounts = array();

                foreach ($config['accounts'] as $account_id => $account_data) {
                    // 检查账户是否有必要的属性
                    if (isset($account_data['account_id']) &&
                        isset($account_data['account_type']) &&
                        isset($account_data['account_data']) &&
                        !empty($account_data['account_id']) &&
                        !empty($account_data['account_type'])) {

                        $valid_accounts[$account_id] = $account_data;
                    } else {
                        $cleanup_count++;
                        $cleanup_details[] = "删除无效账户: " . ($account_id ?: '未知ID');
                    }
                }

                $config['accounts'] = $valid_accounts;
            }

            // 删除旧格式的配置
            $old_keys = array('email_accounts', 'api_accounts', 'paypal_me_accounts');
            foreach ($old_keys as $key) {
                if (isset($config[$key])) {
                    unset($config[$key]);
                    $cleanup_count++;
                    $cleanup_details[] = "删除旧格式配置: " . $key;
                }
            }

            // 保存清理后的配置
            if ($cleanup_count > 0) {
                $result = $config_manager->save_config($config);

                if ($result) {
                    // 清除缓存
                    delete_transient('yxjto_paypal_multi_gateway_all_accounts');
                    delete_transient('yxjto_paypal_multi_gateway_active_accounts');

                    $message = sprintf(__('Successfully cleaned %d items:', 'yxjto-gateway'), $cleanup_count) . "\n\n" . implode("\n", $cleanup_details);
                    wp_send_json_success($message);
                } else {
                    wp_send_json_error(__('Failed to save cleaned configuration.', 'yxjto-gateway'));
                }
            } else {
                wp_send_json_success(__('No cleanup needed. Configuration is already clean.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * 简单的AJAX测试处理器
     */
    public function ajax_test_simple() {
        wp_send_json_success(array(
            'message' => 'Simple AJAX test successful',
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'is_admin' => current_user_can('manage_options')
        ));
    }

    /**
     * 检查AJAX类状态
     */
    public function ajax_check_ajax_class() {
        // 最简单的响应，避免任何可能的错误
        wp_send_json_success(array(
            'message' => 'Main plugin class AJAX working',
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => 'ajax_check_ajax_class',
            'class' => get_class($this)
        ));
    }

    /**
     * 加载管理脚本
     */
    public function enqueue_admin_scripts($hook) {
        if ('settings_page_yxjto-gateway' !== $hook) {
            return;
        }

        wp_enqueue_script(
            'yxjto-gateway-admin',
            YXJTO_GATEWAY_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            YXJTO_GATEWAY_VERSION,
            true
        );

        wp_enqueue_style(
            'yxjto-gateway-admin',
            YXJTO_GATEWAY_PLUGIN_URL . 'assets/css/admin.css',
            [],
            YXJTO_GATEWAY_VERSION
        );

        wp_localize_script('yxjto-gateway-admin', 'yxjtoGateway', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_gateway_nonce'),
            'strings' => [
                'testing' => __('Testing connection...', 'yxjto-gateway'),
                'success' => __('Connection successful!', 'yxjto-gateway'),
                'failed' => __('Connection failed!', 'yxjto-gateway'),
                'switching' => __('Switching database...', 'yxjto-gateway'),
                'switched' => __('Database switched successfully!', 'yxjto-gateway'),
                'switchFailed' => __('Database switch failed!', 'yxjto-gateway'),
                'confirmSwitch' => __('Are you sure you want to switch to this database?', 'yxjto-gateway'),
                'confirmRemoveDatabase' => __('Are you sure you want to remove this database configuration?', 'yxjto-gateway'),
                'confirmRemoveIpRule' => __('Are you sure you want to remove this IP rule?', 'yxjto-gateway'),
                'confirmRemoveUrlRule' => __('Are you sure you want to remove this URL rule?', 'yxjto-gateway'),
                'testConnection' => __('Test Connection', 'yxjto-gateway'),
                'switchToThis' => __('Switch to This', 'yxjto-gateway'),
                'remove' => __('Remove', 'yxjto-gateway'),
                'newDatabase' => __('New Database', 'yxjto-gateway'),
                'selectDatabase' => __('Select Database', 'yxjto-gateway')
            ]
        ]);
    }

    /**
     * AJAX测试数据库连接
     */
    public function ajax_test_connection() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 验证必填字段
        $required_fields = ['host', 'database', 'username'];
        foreach ($required_fields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                wp_send_json([
                    'success' => false,
                    'message' => sprintf(__('Missing required field: %s', 'yxjto-gateway'), $field)
                ]);
                return;
            }
        }

        $config = [
            'host' => sanitize_text_field($_POST['host']),
            'database' => sanitize_text_field($_POST['database']),
            'username' => sanitize_text_field($_POST['username']),
            'password' => $_POST['password'] ?? '', // 密码可以为空
            'charset' => sanitize_text_field($_POST['charset'] ?? 'utf8mb4')
        ];

        $error_message = null;
        $result = $this->test_database_connection($config, $error_message);

        wp_send_json([
            'success' => $result,
            'message' => $result ?
                __('Connection successful!', 'yxjto-gateway') :
                ($error_message ?: __('Connection failed!', 'yxjto-gateway'))
        ]);
    }

    /**
     * AJAX切换数据库
     */
    public function ajax_switch_database() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 验证数据库名称参数
        if (!isset($_POST['database']) || empty($_POST['database'])) {
            wp_send_json([
                'success' => false,
                'message' => __('Database name is required', 'yxjto-gateway'),
                'current_database' => $this->current_database
            ]);
            return;
        }

        $database_name = sanitize_text_field($_POST['database']);
        $result = $this->switch_database($database_name);

        wp_send_json([
            'success' => $result,
            'message' => $result ?
                __('Database switched successfully!', 'yxjto-gateway') :
                __('Database switch failed!', 'yxjto-gateway'),
            'current_database' => $this->current_database
        ]);
    }

    /**
     * AJAX清除日志
     */
    public function ajax_clear_logs() {
        check_ajax_referer('yxjto_gateway_clear_logs', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_gateway_logs';

        $result = $wpdb->query("TRUNCATE TABLE $table_name");

        wp_send_json([
            'success' => $result !== false,
            'message' => $result !== false ?
                __('Logs cleared successfully!', 'yxjto-gateway') :
                __('Failed to clear logs!', 'yxjto-gateway')
        ]);
    }

    /**
     * 插件激活
     */
    public function activate() {
        // 配置文件管理器会自动创建默认配置文件
        // 不再需要在数据库中存储配置

        // 创建数据库表（如果需要）
        $this->create_tables();

        // 迁移现有的数据库配置到文件（如果存在）
        $this->migrate_database_options_to_file();
    }

    /**
     * 迁移数据库选项到配置文件
     */
    private function migrate_database_options_to_file() {
        // 检查是否存在旧的数据库配置
        $old_databases = get_option('wp_multi_db_configs');
        $old_ip_rules = get_option('wp_multi_db_ip_rules');
        $old_url_rules = get_option('wp_multi_db_url_rules');

        $migrated = false;

        // 迁移数据库配置
        if ($old_databases && !empty($old_databases)) {
            WP_Multi_DB_Config_Manager::save_databases($old_databases);
            $migrated = true;
        }

        // 迁移IP规则
        if ($old_ip_rules && !empty($old_ip_rules)) {
            WP_Multi_DB_Config_Manager::save_ip_rules($old_ip_rules);
            $migrated = true;
        }

        // 迁移URL规则
        if ($old_url_rules && !empty($old_url_rules)) {
            WP_Multi_DB_Config_Manager::save_url_rules($old_url_rules);
            $migrated = true;
        }

        if ($migrated) {
            // 记录迁移日志
            error_log('WP Multi Database: Migrated configuration from database options to file');

            // 可选：删除旧的数据库选项（谨慎操作）
            // delete_option('wp_multi_db_configs');
            // delete_option('wp_multi_db_ip_rules');
            // delete_option('wp_multi_db_url_rules');
        }
    }

    /**
     * 插件停用
     */
    public function deactivate() {
        // 清理临时数据
        delete_transient('wp_multi_db_status');
    }

    /**
     * 创建数据库表
     */
    private function create_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_gateway_logs';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            action varchar(50) NOT NULL,
            from_database varchar(100),
            to_database varchar(100),
            ip_address varchar(45),
            user_agent text,
            success tinyint(1) DEFAULT 1,
            message text,
            PRIMARY KEY (id),
            KEY timestamp (timestamp),
            KEY action (action)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * 加载文本域
     */
    public function load_textdomain() {
        // 防止过早加载
        if (!did_action('plugins_loaded')) {
            return;
        }

        $locale = get_locale();

        try {
            // 使用语言管理器自动生成.mo文件
            if (class_exists('WP_Multi_Database_Language_Manager')) {
                $language_manager = WP_Multi_Database_Language_Manager::get_instance();
                $language_manager->auto_generate_mo_file($locale);
            }

            // 尝试加载语言文件
            $loaded = load_plugin_textdomain(
                'yxjto-gateway',
                false,
                dirname(YXJTO_GATEWAY_PLUGIN_BASENAME) . '/languages'
            );

            // 如果是支持的语言但没有加载成功，使用内置翻译
            if (class_exists('WP_Multi_Database_Language_Manager')) {
                $language_manager = WP_Multi_Database_Language_Manager::get_instance();
                $supported_languages = $language_manager->get_supported_languages();
                if (!$loaded && isset($supported_languages[$locale])) {
                    add_filter('gettext', [$this, 'translate_text'], 10, 3);
                    add_filter('gettext_with_context', [$this, 'translate_text_with_context'], 10, 4);
                }
            }

            // 加载PayPal Multi Gateway文本域
            load_plugin_textdomain(
                'yxjto-paypal-multi-gateway',
                false,
                dirname(YXJTO_GATEWAY_PLUGIN_BASENAME) . '/languages'
            );

        } catch (Exception $e) {
            error_log("YXJTO Gateway: Error loading textdomain: " . $e->getMessage());
        }
    }











    /**
     * 内置翻译
     */
    public function translate_text($translation, $text, $domain) {
        if ($domain !== 'yxjto-gateway') {
            return $translation;
        }

        $locale = get_locale();
        $language_manager = WP_Multi_Database_Language_Manager::get_instance();
        $supported_languages = $language_manager->get_supported_languages();

        if (isset($supported_languages[$locale])) {
            $translations = $supported_languages[$locale];
            return isset($translations[$text]) ? $translations[$text] : $translation;
        }

        return $translation;
    }

    /**
     * 带上下文的翻译
     */
    public function translate_text_with_context($translation, $text, $context, $domain) {
        if ($domain !== 'yxjto-gateway') {
            return $translation;
        }

        return $this->translate_text($translation, $text, $domain);
    }

    /**
     * YXJTO主页面
     */
    public function yxjto_main_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('YXJTO Gateway', 'yxjto-gateway'); ?></h1>

            <div class="notice notice-info">
                <p><?php _e('Welcome to YXJTO Gateway - Unified Payment Gateway and Multi-Database Management System', 'yxjto-gateway'); ?></p>
            </div>

            <div class="card-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">

                <!-- 多数据库管理 -->
                <div class="card" style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px;">
                    <h2><?php _e('Multi Database Management', 'yxjto-gateway'); ?></h2>
                    <p><?php _e('Configure and manage multiple databases with intelligent switching rules.', 'yxjto-gateway'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=yxjto-gateway'); ?>" class="button button-primary">
                        <?php _e('Manage Databases', 'yxjto-gateway'); ?>
                    </a>
                </div>

                <!-- 系统设置 -->
                <div class="card" style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px;">
                    <h2><?php _e('System Settings', 'yxjto-gateway'); ?></h2>
                    <p><?php _e('Configure system-wide settings and manage plugin operations across all databases.', 'yxjto-gateway'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=yxjto-system'); ?>" class="button button-primary">
                        <?php _e('System Settings', 'yxjto-gateway'); ?>
                    </a>
                </div>

                <!-- 测试中心 -->
                <div class="card" style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px;">
                    <h2><?php _e('Test Center', 'yxjto-gateway'); ?></h2>
                    <p><?php _e('Access testing tools and debugging utilities for troubleshooting.', 'yxjto-gateway'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=yxjto-test'); ?>" class="button button-primary">
                        <?php _e('Test Center', 'yxjto-gateway'); ?>
                    </a>
                </div>

            </div>

            <!-- 系统状态概览 -->
            <div class="card" style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2><?php _e('System Status Overview', 'yxjto-gateway'); ?></h2>

                <?php
                $databases = WP_Multi_DB_Config_Manager::get_databases();
                $current_db = $this->get_current_database();
                $wp_config_status = WP_Multi_DB_Config_Manager::check_wp_config_integration();
                ?>

                <table class="widefat">
                    <tr>
                        <th><?php _e('Current Database', 'yxjto-gateway'); ?></th>
                        <td><strong><?php echo esc_html($current_db); ?></strong></td>
                    </tr>
                    <tr>
                        <th><?php _e('Total Databases', 'yxjto-gateway'); ?></th>
                        <td><?php echo count($databases); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('wp-config Integration', 'yxjto-gateway'); ?></th>
                        <td>
                            <?php if ($wp_config_status): ?>
                                <span style="color: green;">✅ <?php _e('Enabled', 'yxjto-gateway'); ?></span>
                            <?php else: ?>
                                <span style="color: red;">❌ <?php _e('Disabled', 'yxjto-gateway'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <?php
    }

    /**
     * 系统设置页面
     */
    public function system_settings_page() {
        // 处理表单提交
        if (isset($_POST['action']) && wp_verify_nonce($_POST['_wpnonce'], 'yxjto_system_settings')) {
            $this->handle_system_settings_form();
        }

        ?>
        <div class="wrap">
            <h1><?php _e('System Settings', 'yxjto-gateway'); ?></h1>

            <!-- 标签页导航 -->
            <h2 class="nav-tab-wrapper">
                <a href="#general" class="nav-tab nav-tab-active" onclick="switchTab(event, 'general')"><?php _e('General', 'yxjto-gateway'); ?></a>
                <a href="#user-management" class="nav-tab" onclick="switchTab(event, 'user-management')"><?php _e('User Management', 'yxjto-gateway'); ?></a>
                <a href="#order-replication" class="nav-tab" onclick="switchTab(event, 'order-replication')"><?php _e('Order Replication', 'yxjto-gateway'); ?></a>
                <a href="#status" class="nav-tab" onclick="switchTab(event, 'status')"><?php _e('System Status', 'yxjto-gateway'); ?></a>
            </h2>

            <!-- 用户管理设置表单 -->
            <form method="post" action="" id="user-management-form" style="display: none;">
                <?php wp_nonce_field('yxjto_system_settings'); ?>
                <input type="hidden" name="action" value="save_user_management_settings" />
            </form>

            <!-- 订单复制设置表单 -->
            <form method="post" action="" id="order-replication-form">
                <?php wp_nonce_field('yxjto_system_settings'); ?>
                <input type="hidden" name="action" value="save_order_replication_settings" />

                <!-- 通用设置标签页 -->
                <div class="tab-content" id="general" style="display: block;">
                    <h3><?php _e('General System Settings', 'yxjto-gateway'); ?></h3>
                    <p><?php _e('Configure general system-wide settings.', 'yxjto-gateway'); ?></p>

                    <!-- Plugin Management -->
                    <h4><?php _e('Plugin Management', 'yxjto-gateway'); ?></h4>
                    <p><?php _e('Manage plugin status across all databases.', 'yxjto-gateway'); ?></p>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Plugin Operations', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="enablePluginAllDatabases()"><?php _e('Enable Plugin in All Databases', 'yxjto-gateway'); ?></button>
                                <button type="button" class="button" onclick="disablePluginAllDatabases()"><?php _e('Disable Plugin in All Databases', 'yxjto-gateway'); ?></button>
                            </td>
                        </tr>
                    </table>



                    <!-- Payment Gateway Management -->
                    <h4><?php _e('Payment Gateway Management', 'yxjto-gateway'); ?></h4>
                    <p><?php _e('Manage payment gateway settings across all databases.', 'yxjto-gateway'); ?></p>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Payment Gateway Operations', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="enablePaymentGatewayAllDatabases()"><?php _e('Enable Payment Gateway in All Databases', 'yxjto-gateway'); ?></button>
                                <button type="button" class="button" onclick="disablePaymentGatewayAllDatabases()"><?php _e('Disable Payment Gateway in All Databases', 'yxjto-gateway'); ?></button>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Transaction Log Tables', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="createTransactionLogTables()"><?php _e('Create Transaction Log Tables in All Databases', 'yxjto-gateway'); ?></button>
                                <p class="description">
                                    <?php _e('This will create transaction log tables in databases that don\'t have them.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <!-- Debug Mode -->
                    <h4><?php _e('Debug Settings', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Debug Mode', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="debug_mode" value="1" <?php checked(YXJTO_GATEWAY_DEBUG); ?> disabled />
                                    <?php _e('Enable debug logging', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Debug mode is controlled by the YXJTO_GATEWAY_DEBUG constant in the plugin file.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 用户管理标签页 -->
                <div class="tab-content" id="user-management" style="display: none;">
                    <h3><?php _e('User Management', 'yxjto-gateway'); ?></h3>
                    <p><?php _e('Manage user data across all databases.', 'yxjto-gateway'); ?></p>

                    <?php
                    // 从config-manager加载用户管理设置
                    $user_settings = WP_Multi_DB_Config_Manager::get_user_management_settings();

                    // 调试信息
                    if (YXJTO_GATEWAY_DEBUG) {
                        echo '<div class="notice notice-info"><p><strong>Debug:</strong> User settings loaded from config-manager: ' . json_encode($user_settings) . '</p></div>';

                        // 显示配置文件路径
                        $config_file = WP_Multi_DB_Config_Manager::get_config_file_path();
                        if (file_exists($config_file)) {
                            echo '<div class="notice notice-info"><p><strong>Config file:</strong> ' . $config_file . ' (exists)</p></div>';
                        } else {
                            echo '<div class="notice notice-warning"><p><strong>Config file:</strong> ' . $config_file . ' (not found)</p></div>';
                        }
                    }
                    ?>

                    <!-- 自动复制设置 -->
                    <h4><?php _e('Automatic Copy Settings', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Auto Copy User Creation', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_copy_user_creation" value="1" <?php checked(!empty($user_settings['auto_copy_user_creation'])); ?> form="user-management-form" />
                                    <?php _e('Automatically copy new user accounts to all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled, newly created user accounts will be automatically copied to all other databases. This ensures user consistency across all database environments.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Auto Copy User Sessions', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_copy_user_sessions" value="1" <?php checked(!empty($user_settings['auto_copy_user_sessions'])); ?> form="user-management-form" />
                                    <?php _e('Automatically copy user sessions to all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled, user login sessions will be automatically copied to all other databases. This allows users to maintain their login state when switching between databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Auto Copy WooCommerce Sessions', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_copy_woocommerce_sessions" value="1" <?php checked(!empty($user_settings['auto_copy_woocommerce_sessions'])); ?> form="user-management-form" />
                                    <?php _e('Automatically copy WooCommerce sessions to all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled, WooCommerce customer sessions (cart, checkout data) will be automatically copied to all other databases. This ensures shopping cart continuity when switching between databases.', 'yxjto-gateway'); ?><br>
                                    <strong><?php _e('Note: Requires WooCommerce to be active.', 'yxjto-gateway'); ?></strong>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <!-- 手动操作 -->
                    <h4><?php _e('Manual Operations', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Current User Session', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="copyCurrentUserSession()"><?php _e('Copy Current User Session to All Databases', 'yxjto-gateway'); ?></button>
                                <p class="description">
                                    <?php _e('Copy the current logged-in user session data to all other databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Current User Account', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="copyCurrentUserAccount()"><?php _e('Copy Current User Account to All Databases', 'yxjto-gateway'); ?></button>
                                <p class="description">
                                    <?php _e('Copy the current user account data to all other databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" name="save_user_management" class="button-primary" value="<?php _e('Save User Management Settings', 'yxjto-gateway'); ?>" form="user-management-form" />
                    </p>

                    <!-- 操作结果显示区域 -->
                    <div id="user-operation-result"></div>
                </div>

                <!-- 订单复制设置标签页 -->
                <div class="tab-content" id="order-replication" style="display: none;">
                    <h3><?php _e('Order Replication Settings', 'yxjto-gateway'); ?></h3>
                    <p><?php _e('Configure order replication across multiple databases using HPOS (High-Performance Order Storage).', 'yxjto-gateway'); ?></p>

                    <?php
                    $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
                    ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Order Replication', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_order_replication" value="1"
                                           <?php checked(!empty($order_settings['enable_order_replication'])); ?> />
                                    <?php _e('Immediately replicate orders to all enabled databases on checkout', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Orders will be replicated with the same order ID and line numbers across all databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Status Replication', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_status_replication" value="1"
                                           <?php checked(!empty($order_settings['enable_status_replication'])); ?> />
                                    <?php _e('Immediately replicate order status changes to all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Status changes will be synchronized instantly across all databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Payment Info Replication', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_payment_info_replication" value="1"
                                           <?php checked(!empty($order_settings['enable_payment_info_replication'])); ?> />
                                    <?php _e('Immediately replicate payment gateway information to all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Payment method, transaction ID, and gateway-specific data will be synchronized.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Smart Product Replacement', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_smart_product_replacement" value="1"
                                           <?php checked(!empty($order_settings['enable_smart_product_replacement'])); ?> />
                                    <?php _e('Replace products while maintaining price, quantity, and total amounts', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Products can be replaced with same-price random products or same-ID products.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Product Replacement Mode', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="product_replacement_mode">
                                    <option value="same_price" <?php selected($order_settings['product_replacement_mode'], 'same_price'); ?>>
                                        <?php _e('Same Price Random Products', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="same_id" <?php selected($order_settings['product_replacement_mode'], 'same_id'); ?>>
                                        <?php _e('Same ID Products', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Choose how products should be replaced during replication.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Payment Verification', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_payment_verification" value="1"
                                           <?php checked(!empty($order_settings['enable_payment_verification'])); ?> />
                                    <?php _e('Verify order exists in default database before payment redirect', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Ensures payment processing uses default database order information.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Callback Handling', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_callback_handling" value="1"
                                           <?php checked(!empty($order_settings['enable_callback_handling'])); ?> />
                                    <?php _e('Use current database order information for payment callback pages', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Thank you pages and payment confirmations will use the current database.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Default Database Payment Redirect', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_default_db_payment_redirect" value="1"
                                           <?php checked(!empty($order_settings['enable_default_db_payment_redirect'])); ?> />
                                    <?php _e('Always use default database for payment processing when source order is not from default database', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled: Non-default database orders will switch to default database for payment. When disabled: Orders will use their source database for payment. Default database orders always use default database regardless of this setting.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <h4><?php _e('HPOS Status', 'yxjto-gateway'); ?></h4>
                    <?php
                    $hpos_enabled = false;
                    if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
                        $hpos_enabled = \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
                    }
                    ?>
                    <p>
                        <strong><?php _e('HPOS Status:', 'yxjto-gateway'); ?></strong>
                        <?php if ($hpos_enabled): ?>
                            <span style="color: green;"><?php _e('✅ Enabled', 'yxjto-gateway'); ?></span>
                        <?php else: ?>
                            <span style="color: red;"><?php _e('❌ Disabled', 'yxjto-gateway'); ?></span>
                        <?php endif; ?>
                    </p>
                    <?php if (!$hpos_enabled): ?>
                        <div class="notice notice-warning">
                            <p><?php _e('Order replication requires HPOS (High-Performance Order Storage) to be enabled in WooCommerce settings.', 'yxjto-gateway'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- 系统状态标签页 -->
                <div class="tab-content" id="status" style="display: none;">
                    <h3><?php _e('System Status', 'yxjto-gateway'); ?></h3>
                    <p><?php _e('View current system configuration and status.', 'yxjto-gateway'); ?></p>

                    <?php
                    $databases = WP_Multi_DB_Config_Manager::get_databases();
                    $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();
                    $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
                    $order_status_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
                    $hpos_status_enabled = false;
                    if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
                        $hpos_status_enabled = \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
                    }
                    ?>

                    <table class="widefat">
                        <tr>
                            <th><?php _e('Configured Databases', 'yxjto-gateway'); ?></th>
                            <td><?php echo count($databases); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('IP Rules', 'yxjto-gateway'); ?></th>
                            <td><?php echo count($ip_rules); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('URL Rules', 'yxjto-gateway'); ?></th>
                            <td><?php echo count($url_rules); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Current Database', 'yxjto-gateway'); ?></th>
                            <td><strong><?php echo esc_html($this->get_current_database()); ?></strong></td>
                        </tr>
                        <?php if (class_exists('WooCommerce')): ?>
                        <tr>
                            <th><?php _e('WooCommerce Status', 'yxjto-gateway'); ?></th>
                            <td><span style="color: green;">✅ Active</span></td>
                        </tr>
                        <tr>
                            <th><?php _e('HPOS Status', 'yxjto-gateway'); ?></th>
                            <td><?php echo $hpos_status_enabled ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Order Replication', 'yxjto-gateway'); ?></th>
                            <td><?php echo !empty($order_status_settings['enable_order_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Status Replication', 'yxjto-gateway'); ?></th>
                            <td><?php echo !empty($order_status_settings['enable_status_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Payment Info Replication', 'yxjto-gateway'); ?></th>
                            <td><?php echo !empty($order_status_settings['enable_payment_info_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                        </tr>
                        <?php else: ?>
                        <tr>
                            <th><?php _e('WooCommerce Status', 'yxjto-gateway'); ?></th>
                            <td><span style="color: red;">❌ Inactive</span></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>

                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php _e('Save Settings', 'yxjto-gateway'); ?>" />
                </p>
            </form>
        </div>

        <style>
        .tab-content {
            display: none;
            padding: 20px 0;
        }
        .tab-content.active {
            display: block;
        }
        .nav-tab-wrapper {
            margin-bottom: 20px;
        }
        </style>

        <script>
        function switchTab(evt, tabName) {
            var i, tabcontent, tablinks;

            // Hide all tab content
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // Remove active class from all tabs
            tablinks = document.getElementsByClassName("nav-tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("nav-tab-active");
            }

            // Show the selected tab and mark as active
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.classList.add("nav-tab-active");

            evt.preventDefault();
        }

        // Plugin Management Functions
        function enablePluginAllDatabases() {
            if (!confirm('<?php _e('Are you sure you want to enable the plugin in all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('enable_plugin', '<?php _e('Enabling plugin in all databases...', 'yxjto-gateway'); ?>');
        }

        function disablePluginAllDatabases() {
            if (!confirm('<?php _e('Are you sure you want to disable the plugin in all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('disable_plugin', '<?php _e('Disabling plugin in all databases...', 'yxjto-gateway'); ?>');
        }

        // User Management Functions
        function copyCurrentUserSession() {
            if (!confirm('<?php _e('Copy current user session to all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('copy_user_session', '<?php _e('Copying user session to all databases...', 'yxjto-gateway'); ?>');
        }

        function copyCurrentUserAccount() {
            if (!confirm('<?php _e('Copy current user account to all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('copy_user_account', '<?php _e('Copying user account to all databases...', 'yxjto-gateway'); ?>');
        }

        // Payment Gateway Management Functions
        function enablePaymentGatewayAllDatabases() {
            if (!confirm('<?php _e('Enable payment gateway in all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('enable_payment_gateway', '<?php _e('Enabling payment gateway in all databases...', 'yxjto-gateway'); ?>');
        }

        function disablePaymentGatewayAllDatabases() {
            if (!confirm('<?php _e('Disable payment gateway in all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('disable_payment_gateway', '<?php _e('Disabling payment gateway in all databases...', 'yxjto-gateway'); ?>');
        }

        function createTransactionLogTables() {
            if (!confirm('<?php _e('Create transaction log tables in all databases?', 'yxjto-gateway'); ?>')) {
                return;
            }

            performDatabaseOperation('create_transaction_tables', '<?php _e('Creating transaction log tables...', 'yxjto-gateway'); ?>');
        }

        // Generic function to perform database operations
        function performDatabaseOperation(operation, message) {
            // Show loading message
            const resultDiv = document.getElementById('operation-result') || createResultDiv();
            resultDiv.innerHTML = '<div class="notice notice-info"><p>' + message + '</p></div>';

            const formData = new FormData();
            formData.append('action', 'yxjto_system_operation');
            formData.append('operation', operation);
            formData.append('nonce', '<?php echo wp_create_nonce('yxjto_system_operation'); ?>');

            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Operation response:', data); // 调试日志

                if (data.success) {
                    let html = '<div class="notice notice-success"><p><strong>✅ 成功:</strong> ' + data.data.message + '</p>';

                    // 显示详细信息
                    if (data.data.details && typeof data.data.details === 'object') {
                        html += '<details style="margin-top: 10px;"><summary>详细信息</summary>';
                        html += '<pre style="background: #f0f0f0; padding: 10px; margin-top: 5px; border-radius: 3px;">';
                        html += JSON.stringify(data.data.details, null, 2);
                        html += '</pre></details>';
                    }

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    let errorMessage = data.data ? data.data.message : '<?php _e('Operation failed', 'yxjto-gateway'); ?>';
                    let html = '<div class="notice notice-error"><p><strong>❌ 失败:</strong> ' + errorMessage + '</p>';

                    // 显示错误详细信息
                    if (data.data && data.data.details && typeof data.data.details === 'object') {
                        html += '<details style="margin-top: 10px;"><summary>错误详情</summary>';
                        html += '<pre style="background: #f0f0f0; padding: 10px; margin-top: 5px; border-radius: 3px;">';
                        html += JSON.stringify(data.data.details, null, 2);
                        html += '</pre></details>';
                    }

                    html += '</div>';
                    resultDiv.innerHTML = html;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Network error occurred', 'yxjto-gateway'); ?></p></div>';
            });
        }

        function createResultDiv() {
            const resultDiv = document.createElement('div');
            resultDiv.id = 'operation-result';
            resultDiv.style.marginTop = '20px';

            // 尝试找到当前活跃的标签页
            const generalTab = document.getElementById('general');
            const userManagementTab = document.getElementById('user-management');

            // 检查哪个标签页是可见的
            if (userManagementTab && userManagementTab.style.display !== 'none') {
                userManagementTab.appendChild(resultDiv);
            } else if (generalTab) {
                generalTab.appendChild(resultDiv);
            } else {
                // 如果都找不到，就添加到body
                document.body.appendChild(resultDiv);
            }

            return resultDiv;
        }
        </script>
        <?php
    }

    /**
     * 配送复制设置页面
     */
    public function shipping_replication_settings_page() {
        // 处理表单提交
        if (isset($_POST['action']) && wp_verify_nonce($_POST['_wpnonce'], 'yxjto_shipping_replication_settings')) {
            $this->handle_shipping_replication_settings_form();
        }

        // 获取当前配送复制设置
        $shipping_config = WP_Multi_DB_Config_Manager::get_config('shipping_replication');
        $shipping_replication = YXJTO_Shipping_Replication::get_instance();
        $sync_status = $shipping_replication->get_sync_status();

        ?>
        <div class="wrap">
            <h1><?php _e('Shipping Replication Settings', 'yxjto-gateway'); ?></h1>

            <!-- 状态概览 -->
            <div class="card" style="margin-bottom: 20px;">
                <h2><?php _e('Shipping Replication Status', 'yxjto-gateway'); ?></h2>
                <p><strong><?php _e('Status:', 'yxjto-gateway'); ?></strong> 
                    <span style="color: <?php echo $sync_status['enabled'] ? 'green' : 'red'; ?>">
                        <?php echo $sync_status['enabled'] ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'); ?>
                    </span>
                </p>
                <p><strong><?php _e('Target Databases:', 'yxjto-gateway'); ?></strong> <?php echo $sync_status['target_databases']; ?></p>
                <p><strong><?php _e('Sync Mode:', 'yxjto-gateway'); ?></strong> <?php echo ucfirst($sync_status['sync_mode']); ?></p>
                <p><strong><?php _e('Last Sync:', 'yxjto-gateway'); ?></strong> 
                    <?php echo $sync_status['last_sync'] ? $sync_status['last_sync'] : __('Never', 'yxjto-gateway'); ?>
                </p>
                <p><strong><?php _e('Log File Size:', 'yxjto-gateway'); ?></strong> 
                    <?php echo $sync_status['log_file_size'] ? size_format($sync_status['log_file_size']) : '0 bytes'; ?>
                </p>
            </div>

            <!-- 标签页导航 -->
            <h2 class="nav-tab-wrapper">
                <a href="#general-settings" class="nav-tab nav-tab-active" onclick="switchShippingTab(event, 'general-settings')"><?php _e('General Settings', 'yxjto-gateway'); ?></a>
                <a href="#sync-options" class="nav-tab" onclick="switchShippingTab(event, 'sync-options')"><?php _e('Sync Options', 'yxjto-gateway'); ?></a>
                <a href="#actions" class="nav-tab" onclick="switchShippingTab(event, 'actions')"><?php _e('Actions', 'yxjto-gateway'); ?></a>
                <a href="#logs" class="nav-tab" onclick="switchShippingTab(event, 'logs')"><?php _e('Logs', 'yxjto-gateway'); ?></a>
            </h2>

            <form method="post" action="">
                <?php wp_nonce_field('yxjto_shipping_replication_settings'); ?>
                <input type="hidden" name="action" value="save_shipping_replication_settings" />

                <!-- 基本设置标签页 -->
                <div class="tab-content" id="general-settings" style="display: block;">
                    <h3><?php _e('Basic Shipping Replication Settings', 'yxjto-gateway'); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Shipping Replication', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_shipping_replication" value="1" 
                                           <?php checked($shipping_config['enable_shipping_replication']); ?> />
                                    <?php _e('Enable automatic shipping replication across all databases', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled, shipping zones, methods, classes, and settings will be automatically replicated to all connected databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Sync Mode', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="sync_mode">
                                    <option value="realtime" <?php selected($shipping_config['sync_mode'], 'realtime'); ?>>
                                        <?php _e('Real-time', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="manual" <?php selected($shipping_config['sync_mode'], 'manual'); ?>>
                                        <?php _e('Manual', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Real-time: Changes are synchronized immediately. Manual: Changes are synchronized only when requested.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Conflict Resolution', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="conflict_resolution">
                                    <option value="newest" <?php selected($shipping_config['conflict_resolution'], 'newest'); ?>>
                                        <?php _e('Use Newest', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="skip" <?php selected($shipping_config['conflict_resolution'], 'skip'); ?>>
                                        <?php _e('Skip Conflicts', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('How to handle conflicts when the same shipping data exists in multiple databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Logging', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_sync_logging" value="1" 
                                           <?php checked($shipping_config['enable_sync_logging']); ?> />
                                    <?php _e('Log all shipping synchronization activities', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 同步选项标签页 -->
                <div class="tab-content" id="sync-options" style="display: none;">
                    <h3><?php _e('Detailed Sync Options', 'yxjto-gateway'); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Shipping Zones', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_shipping_zones_sync" value="1" 
                                           <?php checked($shipping_config['enable_shipping_zones_sync']); ?> />
                                    <?php _e('Sync shipping zones and their settings', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('包括配送区域、区域位置、运输方法等的完整同步', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Shipping Methods', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_shipping_methods_sync" value="1" 
                                           <?php checked($shipping_config['enable_shipping_methods_sync']); ?> />
                                    <?php _e('Sync shipping methods configuration', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('同步运输方法的配置和设置', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Shipping Classes', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_shipping_classes_sync" value="1" 
                                           <?php checked($shipping_config['enable_shipping_classes_sync']); ?> />
                                    <?php _e('Sync product shipping classes', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('同步产品配送类别和相关设置', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Local Pickup', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_local_pickup_sync" value="1" 
                                           <?php checked($shipping_config['enable_local_pickup_sync']); ?> />
                                    <?php _e('Sync local pickup settings', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('同步本地自提相关的所有设置和配置', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Advanced Options', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="exclude_disabled_zones" value="1" 
                                           <?php checked($shipping_config['exclude_disabled_zones']); ?> />
                                    <?php _e('Exclude disabled shipping zones', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="sync_shipping_rates" value="1" 
                                           <?php checked($shipping_config['sync_shipping_rates']); ?> />
                                    <?php _e('Sync shipping rates and calculations', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="sync_free_shipping_conditions" value="1" 
                                           <?php checked($shipping_config['sync_free_shipping_conditions']); ?> />
                                    <?php _e('Sync free shipping conditions', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="backup_before_sync" value="1" 
                                           <?php checked($shipping_config['backup_before_sync']); ?> />
                                    <?php _e('Create backup before synchronization', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Retry Settings', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <?php _e('Max Retries:', 'yxjto-gateway'); ?>
                                    <input type="number" name="max_retries" min="1" max="10" 
                                           value="<?php echo esc_attr($shipping_config['max_retries']); ?>" />
                                </label>
                                <p class="description">
                                    <?php _e('Number of retry attempts for failed synchronizations.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 操作标签页 -->
                <div class="tab-content" id="actions" style="display: none;">
                    <h3><?php _e('Shipping Replication Actions', 'yxjto-gateway'); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Manual Synchronization', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button button-primary" onclick="runFullShippingSync()">
                                    <?php _e('Run Full Shipping Sync', 'yxjto-gateway'); ?>
                                </button>
                                <p class="description">
                                    <?php _e('Manually synchronize all shipping data to all databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Test Synchronization', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="testShippingConnection()">
                                    <?php _e('Test Database Connections', 'yxjto-gateway'); ?>
                                </button>
                                <p class="description">
                                    <?php _e('Test connectivity to all target databases.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Log Management', 'yxjto-gateway'); ?></th>
                            <td>
                                <button type="button" class="button" onclick="clearShippingLogs()">
                                    <?php _e('Clear Logs', 'yxjto-gateway'); ?>
                                </button>
                                <button type="button" class="button" onclick="downloadShippingLogs()">
                                    <?php _e('Download Logs', 'yxjto-gateway'); ?>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 日志标签页 -->
                <div class="tab-content" id="logs" style="display: none;">
                    <h3><?php _e('Shipping Replication Logs', 'yxjto-gateway'); ?></h3>
                    
                    <div style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; height: 400px; overflow-y: scroll; font-family: monospace;">
                        <?php
                        $log_file = WP_CONTENT_DIR . '/uploads/yxjto-shipping-replication.log';
                        if (file_exists($log_file)) {
                            $logs = file_get_contents($log_file);
                            echo nl2br(esc_html($logs));
                        } else {
                            echo __('No logs available.', 'yxjto-gateway');
                        }
                        ?>
                    </div>
                </div>

                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" 
                           value="<?php _e('Save Settings', 'yxjto-gateway'); ?>" />
                </p>
            </form>
        </div>

        <script>
        function switchShippingTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("nav-tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" nav-tab-active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " nav-tab-active";
        }

        function runFullShippingSync() {
            if (!confirm('<?php _e('Are you sure you want to run a full shipping synchronization? This may take some time.', 'yxjto-gateway'); ?>')) {
                return;
            }
            
            var resultDiv = createResultDiv();
            resultDiv.innerHTML = '<p><?php _e('Running full shipping synchronization...', 'yxjto-gateway'); ?></p>';
            
            jQuery.post(ajaxurl, {
                action: 'yxjto_run_shipping_sync',
                _wpnonce: '<?php echo wp_create_nonce('yxjto_shipping_sync'); ?>'
            }, function(response) {
                console.log('Shipping sync response:', response); // 调试日志
                if (response.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    resultDiv.innerHTML = '<div class="notice notice-error"><p>同步失败: ' + errorMessage + '</p></div>';
                }
            }).fail(function(xhr, status, error) {
                console.log('AJAX request failed:', xhr, status, error); // 调试日志
                resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Request failed.', 'yxjto-gateway'); ?> Status: ' + status + ', Error: ' + error + '</p></div>';
            });
        }

        function testShippingConnection() {
            var resultDiv = createResultDiv();
            resultDiv.innerHTML = '<p><?php _e('Testing database connections...', 'yxjto-gateway'); ?></p>';
            
            jQuery.post(ajaxurl, {
                action: 'yxjto_test_shipping_connections',
                _wpnonce: '<?php echo wp_create_nonce('yxjto_shipping_test'); ?>'
            }, function(response) {
                if (response.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                } else {
                    resultDiv.innerHTML = '<div class="notice notice-error"><p>' + response.data.message + '</p></div>';
                }
            }).fail(function() {
                resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Request failed.', 'yxjto-gateway'); ?></p></div>';
            });
        }

        function clearShippingLogs() {
            if (!confirm('<?php _e('Are you sure you want to clear all shipping replication logs?', 'yxjto-gateway'); ?>')) {
                return;
            }
            
            jQuery.post(ajaxurl, {
                action: 'yxjto_clear_shipping_logs',
                _wpnonce: '<?php echo wp_create_nonce('yxjto_shipping_logs'); ?>'
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data.message);
                }
            });
        }

        function downloadShippingLogs() {
            window.location.href = ajaxurl + '?action=yxjto_download_shipping_logs&_wpnonce=<?php echo wp_create_nonce('yxjto_shipping_logs'); ?>';
        }

        function createResultDiv() {
            var existingResult = document.getElementById('shipping-sync-result');
            if (existingResult) {
                existingResult.remove();
            }
            
            var resultDiv = document.createElement('div');
            resultDiv.id = 'shipping-sync-result';
            resultDiv.style.marginTop = '20px';
            
            var actionsTab = document.getElementById('actions');
            if (actionsTab) {
                actionsTab.appendChild(resultDiv);
            }
            
            return resultDiv;
        }
        </script>
        <?php
    }

    /**
     * 处理配送复制设置表单提交
     */
    private function handle_shipping_replication_settings_form() {
        if ($_POST['action'] === 'save_shipping_replication_settings') {
            $settings = [
                'enable_shipping_replication' => isset($_POST['enable_shipping_replication']),
                'enable_shipping_zones_sync' => isset($_POST['enable_shipping_zones_sync']),
                'enable_shipping_methods_sync' => isset($_POST['enable_shipping_methods_sync']),
                'enable_shipping_classes_sync' => isset($_POST['enable_shipping_classes_sync']),
                'enable_local_pickup_sync' => isset($_POST['enable_local_pickup_sync']),
                'sync_mode' => sanitize_text_field($_POST['sync_mode']),
                'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution']),
                'enable_sync_logging' => isset($_POST['enable_sync_logging']),
                'exclude_disabled_zones' => isset($_POST['exclude_disabled_zones']),
                'sync_shipping_rates' => isset($_POST['sync_shipping_rates']),
                'sync_free_shipping_conditions' => isset($_POST['sync_free_shipping_conditions']),
                'backup_before_sync' => isset($_POST['backup_before_sync']),
                'max_retries' => intval($_POST['max_retries'])
            ];

            // 保留其他现有设置
            $existing_config = WP_Multi_DB_Config_Manager::get_config('shipping_replication');
            $settings['last_sync'] = $existing_config['last_sync'];

            // 更新配置
            foreach ($settings as $key => $value) {
                WP_Multi_DB_Config_Manager::update_config('shipping_replication', $key, $value);
            }

            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Shipping replication settings saved successfully.', 'yxjto-gateway') . '</p></div>';
            });
        }
    }

    /**
     * 税收复制设置页面
     */
    public function tax_replication_settings_page() {
        // 包含税收复制管理页面文件
        $tax_admin_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-tax-replication-admin.php';
        
        if (file_exists($tax_admin_file)) {
            require_once $tax_admin_file;
            
            // 调用税收复制管理页面的渲染方法
            if (class_exists('YXJTO_Tax_Replication_Admin')) {
                YXJTO_Tax_Replication_Admin::render_admin_page();
            } else {
                echo '<div class="wrap"><h1>' . __('税收复制设置', 'yxjto-gateway') . '</h1>';
                echo '<div class="notice notice-error"><p>' . __('税收复制管理类未找到。', 'yxjto-gateway') . '</p></div></div>';
            }
        } else {
            echo '<div class="wrap"><h1>' . __('税收复制设置', 'yxjto-gateway') . '</h1>';
            echo '<div class="notice notice-error"><p>' . __('税收复制管理文件未找到：', 'yxjto-gateway') . esc_html($tax_admin_file) . '</p></div></div>';
        }
    }

    /**
     * 测试中心页面
     */
    public function test_center_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Test Center', 'yxjto-gateway'); ?></h1>

            <div class="notice notice-info">
                <p><?php _e('Access various testing tools and debugging utilities.', 'yxjto-gateway'); ?></p>
            </div>

            <!-- 综合订单测试功能 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 25px; border-radius: 10px; margin: 20px 0; color: white; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
                <h2 style="color: white; margin-top: 0; font-size: 24px; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"><?php _e('🛒 综合订单到支付测试', 'yxjto-gateway'); ?></h2>
                <p style="font-size: 16px; margin-bottom: 20px; opacity: 0.9;"><?php _e('完整的订单创建到支付流程测试，包括产品选择、服务配置、优惠券应用和多数据库同步验证。', 'yxjto-gateway'); ?></p>
                
                <div style="text-align: center;">
                    <a href="<?php echo admin_url('admin.php?page=yxjto-comprehensive-order-test'); ?>" 
                       style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); 
                              color: white; 
                              padding: 15px 30px; 
                              font-size: 18px; 
                              font-weight: bold; 
                              text-decoration: none; 
                              border-radius: 50px; 
                              box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
                              transition: all 0.3s ease;
                              display: inline-block;
                              text-transform: uppercase;
                              letter-spacing: 1px;">
                        <?php _e('🚀 启动综合测试', 'yxjto-gateway'); ?>
                    </a>
                </div>
                
                <div style="margin-top: 20px; font-size: 14px; opacity: 0.8;">
                    <strong>✨ 功能特色：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>🛍️ 选择任意产品和服务组合</li>
                        <li>🎫 应用多种优惠券和折扣</li>
                        <li>💳 完整的PayPal支付流程测试</li>
                        <li>🔄 多数据库同步验证</li>
                        <li>📊 实时状态监控和报告</li>
                    </ul>
                </div>
            </div>

            <!-- 主要测试工具 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #28a745;"><?php _e('🎯 Primary Test Tools', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Essential testing tools for core functionality verification.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 10px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-all-fixes-verification.php'; ?>" target="_blank" style="font-weight: bold; color: #28a745; font-size: 22px; text-decoration: none;"><?php _e('✅ All Fixes Verification', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-system-settings-debug.php'; ?>" target="_blank" style="font-weight: bold; color: #28a745; font-size: 20px; text-decoration: none;"><?php _e('🔍 System Settings Debug', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-final-status-report.php'; ?>" target="_blank" style="font-weight: bold; color: #6f42c1; font-size: 20px; text-decoration: none;"><?php _e('📊 Final Status Report', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 系统功能测试 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #17a2b8;"><?php _e('🔧 System Function Tests', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Test specific system functions and components.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo admin_url('admin.php?page=yxjto-payment-verification'); ?>" style="font-weight: bold; color: #dc3545; font-size: 20px; text-decoration: none;"><?php _e('🔒 Payment Verification Check', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/simple-payment-verification-check.php'; ?>" target="_blank" style="font-weight: bold; color: #dc3545; font-size: 18px; text-decoration: none;"><?php _e('🔍 Simple Payment Verification', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-user-copy-buttons.php'; ?>" target="_blank" style="font-weight: bold; color: #17a2b8; font-size: 18px; text-decoration: none;"><?php _e('🔄 User Copy Buttons Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-user-copy-debug.php'; ?>" target="_blank" style="font-weight: bold; color: #17a2b8; font-size: 18px; text-decoration: none;"><?php _e('🔍 User Copy Debug', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-button-events.php'; ?>" target="_blank" style="font-weight: bold; color: #17a2b8; font-size: 18px; text-decoration: none;"><?php _e('🔘 Button Events Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-user-management-settings.php'; ?>" target="_blank" style="font-weight: bold; color: #17a2b8; font-size: 18px; text-decoration: none;"><?php _e('🔧 User Management Settings Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-duplicate-prevention.php'; ?>" target="_blank" style="font-weight: bold; color: #17a2b8; font-size: 18px; text-decoration: none;"><?php _e('🛡️ Duplicate Prevention Test', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 订单复制测试 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #fd7e14;"><?php _e('📦 Order Replication Tests', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Comprehensive testing tools for order replication functionality.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-fixed-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('🔧 Fixed Replication Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-fee-shipping-timing.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('⏰ Fee & Shipping Timing Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-calculate-totals-hook.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('🧮 Calculate Totals Hook Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-fee-shipping-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('💰 Fee & Shipping Replication', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-shipping-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #28a745; font-size: 20px; text-decoration: none;"><?php _e('🚚 Shipping Replication Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-multi-item-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('🛒 Multi-Item Order Replication', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-simple-order-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #fd7e14; font-size: 18px; text-decoration: none;"><?php _e('🚀 Simple Order Replication', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 产品复制测试 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #e83e8c;"><?php _e('🛍️ Product Replication Tests', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Testing tools for product replication and management.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-product-replication-summary.php'; ?>" target="_blank" style="font-weight: bold; color: #e83e8c; font-size: 18px; text-decoration: none;"><?php _e('🛍️ Product Replication Summary', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-order-with-products.php'; ?>" target="_blank" style="font-weight: bold; color: #e83e8c; font-size: 18px; text-decoration: none;"><?php _e('🛍️ Create Order with Products', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-enhanced-product-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #e83e8c; font-size: 18px; text-decoration: none;"><?php _e('🛍️ Enhanced Product Replication', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-product-replication.php'; ?>" target="_blank" style="font-weight: bold; color: #e83e8c; font-size: 18px; text-decoration: none;"><?php _e('🛍️ Product Replication Test', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 数据分析工具 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #6c757d;"><?php _e('📊 Data Analysis Tools', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Tools for analyzing and comparing data across databases.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo admin_url('admin.php?page=yxjto-order-comparison'); ?>" style="font-weight: bold; color: #007cba; font-size: 20px; text-decoration: none;"><?php _e('🔄 订单比较测试', 'yxjto-gateway'); ?></a> <span style="background: #007cba; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">专用页面</span></li>
                    <li style="margin: 8px 0;"><a href="<?php echo admin_url('admin.php?page=yxjto-gateway'); ?>" style="font-weight: bold; color: #007cba; font-size: 18px; text-decoration: none;"><?php _e('🔄 订单比较(多标签)', 'yxjto-gateway'); ?></a> <span style="background: #007cba; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">推荐</span></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/wordpress-test.php'; ?>" target="_blank" style="font-weight: bold; color: #28a745; font-size: 18px; text-decoration: none;"><?php _e('🧪 WordPress环境测试', 'yxjto-gateway'); ?></a> <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">新增</span></li>
                    <li style="margin: 8px 0;"><a href="<?php echo admin_url('admin.php?page=yxjto-test-runner'); ?>" style="font-weight: bold; color: #6f42c1; font-size: 18px; text-decoration: none;"><?php _e('🚀 测试运行器', 'yxjto-gateway'); ?></a> <span style="background: #6f42c1; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">新增</span></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-database-comparison.php'; ?>" target="_blank" style="font-weight: bold; color: #6c757d; font-size: 16px; text-decoration: none;"><?php _e('🔍 Database Comparison Tool', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-replication-comparison.php'; ?>" target="_blank" style="font-weight: bold; color: #6c757d; font-size: 16px; text-decoration: none;"><?php _e('📊 Replication Comparison Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-source-database-extraction.php'; ?>" target="_blank" style="font-weight: bold; color: #6c757d; font-size: 16px; text-decoration: none;"><?php _e('🔍 Source Database Extraction', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-order-items-verification.php'; ?>" target="_blank" style="font-weight: bold; color: #6c757d; font-size: 16px; text-decoration: none;"><?php _e('📦 Order Items Verification', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 系统配置工具 -->
            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #6f42c1;"><?php _e('⚙️ System Configuration Tools', 'yxjto-gateway'); ?></h2>
                <p><?php _e('Configuration testing and system information tools.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-config.php?run_config_test=1'; ?>" target="_blank" style="font-weight: bold; color: #6f42c1; font-size: 16px; text-decoration: none;"><?php _e('⚙️ Configuration Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-settings-display.php'; ?>" target="_blank" style="font-weight: bold; color: #6f42c1; font-size: 16px; text-decoration: none;"><?php _e('📋 Settings Display Test', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-syntax-check.php'; ?>" target="_blank" style="font-weight: bold; color: #6f42c1; font-size: 16px; text-decoration: none;"><?php _e('🔍 Syntax Check', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo plugin_dir_url(__FILE__) . 'test/'; ?>" target="_blank" style="font-weight: bold; color: #6f42c1; font-size: 16px; text-decoration: none;"><?php _e('📁 General Test Center', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 调试工具 -->
            <div style="background: #fff8e1; padding: 20px; border: 1px solid #ffc107; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #856404;"><?php _e('🐛 Debug Tools', 'yxjto-gateway'); ?></h2>
                <p style="color: #856404;"><?php _e('Advanced debugging tools for troubleshooting issues.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-order-items-debug.php'; ?>" target="_blank" style="font-weight: bold; color: #856404; font-size: 16px; text-decoration: none;"><?php _e('🐛 Order Items Debug Tool', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-order-items-extraction-debug.php'; ?>" target="_blank" style="font-weight: bold; color: #856404; font-size: 16px; text-decoration: none;"><?php _e('🔍 Order Items Extraction Debug', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-product-debug.php'; ?>" target="_blank" style="font-weight: bold; color: #856404; font-size: 16px; text-decoration: none;"><?php _e('🔍 Product Debug Tool', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-new-order-timing.php'; ?>" target="_blank" style="font-weight: bold; color: #856404; font-size: 16px; text-decoration: none;"><?php _e('⏱️ New Order Timing Analysis', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <!-- 危险操作工具 -->
            <div style="background: #fff5f5; padding: 20px; border: 2px solid #dc3545; border-radius: 4px; margin: 20px 0;">
                <h2 style="color: #dc3545;"><?php _e('⚠️ Dangerous Operations', 'yxjto-gateway'); ?></h2>
                <p style="color: #721c24;"><?php _e('Use these tools with extreme caution. They can permanently delete data.', 'yxjto-gateway'); ?></p>

                <ul style="list-style: none; padding: 0;">
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-order-cleanup.php'; ?>" target="_blank" style="font-weight: bold; color: #dc3545; font-size: 16px; text-decoration: none;"><?php _e('🗑️ Order Data Cleanup Tool', 'yxjto-gateway'); ?></a></li>
                    <li style="margin: 8px 0;"><a href="<?php echo YXJTO_GATEWAY_PLUGIN_URL . 'test/test-clean-duplicate-orders.php'; ?>" target="_blank" style="font-weight: bold; color: #dc3545; font-size: 16px; text-decoration: none;"><?php _e('🧹 Clean Duplicate Orders', 'yxjto-gateway'); ?></a></li>
                </ul>
            </div>

            <div style="background: white; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
                <h2><?php _e('Quick Status Check', 'yxjto-gateway'); ?></h2>

                <?php
                $databases = WP_Multi_DB_Config_Manager::get_databases();
                $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();
                $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
                $order_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
                $hpos_enabled = false;
                if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
                    $hpos_enabled = \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
                }
                ?>

                <table class="widefat">
                    <tr>
                        <th><?php _e('Configured Databases', 'yxjto-gateway'); ?></th>
                        <td><?php echo count($databases); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('IP Rules', 'yxjto-gateway'); ?></th>
                        <td><?php echo count($ip_rules); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('URL Rules', 'yxjto-gateway'); ?></th>
                        <td><?php echo count($url_rules); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Current Database', 'yxjto-gateway'); ?></th>
                        <td><strong><?php echo esc_html($this->get_current_database()); ?></strong></td>
                    </tr>
                    <?php if (class_exists('WooCommerce')): ?>
                    <tr>
                        <th><?php _e('WooCommerce Status', 'yxjto-gateway'); ?></th>
                        <td><span style="color: green;">✅ Active</span></td>
                    </tr>
                    <tr>
                        <th><?php _e('HPOS Status', 'yxjto-gateway'); ?></th>
                        <td><?php echo $hpos_enabled ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Order Replication', 'yxjto-gateway'); ?></th>
                        <td><?php echo !empty($order_settings['enable_order_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Status Replication', 'yxjto-gateway'); ?></th>
                        <td><?php echo !empty($order_settings['enable_status_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Payment Info Replication', 'yxjto-gateway'); ?></th>
                        <td><?php echo !empty($order_settings['enable_payment_info_replication']) ? '<span style="color: green;">✅ Enabled</span>' : '<span style="color: red;">❌ Disabled</span>'; ?></td>
                    </tr>
                    <?php else: ?>
                    <tr>
                        <th><?php _e('WooCommerce Status', 'yxjto-gateway'); ?></th>
                        <td><span style="color: red;">❌ Inactive</span></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
        <?php
    }

    /**
     * 支付验证检查页面
     */
    public function payment_verification_page() {
        $verification_file = plugin_dir_path(__FILE__) . 'test/admin-payment-verification.php';
        
        if (file_exists($verification_file)) {
            include $verification_file;
        } else {
            // 如果文件不存在，显示错误信息
            echo '<div class="wrap">';
            echo '<h1>' . __('PayPal 支付验证', 'yxjto-paypal-multi-gateway') . '</h1>';
            echo '<div class="notice notice-error"><p>' . sprintf(__('验证文件不存在: %s', 'yxjto-paypal-multi-gateway'), $verification_file) . '</p></div>';
            echo '</div>';
        }
    }

    /**
     * 订单比较测试页面
     */
    public function order_comparison_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // 确保订单比较类已加载
        if (!class_exists('YXJTO_Order_Comparison')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-order-comparison.php';
        }

        // 创建订单比较实例并渲染页面
        $order_comparison = new YXJTO_Order_Comparison();
        $order_comparison->render_comparison_page();
    }

    /**
     * 订单号管理页面
     */
    public function order_manager_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // 确保订单管理类已加载
        if (!class_exists('YXJTO_Order_Manager_Admin')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/admin/class-order-manager-admin.php';
        }

        // 创建订单管理实例并渲染页面
        $order_manager = YXJTO_Order_Manager_Admin::get_instance();
        $order_manager->render_admin_page();
    }

    /**
     * 综合订单测试页面
     */
    public function comprehensive_order_test_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // 处理AJAX请求
        if (isset($_POST['action'])) {
            $this->handle_comprehensive_test_ajax();
            return;
        }

        ?>
        <div class="wrap">
            <h1 style="color: #667eea; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">🛒 综合订单到支付测试中心</h1>
            
            <div class="notice notice-info" style="border-left: 4px solid #667eea;">
                <p><strong>🌟 功能说明：</strong>创建完整的订单并进行端到端支付测试，包括产品选择、优惠券应用、多数据库同步验证等。</p>
            </div>

            <!-- 测试状态面板 -->
            <div id="test-status-panel" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2 style="margin-top: 0; color: white;">📊 测试状态监控</h2>
                <div id="status-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0;">💳 PayPal 账户</h4>
                        <div id="paypal-status">检测中...</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0;">🗄️ 数据库连接</h4>
                        <div id="database-status">检测中...</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0;">🛍️ 可用产品</h4>
                        <div id="products-status">加载中...</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4 style="margin: 0 0 10px 0;">🎫 优惠券</h4>
                        <div id="coupons-status">加载中...</div>
                    </div>
                </div>
            </div>

            <!-- 订单创建区域 -->
            <div class="comprehensive-test-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                
                <!-- 左侧：订单配置 -->
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h2 style="color: #495057; border-bottom: 3px solid #667eea; padding-bottom: 10px;">🛍️ 订单配置</h2>
                    
                    <form id="comprehensive-order-form" method="post">
                        <!-- 产品选择 -->
                        <div class="form-section" style="margin: 20px 0;">
                            <h3 style="color: #e83e8c;">📦 选择产品</h3>
                            <div id="products-selection" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px;">
                                <div class="loading">正在加载产品...</div>
                            </div>
                        </div>

                        <!-- 优惠券选择 -->
                        <div class="form-section" style="margin: 20px 0;">
                            <h3 style="color: #fd7e14;">🎫 选择优惠券</h3>
                            <div id="coupons-selection" style="max-height: 150px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px;">
                                <div class="loading">正在加载优惠券...</div>
                            </div>
                        </div>

                        <!-- 额外配置 -->
                        <div class="form-section" style="margin: 20px 0;">
                            <h3 style="color: #17a2b8;">⚙️ 测试配置</h3>
                            
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" id="include-shipping" checked> 
                                包含运费测试
                            </label>
                            
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" id="include-tax" checked> 
                                包含税费计算
                            </label>
                            
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" id="test-replication" checked> 
                                测试多数据库复制
                            </label>
                            
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" id="simulate-payment" checked> 
                                模拟PayPal支付流程
                            </label>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="text-align: center; margin-top: 30px;">
                            <button type="button" id="create-test-order" class="button button-primary" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); border: none; padding: 15px 30px; font-size: 16px; border-radius: 25px;">
                                🚀 创建测试订单
                            </button>
                            
                            <button type="button" id="reset-form" class="button" style="margin-left: 10px; padding: 15px 30px; font-size: 16px; border-radius: 25px;">
                                🔄 重置表单
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 右侧：测试结果 -->
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <h2 style="color: #495057; border-bottom: 3px solid #28a745; padding-bottom: 10px;">📋 测试结果</h2>
                    
                    <div id="test-results" style="min-height: 400px;">
                        <div class="placeholder" style="text-align: center; color: #6c757d; margin-top: 100px;">
                            <p style="font-size: 18px;">📊</p>
                            <p>选择产品和配置后点击"创建测试订单"开始测试</p>
                        </div>
                    </div>
                    
                    <!-- 实时日志 -->
                    <div id="test-log" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-top: 20px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; display: none;">
                        <h4 style="margin: 0 0 10px 0; color: #495057;">📝 实时测试日志</h4>
                        <div id="log-content"></div>
                    </div>
                </div>
            </div>

            <!-- 测试历史 -->
            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin: 20px 0;">
                <h2 style="color: #495057; border-bottom: 3px solid #6f42c1; padding-bottom: 10px;">📈 测试历史记录</h2>
                <div id="test-history">
                    <p style="color: #6c757d; text-align: center;">暂无测试记录</p>
                </div>
            </div>
        </div>

        <style>
        .comprehensive-test-container input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .product-item, .coupon-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .product-item:hover, .coupon-item:hover {
            background: #f8f9fa;
        }
        
        .product-info, .coupon-info {
            margin-left: 10px;
            flex: 1;
        }
        
        .product-name, .coupon-name {
            font-weight: bold;
            color: #495057;
        }
        
        .product-price, .coupon-desc {
            font-size: 12px;
            color: #6c757d;
        }
        
        .test-result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .result-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .result-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .result-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // 初始化页面
            initializeComprehensiveTest();
            
            // 创建测试订单
            $('#create-test-order').on('click', function() {
                createTestOrder();
            });
            
            // 重置表单
            $('#reset-form').on('click', function() {
                resetForm();
            });
            
            function initializeComprehensiveTest() {
                loadSystemStatus();
                loadProducts();
                loadCoupons();
            }
            
            function loadSystemStatus() {
                $.post(ajaxurl, {
                    action: 'yxjto_comprehensive_test',
                    test_action: 'get_system_status',
                    nonce: '<?php echo wp_create_nonce('yxjto_comprehensive_test'); ?>'
                }, function(response) {
                    if (response.success) {
                        updateStatusPanel(response.data);
                    }
                });
            }
            
            function loadProducts() {
                $.post(ajaxurl, {
                    action: 'yxjto_comprehensive_test',
                    test_action: 'get_products',
                    nonce: '<?php echo wp_create_nonce('yxjto_comprehensive_test'); ?>'
                }, function(response) {
                    if (response.success) {
                        renderProducts(response.data);
                    }
                });
            }
            
            function loadCoupons() {
                $.post(ajaxurl, {
                    action: 'yxjto_comprehensive_test',
                    test_action: 'get_coupons',
                    nonce: '<?php echo wp_create_nonce('yxjto_comprehensive_test'); ?>'
                }, function(response) {
                    if (response.success) {
                        renderCoupons(response.data);
                    }
                });
            }
            
            function updateStatusPanel(data) {
                $('#paypal-status').html(data.paypal_status || '未知');
                $('#database-status').html(data.database_status || '未知');
                $('#products-status').html(data.products_count + ' 个产品');
                $('#coupons-status').html(data.coupons_count + ' 个优惠券');
            }
            
            function renderProducts(products) {
                let html = '';
                products.forEach(function(product) {
                    html += `
                        <div class="product-item">
                            <input type="checkbox" name="selected_products[]" value="${product.id}">
                            <div class="product-info">
                                <div class="product-name">${product.name}</div>
                                <div class="product-price">¥${product.price} - SKU: ${product.sku}</div>
                            </div>
                        </div>
                    `;
                });
                $('#products-selection').html(html);
            }
            
            function renderCoupons(coupons) {
                let html = '';
                coupons.forEach(function(coupon) {
                    html += `
                        <div class="coupon-item">
                            <input type="checkbox" name="selected_coupons[]" value="${coupon.id}">
                            <div class="coupon-info">
                                <div class="coupon-name">${coupon.code}</div>
                                <div class="coupon-desc">${coupon.description}</div>
                            </div>
                        </div>
                    `;
                });
                $('#coupons-selection').html(html);
            }
            
            function createTestOrder() {
                const selectedProducts = $('input[name="selected_products[]"]:checked').map(function() {
                    return this.value;
                }).get();
                
                const selectedCoupons = $('input[name="selected_coupons[]"]:checked').map(function() {
                    return this.value;
                }).get();
                
                if (selectedProducts.length === 0) {
                    alert('请至少选择一个产品');
                    return;
                }
                
                $('#test-log').show();
                addLogEntry('开始创建测试订单...', 'info');
                
                const testConfig = {
                    products: selectedProducts,
                    coupons: selectedCoupons,
                    include_shipping: $('#include-shipping').is(':checked'),
                    include_tax: $('#include-tax').is(':checked'),
                    test_replication: $('#test-replication').is(':checked'),
                    simulate_payment: $('#simulate-payment').is(':checked')
                };
                
                $.post(ajaxurl, {
                    action: 'yxjto_comprehensive_test',
                    test_action: 'create_test_order',
                    config: testConfig,
                    nonce: '<?php echo wp_create_nonce('yxjto_comprehensive_test'); ?>'
                }, function(response) {
                    if (response.success) {
                        displayTestResults(response.data);
                        addLogEntry('测试订单创建成功', 'success');
                    } else {
                        addLogEntry('测试失败: ' + response.data, 'error');
                    }
                });
            }
            
            function displayTestResults(results) {
                let html = '<h3>🎉 测试完成</h3>';
                
                results.forEach(function(result) {
                    const className = 'result-' + result.type;
                    html += `<div class="test-result-item ${className}">${result.message}</div>`;
                });
                
                $('#test-results').html(html);
            }
            
            function addLogEntry(message, type) {
                const timestamp = new Date().toLocaleTimeString();
                const html = `<div class="log-entry log-${type}">[${timestamp}] ${message}</div>`;
                $('#log-content').append(html);
                $('#log-content').scrollTop($('#log-content')[0].scrollHeight);
            }
            
            function resetForm() {
                $('input[type="checkbox"]').prop('checked', false);
                $('#test-results').html(`
                    <div class="placeholder" style="text-align: center; color: #6c757d; margin-top: 100px;">
                        <p style="font-size: 18px;">📊</p>
                        <p>选择产品和配置后点击"创建测试订单"开始测试</p>
                    </div>
                `);
                $('#test-log').hide();
                $('#log-content').empty();
            }
        });
        </script>
        <?php
    }

    /**
     * 处理系统设置表单
     */
    private function handle_system_settings_form() {
        if (!isset($_POST['action'])) {
            return;
        }

        $action = sanitize_text_field($_POST['action']);

        if ($action === 'save_order_replication_settings') {
            $this->handle_order_replication_settings();
            return;
        }

        if ($action === 'save_user_management_settings') {
            $this->handle_user_management_settings();
            return;
        }

        // 处理用户管理设置（备用检查）
        if (isset($_POST['save_user_management'])) {
            $this->handle_user_management_settings();
            return;
        }

        // 处理其他系统设置
        $this->handle_other_system_settings($action);
    }

    /**
     * 处理用户管理设置
     */
    private function handle_user_management_settings() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        $user_settings = [
            'auto_copy_user_creation' => !empty($_POST['auto_copy_user_creation']),
            'auto_copy_user_sessions' => !empty($_POST['auto_copy_user_sessions']),
            'auto_copy_woocommerce_sessions' => !empty($_POST['auto_copy_woocommerce_sessions'])
        ];

        // 使用config-manager保存设置
        $result = WP_Multi_DB_Config_Manager::save_user_management_settings($user_settings);

        if ($result) {
            add_settings_error(
                'yxjto_user_management',
                'settings_updated',
                __('User management settings saved to configuration file successfully.', 'yxjto-gateway'),
                'updated'
            );
        } else {
            add_settings_error(
                'yxjto_user_management',
                'settings_error',
                __('Failed to save user management settings to configuration file.', 'yxjto-gateway'),
                'error'
            );
        }
    }





    /**
     * 处理订单复制设置
     */
    private function handle_order_replication_settings() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 收集订单复制设置
        $order_settings = [
            'enable_order_replication' => isset($_POST['enable_order_replication']),
            'enable_status_replication' => isset($_POST['enable_status_replication']),
            'enable_payment_info_replication' => isset($_POST['enable_payment_info_replication']),
            'enable_smart_product_replacement' => isset($_POST['enable_smart_product_replacement']),
            'enable_payment_verification' => isset($_POST['enable_payment_verification']),
            'enable_callback_handling' => isset($_POST['enable_callback_handling']),
            'product_replacement_mode' => sanitize_text_field($_POST['product_replacement_mode'] ?? 'same_price'),
            'enable_default_db_payment_redirect' => isset($_POST['enable_default_db_payment_redirect'])
        ];

        // 保存设置
        $result = WP_Multi_DB_Config_Manager::save_order_replication_settings($order_settings);

        if ($result) {
            echo '<div class="notice notice-success"><p>' . __('Order replication settings saved successfully!', 'yxjto-gateway') . '</p></div>';

            // 记录设置变更
            $this->log_info('Order replication settings updated: ' . json_encode($order_settings));
        } else {
            echo '<div class="notice notice-error"><p>' . __('Failed to save order replication settings.', 'yxjto-gateway') . '</p></div>';
            $this->log_error('Failed to save order replication settings');
        }
    }

    /**
     * 处理其他系统设置表单
     */
    private function handle_other_system_settings($action) {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $results = [];

        switch ($action) {
            case 'enable_all':
                $results = $this->enable_plugin_in_all_databases();
                $success_count = count(array_filter($results));
                $total_count = count($results);
                
                if ($success_count > 0) {
                    add_settings_error('yxjto_system', 'plugin_enabled', 
                        sprintf(__('Plugin enabled in %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                } else {
                    add_settings_error('yxjto_system', 'plugin_enable_failed', 
                        __('Failed to enable plugin in any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'disable_all':
                $results = $this->disable_plugin_in_all_databases();
                $success_count = count(array_filter($results));
                $total_count = count($results);
                
                if ($success_count > 0) {
                    add_settings_error('yxjto_system', 'plugin_disabled', 
                        sprintf(__('Plugin disabled in %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                } else {
                    add_settings_error('yxjto_system', 'plugin_disable_failed', 
                        __('Failed to disable plugin in any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'enable_gateway_all':
                $results = $this->enable_payment_gateway_in_all_databases();
                $success_count = count(array_filter($results));
                $total_count = count($results);

                if ($success_count > 0) {
                    add_settings_error('yxjto_system', 'gateway_enabled',
                        sprintf(__('Payment gateway enabled in %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                } else {
                    add_settings_error('yxjto_system', 'gateway_enable_failed',
                        __('Failed to enable payment gateway in any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'disable_gateway_all':
                $results = $this->disable_payment_gateway_in_all_databases();
                $success_count = count(array_filter($results));
                $total_count = count($results);

                if ($success_count > 0) {
                    add_settings_error('yxjto_system', 'gateway_disabled',
                        sprintf(__('Payment gateway disabled in %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                } else {
                    add_settings_error('yxjto_system', 'gateway_disable_failed',
                        __('Failed to disable payment gateway in any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'copy_current_user_session':
                $current_user_id = get_current_user_id();
                $this->log_info("copy_current_user_session action triggered for user ID: {$current_user_id}");
                
                if ($current_user_id) {
                    $session_results = $this->copy_user_session_to_all_databases_internal($current_user_id);
                    $success_count = count(array_filter($session_results));
                    $total_count = count($session_results);
                    
                    $this->log_info("Session copy results - Success: {$success_count}, Total: {$total_count}");
                    
                    if ($success_count > 0) {
                        add_settings_error('yxjto_system', 'user_session_copied', 
                            sprintf(__('Current user session copied to %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                    } else {
                        add_settings_error('yxjto_system', 'user_session_copy_failed', 
                            __('Failed to copy user session to any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                    }
                } else {
                    $this->log_error("copy_current_user_session: No user is currently logged in");
                    add_settings_error('yxjto_system', 'no_user_session', __('No user is currently logged in.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'copy_all_users':
                $result = $this->copy_all_users_to_all_databases();
                if ($result['success']) {
                    add_settings_error('yxjto_system', 'users_copied', 
                        sprintf(__('Successfully copied %d users to %d databases.', 'yxjto-gateway'), $result['users_count'], $result['db_count']), 'updated');
                } else {
                    add_settings_error('yxjto_system', 'users_copy_failed', 
                        __('Failed to copy users: ', 'yxjto-gateway') . $result['message'], 'error');
                }
                break;

            case 'copy_current_user_account':
                $current_user_id = get_current_user_id();
                $this->log_info("copy_current_user_account action triggered for user ID: {$current_user_id}");
                
                if ($current_user_id) {
                    $account_results = $this->copy_user_account_to_all_databases_internal($current_user_id);
                    $success_count = count(array_filter($account_results));
                    $total_count = count($account_results);
                    
                    $this->log_info("Account copy results - Success: {$success_count}, Total: {$total_count}");
                    
                    if ($success_count > 0) {
                        add_settings_error('yxjto_system', 'user_account_copied', 
                            sprintf(__('Current user account copied to %d out of %d databases.', 'yxjto-gateway'), $success_count, $total_count), 'updated');
                    } else {
                        add_settings_error('yxjto_system', 'user_account_copy_failed', 
                            __('Failed to copy user account to any database. Check error logs for details.', 'yxjto-gateway'), 'error');
                    }
                } else {
                    $this->log_error("copy_current_user_account: No user is currently logged in");
                    add_settings_error('yxjto_system', 'no_user_account', __('No user is currently logged in.', 'yxjto-gateway'), 'error');
                }
                break;

            case 'create_log_tables':
                foreach ($databases as $db_key => $db_config) {
                    if ($db_config['enabled']) {
                        // 这里添加创建交易日志表的逻辑
                        $results[$db_key] = 'Transaction log table created';
                    }
                }
                add_settings_error('yxjto_system', 'log_tables_created', __('Transaction log tables created in all databases.', 'yxjto-gateway'), 'updated');
                break;

            case 'save_user_management_settings':
                $user_management_settings = [
                    'auto_copy_user_creation' => isset($_POST['auto_copy_user_creation']),
                    'auto_copy_user_sessions' => isset($_POST['auto_copy_user_sessions']),
                    'auto_copy_woocommerce_sessions' => isset($_POST['auto_copy_woocommerce_sessions'])
                ];

                $save_result = WP_Multi_DB_Config_Manager::save_user_management_settings($user_management_settings);

                if ($save_result) {
                    add_settings_error('yxjto_system', 'user_management_settings_saved',
                        __('User management settings saved successfully.', 'yxjto-gateway'), 'updated');

                    // 记录设置变更日志
                    $this->log_info("User management settings updated: " . json_encode($user_management_settings));
                } else {
                    add_settings_error('yxjto_system', 'user_management_settings_failed',
                        __('Failed to save user management settings.', 'yxjto-gateway'), 'error');

                    $this->log_error("Failed to save user management settings");
                }
                break;
        }

        settings_errors('yxjto_system');
    }

    /**
     * 公共方法：复制用户会话到所有数据库（用于测试）
     */
    public function public_copy_user_session_to_all_databases($user_id) {
        if (!current_user_can('manage_options')) {
            $this->log_error("public_copy_user_session_to_all_databases: Insufficient permissions");
            return [];
        }
        
        return $this->copy_user_session_to_all_databases_internal($user_id);
    }
    
    /**
     * 公共方法：复制用户账号到所有数据库（用于测试）
     */
    public function public_copy_user_account_to_all_databases($user_id) {
        if (!current_user_can('manage_options')) {
            $this->log_error("public_copy_user_account_to_all_databases: Insufficient permissions");
            return [];
        }
        
        return $this->copy_user_account_to_all_databases_internal($user_id);
    }
    
    /**
     * 内部方法：复制用户会话到所有数据库
     */
    private function copy_user_session_to_all_databases_internal($user_id) {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $original_db = $this->get_current_database(); // 保存原始数据库
        $results = [];

        // 记录原始数据库信息
        $this->log_info("Original database at start: {$original_db}");
        
        // 获取当前用户的会话数据
        $session_tokens = get_user_meta($user_id, 'session_tokens', true);
        
        // 记录调试信息
        $this->log_info("Starting copy_user_session_to_all_databases for user ID: {$user_id}");
        $this->log_info("Original database: {$original_db}");
        $this->log_info("Session tokens: " . (empty($session_tokens) ? 'empty' : 'found ' . count($session_tokens)));
        $results['current_db'] = $current_db;
        if (empty($session_tokens)) {
            $this->log_warning("No session tokens found for user ID: {$user_id}");
            return $results;
        }

        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }
            
            if ($db_key === $current_db) {
                $this->log_info("Current database: {$db_key} (source database, already has the data)");
                $results[$db_key] = true; // 标记当前数据库为成功
                continue;
            }
            
            $this->log_info("Processing database: {$db_key}");
            
            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);
                
                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }
                
                // 检查用户是否存在于目标数据库
                $user_exists = get_user_by('ID', $user_id);
                
                if ($user_exists) {
                    // 检查usermeta表是否存在
                    global $wpdb;
                    $usermeta_table = $wpdb->usermeta;
                    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$usermeta_table}'") == $usermeta_table;
                    
                    if (!$table_exists) {
                        $results[$db_key] = false;
                        $this->log_error("Usermeta table does not exist in database: {$db_key} (table: {$usermeta_table})");
                        continue;
                    }
                    
                    $this->log_info("Found usermeta table: {$usermeta_table} in database: {$db_key}");
                    
                    // 更新用户会话数据
                    $update_result = update_user_meta($user_id, 'session_tokens', $session_tokens);
                    
                    $this->log_info("update_user_meta result for database {$db_key}: " . ($update_result !== false ? 'success' : 'failed'));
                    
                    if ($update_result !== false) {
                        $results[$db_key] = true;
                        $this->log_info("User session copied to database: {$db_key} for user ID: {$user_id}");
                    } else {
                        $results[$db_key] = false;
                        $this->log_error("Failed to update session tokens in database: {$db_key} - update_user_meta returned false");
                        
                        // 尝试直接SQL插入以调试
                        $meta_result = $wpdb->replace(
                            $usermeta_table,
                            [
                                'user_id' => $user_id,
                                'meta_key' => 'session_tokens',
                                'meta_value' => maybe_serialize($session_tokens)
                            ],
                            ['%d', '%s', '%s']
                        );
                        
                        if ($meta_result !== false) {
                            $results[$db_key] = true;
                            $this->log_info("Direct SQL update succeeded for database: {$db_key}");
                        } else {
                            $this->log_error("Direct SQL update also failed for database: {$db_key} - Error: " . $wpdb->last_error);
                        }
                    }
                } else {
                    $results[$db_key] = false;
                    $this->log_error("User ID {$user_id} does not exist in database: {$db_key}");
                }

                // 立即切换回原数据库
                $this->log_info("Switching back from {$db_key} to {$original_db}");
                $switch_back_result = $this->switch_database($original_db);
                if (!$switch_back_result) {
                    $this->log_error("Failed to switch back to original database: {$original_db}");
                }

            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in copy_user_session_to_all_databases for database {$db_key}: " . $e->getMessage());

                // 异常情况下也要切换回原数据库
                try {
                    $this->log_info("Exception recovery: Switching back to {$original_db}");
                    $this->switch_database($original_db);
                } catch (Exception $switch_error) {
                    $this->log_error("Failed to switch back to original database in exception handler: " . $switch_error->getMessage());
                }
            }
        }

        // 最终确保我们回到了正确的数据库
        $final_database = $this->get_current_database();
        if ($final_database !== $original_db) {
            $this->log_error("Database mismatch after user session copy. Expected: {$original_db}, Actual: {$final_database}");
            try {
                $this->log_info("Final recovery: Switching back to {$original_db}");
                $this->switch_database($original_db);
            } catch (Exception $e) {
                $this->log_error("Failed to switch back to original database in final recovery: " . $e->getMessage());
            }
        } else {
            $this->log_info("Database correctly maintained as {$original_db} after user session copy");
        }

        $success_count = count(array_filter($results));
        $this->log_info("copy_user_session_to_all_databases completed. Success: {$success_count}, Total: " . count($results));

        return $results;
    }
    
    /**
     * 内部方法：复制用户账号到所有数据库
     */
    private function copy_user_account_to_all_databases_internal($user_id) {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $results = [];
        $original_db = $current_db;
        
        // 获取当前用户的完整数据
        $user_data = get_userdata($user_id);
        if (!$user_data) {
            $this->log_error("User data not found for user ID: {$user_id}");
            return $results;
        }
        
        // 获取用户的所有meta数据
        $user_meta = get_user_meta($user_id);
        
        // 记录调试信息
        $this->log_info("Starting copy_user_account_to_all_databases for user: {$user_data->user_login} (ID: {$user_id})");
        $this->log_info("Current database: {$current_db}");
        $this->log_info("User meta keys: " . implode(', ', array_keys($user_meta)));
        
        $results['current_db'] = $current_db; // 标记当前数据库为成功
        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }
            
            if ($db_key === $current_db) {
                $this->log_info("Current database: {$db_key} (source database, already has the data)");
                $results[$db_key] = true; // 标记当前数据库为成功
                continue;
            }
            
            $this->log_info("Processing database: {$db_key}");
            
            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);
                
                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }
                
                // 检查用户是否已存在
                $existing_user = get_user_by('ID', $user_id);
                
                if ($existing_user) {
                    // 更新现有用户
                    $user_update_data = [
                        'ID' => $user_data->ID,
                        'user_login' => $user_data->user_login,
                        'user_pass' => $user_data->user_pass,
                        'user_nicename' => $user_data->user_nicename,
                        'user_email' => $user_data->user_email,
                        'user_url' => $user_data->user_url,
                        'user_registered' => $user_data->user_registered,
                        'user_activation_key' => $user_data->user_activation_key,
                        'user_status' => $user_data->user_status,
                        'display_name' => $user_data->display_name
                    ];
                    
                    $update_result = wp_update_user($user_update_data);
                    
                    if (is_wp_error($update_result)) {
                        $results[$db_key] = false;
                        $this->log_error("Failed to update user in database {$db_key}: " . $update_result->get_error_message());
                        continue;
                    } else {
                        $this->log_info("User updated in database: {$db_key}");
                    }
                } else {
                    // 创建新用户 - 使用wp_insert_user保持ID一致性
                    $user_insert_data = [
                        'ID' => $user_data->ID,
                        'user_login' => $user_data->user_login,
                        'user_pass' => $user_data->user_pass,
                        'user_nicename' => $user_data->user_nicename,
                        'user_email' => $user_data->user_email,
                        'user_url' => $user_data->user_url,
                        'user_registered' => $user_data->user_registered,
                        'user_activation_key' => $user_data->user_activation_key,
                        'user_status' => $user_data->user_status,
                        'display_name' => $user_data->display_name
                    ];
                    
                    // 使用数据库直接插入以保持ID
                    global $wpdb;
                    $insert_result = $wpdb->insert(
                        $wpdb->users,
                        $user_insert_data,
                        ['%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s']
                    );
                    
                    if ($insert_result === false) {
                        $results[$db_key] = false;
                        $this->log_error("Failed to insert user in database: {$db_key}");
                        continue;
                    } else {
                        $this->log_info("User created in database: {$db_key}");
                    }
                }
                
                // 复制用户meta数据
                if (!empty($user_meta)) {
                    foreach ($user_meta as $meta_key => $meta_values) {
                        // 删除现有的meta数据
                        delete_user_meta($user_id, $meta_key);
                        
                        // 插入新的meta数据
                        foreach ($meta_values as $meta_value) {
                            add_user_meta($user_id, $meta_key, maybe_unserialize($meta_value));
                        }
                    }
                    $this->log_info("User meta data copied for database: {$db_key}");
                }
                
                $results[$db_key] = true;
                $this->log_info("User account fully copied to database: {$db_key}");

                // 立即切换回原数据库
                $this->log_info("Switching back from {$db_key} to {$original_db}");
                $switch_back_result = $this->switch_database($original_db);
                if (!$switch_back_result) {
                    $this->log_error("Failed to switch back to original database: {$original_db}");
                }

            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in copy_user_account_to_all_databases for database {$db_key}: " . $e->getMessage());

                // 异常情况下也要切换回原数据库
                try {
                    $this->log_info("Exception recovery: Switching back to {$original_db}");
                    $this->switch_database($original_db);
                } catch (Exception $switch_error) {
                    $this->log_error("Failed to switch back to original database in exception handler: " . $switch_error->getMessage());
                }
            }
        }

        // 最终确保我们回到了正确的数据库
        $final_database = $this->get_current_database();
        if ($final_database !== $original_db) {
            $this->log_error("Database mismatch after user account copy. Expected: {$original_db}, Actual: {$final_database}");
            try {
                $this->log_info("Final recovery: Switching back to {$original_db}");
                $this->switch_database($original_db);
            } catch (Exception $e) {
                $this->log_error("Failed to switch back to original database in final recovery: " . $e->getMessage());
            }
        } else {
            $this->log_info("Database correctly maintained as {$original_db} after user account copy");
        }

        $success_count = count(array_filter($results));
        $this->log_info("copy_user_account_to_all_databases completed. Success: {$success_count}, Total: " . count($results));

        return $results;
    }

    /**
     * 在所有数据库中启用插件
     */
    private function enable_plugin_in_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $results = [];
        $original_db = $current_db;
        
        // 插件的基本名称
        $plugin_basename = plugin_basename(__FILE__);
        
        $this->log_info("Starting enable_plugin_in_all_databases for plugin: {$plugin_basename}");
        $this->log_info("Current database: {$current_db}");
        
        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }
            
            if ($db_key === $current_db) {
                $this->log_info("Skipping current database: {$db_key} (plugin already active)");
                $results[$db_key] = true;
                continue;
            }
            
            $this->log_info("Processing database: {$db_key}");
            
            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);
                
                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }
                
                // 获取当前数据库中的活跃插件列表
                $active_plugins = get_option('active_plugins', []);
                $this->log_info("Current active plugins in {$db_key}: " . count($active_plugins) . " plugins");
                
                // 检查插件是否已经激活
                if (!in_array($plugin_basename, $active_plugins)) {
                    // 添加插件到活跃插件列表
                    $active_plugins[] = $plugin_basename;
                    $update_result = update_option('active_plugins', $active_plugins);
                    
                    if ($update_result) {
                        $results[$db_key] = true;
                        $this->log_info("Plugin enabled in database: {$db_key}");
                    } else {
                        $results[$db_key] = false;
                        $this->log_error("Failed to update active_plugins option in database: {$db_key}");
                    }
                } else {
                    $results[$db_key] = true;
                    $this->log_info("Plugin already active in database: {$db_key}");
                }
                
            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in enable_plugin_in_all_databases for database {$db_key}: " . $e->getMessage());
            }
        }
        
        // 确保切换回原始数据库
        try {
            $this->switch_database($original_db);
            $this->log_info("Switched back to original database: {$original_db}");
        } catch (Exception $e) {
            $this->log_error("Failed to switch back to original database: " . $e->getMessage());
        }
        
        $success_count = count(array_filter($results));
        $this->log_info("enable_plugin_in_all_databases completed. Success: {$success_count}, Total: " . count($results));
        
        return $results;
    }
    
    /**
     * 在所有数据库中禁用插件
     */
    private function disable_plugin_in_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $results = [];
        $original_db = $current_db;
        
        // 插件的基本名称
        $plugin_basename = plugin_basename(__FILE__);
        
        $this->log_info("Starting disable_plugin_in_all_databases for plugin: {$plugin_basename}");
        $this->log_info("Current database: {$current_db}");
        
        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }
            
            if ($db_key === $current_db) {
                $this->log_info("Skipping current database: {$db_key} (would deactivate current session)");
                $results[$db_key] = true;
                continue;
            }
            
            $this->log_info("Processing database: {$db_key}");
            
            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);
                
                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }
                
                // 获取当前数据库中的活跃插件列表
                $active_plugins = get_option('active_plugins', []);
                $this->log_info("Current active plugins in {$db_key}: " . count($active_plugins) . " plugins");
                
                // 检查插件是否在活跃列表中
                $plugin_key = array_search($plugin_basename, $active_plugins);
                if ($plugin_key !== false) {
                    // 从活跃插件列表中移除
                    unset($active_plugins[$plugin_key]);
                    $active_plugins = array_values($active_plugins); // 重新索引数组
                    $update_result = update_option('active_plugins', $active_plugins);
                    
                    if ($update_result) {
                        $results[$db_key] = true;
                        $this->log_info("Plugin disabled in database: {$db_key}");
                    } else {
                        $results[$db_key] = false;
                        $this->log_error("Failed to update active_plugins option in database: {$db_key}");
                    }
                } else {
                    $results[$db_key] = true;
                    $this->log_info("Plugin already inactive in database: {$db_key}");
                }
                
            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in disable_plugin_in_all_databases for database {$db_key}: " . $e->getMessage());
            }
        }
        
        // 确保切换回原始数据库
        try {
            $this->switch_database($original_db);
            $this->log_info("Switched back to original database: {$original_db}");
        } catch (Exception $e) {
            $this->log_error("Failed to switch back to original database: " . $e->getMessage());
        }
        
        $success_count = count(array_filter($results));
        $this->log_info("disable_plugin_in_all_databases completed. Success: {$success_count}, Total: " . count($results));

        return $results;
    }

    /**
     * 在所有数据库中启用支付网关
     */
    private function enable_payment_gateway_in_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $results = [];
        $original_db = $current_db;

        $this->log_info("Starting enable_payment_gateway_in_all_databases");
        $this->log_info("Current database: {$current_db}");

        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }

            $this->log_info("Processing database: {$db_key}");

            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);

                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }

                // 启用支付网关
                $gateway_enabled = $this->enable_paypal_gateway_in_current_database();

                if ($gateway_enabled) {
                    $results[$db_key] = true;
                    $this->log_info("Payment gateway enabled in database: {$db_key}");
                } else {
                    $results[$db_key] = false;
                    $this->log_error("Failed to enable payment gateway in database: {$db_key}");
                }

                // 立即切换回原数据库
                $this->log_info("Switching back from {$db_key} to {$original_db}");
                $switch_back_result = $this->switch_database($original_db);
                if (!$switch_back_result) {
                    $this->log_error("Failed to switch back to original database: {$original_db}");
                }

            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in enable_payment_gateway_in_all_databases for database {$db_key}: " . $e->getMessage());

                // 异常情况下也要切换回原数据库
                try {
                    $this->log_info("Exception recovery: Switching back to {$original_db}");
                    $this->switch_database($original_db);
                } catch (Exception $switch_error) {
                    $this->log_error("Failed to switch back to original database in exception handler: " . $switch_error->getMessage());
                }
            }
        }

        // 最终确保我们回到了正确的数据库
        $final_database = $this->get_current_database();
        if ($final_database !== $original_db) {
            $this->log_error("Database mismatch after payment gateway enable. Expected: {$original_db}, Actual: {$final_database}");
            try {
                $this->log_info("Final recovery: Switching back to {$original_db}");
                $this->switch_database($original_db);
            } catch (Exception $e) {
                $this->log_error("Failed to switch back to original database in final recovery: " . $e->getMessage());
            }
        } else {
            $this->log_info("Database correctly maintained as {$original_db} after payment gateway enable");
        }

        $success_count = count(array_filter($results));
        $this->log_info("enable_payment_gateway_in_all_databases completed. Success: {$success_count}, Total: " . count($results));

        return $results;
    }

    /**
     * 在所有数据库中禁用支付网关
     */
    private function disable_payment_gateway_in_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $results = [];
        $original_db = $current_db;

        $this->log_info("Starting disable_payment_gateway_in_all_databases");
        $this->log_info("Current database: {$current_db}");

        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                continue;
            }

            $this->log_info("Processing database: {$db_key}");

            try {
                // 切换到目标数据库
                $switch_result = $this->switch_database($db_key);

                if (!$switch_result) {
                    $results[$db_key] = false;
                    $this->log_error("Failed to switch to database: {$db_key}");
                    continue;
                }

                // 禁用支付网关
                $gateway_disabled = $this->disable_paypal_gateway_in_current_database();

                if ($gateway_disabled) {
                    $results[$db_key] = true;
                    $this->log_info("Payment gateway disabled in database: {$db_key}");
                } else {
                    $results[$db_key] = false;
                    $this->log_error("Failed to disable payment gateway in database: {$db_key}");
                }

                // 立即切换回原数据库
                $this->log_info("Switching back from {$db_key} to {$original_db}");
                $switch_back_result = $this->switch_database($original_db);
                if (!$switch_back_result) {
                    $this->log_error("Failed to switch back to original database: {$original_db}");
                }

            } catch (Exception $e) {
                $results[$db_key] = false;
                $this->log_error("Exception in disable_payment_gateway_in_all_databases for database {$db_key}: " . $e->getMessage());

                // 异常情况下也要切换回原数据库
                try {
                    $this->log_info("Exception recovery: Switching back to {$original_db}");
                    $this->switch_database($original_db);
                } catch (Exception $switch_error) {
                    $this->log_error("Failed to switch back to original database in exception handler: " . $switch_error->getMessage());
                }
            }
        }

        // 最终确保我们回到了正确的数据库
        $final_database = $this->get_current_database();
        if ($final_database !== $original_db) {
            $this->log_error("Database mismatch after payment gateway disable. Expected: {$original_db}, Actual: {$final_database}");
            try {
                $this->log_info("Final recovery: Switching back to {$original_db}");
                $this->switch_database($original_db);
            } catch (Exception $e) {
                $this->log_error("Failed to switch back to original database in final recovery: " . $e->getMessage());
            }
        } else {
            $this->log_info("Database correctly maintained as {$original_db} after payment gateway disable");
        }

        $success_count = count(array_filter($results));
        $this->log_info("disable_payment_gateway_in_all_databases completed. Success: {$success_count}, Total: " . count($results));

        return $results;
    }

    /**
     * 在当前数据库中启用 PayPal 支付网关
     */
    private function enable_paypal_gateway_in_current_database() {
        try {
            // 检查 WooCommerce 是否可用
            if (!class_exists('WooCommerce')) {
                $this->log_error("WooCommerce is not available in current database");
                return false;
            }

            // 获取 WooCommerce 支付网关设置
            $gateway_settings = get_option('woocommerce_yxjto_paypal_multi_gateway_settings', array());

            // 启用支付网关
            $gateway_settings['enabled'] = 'yes';

            // 更新设置
            $update_result = update_option('woocommerce_yxjto_paypal_multi_gateway_settings', $gateway_settings);

            if ($update_result) {
                $this->log_info("PayPal Multi Gateway enabled in current database");
                return true;
            } else {
                $this->log_error("Failed to update PayPal Multi Gateway settings");
                return false;
            }

        } catch (Exception $e) {
            $this->log_error("Exception in enable_paypal_gateway_in_current_database: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 在当前数据库中禁用 PayPal 支付网关
     */
    private function disable_paypal_gateway_in_current_database() {
        try {
            // 检查 WooCommerce 是否可用
            if (!class_exists('WooCommerce')) {
                $this->log_error("WooCommerce is not available in current database");
                return false;
            }

            // 获取 WooCommerce 支付网关设置
            $gateway_settings = get_option('woocommerce_yxjto_paypal_multi_gateway_settings', array());

            // 禁用支付网关
            $gateway_settings['enabled'] = 'no';

            // 更新设置
            $update_result = update_option('woocommerce_yxjto_paypal_multi_gateway_settings', $gateway_settings);

            if ($update_result) {
                $this->log_info("PayPal Multi Gateway disabled in current database");
                return true;
            } else {
                $this->log_error("Failed to update PayPal Multi Gateway settings");
                return false;
            }

        } catch (Exception $e) {
            $this->log_error("Exception in disable_paypal_gateway_in_current_database: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 输出 cookies 调试信息到控制台
     */
    public function output_cookie_debug_info() {
        $cookie_name = 'yxjto_gateway_param';

        // 获取配置的过期时间
        $configured_expire_hours = $this->get_cookie_expire_hours();

        $cookie_info = array(
            'cookie_exists' => isset($_COOKIE[$cookie_name]),
            'cookie_value' => isset($_COOKIE[$cookie_name]) ? $_COOKIE[$cookie_name] : null,
            'clear_timestamp' => get_option('yxjto_gateway_clear_cookies_timestamp', 0),
            'current_time' => time(),
            'configured_expire_hours' => $configured_expire_hours
        );

        if (isset($_COOKIE[$cookie_name])) {
            $cookie_data = $this->parse_cookie_data($_COOKIE[$cookie_name]);
            if ($cookie_data) {
                $remaining_seconds = $cookie_data['expires'] - time();
                $remaining_hours = round($remaining_seconds / 3600, 2);

                $cookie_info['parsed_data'] = array(
                    'param' => $cookie_data['param'],
                    'value' => $cookie_data['value'],
                    'database' => $cookie_data['database'],
                    'timestamp' => $cookie_data['timestamp'],
                    'expires' => $cookie_data['expires'],
                    'expire_hours' => isset($cookie_data['expire_hours']) ? $cookie_data['expire_hours'] : 'unknown',
                    'is_expired' => $this->is_cookie_expired($cookie_data),
                    'remaining_seconds' => max(0, $remaining_seconds),
                    'remaining_hours' => max(0, $remaining_hours),
                    'created_date' => date('Y-m-d H:i:s', $cookie_data['timestamp']),
                    'expires_date' => date('Y-m-d H:i:s', $cookie_data['expires'])
                );
            } else {
                $cookie_info['parse_error'] = 'Failed to parse cookie data';
            }
        }

        echo '<script type="text/javascript">';
        echo 'if (typeof console !== "undefined" && console.log) {';
        echo 'console.group("YXJTO Gateway - Cookie Debug Info");';
        echo 'console.log(' . json_encode($cookie_info) . ');';
        echo 'console.groupEnd();';
        echo '}';
        echo '</script>';
    }

    /**
     * 检查控制台输出是否被禁用
     */
    private function is_console_output_disabled() {
        $settings = WP_Multi_DB_Config_Manager::get_settings();
        return !empty($settings['disable_console_output']);
    }

    /**
     * 获取配置的 cookie 过期时间（秒）
     */
    private function get_cookie_expire_seconds() {
        $settings = WP_Multi_DB_Config_Manager::get_settings();
        $expire_hours = isset($settings['cookie_expire_hours']) ? intval($settings['cookie_expire_hours']) : 24;

        // 确保过期时间在合理范围内（1小时到30天）
        if ($expire_hours < 1 || $expire_hours > 720) {
            $expire_hours = 24; // 默认24小时
        }

        return $expire_hours * 60 * 60; // 转换为秒
    }

    /**
     * 获取配置的 cookie 过期时间（小时）
     */
    private function get_cookie_expire_hours() {
        $settings = WP_Multi_DB_Config_Manager::get_settings();
        $expire_hours = isset($settings['cookie_expire_hours']) ? intval($settings['cookie_expire_hours']) : 24;

        // 确保过期时间在合理范围内（1小时到30天）
        if ($expire_hours < 1 || $expire_hours > 720) {
            $expire_hours = 24; // 默认24小时
        }

        return $expire_hours;
    }

    /**
     * 测试 cookies 功能的调试方法
     */
    public function test_cookie_functionality() {
        $this->log_info("=== Testing Cookie Functionality ===");

        // 测试当前 cookies 状态
        $cookie_name = 'yxjto_gateway_param';
        $this->log_info("Cookie name: {$cookie_name}");
        $this->log_info("Cookie exists: " . (isset($_COOKIE[$cookie_name]) ? 'YES' : 'NO'));

        if (isset($_COOKIE[$cookie_name])) {
            $this->log_info("Cookie value: " . $_COOKIE[$cookie_name]);

            $cookie_data = $this->parse_cookie_data($_COOKIE[$cookie_name]);
            if ($cookie_data) {
                $this->log_info("Cookie data parsed successfully: " . json_encode($cookie_data));
                $this->log_info("Cookie expired: " . ($this->is_cookie_expired($cookie_data) ? 'YES' : 'NO'));
            } else {
                $this->log_error("Failed to parse cookie data");
            }
        }

        // 测试 URL 规则
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
        $this->log_info("URL rules count: " . count($url_rules));

        foreach ($url_rules as $index => $rule) {
            $this->log_info("Rule {$index}: enabled=" . ($rule['enabled'] ? 'YES' : 'NO') .
                           ", param=" . $rule['parameter'] .
                           ", value=" . $rule['value'] .
                           ", database=" . $rule['database']);
        }

        // 测试当前 URL 参数
        $this->log_info("Current GET parameters: " . json_encode($_GET));

        $this->log_info("=== End Cookie Test ===");
    }

    /**
     * 自动复制用户创建到所有数据库（立即执行）
     */
    public function auto_copy_user_creation($user_id) {
        // 从config-manager检查设置是否启用
        $user_management_settings = WP_Multi_DB_Config_Manager::get_user_management_settings();
        if (empty($user_management_settings['auto_copy_user_creation'])) {
            return;
        }

        $this->log_info("Auto copy user creation triggered immediately for user ID: {$user_id}");

        try {
            // 立即执行用户复制
            $results = $this->copy_user_account_to_all_databases_internal($user_id);
            $success_count = count(array_filter($results));
            $total_count = count($results);

            $this->log_info("Auto user copy completed immediately - Success: {$success_count}, Total: {$total_count}");

            if ($success_count > 0) {
                $this->log_info("User account immediately copied to {$success_count} out of {$total_count} databases");
            } else {
                $this->log_error("Failed to immediately copy user account to any database");
            }
        } catch (Exception $e) {
            $this->log_error("Failed to immediately copy user account: " . $e->getMessage());
        }
    }

    /**
     * 自动复制用户会话到所有数据库（立即执行）
     */
    public function auto_copy_user_session($user_login, $user) {
        // 从config-manager检查设置是否启用
        $user_management_settings = WP_Multi_DB_Config_Manager::get_user_management_settings();
        if (empty($user_management_settings['auto_copy_user_sessions'])) {
            return;
        }

        $user_id = $user->ID;
        $this->log_info("Auto copy user session triggered immediately for user ID: {$user_id} (login: {$user_login})");

        try {
            // 立即执行会话复制
            $results = $this->copy_user_session_to_all_databases_internal($user_id);
            $success_count = count(array_filter($results));
            $total_count = count($results);

            $this->log_info("Auto session copy completed immediately - Success: {$success_count}, Total: {$total_count}");

            if ($success_count > 0) {
                $this->log_info("User session immediately copied to {$success_count} out of {$total_count} databases");
            } else {
                $this->log_error("Failed to immediately copy user session to any database");
            }
        } catch (Exception $e) {
            $this->log_error("Failed to immediately copy user session: " . $e->getMessage());
        }
    }

    /**
     * 立即执行用户复制
     */
    public function delayed_user_copy($user_id) {
        $this->log_info("Executing immediate user copy for user ID: {$user_id}");

        try {
            $results = $this->copy_user_account_to_all_databases_internal($user_id);
            $success_count = count(array_filter($results));
            $total_count = count($results);

            $this->log_info("Auto user copy completed - Success: {$success_count}, Total: {$total_count}");

            if ($success_count > 0) {
                $this->log_info("User account automatically copied to {$success_count} out of {$total_count} databases");
            } else {
                $this->log_error("Failed to automatically copy user account to any database");
            }
        } catch (Exception $e) {
            $this->log_error("Error in immediate user copy: " . $e->getMessage());
        }
    }

    /**
     * 立即执行会话复制
     */
    public function delayed_session_copy($user_id) {
        $this->log_info("Executing immediate session copy for user ID: {$user_id}");

        try {
            $results = $this->copy_user_session_to_all_databases_internal($user_id);
            $success_count = count(array_filter($results));
            $total_count = count($results);

            $this->log_info("Auto session copy completed - Success: {$success_count}, Total: {$total_count}");

            if ($success_count > 0) {
                $this->log_info("User session automatically copied to {$success_count} out of {$total_count} databases");
            } else {
                $this->log_error("Failed to automatically copy user session to any database");
            }
        } catch (Exception $e) {
            $this->log_error("Error in immediate session copy: " . $e->getMessage());
        }
    }

    /**
     * 自动复制WooCommerce会话到所有数据库（立即执行）
     */
    public function auto_copy_woocommerce_session($set_cookies = true) {
        // 从config-manager检查设置是否启用
        $user_management_settings = WP_Multi_DB_Config_Manager::get_user_management_settings();
        if (empty($user_management_settings['auto_copy_woocommerce_sessions'])) {
            return;
        }

        // 检查WooCommerce是否可用
        if (!class_exists('WooCommerce') || !function_exists('WC')) {
            return;
        }

        $this->log_info("Auto copy WooCommerce session triggered immediately");

        try {
            $results = $this->copy_woocommerce_session_to_all_databases_internal();
            $success_count = count(array_filter($results));
            $total_count = count($results);

            $this->log_info("Auto WooCommerce session copy completed immediately - Success: {$success_count}, Total: {$total_count}");

            if ($success_count > 0) {
                $this->log_info("WooCommerce session immediately copied to {$success_count} out of {$total_count} databases");
            } else {
                $this->log_error("Failed to immediately copy WooCommerce session to any database");
            }
        } catch (Exception $e) {
            $this->log_error("Failed to immediately copy WooCommerce session: " . $e->getMessage());
        }
    }

    /**
     * 购物车更新时复制WooCommerce会话
     */
    public function auto_copy_woocommerce_session_on_cart_update() {
        $this->auto_copy_woocommerce_session(false);
    }

    /**
     * 结账时复制WooCommerce会话
     */
    public function auto_copy_woocommerce_session_on_checkout($user_id, $data) {
        $this->auto_copy_woocommerce_session(false);
    }

    /**
     * 复制WooCommerce会话到所有数据库的内部方法
     */
    private function copy_woocommerce_session_to_all_databases_internal() {
        if (!class_exists('WooCommerce') || !function_exists('WC')) {
            $this->log_error("WooCommerce is not available for session copying");
            return [];
        }

        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_database = $this->get_current_database();
        $results = [];

        // 获取当前WooCommerce会话数据
        $wc_session_data = $this->get_current_woocommerce_session_data();

        if (empty($wc_session_data)) {
            $this->log_info("No WooCommerce session data to copy");
            return [];
        }

        $this->log_info("Starting WooCommerce session copy to all databases. Session data size: " . count($wc_session_data) . " items");

        foreach ($databases as $db_key => $db_config) {
            if (!$db_config['enabled']) {
                $this->log_info("Skipping disabled database: {$db_key}");
                $results[$db_key] = false;
                continue;
            }

            if ($db_key === $current_database) {
                $this->log_info("Skipping current database: {$db_key}");
                $results[$db_key] = true;
                continue;
            }

            try {
                $this->log_info("Copying WooCommerce session to database: {$db_key}");

                // 切换到目标数据库
                $original_database = $current_database; // 使用正确的当前数据库
                $this->log_info("Switching from {$original_database} to {$db_key} for WooCommerce session copy");

                if ($this->switch_database($db_key)) {

                    // 复制WooCommerce会话数据
                    $copy_result = $this->copy_woocommerce_session_data($wc_session_data);

                    if ($copy_result) {
                        $this->log_info("Successfully copied WooCommerce session to database: {$db_key}");
                        $results[$db_key] = true;
                    } else {
                        $this->log_error("Failed to copy WooCommerce session data to database: {$db_key}");
                        $results[$db_key] = false;
                    }

                    // 切换回原数据库
                    $this->log_info("Switching back from {$db_key} to {$original_database}");
                    $switch_back_result = $this->switch_database($original_database);
                    if (!$switch_back_result) {
                        $this->log_error("Failed to switch back to original database: {$original_database}");
                    }
                } else {
                    $this->log_error("Failed to switch to database: {$db_key}");
                    $results[$db_key] = false;
                }

            } catch (Exception $e) {
                $this->log_error("Error copying WooCommerce session to database {$db_key}: " . $e->getMessage());
                $results[$db_key] = false;

                // 确保切换回原数据库
                try {
                    $original_database = $current_database; // 使用正确的当前数据库
                    $this->log_info("Exception recovery: Switching back to {$original_database}");
                    $this->switch_database($original_database);
                } catch (Exception $switch_error) {
                    $this->log_error("Failed to switch back to original database: " . $switch_error->getMessage());
                }
            }
        }

        // 最终确保我们回到了正确的数据库
        $final_database = $this->get_current_database();
        if ($final_database !== $current_database) {
            $this->log_error("Database mismatch after WooCommerce session copy. Expected: {$current_database}, Actual: {$final_database}");
            $this->log_info("Final recovery: Switching back to {$current_database}");
            $this->switch_database($current_database);
        } else {
            $this->log_info("Database correctly maintained as {$current_database} after WooCommerce session copy");
        }

        $this->log_info("WooCommerce session copy completed. Results: " . json_encode($results));
        return $results;
    }

    /**
     * 获取当前WooCommerce会话数据
     */
    private function get_current_woocommerce_session_data() {
        if (!class_exists('WooCommerce') || !function_exists('WC')) {
            return [];
        }

        $session_data = [];

        try {
            // 获取WooCommerce会话处理器
            $session_handler = WC()->session;

            if (!$session_handler) {
                $this->log_info("No WooCommerce session handler available");
                return [];
            }

            // 获取客户ID（可能是用户ID或会话ID）
            $customer_id = $session_handler->get_customer_id();

            if (!$customer_id) {
                $this->log_info("No WooCommerce customer ID available");
                return [];
            }

            // 获取会话数据
            global $wpdb;

            // 查询WooCommerce会话表
            $session_table = $wpdb->prefix . 'woocommerce_sessions';

            // 检查表是否存在
            $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $session_table));

            if (!$table_exists) {
                $this->log_error("WooCommerce sessions table does not exist: {$session_table}");
                return [];
            }

            // 获取会话数据
            $session_row = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$session_table} WHERE session_key = %s",
                $customer_id
            ));

            if ($session_row) {
                $session_data = [
                    'session_key' => $session_row->session_key,
                    'session_value' => $session_row->session_value,
                    'session_expiry' => $session_row->session_expiry
                ];

                $this->log_info("Retrieved WooCommerce session data for customer: {$customer_id}");
            } else {
                $this->log_info("No WooCommerce session data found for customer: {$customer_id}");
            }

        } catch (Exception $e) {
            $this->log_error("Error retrieving WooCommerce session data: " . $e->getMessage());
        }

        return $session_data;
    }

    /**
     * 复制WooCommerce会话数据到当前数据库
     */
    private function copy_woocommerce_session_data($session_data) {
        if (empty($session_data) || !isset($session_data['session_key'])) {
            return false;
        }

        try {
            global $wpdb;

            $session_table = $wpdb->prefix . 'woocommerce_sessions';

            // 检查表是否存在
            $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $session_table));

            if (!$table_exists) {
                $this->log_error("WooCommerce sessions table does not exist in target database: {$session_table}");
                return false;
            }

            // 使用REPLACE INTO来插入或更新会话数据
            $result = $wpdb->replace(
                $session_table,
                [
                    'session_key' => $session_data['session_key'],
                    'session_value' => $session_data['session_value'],
                    'session_expiry' => $session_data['session_expiry']
                ],
                ['%s', '%s', '%d']
            );

            if ($result !== false) {
                $this->log_info("Successfully copied WooCommerce session data for key: " . $session_data['session_key']);
                return true;
            } else {
                $this->log_error("Failed to copy WooCommerce session data. Database error: " . $wpdb->last_error);
                return false;
            }

        } catch (Exception $e) {
            $this->log_error("Error copying WooCommerce session data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理系统操作AJAX请求
     */
    public function handle_system_operation_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_system_operation')) {
            wp_send_json_error(['message' => __('Security check failed', 'yxjto-gateway')]);
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'yxjto-gateway')]);
        }

        $operation = sanitize_text_field($_POST['operation']);

        switch ($operation) {
            case 'test_connection':
                $result = [
                    'success' => true,
                    'message' => __('AJAX connection test successful', 'yxjto-gateway'),
                    'timestamp' => current_time('mysql'),
                    'user_id' => get_current_user_id()
                ];
                break;
            case 'enable_plugin':
                $results = $this->enable_plugin_in_all_databases();
                $success_count = count(array_filter($results));
                $result = [
                    'success' => $success_count > 0,
                    'message' => sprintf(__('Plugin enabled in %d databases', 'yxjto-gateway'), $success_count),
                    'details' => $results
                ];
                break;
            case 'disable_plugin':
                $results = $this->disable_plugin_in_all_databases();
                $success_count = count(array_filter($results));
                $result = [
                    'success' => $success_count > 0,
                    'message' => sprintf(__('Plugin disabled in %d databases', 'yxjto-gateway'), $success_count),
                    'details' => $results
                ];
                break;
            case 'copy_user_session':
                $current_user_id = get_current_user_id();
                if (!$current_user_id) {
                    $result = [
                        'success' => false,
                        'message' => __('No user logged in', 'yxjto-gateway')
                    ];
                } else {
                    // 检查用户是否有会话令牌
                    $session_tokens = get_user_meta($current_user_id, 'session_tokens', true);
                    if (empty($session_tokens)) {
                        $result = [
                            'success' => false,
                            'message' => __('No active session tokens found for current user', 'yxjto-gateway'),
                            'details' => ['error' => 'No session tokens to copy']
                        ];
                    } else {
                        $results = $this->copy_user_session_to_all_databases_internal($current_user_id);
                        $success_count = count(array_filter($results));
                        $total_count = count($results);

                        if ($total_count === 0) {
                            $result = [
                                'success' => false,
                                'message' => __('No target databases configured or enabled', 'yxjto-gateway'),
                                'details' => ['error' => 'No databases to copy to']
                            ];
                        } else {
                            $result = [
                                'success' => $success_count > 0,
                                'message' => sprintf(__('User session copied to %d out of %d databases', 'yxjto-gateway'), $success_count, $total_count),
                                'details' => $results
                            ];
                        }
                    }
                }
                break;
            case 'copy_user_account':
                $current_user_id = get_current_user_id();
                if (!$current_user_id) {
                    $result = [
                        'success' => false,
                        'message' => __('No user logged in', 'yxjto-gateway')
                    ];
                } else {
                    // 检查用户数据是否存在
                    $user_data = get_userdata($current_user_id);
                    if (!$user_data) {
                        $result = [
                            'success' => false,
                            'message' => __('User data not found for current user', 'yxjto-gateway'),
                            'details' => ['error' => 'User data not found']
                        ];
                    } else {
                        $results = $this->copy_user_account_to_all_databases_internal($current_user_id);
                        $success_count = count(array_filter($results));
                        $total_count = count($results);

                        if ($total_count === 0) {
                            $result = [
                                'success' => false,
                                'message' => __('No target databases configured or enabled', 'yxjto-gateway'),
                                'details' => ['error' => 'No databases to copy to']
                            ];
                        } else {
                            $result = [
                                'success' => $success_count > 0,
                                'message' => sprintf(__('User account copied to %d out of %d databases', 'yxjto-gateway'), $success_count, $total_count),
                                'details' => $results
                            ];
                        }
                    }
                }
                break;
            case 'enable_payment_gateway':
                $results = $this->enable_payment_gateway_in_all_databases();
                $success_count = count(array_filter($results));
                $result = [
                    'success' => $success_count > 0,
                    'message' => sprintf(__('Payment gateway enabled in %d databases', 'yxjto-gateway'), $success_count),
                    'details' => $results
                ];
                break;
            case 'disable_payment_gateway':
                $results = $this->disable_payment_gateway_in_all_databases();
                $success_count = count(array_filter($results));
                $result = [
                    'success' => $success_count > 0,
                    'message' => sprintf(__('Payment gateway disabled in %d databases', 'yxjto-gateway'), $success_count),
                    'details' => $results
                ];
                break;
            case 'create_transaction_tables':
                // 这个方法需要实现
                $result = [
                    'success' => false,
                    'message' => __('Transaction table creation not yet implemented', 'yxjto-gateway')
                ];
                break;
            default:
                wp_send_json_error(['message' => __('Unknown operation', 'yxjto-gateway')]);
        }

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * 处理综合测试AJAX请求
     */
    public function handle_comprehensive_test_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_comprehensive_test')) {
            wp_die('Security check failed');
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $test_action = sanitize_text_field($_POST['test_action']);

        switch ($test_action) {
            case 'get_system_status':
                $this->get_system_status_for_test();
                break;
            
            case 'get_products':
                $this->get_products_for_test();
                break;
            
            case 'get_coupons':
                $this->get_coupons_for_test();
                break;
            
            case 'create_test_order':
                $this->create_comprehensive_test_order();
                break;
            
            default:
                wp_send_json_error('Unknown action');
        }
    }

    /**
     * 获取系统状态信息
     */
    private function get_system_status_for_test() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $paypal_status = $this->check_paypal_accounts_status();
        
        $products_count = 0;
        $coupons_count = 0;
        
        if (class_exists('WooCommerce')) {
            $products_count = wc_get_products(['limit' => -1, 'return' => 'ids', 'status' => 'publish']);
            $products_count = count($products_count);
            
            $args = array(
                'posts_per_page' => -1,
                'post_type' => 'shop_coupon',
                'post_status' => 'publish'
            );
            $coupons = get_posts($args);
            $coupons_count = count($coupons);
        }

        $status_data = [
            'paypal_status' => count($paypal_status) > 0 ? 
                '<span style="color: green;">✅ ' . count($paypal_status) . ' 个账户可用</span>' : 
                '<span style="color: red;">❌ 无可用账户</span>',
            'database_status' => count($databases) > 0 ? 
                '<span style="color: green;">✅ ' . count($databases) . ' 个数据库连接</span>' : 
                '<span style="color: red;">❌ 无数据库配置</span>',
            'products_count' => $products_count,
            'coupons_count' => $coupons_count
        ];

        wp_send_json_success($status_data);
    }

    /**
     * 获取可用产品列表
     */
    private function get_products_for_test() {
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce not active');
        }

        $products = wc_get_products([
            'limit' => 20,
            'status' => 'publish',
            'orderby' => 'name',
            'order' => 'ASC'
        ]);

        $products_data = [];
        foreach ($products as $product) {
            $products_data[] = [
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'sku' => $product->get_sku() ?: 'N/A',
                'stock_status' => $product->get_stock_status()
            ];
        }

        wp_send_json_success($products_data);
    }

    /**
     * 获取可用优惠券列表
     */
    private function get_coupons_for_test() {
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce not active');
        }

        $args = array(
            'posts_per_page' => 20,
            'post_type' => 'shop_coupon',
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        );
        
        $coupon_posts = get_posts($args);
        $coupons_data = [];

        foreach ($coupon_posts as $coupon_post) {
            $coupon = new WC_Coupon($coupon_post->ID);
            $coupons_data[] = [
                'id' => $coupon->get_id(),
                'code' => $coupon->get_code(),
                'description' => $coupon->get_description() ?: $coupon->get_discount_type() . ' - ' . $coupon->get_amount(),
                'amount' => $coupon->get_amount(),
                'type' => $coupon->get_discount_type()
            ];
        }

        wp_send_json_success($coupons_data);
    }

    /**
     * 创建综合测试订单
     */
    private function create_comprehensive_test_order() {
        if (!class_exists('WooCommerce')) {
            wp_send_json_error('WooCommerce not active');
        }

        $config = $_POST['config'];
        $selected_products = $config['products'];
        $selected_coupons = $config['coupons'];

        $test_results = [];
        
        try {
            // 1. 创建订单
            $order = wc_create_order();
            $test_results[] = [
                'type' => 'success',
                'message' => '✅ 成功创建订单 #' . $order->get_id()
            ];

            // 2. 添加产品到订单
            foreach ($selected_products as $product_id) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $order->add_product($product);
                    $test_results[] = [
                        'type' => 'info',
                        'message' => '📦 添加产品: ' . $product->get_name() . ' (¥' . $product->get_price() . ')'
                    ];
                }
            }

            // 3. 应用优惠券
            foreach ($selected_coupons as $coupon_id) {
                $coupon = new WC_Coupon($coupon_id);
                if ($coupon->is_valid()) {
                    $order->apply_coupon($coupon->get_code());
                    $test_results[] = [
                        'type' => 'info',
                        'message' => '🎫 应用优惠券: ' . $coupon->get_code()
                    ];
                }
            }

            // 4. 添加运费（如果启用）
            if ($config['include_shipping']) {
                $shipping = new WC_Order_Item_Shipping();
                $shipping->set_method_title('测试运费');
                $shipping->set_method_id('test_shipping');
                $shipping->set_total(10.00);
                $order->add_item($shipping);
                $test_results[] = [
                    'type' => 'info',
                    'message' => '🚚 添加运费: ¥10.00'
                ];
            }

            // 5. 计算订单总计
            $order->calculate_totals();
            $test_results[] = [
                'type' => 'success',
                'message' => '💰 订单总计: ¥' . $order->get_total()
            ];

            // 6. 设置订单状态和客户信息
            $order->set_status('pending');
            $order->set_customer_id(get_current_user_id());
            
            // 添加测试地址
            $order->set_billing_first_name('测试');
            $order->set_billing_last_name('用户');
            $order->set_billing_email('<EMAIL>');
            $order->set_billing_phone('1234567890');
            
            $order->save();

            // 7. 测试订单复制（如果启用）
            if ($config['test_replication']) {
                if (class_exists('YXJTO_Order_Replication')) {
                    $replication = YXJTO_Order_Replication::get_instance();
                    $replication_result = $replication->replicate_order($order->get_id());
                    
                    if ($replication_result) {
                        $test_results[] = [
                            'type' => 'success',
                            'message' => '🔄 订单复制成功完成'
                        ];
                    } else {
                        $test_results[] = [
                            'type' => 'warning',
                            'message' => '⚠️ 订单复制可能存在问题'
                        ];
                    }
                } else {
                    $test_results[] = [
                        'type' => 'warning',
                        'message' => '⚠️ 订单复制类未找到'
                    ];
                }
            }

            // 8. 模拟PayPal支付（如果启用）
            if ($config['simulate_payment']) {
                $payment_gateways = WC()->payment_gateways->payment_gateways();
                $paypal_gateways = [];
                
                foreach ($payment_gateways as $gateway_id => $gateway) {
                    if (strpos($gateway_id, 'paypal') !== false || strpos($gateway_id, 'yxjto') !== false) {
                        $paypal_gateways[] = $gateway->get_title();
                    }
                }
                
                if (!empty($paypal_gateways)) {
                    $test_results[] = [
                        'type' => 'info',
                        'message' => '💳 发现PayPal网关: ' . implode(', ', $paypal_gateways)
                    ];
                    
                    // 设置订单为处理中状态（模拟支付成功）
                    $order->update_status('processing', '模拟PayPal支付成功');
                    $test_results[] = [
                        'type' => 'success',
                        'message' => '✅ 模拟支付流程完成'
                    ];
                } else {
                    $test_results[] = [
                        'type' => 'warning',
                        'message' => '⚠️ 未找到PayPal支付网关'
                    ];
                }
            }

            // 9. 最终检查
            $test_results[] = [
                'type' => 'success',
                'message' => '🎉 综合测试完成！订单ID: ' . $order->get_id() . ', 状态: ' . $order->get_status()
            ];

            wp_send_json_success($test_results);

        } catch (Exception $e) {
            $test_results[] = [
                'type' => 'error',
                'message' => '❌ 测试失败: ' . $e->getMessage()
            ];
            wp_send_json_error($test_results);
        }
    }

    /**
     * 检查PayPal账户状态
     */
    private function check_paypal_accounts_status() {
        $available_accounts = [];
        
        // 检查PayPal Multi Gateway配置
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Payment')) {
            $paypal_gateway = new YXJTO_PayPal_Multi_Gateway_Payment();
            $settings = get_option('woocommerce_yxjto_paypal_multi_gateway_settings', []);
            
            if (!empty($settings['enabled']) && $settings['enabled'] === 'yes') {
                $available_accounts[] = 'PayPal Multi Gateway';
            }
        }
        
        // 检查其他PayPal网关
        if (class_exists('WC_Payment_Gateways')) {
            $gateways = WC()->payment_gateways->payment_gateways();
            foreach ($gateways as $gateway_id => $gateway) {
                if (strpos($gateway_id, 'paypal') !== false && $gateway->is_available()) {
                    $available_accounts[] = $gateway->get_title();
                }
            }
        }
        
        return $available_accounts;
    }

    /**
     * 在所有数据库中启用插件
     */
    private function enable_plugin_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $results = [];
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                // 这里可以添加具体的插件启用逻辑
                $results[] = "Plugin enabled in database: {$db_name}";
                $success_count++;
            } catch (Exception $e) {
                $results[] = "Failed to enable plugin in {$db_name}: " . $e->getMessage();
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('Plugin enabled in %d databases', 'yxjto-gateway'), $success_count),
            'details' => $results
        ];
    }

    /**
     * 在所有数据库中禁用插件
     */
    private function disable_plugin_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $results = [];
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                // 这里可以添加具体的插件禁用逻辑
                $results[] = "Plugin disabled in database: {$db_name}";
                $success_count++;
            } catch (Exception $e) {
                $results[] = "Failed to disable plugin in {$db_name}: " . $e->getMessage();
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('Plugin disabled in %d databases', 'yxjto-gateway'), $success_count),
            'details' => $results
        ];
    }

    /**
     * 复制用户会话到所有数据库
     */
    private function copy_user_session_all_databases() {
        $current_user = wp_get_current_user();
        if (!$current_user->ID) {
            return [
                'success' => false,
                'message' => __('No user logged in', 'yxjto-gateway')
            ];
        }

        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            // 跳过当前数据库（源数据库）
            if ($db_name === $current_db) {
                continue;
            }

            if ($this->copy_user_session_to_database($current_user->ID, $config)) {
                $success_count++;
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('User session copied to %d databases', 'yxjto-gateway'), $success_count)
        ];
    }

    /**
     * 复制用户账户到所有数据库
     */
    private function copy_user_account_all_databases() {
        $current_user = wp_get_current_user();
        if (!$current_user->ID) {
            return [
                'success' => false,
                'message' => __('No user logged in', 'yxjto-gateway')
            ];
        }

        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $current_db = $this->get_current_database();
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            // 跳过当前数据库（源数据库）
            if ($db_name === $current_db) {
                continue;
            }

            if ($this->copy_user_account_to_database($current_user->ID, $config)) {
                $success_count++;
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('User account copied to %d databases', 'yxjto-gateway'), $success_count)
        ];
    }

    /**
     * 在所有数据库中启用支付网关
     */
    private function enable_payment_gateway_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                // 这里可以添加具体的支付网关启用逻辑
                $success_count++;
            } catch (Exception $e) {
                // 记录错误但继续处理其他数据库
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('Payment gateway enabled in %d databases', 'yxjto-gateway'), $success_count)
        ];
    }

    /**
     * 在所有数据库中禁用支付网关
     */
    private function disable_payment_gateway_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                // 这里可以添加具体的支付网关禁用逻辑
                $success_count++;
            } catch (Exception $e) {
                // 记录错误但继续处理其他数据库
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('Payment gateway disabled in %d databases', 'yxjto-gateway'), $success_count)
        ];
    }

    /**
     * 在所有数据库中创建交易日志表
     */
    private function create_transaction_tables_all_databases() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $success_count = 0;

        foreach ($databases as $db_name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                // 这里可以添加具体的表创建逻辑
                $success_count++;
            } catch (Exception $e) {
                // 记录错误但继续处理其他数据库
            }
        }

        return [
            'success' => $success_count > 0,
            'message' => sprintf(__('Transaction tables created in %d databases', 'yxjto-gateway'), $success_count)
        ];
    }

    /**
     * 处理配送同步AJAX请求
     */
    public function handle_shipping_sync_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['_wpnonce'], 'yxjto_shipping_sync')) {
            wp_send_json_error(['message' => __('Security check failed', 'yxjto-gateway')]);
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'yxjto-gateway')]);
        }

        try {
            // 增加执行时间限制
            set_time_limit(300); // 5分钟

            // 增加内存限制
            ini_set('memory_limit', '512M');

            // 检查WooCommerce是否激活
            if (!class_exists('WooCommerce')) {
                wp_send_json_error(['message' => __('WooCommerce is not active', 'yxjto-gateway')]);
                return;
            }

            // 检查配送复制类是否存在
            if (!class_exists('YXJTO_Shipping_Replication')) {
                wp_send_json_error(['message' => __('Shipping replication class not found', 'yxjto-gateway')]);
                return;
            }

            $shipping_replication = YXJTO_Shipping_Replication::get_instance();

            // 检查实例是否创建成功
            if (!$shipping_replication) {
                wp_send_json_error(['message' => __('Failed to create shipping replication instance', 'yxjto-gateway')]);
                return;
            }

            // 执行同步
            $result = $shipping_replication->batch_sync_all_shipping();

            // 检查同步结果
            if ($result === false) {
                wp_send_json_error(['message' => __('Shipping synchronization returned false', 'yxjto-gateway')]);
                return;
            }

            wp_send_json_success([
                'message' => __('Full shipping synchronization completed successfully.', 'yxjto-gateway')
            ]);
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => sprintf(__('Shipping synchronization failed: %s', 'yxjto-gateway'), $e->getMessage())
            ]);
        } catch (Error $e) {
            wp_send_json_error([
                'message' => sprintf(__('Shipping synchronization fatal error: %s', 'yxjto-gateway'), $e->getMessage())
            ]);
        }
    }

    /**
     * 处理配送连接测试AJAX请求
     */
    public function handle_shipping_test_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['_wpnonce'], 'yxjto_shipping_test')) {
            wp_send_json_error(['message' => __('Security check failed', 'yxjto-gateway')]);
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'yxjto-gateway')]);
        }

        try {
            $databases = WP_Multi_DB_Config_Manager::get_config('databases');
            $tested_count = 0;
            $results = [];

            foreach ($databases as $db_name => $db_config) {
                if ($db_name === 'default' || empty($db_config['host'])) {
                    continue;
                }

                try {
                    $test_db = new wpdb(
                        $db_config['username'],
                        $db_config['password'],
                        $db_config['database'],
                        $db_config['host']
                    );

                    // 测试连接
                    $test_query = $test_db->get_var("SELECT 1");
                    if ($test_query === '1') {
                        $results[$db_name] = __('Connected successfully', 'yxjto-gateway');
                        $tested_count++;
                    } else {
                        $results[$db_name] = __('Connection failed', 'yxjto-gateway');
                    }
                } catch (Exception $e) {
                    $results[$db_name] = sprintf(__('Error: %s', 'yxjto-gateway'), $e->getMessage());
                }
            }

            $message = sprintf(__('Tested %d databases. Results: %s', 'yxjto-gateway'), 
                              count($results), 
                              implode(', ', array_map(function($k, $v) { 
                                  return "$k: $v"; 
                              }, array_keys($results), array_values($results))));

            wp_send_json_success(['message' => $message]);

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => sprintf(__('Database connection test failed: %s', 'yxjto-gateway'), $e->getMessage())
            ]);
        }
    }

    /**
     * 处理配送日志清理AJAX请求
     */
    public function handle_shipping_logs_ajax() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['_wpnonce'], 'yxjto_shipping_logs')) {
            wp_send_json_error(['message' => __('Security check failed', 'yxjto-gateway')]);
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'yxjto-gateway')]);
        }

        try {
            $shipping_replication = YXJTO_Shipping_Replication::get_instance();
            $shipping_replication->cleanup_logs();

            wp_send_json_success([
                'message' => __('Shipping logs cleared successfully.', 'yxjto-gateway')
            ]);
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => sprintf(__('Failed to clear logs: %s', 'yxjto-gateway'), $e->getMessage())
            ]);
        }
    }

    /**
     * 处理配送日志下载请求
     */
    public function handle_shipping_logs_download() {
        // 验证nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'yxjto_shipping_logs')) {
            wp_die(__('Security check failed', 'yxjto-gateway'));
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        $log_file = WP_CONTENT_DIR . '/uploads/yxjto-shipping-replication.log';

        if (!file_exists($log_file)) {
            wp_die(__('Log file not found', 'yxjto-gateway'));
        }

        // 设置下载头
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="yxjto-shipping-replication-' . date('Y-m-d-H-i-s') . '.log"');
        header('Content-Length: ' . filesize($log_file));

        // 输出文件内容
        readfile($log_file);
        exit;
    }

    /**
     * Coupon Sync页面
     */
    public function coupon_sync_page() {
        // 调用静态渲染方法（类已在管理后台初始化时加载）
        if (class_exists('YXJTO_Coupon_Sync_Admin')) {
            YXJTO_Coupon_Sync_Admin::render_page();
        } else {
            echo '<div class="notice notice-error"><p>Coupon Sync Admin class not found.</p></div>';
        }
    }

    /**
     * Shipping Sync页面
     */
    public function shipping_sync_page() {
        // 调用静态渲染方法（类已在管理后台初始化时加载）
        if (class_exists('YXJTO_Shipping_Sync_Admin')) {
            YXJTO_Shipping_Sync_Admin::render_page();
        } else {
            echo '<div class="notice notice-error"><p>Shipping Sync Admin class not found.</p></div>';
        }
    }

    // ===== 订单管理AJAX处理器 =====

    /**
     * AJAX搜索订单
     */
    public function ajax_search_orders() {
        check_ajax_referer('yxjto_order_manager', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        $search_term = sanitize_text_field($_POST['search_term']);
        $search_type = sanitize_text_field($_POST['search_type']);

        // 这里应该实现实际的搜索逻辑
        $results = $this->search_orders_across_databases($search_term, $search_type);

        wp_send_json_success($results);
    }

    /**
     * AJAX同步订单
     */
    public function ajax_sync_orders() {
        check_ajax_referer('yxjto_order_manager', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 这里应该实现实际的订单同步逻辑
        $result = $this->sync_orders_across_databases();

        if ($result) {
            wp_send_json_success(__('Orders synced successfully', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to sync orders', 'yxjto-gateway'));
        }
    }

    /**
     * AJAX清理订单
     */
    public function ajax_cleanup_orders() {
        check_ajax_referer('yxjto_order_manager', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 这里应该实现实际的订单清理逻辑
        $result = $this->cleanup_old_orders();

        if ($result) {
            wp_send_json_success(__('Orders cleaned up successfully', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to cleanup orders', 'yxjto-gateway'));
        }
    }

    /**
     * AJAX更新订单起始ID
     */
    public function ajax_update_order_start_id() {
        check_ajax_referer('yxjto_order_manager', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        $database = sanitize_text_field($_POST['database']);
        $start_id = intval($_POST['start_id']);

        $result = $this->update_order_start_id($database, $start_id);

        if ($result) {
            wp_send_json_success(__('Order start ID updated successfully', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to update order start ID', 'yxjto-gateway'));
        }
    }

    // ===== 订单管理辅助方法 =====

    /**
     * 跨数据库搜索订单
     */
    private function search_orders_across_databases($search_term, $search_type) {
        // 实现跨数据库搜索订单的逻辑
        // 暂时返回模拟数据
        return '<p>搜索结果：找到订单 ' . esc_html($search_term) . '</p>';
    }

    /**
     * 跨数据库同步订单
     */
    private function sync_orders_across_databases() {
        // 实现跨数据库同步订单的逻辑
        return true;
    }

    /**
     * 清理旧订单
     */
    private function cleanup_old_orders() {
        // 实现清理旧订单的逻辑
        return true;
    }

    /**
     * 更新订单起始ID
     */
    private function update_order_start_id($database, $start_id) {
        // 更新指定数据库的订单起始ID
        $option_name = "yxjto_order_start_id_{$database}";
        $result = update_option($option_name, $start_id);
        
        $this->log_info("Updated order start ID for database {$database} to {$start_id}");
        
        return $result;
    }

    /**
     * 获取订单起始ID
     */
    private function get_order_start_id($database) {
        $option_name = "yxjto_order_start_id_{$database}";
        return get_option($option_name, 10000); // 默认起始ID为10000
    }

    /**
     * 获取所有数据库的订单起始ID
     */
    private function get_all_order_start_ids() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $start_ids = array();
        
        foreach ($databases as $db_key => $db_config) {
            if ($db_config['enabled']) {
                $start_ids[$db_key] = array(
                    'name' => $db_config['name'] ?? $db_key,
                    'start_id' => $this->get_order_start_id($db_key),
                    'current_max_id' => $this->get_current_max_order_id($db_key)
                );
            }
        }
        
        return $start_ids;
    }

    /**
     * 获取当前最大订单ID
     */
    private function get_current_max_order_id($database) {
        // 这里应该连接到指定数据库查询最大订单ID
        // 暂时返回模拟数据
        return rand(10000, 50000);
    }

    // ===== 订单比较AJAX委托方法 =====

    /**
     * 委托给订单比较类的AJAX处理器
     */
    public function ajax_compare_order_handler() {
        $this->delegate_to_order_comparison('ajax_compare_order');
    }

    public function ajax_comparison_search_orders_handler() {
        $this->delegate_to_order_comparison('ajax_search_orders');
    }

    public function ajax_test_database_switch_handler() {
        $this->delegate_to_order_comparison('ajax_test_database_switch');
    }

    public function ajax_test_database_connection_handler() {
        $this->delegate_to_order_comparison('ajax_test_database_connection');
    }

    public function ajax_save_comparison_html_handler() {
        $this->delegate_to_order_comparison('ajax_save_comparison_html');
    }

    /**
     * 委托方法到订单比较类
     */
    private function delegate_to_order_comparison($method_name) {
        if (!class_exists('YXJTO_Order_Comparison')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-order-comparison.php';
        }

        $order_comparison = new YXJTO_Order_Comparison();
        
        if (method_exists($order_comparison, $method_name)) {
            $order_comparison->$method_name();
        } else {
            wp_send_json_error('Method not found: ' . $method_name);
        }
    }

}

// 初始化插件
function yxjto_gateway_init() {
    return YXJTO_Gateway::get_instance();
}

// 启动插件
yxjto_gateway_init();

// 公共API函数
function yxjto_gateway_switch_db($database_name) {
    return YXJTO_Gateway::get_instance()->switch_database($database_name);
}

function yxjto_gateway_current_db() {
    return YXJTO_Gateway::get_instance()->get_current_database();
}

function yxjto_gateway_db_list() {
    return YXJTO_Gateway::get_instance()->get_database_list();
}

function yxjto_gateway_db_info() {
    return YXJTO_Gateway::get_instance()->get_actual_database_info();
}

/**
 * 插件激活时的处理
 */
function yxjto_gateway_activate() {
    // 确保配置管理器已初始化
    if (!class_exists('WP_Multi_DB_Config_Manager')) {
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/config-manager.php';
    }

    // 自动启用wp-config集成
    $result = WP_Multi_DB_Config_Manager::enable_wp_config_integration();

    // 记录激活日志
    if ($result['success']) {
        error_log('YXJTO Gateway: Plugin activated and wp-config integration enabled');
    } else {
        error_log('YXJTO Gateway: Plugin activated but wp-config integration failed: ' . $result['message']);
    }
}

/**
 * 插件停用时的处理
 */
function yxjto_gateway_deactivate() {
    // 确保配置管理器已初始化
    if (!class_exists('WP_Multi_DB_Config_Manager')) {
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/config-manager.php';
    }

    // 自动禁用wp-config集成
    $result = WP_Multi_DB_Config_Manager::disable_wp_config_integration();

    // 记录停用日志
    if ($result['success']) {
        error_log('YXJTO Gateway: Plugin deactivated and wp-config integration disabled');
    } else {
        error_log('YXJTO Gateway: Plugin deactivated but wp-config integration removal failed: ' . $result['message']);
    }
}

// 注册激活和停用钩子
register_activation_hook(__FILE__, 'yxjto_gateway_activate');
register_deactivation_hook(__FILE__, 'yxjto_gateway_deactivate');

// 添加定期清理任务
add_action('init', function() {
    if (!wp_next_scheduled('yxjto_paypal_order_record_cleanup')) {
        wp_schedule_event(time(), 'daily', 'yxjto_paypal_order_record_cleanup');
    }
});

// 清理任务回调
add_action('yxjto_paypal_order_record_cleanup', function() {
    if (class_exists('YXJTO_PayPal_Order_Record')) {
        $order_record = YXJTO_PayPal_Order_Record::get_instance();
        $deleted_count = $order_record->cleanup_old_records();
        error_log("YXJTO PayPal Order Record: Daily cleanup completed, deleted {$deleted_count} old records");
    }
});

// 初始化插件
YXJTO_Gateway::get_instance();
