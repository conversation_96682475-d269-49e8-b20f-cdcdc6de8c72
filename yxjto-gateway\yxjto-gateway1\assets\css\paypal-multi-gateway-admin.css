/* YXJTO PayPal Multi Gateway Admin Styles */

.yxjto-paypal-multi-gateway-accounts {
    margin-top: 20px;
}

.accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.accounts-header h2 {
    margin: 0;
}

.no-accounts {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.no-accounts p {
    margin: 10px 0;
    color: #666;
}

/* Account Status Indicators */
.status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-denied {
    background: #f8d7da;
    color: #721c24;
}

.status-refunded {
    background: #e2e3e5;
    color: #383d41;
}

.status-reversed {
    background: #f5c6cb;
    color: #721c24;
}

/* Account Type Indicators */
.account-type {
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.account-type-email {
    background: #cce5ff;
    color: #004085;
}

.account-type-paypal_me {
    background: #d1ecf1;
    color: #0c5460;
}

.account-type-api {
    background: #d4edda;
    color: #155724;
}

/* Account Details */
.account-details {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* Modal Styles */
.yxjto-paypal-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.yxjto-paypal-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #888;
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.yxjto-paypal-modal-header {
    padding: 20px;
    background: #f1f1f1;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.yxjto-paypal-modal-header h3 {
    margin: 0;
}

.yxjto-paypal-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.yxjto-paypal-modal-close:hover {
    color: #000;
}

.yxjto-paypal-modal-body {
    padding: 20px;
}

.yxjto-paypal-modal-footer {
    padding: 20px;
    background: #f1f1f1;
    border-top: 1px solid #ddd;
    text-align: right;
}

.yxjto-paypal-modal-footer .button {
    margin-left: 10px;
}

/* Account Type Fields */
.account-type-fields {
    margin-top: 20px;
}

/* Statistics */
.yxjto-paypal-multi-gateway-statistics {
    margin-top: 20px;
}

.yxjto-paypal-multi-gateway-statistics table {
    margin-top: 20px;
}

/* Logs */
.yxjto-paypal-multi-gateway-logs {
    margin-top: 20px;
}

.yxjto-paypal-multi-gateway-logs table {
    margin-top: 20px;
}

/* Settings */
.yxjto-paypal-multi-gateway-settings {
    margin-top: 20px;
}

.yxjto-paypal-multi-gateway-settings .form-table {
    margin-top: 20px;
}

/* Configuration */
.yxjto-paypal-multi-gateway-config {
    margin-top: 20px;
}

/* Tab Navigation */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

.tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-top: none;
}

/* Responsive */
@media (max-width: 768px) {
    .yxjto-paypal-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .accounts-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Loading States */
.yxjto-paypal-loading {
    opacity: 0.6;
    pointer-events: none;
}

.yxjto-paypal-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: yxjto-paypal-spin 1s linear infinite;
}

@keyframes yxjto-paypal-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.yxjto-paypal-message {
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 4px;
}

.yxjto-paypal-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.yxjto-paypal-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.yxjto-paypal-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.yxjto-paypal-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Button Styles */
.yxjto-paypal-button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.yxjto-paypal-button-small {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.2;
}

/* Form Enhancements */
.yxjto-paypal-form-row {
    margin-bottom: 20px;
}

.yxjto-paypal-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.yxjto-paypal-form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.yxjto-paypal-form-input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.yxjto-paypal-form-description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* Account Actions */
.yxjto-paypal-account-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.yxjto-paypal-account-actions .button {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.2;
}

/* Test Connection */
.yxjto-paypal-test-connection {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.yxjto-paypal-test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

.yxjto-paypal-test-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.yxjto-paypal-test-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
