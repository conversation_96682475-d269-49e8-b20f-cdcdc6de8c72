/**
 * YXJTO PayPal Multi Gateway Checkout Styles
 * 参考 WooCommerce PayPal Payments 的样式设计
 */

/* PayPal 支付方式容器 */
.payment_method_yxjto_paypal_multi_gateway {
    position: relative;
}

.payment_method_yxjto_paypal_multi_gateway .payment_box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-top: 10px;
}

.payment_method_yxjto_paypal_multi_gateway.yxjto-paypal-multi-gateway-active .payment_box {
    background: #fff;
    border-color: #0070ba;
    box-shadow: 0 2px 8px rgba(0, 112, 186, 0.1);
}

/* PayPal 图标样式 */
.yxjto-paypal-multi-gateway-icons {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.yxjto-paypal-multi-gateway-icons img,
.yxjto-paypal-multi-gateway-icons svg {
    max-height: 24px;
    vertical-align: middle;
}

/* SVG 图标通用样式 */
svg {
    display: inline-block;
    vertical-align: middle;
}

svg use {
    pointer-events: none;
}

/* 支付信息区域 */
.yxjto-paypal-payment-info {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.yxjto-paypal-security-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #28a745;
    font-size: 14px;
}

.yxjto-paypal-security-icon {
    font-size: 16px;
}

.yxjto-paypal-process-info {
    color: #6c757d;
    font-size: 13px;
}

.yxjto-paypal-process-info p {
    margin: 0;
}

/* 账户状态显示 */
.yxjto-paypal-account-status {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 4px;
}

.yxjto-paypal-status-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
}

.yxjto-paypal-status-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
}

.yxjto-paypal-status-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
}

/* 账户切换按钮 */
.yxjto-paypal-account-switcher {
    margin-top: 10px;
    text-align: center;
}

.yxjto-paypal-switch-account-btn {
    background: #0070ba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s;
}

.yxjto-paypal-switch-account-btn:hover {
    background: #005a9a;
}

.yxjto-paypal-switch-account-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 加载状态 */
.yxjto-paypal-loading {
    opacity: 0.6;
    pointer-events: none;
}

.yxjto-paypal-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0070ba;
    border-radius: 50%;
    animation: yxjto-paypal-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes yxjto-paypal-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 账户信息显示 */
.yxjto-paypal-account-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

.yxjto-paypal-account-type {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.yxjto-paypal-account-details {
    font-size: 13px;
    color: #6c757d;
}

/* 错误消息 */
.yxjto-paypal-error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.yxjto-paypal-error-message p {
    margin: 0;
}

/* 成功消息 */
.yxjto-paypal-success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.yxjto-paypal-success-message p {
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .payment_method_yxjto_paypal_multi_gateway .payment_box {
        padding: 15px;
    }
    
    .yxjto-paypal-security-info,
    .yxjto-paypal-process-info {
        font-size: 12px;
    }
    
    .yxjto-paypal-switch-account-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .payment_method_yxjto_paypal_multi_gateway .payment_box {
        border-width: 2px;
    }
    
    .yxjto-paypal-status-success,
    .yxjto-paypal-status-error,
    .yxjto-paypal-status-warning {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .yxjto-paypal-spinner {
        animation: none;
    }
    
    .yxjto-paypal-switch-account-btn {
        transition: none;
    }
}

/* 打印样式 */
@media print {
    .yxjto-paypal-account-switcher,
    .yxjto-paypal-switch-account-btn {
        display: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .payment_method_yxjto_paypal_multi_gateway .payment_box {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .yxjto-paypal-account-info {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .yxjto-paypal-process-info {
        color: #a0aec0;
    }
}

/* 焦点样式 */
.yxjto-paypal-switch-account-btn:focus {
    outline: 2px solid #0070ba;
    outline-offset: 2px;
}

/* 工具提示样式 */
.yxjto-paypal-tooltip {
    position: relative;
    display: inline-block;
}

.yxjto-paypal-tooltip .yxjto-paypal-tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.yxjto-paypal-tooltip:hover .yxjto-paypal-tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 账户锁定状态 */
.yxjto-paypal-account-locked {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.yxjto-paypal-account-locked .dashicons {
    font-size: 16px;
}

/* 账户可用性指示器 */
.yxjto-paypal-accounts-available {
    font-size: 12px;
    color: #28a745;
    margin-top: 5px;
}

.yxjto-paypal-accounts-unavailable {
    font-size: 12px;
    color: #dc3545;
    margin-top: 5px;
}
