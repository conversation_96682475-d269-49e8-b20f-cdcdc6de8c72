<?php
/**
 * WordPress多数据库插件 - 配置文件管理器
 * 将所有配置保存到PHP文件而不是数据库
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Multi_DB_Config_Manager {
    
    /**
     * 配置文件路径
     */
    private static $config_file = null;

    /**
     * wp-config.php hook文件路径
     */
    private static $wp_config_hook_file = null;

    /**
     * 默认配置
     */
    private static $default_config = [
        'databases' => [
            'default' => [
                'name' => 'Default Database',
                'host' => 'localhost',
                'database' => '',
                'username' => '',
                'password' => '',
                'charset' => 'utf8mb4',
                'prefix' => 'wp_',
                'enabled' => true
            ]
        ],
        'ip_rules' => [],
        'url_rules' => [],
        'crawler_detection' => [
            'enabled' => true,
            'log_visits' => true,
            'enable_ip_detection' => true,
            'enable_ip_verification' => true,
            'enable_reverse_dns' => false,
            'enabled_crawlers' => ['googlebot', 'bingbot', 'facebookbot', 'twitterbot', 'linkedinbot', 'baidubot', 'yandexbot'],
            'custom_patterns' => [],
            'custom_ip_ranges' => []
        ],
        'settings' => [
            'debug' => false,
            'log_enabled' => true,
            'max_log_entries' => 1000,
            'version' => '1.0.0',
            'disable_console_output' => false,
            'cookie_expire_hours' => 24
        ],
        'user_management' => [
            'auto_copy_user_creation' => false,
            'auto_copy_user_sessions' => false,
            'auto_copy_woocommerce_sessions' => false
        ],
        'order_replication' => [
            'enable_order_replication' => false,
            'enable_status_replication' => false,
            'enable_payment_info_replication' => false,
            'enable_smart_product_replacement' => false,
            'enable_payment_verification' => false,
            'enable_callback_handling' => false,
            'product_replacement_mode' => 'same_price',
            'enable_default_db_payment_redirect' => true  // 新增：启用用默认数据库进行支付跳转
        ],
        'coupon_sync' => [
            'enable_coupon_sync' => false,
            'sync_mode' => 'realtime',
            'batch_interval' => 'daily',
            'conflict_resolution' => 'newest',
            'enable_sync_logging' => true,
            'exclude_expired' => true,
            'max_retries' => 3,
            'last_sync' => null
        ],
        'shipping_replication' => [
            'enable_shipping_replication' => false,
            'enable_shipping_zones_sync' => false,
            'enable_shipping_methods_sync' => false,
            'enable_shipping_classes_sync' => false,
            'enable_local_pickup_sync' => false,
            'sync_mode' => 'realtime',
            'batch_interval' => 'daily',
            'conflict_resolution' => 'newest',
            'enable_sync_logging' => true,
            'max_retries' => 3,
            'last_sync' => null,
            'exclude_disabled_zones' => true,
            'sync_shipping_rates' => true,
            'sync_free_shipping_conditions' => true,
            'backup_before_sync' => true
        ],
        'tax_replication' => [
            'enable_tax_replication' => false,
            'enable_tax_rates_sync' => true,
            'enable_tax_classes_sync' => true,
            'enable_tax_settings_sync' => true,
            'enable_standard_rates_sync' => true,
            'enable_reduced_rates_sync' => true,
            'enable_zero_rates_sync' => true,
            'sync_mode' => 'realtime',
            'batch_interval' => 'daily',
            'conflict_resolution' => 'newest',
            'enable_sync_logging' => true,
            'max_retries' => 3,
            'last_sync' => null,
            'backup_before_sync' => true,
            'sync_tax_based_on' => true,
            'sync_shipping_tax_class' => true,
            'sync_tax_round_at_subtotal' => true,
            'sync_tax_total_display' => true,
            'sync_tax_display_shop' => true,
            'sync_tax_display_cart' => true,
            'sync_prices_include_tax' => true,
            'sync_calc_taxes' => true,
            'sync_default_customer_address' => true,
            'sync_store_address' => true
        ],
        'paypal_multi_gateway' => [
            'version' => '1.0.0',
            'last_updated' => '',
            'settings' => [
                'load_balancing' => 'random',
                'retry_count' => 1,
                'timeout' => 30,
                'test_mode' => 'no',
                'enabled' => true
            ],
            'accounts' => []
        ]
    ];
    
    /**
     * 初始化配置管理器
     */
    public static function init() {
        // 智能确定WP_CONTENT_DIR路径
        if (!defined('WP_CONTENT_DIR')) {
            // 方法1: 如果在WordPress环境中，使用ABSPATH
            if (defined('ABSPATH')) {
                define('WP_CONTENT_DIR', ABSPATH . 'wp-content');
            } 
            // 方法2: 如果定义了YXJTO_GATEWAY_PLUGIN_DIR，计算相对路径
            elseif (defined('YXJTO_GATEWAY_PLUGIN_DIR')) {
                // 从插件目录向上找到wp-content目录
                $plugin_dir = realpath(YXJTO_GATEWAY_PLUGIN_DIR);
                $wp_content_dir = dirname($plugin_dir);
                define('WP_CONTENT_DIR', $wp_content_dir);
            }
            // 方法3: 使用当前文件路径推断
            else {
                // 当前文件在 wp-content/plugins/plugin-name/includes/
                $current_dir = dirname(__FILE__);  // includes目录
                $plugin_dir = dirname($current_dir);  // 插件目录
                $plugins_dir = dirname($plugin_dir);  // plugins目录
                $wp_content_dir = dirname($plugins_dir);  // wp-content目录
                define('WP_CONTENT_DIR', $wp_content_dir);
            }
        }

        self::$config_file = WP_CONTENT_DIR . '/yxjto-gateway-config.php';
        self::$wp_config_hook_file = WP_CONTENT_DIR . '/yxjto-gateway-hook.php';

        // 检查是否可以写入wp-content目录，如果不行则使用插件目录
        if (!self::is_path_writable(WP_CONTENT_DIR)) {
            // 使用插件目录作为备用位置
            $plugin_config_dir = dirname(__FILE__) . '/../config';
            if (!is_dir($plugin_config_dir)) {
                wp_mkdir_p($plugin_config_dir);
            }
            
            self::$config_file = $plugin_config_dir . '/yxjto-gateway-config.php';
            self::$wp_config_hook_file = $plugin_config_dir . '/yxjto-gateway-hook.php';
            
            error_log('YXJTO Gateway: Using plugin directory for config due to wp-content write restrictions');
        }

        // 如果配置文件不存在，创建默认配置
        if (!file_exists(self::$config_file)) {
            self::create_default_config();
        }

        // 创建或更新wp-config hook文件
        self::create_wp_config_hook();
    }
    
    /**
     * 获取配置文件路径
     */
    public static function get_config_file_path() {
        return self::$config_file;
    }

    /**
     * 检查路径是否可写
     */
    private static function is_path_writable($path) {
        // 检查目录是否存在且可写
        if (!is_dir($path)) {
            return false;
        }
        
        if (!is_writable($path)) {
            return false;
        }
        
        // 尝试创建一个测试文件
        $test_file = $path . '/yxjto-write-test-' . time() . '.tmp';
        $test_result = @file_put_contents($test_file, 'test');
        
        if ($test_result !== false) {
            // 测试成功，删除测试文件
            @unlink($test_file);
            return true;
        }
        
        return false;
    }

    /**
     * 强制重新生成配置文件（使用当前WordPress数据库配置）
     */
    public static function force_regenerate_config() {
        // 删除现有配置文件
        if (file_exists(self::$config_file)) {
            unlink(self::$config_file);
        }

        // 重新创建配置文件
        self::create_default_config();

        // 重新创建hook文件
        self::create_wp_config_hook();

        return file_exists(self::$config_file);
    }
    
    /**
     * 创建默认配置文件
     */
    private static function create_default_config() {
        // 获取当前WordPress数据库配置作为默认数据库
        $default_config = self::get_default_config_with_current_db();

        // 调试信息
        $current_db = $default_config['databases']['default'];
        error_log('YXJTO Gateway: Creating config with database: ' . $current_db['database'] . ' on host: ' . $current_db['host']);

        $config_content = self::generate_config_content($default_config);

        if (self::write_config_file($config_content)) {
            error_log('YXJTO Gateway: Created default configuration file with current database settings');
        } else {
            error_log('YXJTO Gateway: Failed to create configuration file');
        }
    }

    /**
     * 获取包含当前数据库配置的默认配置
     */
    private static function get_default_config_with_current_db() {
        $config = self::$default_config;

        // 尝试获取当前WordPress数据库配置
        $current_db_config = self::get_current_wordpress_db_config();

        if ($current_db_config) {
            // 使用当前数据库配置更新默认配置
            $config['databases']['default'] = array_merge(
                $config['databases']['default'],
                $current_db_config
            );
        }

        return $config;
    }

    /**
     * 获取当前WordPress数据库配置
     */
    private static function get_current_wordpress_db_config() {
        $db_config = [];

        // 调试信息
        error_log('YXJTO Gateway: Checking WordPress constants...');
        error_log('YXJTO Gateway: DB_HOST defined: ' . (defined('DB_HOST') ? 'YES (' . DB_HOST . ')' : 'NO'));
        error_log('YXJTO Gateway: DB_NAME defined: ' . (defined('DB_NAME') ? 'YES (' . DB_NAME . ')' : 'NO'));
        error_log('YXJTO Gateway: DB_USER defined: ' . (defined('DB_USER') ? 'YES (' . DB_USER . ')' : 'NO'));

        // 尝试从WordPress常量获取数据库配置
        if (defined('DB_HOST')) {
            $db_config['host'] = DB_HOST;
        } else {
            $db_config['host'] = 'localhost'; // 默认值
        }

        if (defined('DB_NAME')) {
            $db_config['database'] = DB_NAME;
            $db_config['name'] = 'Current WordPress Database (' . DB_NAME . ')';
        } else {
            // 如果没有DB_NAME，尝试从其他地方获取或使用默认值
            $db_config['database'] = 'wordpress';
            $db_config['name'] = 'Default WordPress Database';
            error_log('YXJTO Gateway: DB_NAME not defined, using default');
        }

        if (defined('DB_USER')) {
            $db_config['username'] = DB_USER;
        } else {
            $db_config['username'] = 'root'; // 默认值
            error_log('YXJTO Gateway: DB_USER not defined, using default');
        }

        if (defined('DB_PASSWORD')) {
            $db_config['password'] = DB_PASSWORD;
        } else {
            $db_config['password'] = ''; // 默认值
        }

        if (defined('DB_CHARSET')) {
            $db_config['charset'] = DB_CHARSET;
        } else {
            $db_config['charset'] = 'utf8mb4'; // 默认值
        }

        if (defined('DB_COLLATE')) {
            $db_config['collate'] = DB_COLLATE;
        } else {
            $db_config['collate'] = ''; // 默认值
        }

        $db_config['enabled'] = true;

        error_log('YXJTO Gateway: Final db_config: ' . print_r($db_config, true));

        return $db_config;
    }

    /**
     * 生成配置文件内容
     */
    private static function generate_config_content($config) {
        // 兼容非WordPress环境
        $timestamp = function_exists('current_time') ? current_time('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * WordPress多数据库插件配置文件\n";
        $content .= " * 自动生成于: {$timestamp}\n";
        $content .= " * 请勿手动编辑此文件，使用插件管理界面进行配置\n";
        $content .= " */\n\n";
        $content .= "if (!defined('ABSPATH')) {\n";
        $content .= "    exit;\n";
        $content .= "}\n\n";
        $content .= "return " . var_export($config, true) . ";\n";

        return $content;
    }
    
    /**
     * 写入配置文件
     */
    private static function write_config_file($content) {
        // 创建备份
        if (file_exists(self::$config_file)) {
            $backup_file = self::$config_file . '.backup.' . time();
            copy(self::$config_file, $backup_file);
            
            // 只保留最近5个备份
            self::cleanup_backups();
        }
        
        // 写入新配置
        $result = file_put_contents(self::$config_file, $content, LOCK_EX);
        
        if ($result === false) {
            error_log('WP Multi Database: Failed to write configuration file');
            return false;
        }
        
        // 设置文件权限
        chmod(self::$config_file, 0644);
        
        return true;
    }
    
    /**
     * 清理旧备份文件
     */
    private static function cleanup_backups() {
        $backup_pattern = self::$config_file . '.backup.*';
        $backup_files = glob($backup_pattern);
        
        if (count($backup_files) > 5) {
            // 按修改时间排序
            usort($backup_files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的备份文件
            $files_to_delete = array_slice($backup_files, 0, count($backup_files) - 5);
            foreach ($files_to_delete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * 读取配置
     */
    public static function get_config($key = null) {
        if (!file_exists(self::$config_file)) {
            self::create_default_config();
        }
        
        $config = include self::$config_file;
        
        if (!is_array($config)) {
            error_log('WP Multi Database: Invalid configuration file format');
            $config = self::$default_config;
        }
        
        // 合并默认配置，确保所有必需的键都存在
        $config = array_merge(self::$default_config, $config);
        
        if ($key === null) {
            return $config;
        }
        
        return isset($config[$key]) ? $config[$key] : null;
    }
    
    /**
     * 保存配置
     */
    public static function save_config($config) {
        // 验证配置格式
        if (!self::validate_config($config)) {
            return false;
        }
        
        // 合并默认配置
        $config = array_merge(self::$default_config, $config);
        
        // 更新版本和时间戳
        $config['settings']['last_updated'] = function_exists('current_time') ? current_time('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        
        $content = self::generate_config_content($config);
        return self::write_config_file($content);
    }
    
    /**
     * 更新配置的特定键值
     */
    public static function update_config($section, $key, $value) {
        $config = self::get_config();

        if (!isset($config[$section])) {
            $config[$section] = [];
        }

        $config[$section][$key] = $value;

        return self::save_config($config);
    }

    /**
     * 批量更新配置节
     */
    public static function update_config_section($section, $settings) {
        $config = self::get_config();

        if (!isset($config[$section])) {
            $config[$section] = [];
        }

        $config[$section] = array_merge($config[$section], $settings);

        return self::save_config($config);
    }
    
    /**
     * 验证配置格式
     */
    private static function validate_config($config) {
        if (!is_array($config)) {
            return false;
        }
        
        // 检查必需的键
        $required_keys = ['databases', 'ip_rules', 'url_rules', 'settings'];
        foreach ($required_keys as $key) {
            if (!isset($config[$key]) || !is_array($config[$key])) {
                error_log("WP Multi Database: Missing or invalid config key: {$key}");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取数据库配置
     */
    public static function get_databases() {
        $databases = self::get_config('databases');

        // 确保每个数据库配置都有完整的字段
        $default_db_config = [
            'name' => '',
            'host' => 'localhost',
            'database' => '',
            'username' => '',
            'password' => '',
            'charset' => 'utf8mb4',
            'prefix' => 'wp_',
            'enabled' => true
        ];

        foreach ($databases as $key => $config) {
            $databases[$key] = array_merge($default_db_config, $config);
        }

        return $databases;
    }
    
    /**
     * 获取默认数据库键名
     */
    public static function get_default_database() {
        return 'default';
    }
    
    /**
     * 保存数据库配置
     */
    public static function save_databases($databases) {
        $config = self::get_config();
        $config['databases'] = $databases;
        return self::save_config($config);
    }

    /**
     * 删除数据库配置
     */
    public static function delete_database($key) {
        if ($key === 'default') {
            return false; // 不允许删除默认数据库
        }

        $databases = self::get_databases();

        if (!isset($databases[$key])) {
            return false; // 数据库配置不存在
        }

        unset($databases[$key]);
        return self::save_databases($databases);
    }
    
    /**
     * 获取IP规则
     */
    public static function get_ip_rules() {
        return self::get_config('ip_rules');
    }
    
    /**
     * 保存IP规则
     */
    public static function save_ip_rules($ip_rules) {
        $config = self::get_config();
        $config['ip_rules'] = $ip_rules;
        return self::save_config($config);
    }
    
    /**
     * 获取URL规则
     */
    public static function get_url_rules() {
        return self::get_config('url_rules');
    }
    
    /**
     * 保存URL规则
     */
    public static function save_url_rules($url_rules) {
        $config = self::get_config();
        $config['url_rules'] = $url_rules;
        return self::save_config($config);
    }
    
    /**
     * 获取设置
     */
    public static function get_settings() {
        return self::get_config('settings');
    }
    
    /**
     * 保存设置
     */
    public static function save_settings($settings) {
        $config = self::get_config();
        $config['settings'] = array_merge($config['settings'], $settings);
        return self::save_config($config);
    }

    /**
     * 获取爬虫检测设置
     */
    public static function get_crawler_settings() {
        return self::get_config('crawler_detection');
    }

    /**
     * 保存爬虫检测设置
     */
    public static function save_crawler_settings($crawler_settings) {
        $config = self::get_config();
        $config['crawler_detection'] = array_merge($config['crawler_detection'], $crawler_settings);
        return self::save_config($config);
    }

    /**
     * 获取用户管理设置
     */
    public static function get_user_management_settings() {
        return self::get_config('user_management');
    }

    /**
     * 保存用户管理设置
     */
    public static function save_user_management_settings($user_management_settings) {
        $config = self::get_config();
        $config['user_management'] = array_merge($config['user_management'], $user_management_settings);
        return self::save_config($config);
    }

    /**
     * 获取订单复制设置
     */
    public static function get_order_replication_settings() {
        $config = self::get_config('order_replication');

        // 确保所有必需的键都存在，如果不存在则使用默认值
        $defaults = [
            'enable_order_replication' => true,  // 🎯 默认启用订单复制
            'enable_status_replication' => false,
            'enable_payment_info_replication' => false,
            'enable_smart_product_replacement' => true,  // 🎯 默认启用智能产品替换
            'enable_payment_verification' => false,
            'enable_callback_handling' => false,
            'product_replacement_mode' => 'same_price',
            'enable_default_db_payment_redirect' => true  // 🎯 默认启用用默认数据库进行支付跳转
        ];

        return array_merge($defaults, $config);
    }

    /**
     * 保存订单复制设置
     */
    public static function save_order_replication_settings($settings) {
        $config = self::get_config();
        $config['order_replication'] = array_merge($config['order_replication'], $settings);
        return self::save_config($config);
    }

    /**
     * 获取优惠券同步设置
     */
    public static function get_coupon_sync_settings() {
        $config = self::get_config('coupon_sync');

        // 确保所有必需的键都存在，如果不存在则使用默认值
        $defaults = [
            'enable_coupon_sync' => false,
            'sync_mode' => 'realtime',
            'batch_interval' => 'daily',
            'conflict_resolution' => 'newest',
            'enable_sync_logging' => true,
            'exclude_expired' => true,
            'max_retries' => 3,
            'last_sync' => null
        ];

        return array_merge($defaults, $config);
    }

    /**
     * 保存优惠券同步设置
     */
    public static function save_coupon_sync_settings($settings) {
        return self::update_config_section('coupon_sync', $settings);
    }

    /**
     * 获取PayPal Multi Gateway设置
     */
    public static function get_paypal_multi_gateway_settings() {
        $config = self::get_config('paypal_multi_gateway');

        // 确保所有必需的键都存在，如果不存在则使用默认值
        $defaults = [
            'version' => '1.0.0',
            'last_updated' => '',
            'settings' => [
                'load_balancing' => 'random',
                'retry_count' => 1,
                'timeout' => 30,
                'test_mode' => 'no',
                'enabled' => true
            ],
            'accounts' => []
        ];

        return array_merge($defaults, $config);
    }

    /**
     * 保存PayPal Multi Gateway设置
     */
    public static function save_paypal_multi_gateway_settings($settings) {
        $config = self::get_config();
        $config['paypal_multi_gateway'] = array_merge($config['paypal_multi_gateway'], $settings);
        return self::save_config($config);
    }

    /**
     * 获取PayPal账户配置
     */
    public static function get_paypal_accounts() {
        $paypal_config = self::get_paypal_multi_gateway_settings();
        return isset($paypal_config['accounts']) ? $paypal_config['accounts'] : [];
    }

    /**
     * 保存PayPal账户配置
     */
    public static function save_paypal_accounts($accounts) {
        $config = self::get_config();
        $config['paypal_multi_gateway']['accounts'] = $accounts;
        $config['paypal_multi_gateway']['last_updated'] = function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s');
        return self::save_config($config);
    }

    /**
     * 添加PayPal账户
     */
    public static function add_paypal_account($account_id, $account_data) {
        $accounts = self::get_paypal_accounts();
        $accounts[$account_id] = array_merge($account_data, [
            'account_id' => $account_id,
            'created_at' => function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s'),
            'updated_at' => function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s')
        ]);
        return self::save_paypal_accounts($accounts);
    }

    /**
     * 更新PayPal账户
     */
    public static function update_paypal_account($account_id, $account_data) {
        $accounts = self::get_paypal_accounts();
        if (isset($accounts[$account_id])) {
            $accounts[$account_id] = array_merge($accounts[$account_id], $account_data, [
                'updated_at' => function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s')
            ]);
            return self::save_paypal_accounts($accounts);
        }
        return false;
    }

    /**
     * 删除PayPal账户
     */
    public static function delete_paypal_account($account_id) {
        $accounts = self::get_paypal_accounts();
        if (isset($accounts[$account_id])) {
            unset($accounts[$account_id]);
            return self::save_paypal_accounts($accounts);
        }
        return false;
    }

    /**
     * 更新PayPal账户状态
     */
    public static function update_paypal_account_status($account_id, $status) {
        $accounts = self::get_paypal_accounts();
        if (isset($accounts[$account_id])) {
            $accounts[$account_id]['status'] = $status;
            $accounts[$account_id]['updated_at'] = function_exists('current_time') ? current_time('mysql') : date('Y-m-d H:i:s');
            return self::save_paypal_accounts($accounts);
        }
        return false;
    }

    /**
     * 获取PayPal账户
     */
    public static function get_paypal_account($account_id) {
        $accounts = self::get_paypal_accounts();
        return isset($accounts[$account_id]) ? $accounts[$account_id] : null;
    }

    /**
     * 获取税收复制设置
     */
    public static function get_tax_replication_settings() {
        $config = self::get_config('tax_replication');

        // 确保所有必需的键都存在，如果不存在则使用默认值
        $defaults = [
            'enable_tax_replication' => false,
            'enable_tax_rates_sync' => true,
            'enable_tax_classes_sync' => true,
            'enable_tax_settings_sync' => true,
            'enable_standard_rates_sync' => true,
            'enable_reduced_rates_sync' => true,
            'enable_zero_rates_sync' => true,
            'sync_mode' => 'realtime',
            'batch_interval' => 'daily',
            'conflict_resolution' => 'newest',
            'enable_sync_logging' => true,
            'max_retries' => 3,
            'last_sync' => null,
            'backup_before_sync' => true,
            'sync_tax_based_on' => true,
            'sync_shipping_tax_class' => true,
            'sync_tax_round_at_subtotal' => true,
            'sync_tax_total_display' => true,
            'sync_tax_display_shop' => true,
            'sync_tax_display_cart' => true,
            'sync_prices_include_tax' => true,
            'sync_calc_taxes' => true,
            'sync_default_customer_address' => true,
            'sync_store_address' => true
        ];

        return array_merge($defaults, $config);
    }

    /**
     * 保存税收复制设置
     */
    public static function save_tax_replication_settings($settings) {
        $config = self::get_config();
        $config['tax_replication'] = array_merge($config['tax_replication'], $settings);
        return self::save_config($config);
    }

    /**
     * 更新单个税收复制设置
     */
    public static function update_tax_replication_setting($key, $value) {
        $config = self::get_config();
        $config['tax_replication'][$key] = $value;
        return self::save_config($config);
    }

    /**
     * 获取单个税收复制设置
     */
    public static function get_tax_replication_setting($key, $default = null) {
        $tax_settings = self::get_tax_replication_settings();
        return isset($tax_settings[$key]) ? $tax_settings[$key] : $default;
    }

    /**
     * 启用/禁用税收复制
     */
    public static function toggle_tax_replication($enable) {
        return self::update_tax_replication_setting('enable_tax_replication', (bool) $enable);
    }

    /**
     * 设置税收同步模式
     */
    public static function set_tax_sync_mode($mode) {
        $allowed_modes = ['realtime', 'scheduled', 'manual'];
        if (in_array($mode, $allowed_modes)) {
            return self::update_tax_replication_setting('sync_mode', $mode);
        }
        return false;
    }

    /**
     * 获取税收同步统计信息
     */
    public static function get_tax_sync_stats() {
        $tax_settings = self::get_tax_replication_settings();
        
        return [
            'enabled' => !empty($tax_settings['enable_tax_replication']),
            'sync_mode' => $tax_settings['sync_mode'] ?? 'manual',
            'last_sync' => $tax_settings['last_sync'] ?? null,
            'total_synced_databases' => 0, // 这里可以添加更详细的统计逻辑
            'sync_errors' => 0, // 这里可以添加错误统计
            'next_scheduled_sync' => null // 这里可以添加计划任务信息
        ];
    }
    
    /**
     * 导出配置
     */
    public static function export_config() {
        return self::get_config();
    }
    
    /**
     * 导入配置
     */
    public static function import_config($imported_config) {
        if (!self::validate_config($imported_config)) {
            return false;
        }
        
        return self::save_config($imported_config);
    }
    
    /**
     * 重置为默认配置
     */
    public static function reset_config() {
        return self::save_config(self::$default_config);
    }
    
    /**
     * 创建wp-config hook文件
     */
    private static function create_wp_config_hook() {
        $hook_content = self::generate_wp_config_hook_content();

        if (file_put_contents(self::$wp_config_hook_file, $hook_content, LOCK_EX) !== false) {
            chmod(self::$wp_config_hook_file, 0644);
            return true;
        }

        return false;
    }

    /**
     * 生成wp-config hook文件内容
     */
    private static function generate_wp_config_hook_content() {
        // 兼容非WordPress环境
        $timestamp = function_exists('current_time') ? current_time('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        $config_file = self::$config_file;

        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * YXJTO Gateway - wp-config.php Hook文件\n";
        $content .= " * 自动生成于: {$timestamp}\n";
        $content .= " * 此文件在WordPress加载前执行数据库切换\n";
        $content .= " * 请在wp-config.php中包含此文件：\n";
        $content .= " * \$hook_file = dirname(__FILE__) . '/wp-content/yxjto-gateway-hook.php';\n";
        $content .= " * if (file_exists(\$hook_file)) { require_once(\$hook_file); }\n";
        $content .= " * \n";
        $content .= " * 然后使用安全的条件定义避免重复定义警告：\n";
        $content .= " * if (!defined('DB_NAME')) { define('DB_NAME', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['name'] : 'your_database'); }\n";
        $content .= " */\n\n";
        $content .= "// 确保WP_CONTENT_DIR常量已定义\n";
        $content .= "if (!defined('WP_CONTENT_DIR')) {\n";
        $content .= "    // 在wp-config.php早期阶段，WP_CONTENT_DIR可能未定义\n";
        $content .= "    // 根据hook文件位置推断wp-content目录\n";
        $content .= "    define('WP_CONTENT_DIR', dirname(__FILE__));\n";
        $content .= "}\n\n";
        $content .= "// 确保ABSPATH常量已定义\n";
        $content .= "if (!defined('ABSPATH')) {\n";
        $content .= "    // 如果不在WordPress环境中，定义基本常量\n";
        $content .= "    define('ABSPATH', dirname(WP_CONTENT_DIR) . '/');\n";
        $content .= "}\n\n";
        $content .= "// WordPress多数据库切换器\n";
        $content .= "if (!class_exists('WP_Multi_DB_Early_Switcher')) {\n";
        $content .= "class WP_Multi_DB_Early_Switcher {\n";
        $content .= "    \n";
        $content .= "    private static \$config_file = '{$config_file}';\n";
        $content .= "    private static \$switched_database = null;\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 执行数据库切换\n";
        $content .= "     */\n";
        $content .= "    public static function switch_database() {\n";
        $content .= "        // 只在未切换时执行\n";
        $content .= "        if (self::\$switched_database !== null) {\n";
        $content .= "            return self::\$switched_database;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 加载配置\n";
        $content .= "        \$config = self::load_config();\n";
        $content .= "        if (!\$config) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 确保配置结构完整\n";
        $content .= "        if (!isset(\$config['databases']) || !is_array(\$config['databases'])) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        if (!isset(\$config['url_rules'])) {\n";
        $content .= "            \$config['url_rules'] = array();\n";
        $content .= "        }\n";
        $content .= "        if (!isset(\$config['ip_rules'])) {\n";
        $content .= "            \$config['ip_rules'] = array();\n";
        $content .= "        }\n";
        $content .= "        if (!isset(\$config['crawler_detection'])) {\n";
        $content .= "            \$config['crawler_detection'] = array('enabled' => false);\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 首先检查爬虫检测（优先级最高）\n";
        $content .= "        if (!empty(\$config['crawler_detection']['enabled'])) {\n";
        $content .= "            if (self::is_crawler(\$config['crawler_detection'])) {\n";
        $content .= "                // 爬虫始终使用默认数据库\n";
        $content .= "                if (isset(\$config['databases']['default'])) {\n";
        $content .= "                    return self::apply_database_config(\$config['databases']['default'], 'default', 'Crawler Detection');\n";
        $content .= "                }\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 然后检查URL参数规则（优先级高）\n";
        $content .= "        \$url_database = self::check_url_rules(\$config['url_rules']);\n";
        $content .= "        if (\$url_database && isset(\$config['databases'][\$url_database])) {\n";
        $content .= "            // URL参数匹配，设置到cookies中\n";
        $content .= "            self::set_url_parameter_cookie(\$url_database, \$config['url_rules']);\n";
        $content .= "            return self::apply_database_config(\$config['databases'][\$url_database], \$url_database, 'URL Parameter');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 然后检查cookies中的URL参数（优先级中等）\n";
        $content .= "        \$cookie_database = self::check_cookie_rules(\$config['url_rules']);\n";
        $content .= "        if (\$cookie_database && isset(\$config['databases'][\$cookie_database])) {\n";
        $content .= "            return self::apply_database_config(\$config['databases'][\$cookie_database], \$cookie_database, 'Cookie Parameter');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 检查IP地址规则\n";
        $content .= "        \$ip_database = self::check_ip_rules(\$config['ip_rules']);\n";
        $content .= "        if (\$ip_database && isset(\$config['databases'][\$ip_database])) {\n";
        $content .= "            return self::apply_database_config(\$config['databases'][\$ip_database], \$ip_database, 'IP Address');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 使用默认数据库\n";
        $content .= "        if (isset(\$config['databases']['default'])) {\n";
        $content .= "            return self::apply_database_config(\$config['databases']['default'], 'default', 'Default');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 如果没有默认数据库配置，返回default\n";
        $content .= "        self::\$switched_database = 'default';\n";
        $content .= "        return 'default';\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 加载配置文件\n";
        $content .= "     */\n";
        $content .= "    private static function load_config() {\n";
        $content .= "        if (!file_exists(self::\$config_file)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        \$config = include self::\$config_file;\n";
        $content .= "        return is_array(\$config) ? \$config : false;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检查URL参数规则\n";
        $content .= "     */\n";
        $content .= "    private static function check_url_rules(\$url_rules) {\n";
        $content .= "        if (empty(\$url_rules) || !is_array(\$url_rules)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        foreach (\$url_rules as \$rule) {\n";
        $content .= "            if (empty(\$rule['enabled']) || empty(\$rule['parameter']) || empty(\$rule['value']) || empty(\$rule['database'])) {\n";
        $content .= "                continue;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            \$param_value = \$_GET[\$rule['parameter']] ?? '';\n";
        $content .= "            if (\$param_value === \$rule['value']) {\n";
        $content .= "                return \$rule['database'];\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return false;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检查cookies中的URL参数规则\n";
        $content .= "     */\n";
        $content .= "    private static function check_cookie_rules(\$url_rules) {\n";
        $content .= "        if (empty(\$url_rules) || !is_array(\$url_rules)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        \$cookie_name = 'yxjto_gateway_param';\n";
        $content .= "        if (!isset(\$_COOKIE[\$cookie_name])) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        \$cookie_data = self::parse_cookie_data(\$_COOKIE[\$cookie_name]);\n";
        $content .= "        if (!\$cookie_data) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 检查cookie是否过期\n";
        $content .= "        if (self::is_cookie_expired(\$cookie_data)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 验证cookie中的参数是否匹配某个规则\n";
        $content .= "        foreach (\$url_rules as \$rule) {\n";
        $content .= "            if (empty(\$rule['enabled']) || empty(\$rule['parameter']) || empty(\$rule['value']) || empty(\$rule['database'])) {\n";
        $content .= "                continue;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            if (isset(\$cookie_data['param']) && isset(\$cookie_data['value']) &&\n";
        $content .= "                \$cookie_data['param'] === \$rule['parameter'] &&\n";
        $content .= "                \$cookie_data['value'] === \$rule['value']) {\n";
        $content .= "                return \$rule['database'];\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return false;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 设置URL参数到cookies\n";
        $content .= "     */\n";
        $content .= "    private static function set_url_parameter_cookie(\$database, \$url_rules) {\n";
        $content .= "        // 找到匹配的规则\n";
        $content .= "        \$matched_rule = null;\n";
        $content .= "        foreach (\$url_rules as \$rule) {\n";
        $content .= "            if (empty(\$rule['enabled']) || empty(\$rule['parameter']) || empty(\$rule['value']) || empty(\$rule['database'])) {\n";
        $content .= "                continue;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            \$param_value = \$_GET[\$rule['parameter']] ?? '';\n";
        $content .= "            if (\$param_value === \$rule['value'] && \$rule['database'] === \$database) {\n";
        $content .= "                \$matched_rule = \$rule;\n";
        $content .= "                break;\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        if (!\$matched_rule) {\n";
        $content .= "            return;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 创建cookie数据\n";
        $content .= "        \$expire_seconds = 24 * 60 * 60; // 24小时过期\n";
        $content .= "        \$cookie_data = array(\n";
        $content .= "            'param' => \$matched_rule['parameter'],\n";
        $content .= "            'value' => \$matched_rule['value'],\n";
        $content .= "            'database' => \$database,\n";
        $content .= "            'timestamp' => time(),\n";
        $content .= "            'expires' => time() + \$expire_seconds,\n";
        $content .= "            'expire_hours' => 24 // 记录过期小时数\n";
        $content .= "        );\n";
        $content .= "        \n";
        $content .= "        // 使用与yxjto-gateway.php相同的加密方式\n";
        $content .= "        \$json_data = json_encode(\$cookie_data);\n";
        $content .= "        \$checksum = md5(\$json_data . 'yxjto_gateway_salt');\n";
        $content .= "        \$cookie_value = base64_encode(\$json_data . '|' . \$checksum);\n";
        $content .= "        \$cookie_name = 'yxjto_gateway_param';\n";
        $content .= "        \n";
        $content .= "        // 设置cookie（24小时过期）\n";
        $content .= "        if (!headers_sent()) {\n";
        $content .= "            \$secure = isset(\$_SERVER['HTTPS']) && \$_SERVER['HTTPS'] === 'on';\n";
        $content .= "            setcookie(\$cookie_name, \$cookie_value, time() + \$expire_seconds, '/', '', \$secure, true);\n";
        $content .= "        }\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 解析cookie数据\n";
        $content .= "     */\n";
        $content .= "    private static function parse_cookie_data(\$cookie_value) {\n";
        $content .= "        if (empty(\$cookie_value)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        try {\n";
        $content .= "            // 首先尝试base64解码\n";
        $content .= "            \$decoded_data = base64_decode(\$cookie_value);\n";
        $content .= "            if (!\$decoded_data) {\n";
        $content .= "                return false;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            // 检查是否包含管道符（带哈希的格式）\n";
        $content .= "            if (strpos(\$decoded_data, '|') !== false) {\n";
        $content .= "                \$parts = explode('|', \$decoded_data);\n";
        $content .= "                \n";
        $content .= "                if (count(\$parts) === 2) {\n";
        $content .= "                    // 格式: json_data|hash\n";
        $content .= "                    \$json_data = \$parts[0];\n";
        $content .= "                    \$checksum = \$parts[1];\n";
        $content .= "                    \n";
        $content .= "                    // 验证校验和\n";
        $content .= "                    \$expected_checksum = md5(\$json_data . 'yxjto_gateway_salt');\n";
        $content .= "                    if (\$checksum !== \$expected_checksum) {\n";
        $content .= "                        // 尝试不验证校验和，直接解析JSON\n";
        $content .= "                        \$data = json_decode(\$json_data, true);\n";
        $content .= "                        if (\$data && is_array(\$data)) {\n";
        $content .= "                            return self::validate_cookie_fields(\$data);\n";
        $content .= "                        }\n";
        $content .= "                        return false;\n";
        $content .= "                    }\n";
        $content .= "                    \n";
        $content .= "                    \$data = json_decode(\$json_data, true);\n";
        $content .= "                    if (!\$data || !is_array(\$data)) {\n";
        $content .= "                        return false;\n";
        $content .= "                    }\n";
        $content .= "                    \n";
        $content .= "                    return self::validate_cookie_fields(\$data);\n";
        $content .= "                } else {\n";
        $content .= "                    // 可能是其他格式，尝试直接解析为JSON\n";
        $content .= "                    \$data = json_decode(\$decoded_data, true);\n";
        $content .= "                    if (\$data && is_array(\$data)) {\n";
        $content .= "                        return self::validate_cookie_fields(\$data);\n";
        $content .= "                    }\n";
        $content .= "                    return false;\n";
        $content .= "                }\n";
        $content .= "            } else {\n";
        $content .= "                // 没有管道符，尝试直接解析为JSON\n";
        $content .= "                \$data = json_decode(\$decoded_data, true);\n";
        $content .= "                if (\$data && is_array(\$data)) {\n";
        $content .= "                    return self::validate_cookie_fields(\$data);\n";
        $content .= "                }\n";
        $content .= "                return false;\n";
        $content .= "            }\n";
        $content .= "        } catch (Exception \$e) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 验证Cookie字段\n";
        $content .= "     */\n";
        $content .= "    private static function validate_cookie_fields(\$data) {\n";
        $content .= "        if (!\$data || !is_array(\$data)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 验证必需的字段\n";
        $content .= "        \$required_fields = ['param', 'value', 'database', 'timestamp'];\n";
        $content .= "        foreach (\$required_fields as \$field) {\n";
        $content .= "            if (!isset(\$data[\$field])) {\n";
        $content .= "                return false;\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 如果没有expires字段，根据timestamp计算\n";
        $content .= "        if (!isset(\$data['expires'])) {\n";
        $content .= "            \$data['expires'] = \$data['timestamp'] + (24 * 60 * 60); // 默认24小时\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return \$data;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检查cookie是否过期\n";
        $content .= "     */\n";
        $content .= "    private static function is_cookie_expired(\$cookie_data) {\n";
        $content .= "        if (!isset(\$cookie_data['expires'])) {\n";
        $content .= "            // 如果没有过期时间，检查时间戳（兼容旧版本）\n";
        $content .= "            if (isset(\$cookie_data['timestamp'])) {\n";
        $content .= "                return (time() - \$cookie_data['timestamp']) > (24 * 60 * 60);\n";
        $content .= "            }\n";
        $content .= "            return true; // 无法确定时间，认为过期\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return time() > \$cookie_data['expires'];\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检查IP地址规则\n";
        $content .= "     */\n";
        $content .= "    private static function check_ip_rules(\$ip_rules) {\n";
        $content .= "        if (empty(\$ip_rules) || !is_array(\$ip_rules)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        \$client_ip = self::get_client_ip();\n";
        $content .= "        \n";
        $content .= "        foreach (\$ip_rules as \$rule) {\n";
        $content .= "            if (empty(\$rule['enabled']) || empty(\$rule['ip_range']) || empty(\$rule['database'])) {\n";
        $content .= "                continue;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            if (self::ip_in_range(\$client_ip, \$rule['ip_range'])) {\n";
        $content .= "                return \$rule['database'];\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return false;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 获取客户端IP\n";
        $content .= "     */\n";
        $content .= "    private static function get_client_ip() {\n";
        $content .= "        \$ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];\n";
        $content .= "        \n";
        $content .= "        foreach (\$ip_keys as \$key) {\n";
        $content .= "            if (!empty(\$_SERVER[\$key])) {\n";
        $content .= "                \$ip = \$_SERVER[\$key];\n";
        $content .= "                if (strpos(\$ip, ',') !== false) {\n";
        $content .= "                    \$ip = trim(explode(',', \$ip)[0]);\n";
        $content .= "                }\n";
        $content .= "                if (filter_var(\$ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {\n";
        $content .= "                    return \$ip;\n";
        $content .= "                }\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return \$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检查IP是否在范围内\n";
        $content .= "     */\n";
        $content .= "    private static function ip_in_range(\$ip, \$range) {\n";
        $content .= "        if (strpos(\$range, '/') !== false) {\n";
        $content .= "            // CIDR格式\n";
        $content .= "            list(\$subnet, \$mask) = explode('/', \$range);\n";
        $content .= "            \$ip_long = ip2long(\$ip);\n";
        $content .= "            \$subnet_long = ip2long(\$subnet);\n";
        $content .= "            \$mask_long = -1 << (32 - \$mask);\n";
        $content .= "            return (\$ip_long & \$mask_long) === (\$subnet_long & \$mask_long);\n";
        $content .= "        } elseif (strpos(\$range, '-') !== false) {\n";
        $content .= "            // IP范围格式\n";
        $content .= "            list(\$start_ip, \$end_ip) = explode('-', \$range);\n";
        $content .= "            \$ip_long = ip2long(\$ip);\n";
        $content .= "            return \$ip_long >= ip2long(trim(\$start_ip)) && \$ip_long <= ip2long(trim(\$end_ip));\n";
        $content .= "        } else {\n";
        $content .= "            // 单个IP\n";
        $content .= "            return \$ip === trim(\$range);\n";
        $content .= "        }\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 应用数据库配置\n";
        $content .= "     */\n";
        $content .= "    private static function apply_database_config(\$db_config, \$db_key, \$rule_type) {\n";
        $content .= "        if (empty(\$db_config) || empty(\$db_config['enabled'])) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 使用runkit扩展或其他方法重新定义常量（如果可能）\n";
        $content .= "        // 由于PHP不允许重新定义常量，我们使用全局变量来覆盖\n";
        $content .= "        \n";
        $content .= "        // 保存原始数据库配置\n";
        $content .= "        if (!defined('WP_MULTI_DB_ORIGINAL_HOST')) {\n";
        $content .= "            define('WP_MULTI_DB_ORIGINAL_HOST', defined('DB_HOST') ? DB_HOST : 'localhost');\n";
        $content .= "            define('WP_MULTI_DB_ORIGINAL_NAME', defined('DB_NAME') ? DB_NAME : '');\n";
        $content .= "            define('WP_MULTI_DB_ORIGINAL_USER', defined('DB_USER') ? DB_USER : '');\n";
        $content .= "            define('WP_MULTI_DB_ORIGINAL_PASSWORD', defined('DB_PASSWORD') ? DB_PASSWORD : '');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 设置新的数据库配置到全局变量\n";
        $content .= "        \$GLOBALS['yxjto_gateway_config'] = array(\n";
        $content .= "            'host' => \$db_config['host'],\n";
        $content .= "            'name' => \$db_config['database'],\n";
        $content .= "            'user' => \$db_config['username'],\n";
        $content .= "            'password' => \$db_config['password'],\n";
        $content .= "            'charset' => isset(\$db_config['charset']) ? \$db_config['charset'] : 'utf8mb4',\n";
        $content .= "            'collate' => isset(\$db_config['collate']) ? \$db_config['collate'] : ''\n";
        $content .= "        );\n";
        $content .= "        \n";
        $content .= "        // 如果常量还未定义，直接定义新值\n";
        $content .= "        // 注意：如果wp-config.php中已经定义了数据库常量，\n";
        $content .= "        // 请删除原始定义并使用条件定义方式\n";
        $content .= "        if (!defined('DB_HOST')) {\n";
        $content .= "            define('DB_HOST', \$db_config['host']);\n";
        $content .= "        }\n";
        $content .= "        if (!defined('DB_NAME')) {\n";
        $content .= "            define('DB_NAME', \$db_config['database']);\n";
        $content .= "        }\n";
        $content .= "        if (!defined('DB_USER')) {\n";
        $content .= "            define('DB_USER', \$db_config['username']);\n";
        $content .= "        }\n";
        $content .= "        if (!defined('DB_PASSWORD')) {\n";
        $content .= "            define('DB_PASSWORD', \$db_config['password']);\n";
        $content .= "        }\n";
        $content .= "        if (!defined('DB_CHARSET')) {\n";
        $content .= "            define('DB_CHARSET', isset(\$db_config['charset']) ? \$db_config['charset'] : 'utf8mb4');\n";
        $content .= "        }\n";
        $content .= "        if (!defined('DB_COLLATE')) {\n";
        $content .= "            define('DB_COLLATE', isset(\$db_config['collate']) ? \$db_config['collate'] : '');\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 记录切换信息\n";
        $content .= "        define('WP_MULTI_DB_SWITCHED', true);\n";
        $content .= "        define('WP_MULTI_DB_CURRENT_DATABASE', \$db_key);\n";
        $content .= "        define('WP_MULTI_DB_SWITCH_RULE', \$rule_type);\n";
        $content .= "        define('WP_MULTI_DB_SWITCH_TIME', time());\n";
        $content .= "        \n";
        $content .= "        self::\$switched_database = \$db_key;\n";
        $content .= "        return \$db_key;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 检测是否为爬虫\n";
        $content .= "     */\n";
        $content .= "    private static function is_crawler(\$crawler_settings) {\n";
        $content .= "        // 如果爬虫检测被禁用，返回false\n";
        $content .= "        if (empty(\$crawler_settings['enabled'])) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        \$user_agent = \$_SERVER['HTTP_USER_AGENT'] ?? '';\n";
        $content .= "        \$client_ip = self::get_client_ip();\n";
        $content .= "        \n";
        $content .= "        // 如果没有User-Agent，不是爬虫\n";
        $content .= "        if (empty(\$user_agent)) {\n";
        $content .= "            return false;\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 默认爬虫模式\n";
        $content .= "        \$default_crawlers = array(\n";
        $content .= "            'googlebot' => array('Googlebot', 'Google-InspectionTool'),\n";
        $content .= "            'bingbot' => array('bingbot', 'BingPreview'),\n";
        $content .= "            'facebookbot' => array('facebookexternalhit', 'Facebot'),\n";
        $content .= "            'twitterbot' => array('Twitterbot'),\n";
        $content .= "            'linkedinbot' => array('LinkedInBot'),\n";
        $content .= "            'baidubot' => array('Baiduspider', 'BaiduSpider'),\n";
        $content .= "            'yandexbot' => array('YandexBot', 'YandexImages')\n";
        $content .= "        );\n";
        $content .= "        \n";
        $content .= "        // 检查已启用的爬虫\n";
        $content .= "        \$enabled_crawlers = isset(\$crawler_settings['enabled_crawlers']) ? \$crawler_settings['enabled_crawlers'] : array();\n";
        $content .= "        \n";
        $content .= "        foreach (\$default_crawlers as \$crawler_key => \$patterns) {\n";
        $content .= "            // 如果这个爬虫被禁用，跳过\n";
        $content .= "            if (!in_array(\$crawler_key, \$enabled_crawlers)) {\n";
        $content .= "                continue;\n";
        $content .= "            }\n";
        $content .= "            \n";
        $content .= "            foreach (\$patterns as \$pattern) {\n";
        $content .= "                if (stripos(\$user_agent, \$pattern) !== false) {\n";
        $content .= "                    return true;\n";
        $content .= "                }\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        // 检查自定义爬虫模式\n";
        $content .= "        if (!empty(\$crawler_settings['custom_patterns'])) {\n";
        $content .= "            foreach (\$crawler_settings['custom_patterns'] as \$pattern) {\n";
        $content .= "                if (!empty(\$pattern['pattern']) && !empty(\$pattern['enabled'])) {\n";
        $content .= "                    if (stripos(\$user_agent, \$pattern['pattern']) !== false) {\n";
        $content .= "                        return true;\n";
        $content .= "                    }\n";
        $content .= "                }\n";
        $content .= "            }\n";
        $content .= "        }\n";
        $content .= "        \n";
        $content .= "        return false;\n";
        $content .= "    }\n";
        $content .= "    \n";
        $content .= "    /**\n";
        $content .= "     * 获取当前切换的数据库\n";
        $content .= "     */\n";
        $content .= "    public static function get_switched_database() {\n";
        $content .= "        return self::\$switched_database;\n";
        $content .= "    }\n";
        $content .= "  }\n";
        $content .= "}\n";
        $content .= "// 执行数据库切换（只执行一次）\n";
        $content .= "if (class_exists('WP_Multi_DB_Early_Switcher') && !defined('WP_MULTI_DB_HOOK_EXECUTED')) {\n";
        $content .= "    define('WP_MULTI_DB_HOOK_EXECUTED', true);\n";
        $content .= "    WP_Multi_DB_Early_Switcher::switch_database();\n";
        $content .= "}\n";

        return $content;
    }

    /**
     * 获取配置文件信息
     */
    public static function get_config_info() {
        if (!file_exists(self::$config_file)) {
            return null;
        }

        return [
            'file_path' => self::$config_file,
            'file_size' => filesize(self::$config_file),
            'last_modified' => filemtime(self::$config_file),
            'is_writable' => is_writable(self::$config_file),
            'backup_count' => count(glob(self::$config_file . '.backup.*')),
            'wp_config_hook_file' => self::$wp_config_hook_file,
            'wp_config_hook_exists' => file_exists(self::$wp_config_hook_file)
        ];
    }

    /**
     * 检查wp-config.php集成状态
     */
    public static function check_wp_config_integration() {
        $wp_config_path = ABSPATH . 'wp-config.php';

        if (!file_exists($wp_config_path)) {
            return false;
        }

        $content = file_get_contents($wp_config_path);

        // 检查是否包含我们插件添加的完整集成代码
        $has_hook = strpos($content, 'yxjto-gateway-hook.php') !== false;
        $has_plugin_comment = strpos($content, 'YXJTO Gateway') !== false ||
                             strpos($content, 'WordPress多数据库插件') !== false ||
                             strpos($content, 'WordPress Multi-Database Plugin Hook') !== false;
        $has_dynamic_config = strpos($content, "isset(\$GLOBALS['yxjto_gateway_config'])") !== false;
        $has_safe_define = strpos($content, "!defined('DB_NAME')") !== false;

        // 只有当包含我们的标识注释和动态配置时才认为是已集成
        return $has_hook && $has_plugin_comment && $has_dynamic_config;
    }

    /**
     * 获取wp-config.php集成详细状态
     */
    public static function get_wp_config_integration_details() {
        $wp_config_path = ABSPATH . 'wp-config.php';

        if (!file_exists($wp_config_path)) {
            return [
                'file_exists' => false,
                'is_writable' => false,
                'has_hook' => false,
                'has_plugin_comment' => false,
                'has_dynamic_config' => false,
                'has_backup' => false,
                'integration_status' => false
            ];
        }

        $content = file_get_contents($wp_config_path);
        $backup_path = $wp_config_path . '.wp-multi-db-backup';

        $has_hook = strpos($content, 'yxjto-gateway-hook.php') !== false;
        $has_plugin_comment = strpos($content, 'YXJTO Gateway') !== false ||
                             strpos($content, 'WordPress多数据库插件') !== false ||
                             strpos($content, 'WordPress Multi-Database Plugin Hook') !== false;
        $has_dynamic_config = strpos($content, "isset(\$GLOBALS['yxjto_gateway_config'])") !== false;
        $has_safe_define = strpos($content, "!defined('DB_NAME')") !== false;
        $has_backup = file_exists($backup_path);

        return [
            'file_exists' => true,
            'is_writable' => is_writable($wp_config_path),
            'has_hook' => $has_hook,
            'has_plugin_comment' => $has_plugin_comment,
            'has_dynamic_config' => $has_dynamic_config,
            'has_safe_define' => $has_safe_define,
            'has_backup' => $has_backup,
            'integration_status' => $has_hook && $has_plugin_comment && $has_dynamic_config,
            'file_size' => filesize($wp_config_path),
            'last_modified' => filemtime($wp_config_path),
            'backup_exists' => $has_backup,
            'backup_size' => $has_backup ? filesize($backup_path) : 0,
            'backup_modified' => $has_backup ? filemtime($backup_path) : 0
        ];
    }

    /**
     * 启用wp-config.php集成
     */
    public static function enable_wp_config_integration() {
        $wp_config_path = ABSPATH . 'wp-config.php';

        if (!file_exists($wp_config_path)) {
            return [
                'success' => false,
                'message' => 'wp-config.php file not found'
            ];
        }

        if (!is_writable($wp_config_path)) {
            return [
                'success' => false,
                'message' => 'wp-config.php is not writable'
            ];
        }

        // 读取当前wp-config.php内容
        $content = file_get_contents($wp_config_path);

        // 检查是否已经集成
        if (strpos($content, 'yxjto-gateway-hook.php') !== false) {
            return [
                'success' => false,
                'message' => 'WP-Config integration is already enabled'
            ];
        }

        // 备份原始wp-config.php
        $backup_path = $wp_config_path . '.wp-multi-db-backup';
        if (!copy($wp_config_path, $backup_path)) {
            return [
                'success' => false,
                'message' => 'Failed to create backup of wp-config.php'
            ];
        }

        // 获取默认数据库配置
        $databases = self::get_databases();
        $default_db = isset($databases['default']) ? $databases['default'] : null;

        if (!$default_db) {
            return [
                'success' => false,
                'message' => 'Default database configuration not found'
            ];
        }

        // 修改wp-config.php
        $modified_content = self::modify_wp_config_content($content, $default_db);

        if (file_put_contents($wp_config_path, $modified_content) === false) {
            return [
                'success' => false,
                'message' => 'Failed to write modified wp-config.php'
            ];
        }

        // 确保hook文件存在
        self::create_wp_config_hook();

        return [
            'success' => true,
            'message' => 'WP-Config integration enabled successfully'
        ];
    }

    /**
     * 禁用wp-config.php集成
     */
    public static function disable_wp_config_integration() {
        $wp_config_path = ABSPATH . 'wp-config.php';
        $backup_path = $wp_config_path . '.wp-multi-db-backup';

        if (!file_exists($wp_config_path)) {
            return [
                'success' => false,
                'message' => 'wp-config.php file not found'
            ];
        }

        if (!is_writable($wp_config_path)) {
            return [
                'success' => false,
                'message' => 'wp-config.php is not writable'
            ];
        }

        // 如果有备份文件，恢复备份
        if (file_exists($backup_path)) {
            if (copy($backup_path, $wp_config_path)) {
                unlink($backup_path); // 删除备份文件
                return [
                    'success' => true,
                    'message' => 'WP-Config integration disabled and original configuration restored'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to restore backup'
                ];
            }
        }

        // 如果没有备份文件，手动移除集成代码
        $content = file_get_contents($wp_config_path);
        $restored_content = self::remove_wp_config_integration($content);

        if (file_put_contents($wp_config_path, $restored_content) === false) {
            return [
                'success' => false,
                'message' => 'Failed to write restored wp-config.php'
            ];
        }

        return [
            'success' => true,
            'message' => 'WP-Config integration disabled successfully'
        ];
    }

    /**
     * 修改wp-config.php内容
     */
    private static function modify_wp_config_content($content, $default_db) {
        // 添加多数据库hook代码
        $hook_code = "\n// YXJTO Gateway - 早期数据库切换\n";
        $hook_code .= "\$yxjto_gateway_hook_file = dirname(__FILE__) . '/wp-content/yxjto-gateway-hook.php';\n";
        $hook_code .= "if (file_exists(\$yxjto_gateway_hook_file)) {\n";
        $hook_code .= "    require_once(\$yxjto_gateway_hook_file);\n";
        $hook_code .= "}\n\n";

        // 查找MySQL设置部分的开始
        $mysql_start = strpos($content, '// ** MySQL');
        if ($mysql_start === false) {
            $mysql_start = strpos($content, '/** MySQL');
        }

        if ($mysql_start !== false) {
            // 在MySQL设置之前插入hook代码
            $content = substr_replace($content, $hook_code, $mysql_start, 0);
        } else {
            // 如果找不到MySQL设置，在<?php之后插入
            $php_start = strpos($content, '<?php');
            if ($php_start !== false) {
                $insert_pos = $php_start + 5;
                $content = substr_replace($content, $hook_code, $insert_pos, 0);
            }
        }

        // 替换数据库配置 - 使用安全的条件定义
        $db_patterns = [
            "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
            "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
            "/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
            "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
            "/define\s*\(\s*['\"]DB_CHARSET['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/",
            "/define\s*\(\s*['\"]DB_COLLATE['\"]\s*,\s*['\"][^'\"]*['\"]\s*\)\s*;/"
        ];

        // 安全的条件数据库常量定义（避免重复定义）
        $db_replacements = [
            "// 安全的条件数据库常量定义（避免重复定义）\nif (!defined('DB_NAME')) {\n    define( 'DB_NAME', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['name'] : '" . addslashes($default_db['database']) . "' );\n}",
            "if (!defined('DB_USER')) {\n    define( 'DB_USER', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['user'] : '" . addslashes($default_db['username']) . "' );\n}",
            "if (!defined('DB_PASSWORD')) {\n    define( 'DB_PASSWORD', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['password'] : '" . addslashes($default_db['password']) . "' );\n}",
            "if (!defined('DB_HOST')) {\n    define( 'DB_HOST', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['host'] : '" . addslashes($default_db['host']) . "' );\n}",
            "if (!defined('DB_CHARSET')) {\n    define( 'DB_CHARSET', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['charset'] : '" . addslashes($default_db['charset']) . "' );\n}",
            "if (!defined('DB_COLLATE')) {\n    define( 'DB_COLLATE', isset(\$GLOBALS['yxjto_gateway_config']) ? \$GLOBALS['yxjto_gateway_config']['collate'] : '' );\n}"
        ];

        $content = preg_replace($db_patterns, $db_replacements, $content);

        return $content;
    }

    /**
     * 移除wp-config.php集成
     */
    private static function remove_wp_config_integration($content) {
        // 移除多数据库hook代码（支持新旧两种注释格式）
        $content = preg_replace('/\/\/ WordPress Multi-Database Plugin Hook.*?}\s*\n/s', '', $content);
        $content = preg_replace('/\/\/ WordPress多数据库插件.*?}\s*\n/s', '', $content);

        // 移除安全条件定义代码块
        $content = preg_replace('/\/\/ 安全的条件数据库常量定义.*?if \(!defined\(\'DB_COLLATE\'\)\) \{.*?}\s*\n/s', '', $content);

        // 移除单独的条件定义
        $db_constants = ['DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_CHARSET', 'DB_COLLATE'];
        foreach ($db_constants as $constant) {
            $pattern = '/if \(!defined\(\'' . $constant . '\'\)\) \{.*?}\s*\n/s';
            $content = preg_replace($pattern, '', $content);
        }

        return $content;
    }
}

// 初始化配置管理器
WP_Multi_DB_Config_Manager::init();
