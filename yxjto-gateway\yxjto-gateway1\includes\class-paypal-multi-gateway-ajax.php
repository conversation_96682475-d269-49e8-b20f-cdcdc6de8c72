<?php
/**
 * YXJTO PayPal Multi Gateway AJAX 处理类
 * 专门处理 AJAX 请求，确保钩子正确注册
 * 基于原始 PayPal Multi Gateway 的 AJAX 处理逻辑
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Ajax {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 主要的AJAX钩子 - YXJTO命名空间
        add_action('wp_ajax_yxjto_paypal_multi_gateway_check_status', array($this, 'ajax_check_status'));
        add_action('wp_ajax_nopriv_yxjto_paypal_multi_gateway_check_status', array($this, 'ajax_check_status'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_simulate_checkout', array($this, 'ajax_simulate_checkout'));

        // 兼容性钩子 - 原始命名空间（如果有其他地方还在使用）
        add_action('wp_ajax_paypal_multi_gateway_check_status', array($this, 'ajax_check_status'));
        add_action('wp_ajax_nopriv_paypal_multi_gateway_check_status', array($this, 'ajax_check_status'));
        add_action('wp_ajax_paypal_multi_gateway_simulate_checkout', array($this, 'ajax_simulate_checkout'));

        // WooCommerce AJAX钩子（用于前端结账）
        add_action('wc_ajax_yxjto_paypal_multi_gateway_check_status', array($this, 'ajax_check_status'));

        // 注册账户切换 AJAX 处理器
        add_action('wp_ajax_yxjto_paypal_multi_gateway_switch_account', array($this, 'ajax_switch_account'));
        add_action('wp_ajax_nopriv_yxjto_paypal_multi_gateway_switch_account', array($this, 'ajax_switch_account'));

        // 注册账户选择 AJAX 处理器（用于结账页面）
        add_action('wp_ajax_yxjto_paypal_multi_gateway_select_account', array($this, 'ajax_select_account'));
        add_action('wp_ajax_nopriv_yxjto_paypal_multi_gateway_select_account', array($this, 'ajax_select_account'));

        // 添加AJAX类自检方法
        add_action('wp_ajax_yxjto_ajax_class_self_check', array($this, 'ajax_self_check'));

        // 添加主插件类状态检查方法
        add_action('wp_ajax_yxjto_main_plugin_status', array($this, 'ajax_main_plugin_status'));
    }
    
    /**
     * AJAX: 检查 PayPal 状态
     */
    public function ajax_check_status() {
        // 检查 nonce 是否存在
        if (!isset($_POST['nonce'])) {
            wp_send_json_error(__('Missing security token', 'yxjto-paypal-multi-gateway'));
            return;
        }

        // 验证 nonce
        $nonce_check = wp_verify_nonce($_POST['nonce'], 'yxjto_paypal_multi_gateway_checkout');
        if (!$nonce_check) {
            // 尝试其他可能的 nonce 名称
            $alt_nonce_check = wp_verify_nonce($_POST['nonce'], 'yxjto_paypal_multi_gateway_nonce');
            if (!$alt_nonce_check) {
                wp_send_json_error(__('Security verification failed', 'yxjto-paypal-multi-gateway'));
                return;
            }
        }

        try {
            // 获取支付网关实例
            $gateway = $this->get_payment_gateway();
            if (!$gateway) {
                wp_send_json_error(__('Payment gateway not initialized', 'yxjto-paypal-multi-gateway'));
                return;
            }

            // 使用真实的验证逻辑
            $has_valid_accounts = $this->has_valid_accounts_for_checkout();
            $currency_supported = $this->is_currency_supported();
            $gateway_available = $gateway->is_available();

            // 获取选中的账户信息用于结账显示
            $selected_account_info = $this->get_selected_account_info();

            $response_data = array(
                'has_valid_accounts' => $has_valid_accounts,
                'currency_supported' => $currency_supported,
                'gateway_available' => $gateway_available,
                'testmode' => $gateway->testmode,
                'currency' => get_woocommerce_currency(),
                'validation_strategy' => __('Fast validation: Email and URL types skip deep verification', 'yxjto-paypal-multi-gateway'),
                'selected_account' => $selected_account_info
            );

            // 添加额外的状态信息
            if (!$has_valid_accounts) {
                $response_data['message'] = __('No valid PayPal account configuration available', 'yxjto-paypal-multi-gateway');
                $response_data['status'] = 'no_valid_accounts';
            } elseif (!$currency_supported) {
                $response_data['message'] = __('Current currency is not supported for PayPal payments', 'yxjto-paypal-multi-gateway');
                $response_data['status'] = 'currency_not_supported';
            } elseif (!$gateway_available) {
                $response_data['message'] = __('PayPal payment is temporarily unavailable', 'yxjto-paypal-multi-gateway');
                $response_data['status'] = 'gateway_unavailable';
            } else {
                $response_data['message'] = __('Verification completed', 'yxjto-paypal-multi-gateway');
                $response_data['status'] = 'available';
            }

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            wp_send_json_error(__('Error occurred while checking status:', 'yxjto-paypal-multi-gateway') . ' ' . $e->getMessage());
        }
    }

    /**
     * AJAX: 切换到下一个账户
     */
    public function ajax_switch_account() {
        // 检查 nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'yxjto_paypal_multi_gateway_checkout')) {
            wp_send_json_error(__('Security verification failed', 'yxjto-paypal-multi-gateway'));
            return;
        }

        try {
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
                wp_send_json_error(__('Account management class does not exist', 'yxjto-paypal-multi-gateway'));
                return;
            }

            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();

            // 手动切换：强制获取下一个账户（轮询方式）
            $next_account = $accounts->get_next_global_account();

            if (!$next_account) {
                wp_send_json_error(__('No available accounts', 'yxjto-paypal-multi-gateway'));
                return;
            }

            // 返回新选择的账户信息
            $account_info = $this->format_account_info($next_account);

            wp_send_json_success(array(
                'account' => $account_info,
                'message' => __('Switched to next account', 'yxjto-paypal-multi-gateway'),
                'selection_method' => 'manual_switch'
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Error switching account:', 'yxjto-paypal-multi-gateway') . ' ' . $e->getMessage());
        }
    }

    /**
     * AJAX: 选择账户（使用负载均衡）
     */
    public function ajax_select_account() {
        // 检查 nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'yxjto_paypal_multi_gateway_checkout')) {
            wp_send_json_error(__('Security verification failed', 'yxjto-paypal-multi-gateway'));
            return;
        }

        try {
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
                wp_send_json_error(__('Account management class does not exist', 'yxjto-paypal-multi-gateway'));
                return;
            }

            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();

            // 使用真实的负载均衡选择账户
            $selected_account = $accounts->select_account_for_checkout_with_load_balancing();

            if (!$selected_account) {
                wp_send_json_error(__('No available accounts for checkout', 'yxjto-paypal-multi-gateway'));
                return;
            }

            // 返回选择的账户信息
            $account_info = $this->format_account_info($selected_account);

            wp_send_json_success(array(
                'account' => $account_info,
                'message' => __('Account selected for checkout', 'yxjto-paypal-multi-gateway'),
                'selection_method' => 'load_balancing'
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Error selecting account:', 'yxjto-paypal-multi-gateway') . ' ' . $e->getMessage());
        }
    }

    /**
     * AJAX: 模拟结账
     */
    public function ajax_simulate_checkout() {
        // 检查 nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'yxjto_paypal_multi_gateway_checkout')) {
            wp_send_json_error(__('Security verification failed', 'yxjto-paypal-multi-gateway'));
            return;
        }

        try {
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
                wp_send_json_error(__('Account management class does not exist', 'yxjto-paypal-multi-gateway'));
                return;
            }

            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();

            // 模拟结账流程中的账户选择
            $selected_account = $accounts->select_account_for_checkout_with_load_balancing();

            if (!$selected_account) {
                wp_send_json_error(__('No accounts available for checkout simulation', 'yxjto-paypal-multi-gateway'));
                return;
            }

            // 返回模拟结果
            $account_info = $this->format_account_info($selected_account);

            wp_send_json_success(array(
                'simulation_result' => 'success',
                'selected_account' => $account_info,
                'message' => __('Checkout simulation completed successfully', 'yxjto-paypal-multi-gateway'),
                'selection_method' => 'load_balancing_simulation'
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Checkout simulation failed:', 'yxjto-paypal-multi-gateway') . ' ' . $e->getMessage());
        }
    }

    /**
     * AJAX: 自检方法
     */
    public function ajax_self_check() {
        wp_send_json_success(array(
            'class_name' => get_class($this),
            'method_called' => 'ajax_self_check',
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => 'AJAX class self-check successful',
            'instance_id' => spl_object_hash($this)
        ));
    }

    /**
     * AJAX: 检查主插件类状态
     */
    public function ajax_main_plugin_status() {
        global $wp_filter;

        $status = array(
            'main_class_exists' => class_exists('YXJTO_Gateway'),
            'main_hook_registered' => isset($wp_filter['wp_ajax_yxjto_check_ajax_class']),
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => 'Main plugin status check from AJAX class'
        );

        // 检查主插件类的钩子
        if (isset($wp_filter['wp_ajax_yxjto_check_ajax_class'])) {
            $callbacks = array();
            foreach ($wp_filter['wp_ajax_yxjto_check_ajax_class']->callbacks as $priority => $callback_group) {
                foreach ($callback_group as $callback) {
                    if (is_array($callback['function'])) {
                        $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                        $method = $callback['function'][1];
                        $callbacks[] = $class . '::' . $method;
                    } else {
                        $callbacks[] = $callback['function'];
                    }
                }
            }
            $status['main_hook_callbacks'] = $callbacks;
        }

        wp_send_json_success($status);
    }

    /**
     * 获取支付网关实例
     */
    private function get_payment_gateway() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Payment')) {
            return new YXJTO_PayPal_Multi_Gateway_Payment();
        }
        return null;
    }

    /**
     * 检查是否有有效的结账账户
     */
    private function has_valid_accounts_for_checkout() {
        if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            return false;
        }

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $active_accounts = $accounts->get_active_accounts();

        foreach ($active_accounts as $account) {
            if ($this->is_account_valid($account)) {
                return true;
            }
        }

        return false;
    }



    /**
     * 检查账户是否有效
     */
    private function is_account_valid($account) {
        if (!$account || !isset($account->account_type) || !isset($account->account_data)) {
            return false;
        }

        $account_data = is_string($account->account_data) ?
            json_decode($account->account_data, true) :
            $account->account_data;

        if (!is_array($account_data)) {
            return false;
        }

        switch ($account->account_type) {
            case 'email':
                return !empty($account_data['email']) && is_email($account_data['email']);
            case 'paypal_me':
                return !empty($account_data['paypal_me_url']) && filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL);
            case 'api':
                return !empty($account_data['client_id']) && !empty($account_data['client_secret']);
            default:
                return false;
        }
    }

    /**
     * 检查货币是否支持
     */
    private function is_currency_supported() {
        $supported_currencies = array(
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'NOK', 'SEK', 'DKK',
            'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP', 'TWD', 'THB',
            'SGD', 'HKD', 'CNY', 'NZD', 'TRY', 'INR', 'RUB'
        );

        $current_currency = get_woocommerce_currency();
        return in_array($current_currency, $supported_currencies);
    }

    /**
     * 获取选中的账户信息
     */
    private function get_selected_account_info() {
        try {
            if (!class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
                return null;
            }

            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            $selected_account = $accounts->select_account_for_checkout_with_load_balancing();

            if ($selected_account) {
                $formatted_info = $this->format_account_info($selected_account);
                // 调试日志：记录 AJAX 返回的账户信息
                error_log("PayPal Multi Gateway [AJAX Debug]: Selected account for status check: {$selected_account->account_id}");
                error_log("PayPal Multi Gateway [AJAX Debug]: Formatted account info: " . print_r($formatted_info, true));
                return $formatted_info;
            }
        } catch (Exception $e) {
            // 静默处理错误，返回null
        }

        return null;
    }

    /**
     * 格式化账户信息
     */
    private function format_account_info($account) {
        if (!$account) {
            return null;
        }

        $account_data = is_string($account->account_data) ?
            json_decode($account->account_data, true) :
            $account->account_data;

        if (!is_array($account_data)) {
            $account_data = array();
        }

        $formatted = array(
            'id' => isset($account->account_id) ? $account->account_id : '',
            'type' => isset($account->account_type) ? $account->account_type : '',
            'status' => isset($account->status) ? $account->status : '',
            'weight' => isset($account->weight) ? $account->weight : 100
        );

        // 根据账户类型添加显示信息（不包含图标）
        switch ($account->account_type) {
            case 'email':
                $formatted['display'] = 'Email: '.($account_data['email'] ?? __('Email Account', 'yxjto-paypal-multi-gateway'));
                break;
            case 'paypal_me':
                $formatted['display'] = 'paypalme: '.($account_data['paypal_me_url'] ?? __('PayPal.me Account', 'yxjto-paypal-multi-gateway'));
                break;
            case 'api':
                $client_id = $account_data['client_id'] ?? '';
                $formatted['display'] = $client_id ? __('Current account valid', 'yxjto-paypal-multi-gateway') : __('API Account', 'yxjto-paypal-multi-gateway');
                break;
            default:
                $formatted['display'] = __('PayPal Account', 'yxjto-paypal-multi-gateway');
        }

        return $formatted;
    }
}
