/* PayPal Multi Gateway Admin Statistics & Logs Styles */

/* Statistics Page Styles */
.paypal-multi-gateway-statistics {
    margin: 20px 0;
    max-width: 1400px;
}

/* Page Header */
.statistics-page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-title-section {
    flex: 1;
}

.page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.page-description {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-actions .button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 8px;
}

.page-actions .button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.page-actions .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Filter Panel */
.statistics-filter-panel {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.filter-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.filter-panel-header:hover {
    background: #e9ecef;
}

.filter-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-title .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: #6c757d;
}

.filter-toggle-btn {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.filter-toggle-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.filter-toggle-btn .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: #6c757d;
    transition: transform 0.3s ease;
}

.filter-panel-content {
    padding: 24px;
}

.statistics-filter-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-row {
    display: flex;
    gap: 24px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #6c757d;
}

.filter-select,
.date-input {
    padding: 10px 14px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.filter-select:focus,
.date-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.custom-date-range {
    flex: 1;
    min-width: 300px;
}

.date-range-inputs {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.date-input-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.date-label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-separator {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    color: #6c757d;
}

.date-separator .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.filter-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    padding-top: 8px;
}

.filter-apply-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.filter-apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.filter-clear-btn {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #6c757d;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.filter-clear-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
}

.filter-actions .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Overview Statistics Cards */
.statistics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 28px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.stat-icon {
    font-size: 36px;
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 18px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
    line-height: 1.2;
    word-break: break-all;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    line-height: 1.4;
}

/* Add subtle animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Charts */
.statistics-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 32px;
    margin-bottom: 40px;
}

.chart-container {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 28px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.chart-container h3 {
    margin: 0 0 24px 0;
    color: #2d3748;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container h3::before {
    content: '📊';
    font-size: 24px;
}

.chart-container:nth-child(2) h3::before {
    content: '📈';
}

/* Statistics Table */
.statistics-table {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 28px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.statistics-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.statistics-table h3 {
    margin: 0 0 24px 0;
    color: #2d3748;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.statistics-table h3::before {
    content: '📋';
    font-size: 24px;
}

.no-data-message {
    text-align: center;
    padding: 60px 40px;
    color: #718096;
    background: #f7fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
}

.no-data-message::before {
    content: '📊';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.account-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-top: 4px;
    display: inline-block;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.account-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.type-email {
    background: #e3f2fd;
    color: #1976d2;
}

.type-api {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-paypal_me {
    background: #e8f5e8;
    color: #388e3c;
}

.success-count {
    color: #28a745;
    font-weight: 600;
}

.failed-count {
    color: #dc3545;
    font-weight: 600;
}

.success-rate-bar {
    position: relative;
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    min-width: 100px;
}

.rate-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.3s ease;
}

.rate-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #333;
    text-shadow: 0 0 3px rgba(255,255,255,0.8);
}

.view-details {
    background: #007cba;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.view-details:hover {
    background: #005a87;
}

/* Sortable table headers */
.sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.sortable:hover {
    background: #f8f9fa;
}

.sort-indicator {
    margin-left: 5px;
    opacity: 0.5;
}

.sort-indicator:after {
    content: "↕";
}

.sortable.asc .sort-indicator:after {
    content: "↑";
    opacity: 1;
}

.sortable.desc .sort-indicator:after {
    content: "↓";
    opacity: 1;
}

/* Logs Page Styles */
.paypal-multi-gateway-logs {
    margin: 20px 0;
}

.logs-header {
    margin-bottom: 30px;
}

.logs-filters {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.logs-filter-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: end;
}

.logs-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.no-logs-message {
    text-align: center;
    padding: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #666;
}

/* Logs Table */
.logs-table-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logs-table {
    margin: 0;
}

.logs-table th,
.logs-table td {
    padding: 12px;
    vertical-align: middle;
}

.column-checkbox {
    width: 40px;
    text-align: center;
}

.column-transaction-id {
    min-width: 150px;
}

.column-order-id {
    width: 100px;
}

.column-account-id {
    min-width: 120px;
}

.column-amount {
    width: 120px;
    text-align: right;
}

.column-status {
    width: 100px;
}

.column-date {
    width: 180px;
}

.column-actions {
    width: 120px;
    text-align: center;
}

.account-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.account-email {
    background: #e3f2fd;
    color: #1976d2;
}

.account-api {
    background: #f3e5f5;
    color: #7b1fa2;
}

.account-paypal {
    background: #e8f5e8;
    color: #388e3c;
}

.currency-info {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-completed .status-indicator {
    background: #28a745;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-pending .status-indicator {
    background: #ffc107;
}

.status-failed,
.status-error {
    background: #f8d7da;
    color: #721c24;
}

.status-failed .status-indicator,
.status-error .status-indicator {
    background: #dc3545;
}

.date-display {
    font-weight: 500;
    color: #333;
}

.relative-time {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-buttons .button {
    padding: 4px 8px;
    min-height: auto;
    line-height: 1;
}

.action-buttons .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Bulk Actions */
.bulk-actions-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.logs-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.logs-count {
    font-size: 14px;
    color: #666;
}

/* Clear Logs Dropdown */
.clear-logs-dropdown {
    position: relative;
    display: inline-block;
}

.clear-logs-toggle {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
}

.clear-logs-toggle:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
}

.clear-logs-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 350px;
    max-width: 400px;
}

.clear-logs-options {
    padding: 20px;
}

.clear-logs-options h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.clear-option {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.clear-option:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.clear-option .button {
    margin-bottom: 8px;
    width: 100%;
    justify-content: center;
}

.clear-option .description {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.danger-zone {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.danger-title {
    color: #e53e3e;
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
}

.danger-text {
    color: #e53e3e;
    font-weight: 500;
}

.button-delete {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
}

.button-delete:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Modal Styles */
.log-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.log-modal-content {
    background: #fff;
    border-radius: 8px;
    max-width: 800px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.log-modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.log-modal-header h3 {
    margin: 0;
    color: #333;
}

.log-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.log-modal-close:hover {
    color: #333;
}

.log-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Clear by Status Modal */
.clear-by-status-form {
    max-width: 500px;
}

.status-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.status-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.status-checkbox:hover {
    background-color: #f8f9fa;
}

.status-checkbox input[type="checkbox"] {
    margin: 0;
}

.date-range-option {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.date-range-inputs {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.date-input-group {
    flex: 1;
}

.date-input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.date-input-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Log Maintenance Modal */
.maintenance-options {
    max-width: 600px;
}

.maintenance-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.maintenance-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.maintenance-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.db-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.auto-cleanup-options {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.cleanup-settings {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.setting-group select {
    width: 100%;
    max-width: 200px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Log Details Modal Content */
.log-details {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.log-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.log-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.log-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.log-details-table {
    width: 100%;
    border-collapse: collapse;
}

.log-details-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.log-details-table td:first-child {
    width: 150px;
    font-weight: 500;
    color: #666;
}

.gateway-response {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Loading and Error States */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-card {
    animation: fadeIn 0.3s ease-out;
}

/* Chart Container Enhancements */
.chart-container canvas {
    max-height: 400px;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
    font-style: italic;
}

/* Enhanced Status Badges */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

/* Enhanced Table Interactions */
.logs-table tbody tr {
    transition: background-color 0.2s ease;
}

.logs-table tbody tr:hover {
    background-color: #f8f9fa;
}

.logs-table tbody tr.selected {
    background-color: #e3f2fd;
}

/* Enhanced Filter Form */
.logs-filter-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* Enhanced Buttons */
.button {
    transition: all 0.2s ease;
}

.button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button-primary:hover {
    background-color: #005a87;
}

/* Enhanced Statistics Cards */
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* Enhanced Success Rate Bar */
.success-rate-bar {
    position: relative;
    overflow: hidden;
}

.success-rate-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

/* Print Styles */
@media print {
    .statistics-controls,
    .logs-filters,
    .bulk-actions-container,
    .action-buttons,
    .log-modal {
        display: none !important;
    }

    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .chart-container {
        break-inside: avoid;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .statistics-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .statistics-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .paypal-multi-gateway-statistics {
        margin: 10px 0;
    }

    .statistics-page-header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        text-align: center;
    }

    .page-title {
        font-size: 24px;
        justify-content: center;
    }

    .page-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-panel-content {
        padding: 20px;
    }

    .filter-row {
        flex-direction: column;
        gap: 16px;
    }

    .filter-group {
        min-width: auto;
    }

    .custom-date-range {
        min-width: auto;
    }

    .date-range-inputs {
        flex-direction: column;
        gap: 16px;
    }

    .date-separator {
        display: none;
    }

    .filter-actions {
        flex-direction: column;
        gap: 12px;
    }

    .filter-apply-btn,
    .filter-clear-btn {
        width: 100%;
        justify-content: center;
    }

    .statistics-overview {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 20px;
        gap: 16px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 28px;
    }

    .stat-value {
        font-size: 24px;
    }

    .statistics-charts {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        padding: 20px;
    }

    .statistics-table {
        padding: 20px;
        overflow-x: auto;
    }

    .logs-summary {
        flex-direction: column;
        gap: 15px;
    }

    .logs-table-container {
        overflow-x: auto;
    }

    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .log-modal-content {
        width: 95%;
        margin: 20px;
        max-height: 90vh;
    }

    .bulk-actions-container {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .logs-info {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .statistics-page-header {
        padding: 16px;
    }

    .page-title {
        font-size: 20px;
    }

    .page-description {
        font-size: 14px;
    }

    .filter-panel-content {
        padding: 16px;
    }

    .stat-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
        margin: 0 auto;
    }

    .stat-value {
        font-size: 20px;
    }

    .chart-container,
    .statistics-table {
        padding: 16px;
    }

    .chart-container h3,
    .statistics-table h3 {
        font-size: 18px;
    }
}
