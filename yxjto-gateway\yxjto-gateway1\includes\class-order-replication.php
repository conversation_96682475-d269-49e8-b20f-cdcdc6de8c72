<?php
/**
 * 订单复制类
 * 
 * 实现WooCommerce订单在多数据库间的实时复制功能
 * 只使用HPOS（高性能订单存储）
 * 
 * @package YXJTO_Gateway
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Order_Replication {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 订单复制设置
     */
    private $settings = [];
    
    /**
     * 是否启用HPOS
     */
    private $is_hpos_enabled = false;
    
    /**
     * 调试模式
     */
    private $debug = true;
    
    /**
     * 设置订单的源数据库metadata
     * 在第一次复制前设置，如果已存在则不再重复设置
     * 
     * @param int $order_id 订单ID
     * @param string $source_database 源数据库名称
     * @return bool 是否设置成功
     */
    private function set_order_source_database($order_id, $source_database) {
        // 检查是否已经设置过
        $existing_source = $this->get_order_source_database($order_id);
        if (!empty($existing_source)) {
            $this->log_debug("Order #{$order_id} already has source database: {$existing_source}, skipping");
            return true;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} when setting source database");
            return false;
        }
        
        // 设置源数据库metadata
        $order->update_meta_data('_yxjto_current_database', $source_database);
        $order->save();
        
        $this->log_debug("Set source database for order #{$order_id}: {$source_database}");
        return true;
    }
    
    /**
     * 获取订单的源数据库
     * 优先从_yxjto_current_database meta中获取，如果为空则使用其他方法
     * 
     * @param int $order_id 订单ID
     * @return string 源数据库名称
     */
    private function get_order_source_database($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} when getting source database");
            return '';
        }
        
        // 首先尝试从metadata中获取
        $source_database = $order->get_meta('_yxjto_current_database', true);
        if (!empty($source_database)) {
            $this->log_debug("Found source database from metadata for order #{$order_id}: {$source_database}");
            return $source_database;
        }
        
        // // 如果metadata为空，使用其他方法获取当前数据库
        // $gateway = $this->get_gateway_instance();
        // if ($gateway) {
        //     $current_db = $gateway->get_current_database();
        //     if (!empty($current_db)) {
        //         $this->log_debug("Using current database as source for order #{$order_id}: {$current_db}");
        //         return $current_db;
        //     }
        // }
        
        // // 最后的备用方法：从配置管理器获取当前数据库
        // $config_manager = WP_Multi_DB_Config_Manager::get_instance();
        // if ($config_manager) {
        //     $current_db = $config_manager->get_current_database();
        //     if (!empty($current_db)) {
        //         $this->log_debug("Using config manager database as source for order #{$order_id}: {$current_db}");
        //         return $current_db;
        //     }
        // }
        
        $this->log_error("Could not determine source database for order #{$order_id}");
        return '';
    }
    
    /**
     * 安全获取YXJTO_Gateway实例
     * 
     * @return YXJTO_Gateway|null
     */
    private function get_gateway_instance() {
        if (!class_exists('YXJTO_Gateway')) {
            return null;
        }
        
        try {
            return YXJTO_Gateway::get_instance();
        } catch (Exception $e) {
            $this->log_error("Failed to get YXJTO_Gateway instance: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * 初始化
     */
    private function init() {
        // 检查HPOS是否启用
        $this->check_hpos_support();
        
        // 加载设置
        $this->load_settings();
        
        // 注册钩子
        $this->register_hooks();
        
    //     // 添加详细的调试信息
    //     $this->log_debug("=== ORDER REPLICATION INITIALIZATION ===");
    //     $this->log_debug("HPOS enabled: " . ($this->is_hpos_enabled ? "YES" : "NO"));
    //     $this->log_debug("Order replication enabled: " . ($this->settings['enable_order_replication'] ? "YES" : "NO"));
    //     $this->log_debug("Smart product replacement enabled: " . ($this->settings['enable_smart_product_replacement'] ? "YES" : "NO"));
    //     $this->log_debug("Settings loaded: " . json_encode($this->settings));
    //     $this->log_debug("Hooks registered for order replication");
    //     $this->log_debug("=== INITIALIZATION COMPLETE ===");
    }
    
    /**
     * 检查HPOS支持
     */
    private function check_hpos_support() {
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
            $this->is_hpos_enabled = \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
        }
        
        if (!$this->is_hpos_enabled) {
            $this->log_error('HPOS is not enabled. Order replication requires HPOS to be enabled.');
            return;
        }
        
        $this->log_debug('HPOS is enabled and ready for order replication');
    }
    
    /**
     * 加载设置
     */
    private function load_settings() {
        $default_settings = [
            'enable_order_replication' => true,  // 🎯 默认启用订单复制
            'enable_status_replication' => false,
            'enable_payment_info_replication' => false,
            'enable_smart_product_replacement' => true,  // 🎯 默认启用智能产品替换
            'enable_payment_verification' => false,
            'enable_callback_handling' => false,
            'product_replacement_mode' => 'same_price', // 'same_price' or 'same_id'
            'enable_default_db_payment_redirect' => true,  // 🎯 默认启用用默认数据库进行支付跳转
        ];
        
        // 从配置管理器获取设置
        $saved_settings = WP_Multi_DB_Config_Manager::get_order_replication_settings();
        $this->settings = array_merge($default_settings, $saved_settings);
        
        // $this->log_debug('Order replication settings loaded: ' . json_encode($this->settings));
    }
    
    /**
     * 注册钩子
     */
    private function register_hooks() {
        $this->log_debug("Starting hook registration...");
        
        if (!$this->is_hpos_enabled) {
            $this->log_debug("HPOS not enabled, skipping hook registration");
            return;
        }
        
        
        // 核心订单复制策略：多重时机确保复制成功
        if ($this->settings['enable_order_replication']) {
            // // 💡 主要复制时机：结账订单处理完成时（最可靠的时机）
            // add_action('woocommerce_checkout_order_processed', [$this, 'replicate_on_checkout_processed'], 5, 1);
            // $this->log_debug("✅ Registered PRIMARY replication hook: woocommerce_checkout_order_processed");
            
            // // 🎯 支付前复制：支付跳转时（如果被触发的话）
            // add_filter('woocommerce_get_checkout_payment_url', [$this, 'replicate_before_payment_redirect'], 5, 2);
            // $this->log_debug("✅ Registered payment redirect hook: woocommerce_get_checkout_payment_url");
            
            // 🛡️ 备用时机：新订单创建时
            add_action('woocommerce_new_order', [$this, 'replicate_on_new_order'], 10, 1);
            $this->log_debug("✅ Registered backup replication hook: woocommerce_new_order");
            
            // // 📄 最后备用：订单状态变更到pending时
            // add_action('woocommerce_order_status_pending', [$this, 'replicate_on_status_pending'], 10, 1);
            // $this->log_debug("✅ Registered fallback replication hook: woocommerce_order_status_pending");
            
            // $this->log_debug("Order replication strategy: MULTIPLE hooks for maximum reliability");
        } else {
            $this->log_debug("❌ Order replication is DISABLED in settings");
        }
        
        // 支付验证 - 确保支付时使用默认数据库
        add_filter('yxjto_order_replication_verify_payment', [$this, 'verify_payment_filter'], 10, 2);
        $this->log_debug("✅ Registered payment verification hook");
        
        // 购物车清空处理 - 确保订单创建后立即清空购物车
        add_action('woocommerce_checkout_order_processed', [$this, 'clear_cart_after_order_creation'], 5, 1);
        add_action('woocommerce_new_order', [$this, 'clear_cart_after_new_order'], 5, 1);
        $this->log_debug("✅ Registered cart clearing hooks");
        
        $this->log_debug("Hook registration completed");
    }

    /**
     * 手动复制订单到所有数据库（用于测试和手动触发）
     *
     * @param int $order_id 订单ID
     * @return bool 复制是否成功
     */
    public function replicate_order($order_id) {
        $this->log_debug("=== MANUAL ORDER REPLICATION TRIGGERED ===");
        $this->log_debug("Manual replication requested for order #{$order_id}");

        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} in manual replication");
            return false;
        }

        try {
            // 检查订单是否有足够的内容进行复制
            $line_items = $order->get_items('line_item');
            $total_items = count($line_items);
            $order_total = $order->get_total();

            if ($total_items === 0 || $order_total <= 0) {
                $this->log_debug("Order #{$order_id} not ready for manual replication (items: {$total_items}, total: {$order_total})");
                return false;
            }

            $this->log_debug("Order #{$order_id} ready for manual replication: {$total_items} items, total: \${$order_total}");

            // 复制订单到所有启用的数据库
            $this->replicate_order_to_all_databases($order);

            // 标记为已复制
            update_post_meta($order_id, '_yxjto_replicated', time());
            update_post_meta($order_id, '_yxjto_replication_strategy', 'manual');

            $this->log_debug("=== MANUAL ORDER REPLICATION COMPLETE ===");
            return true;

        } catch (Exception $e) {
            $this->log_error("Error in manual order replication: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 🎯 核心方法：支付跳转前复制订单到所有数据库，然后切换到默认数据库
     */
    public function replicate_before_payment_redirect($payment_url, $order) {
        if (!$order || !is_a($order, 'WC_Order')) {
            $this->log_error('Invalid order object in replicate_before_payment_redirect');
            return $payment_url;
        }

        $order_id = $order->get_id();
        $this->log_debug("=== PAYMENT REDIRECT REPLICATION START ===");
        $this->log_debug("Starting pre-payment replication for order #{$order_id}");

        try {
            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                $this->log_error("Gateway instance not available for pre-payment replication");
                return $payment_url;
            }

            // 优先使用存储的源数据库
            $source_database = $this->get_order_source_database($order_id);
            if (empty($source_database)) {
                $source_database = $gateway->get_current_database();
            }
            $this->log_debug("Source database for order #{$order_id}: {$source_database}");

            // 检查订单是否有足够的内容进行复制
            $line_items = $order->get_items('line_item');
            $total_items = count($line_items);
            $order_total = $order->get_total();

            if ($total_items === 0 || $order_total <= 0) {
                $this->log_debug("Order #{$order_id} not ready for replication (items: {$total_items}, total: {$order_total})");
                return $payment_url;
            }

            $this->log_debug("Order #{$order_id} ready for replication: {$total_items} items, total: \${$order_total}");

            // 复制订单到所有启用的数据库
            $this->replicate_order_to_all_databases($order);

            // 标记为已复制
            update_post_meta($order_id, '_yxjto_replicated', time());
            update_post_meta($order_id, '_yxjto_replication_strategy', 'pre_payment');

            // 切换到默认数据库进行支付
            $default_database = WP_Multi_DB_Config_Manager::get_default_database();
            if ($current_database !== $default_database) {
                $this->log_debug("Switching from {$current_database} to default database {$default_database} for payment");
                $gateway->switch_database($default_database);

                // 获取默认数据库中的订单支付URL
                $default_order = wc_get_order($order_id);
                if ($default_order) {
                    $payment_url = $default_order->get_checkout_payment_url();
                    $this->log_debug("Updated payment URL for default database: {$payment_url}");
                } else {
                    $this->log_error("Order #{$order_id} not found in default database {$default_database}");
                }
            }

            $this->log_debug("=== PAYMENT REDIRECT REPLICATION COMPLETE ===");

        } catch (Exception $e) {
            $this->log_error("Error in pre-payment replication: " . $e->getMessage());
        }

        return $payment_url;
    }

    /**
     * � 主要方法：结账处理完成后复制订单（最可靠的时机）
     */
    public function replicate_on_checkout_processed($order_id) {
        $this->log_debug("=== PRIMARY CHECKOUT REPLICATION TRIGGERED ===");
        $this->log_debug("Checkout processed for order #{$order_id}");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} in primary checkout replication");
            return;
        }
        
        // 检查是否已经复制过
        $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
        if ($replicated_flag) {
            $this->log_debug("Order #{$order_id} already replicated by another method, skipping primary checkout replication");
            return;
        }
        
        try {
            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                $this->log_error("Gateway instance not available for primary checkout replication");
                return;
            }
            
            // 检查订单是否有足够的内容进行复制
            $line_items = $order->get_items('line_item');
            $total_items = count($line_items);
            $order_total = $order->get_total();
            
            if ($total_items === 0 || $order_total <= 0) {
                $this->log_debug("Order #{$order_id} not ready for primary checkout replication (items: {$total_items}, total: {$order_total})");
                return;
            }
            
            $this->log_debug("Order #{$order_id} ready for primary checkout replication: {$total_items} items, total: \${$order_total}");
            
            // 复制订单到所有启用的数据库
            $this->replicate_order_to_all_databases($order);
            
            // 标记为已复制
            update_post_meta($order_id, '_yxjto_replicated', time());
            update_post_meta($order_id, '_yxjto_replication_strategy', 'primary_checkout');
            
            $this->log_debug("=== PRIMARY CHECKOUT REPLICATION COMPLETE ===");
            
        } catch (Exception $e) {
            $this->log_error("Error in primary checkout replication: " . $e->getMessage());
        }
    }

    /**
     * 🆕 新订单创建时复制（备用时机2）
     */
    public function replicate_on_new_order($order_id) {
        $this->log_debug("=== NEW ORDER REPLICATION TRIGGERED ===");
        $this->log_debug("New order created: #{$order_id}");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} in new order replication");
            return;
        }
        
        // 检查是否已经复制过
        $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
        if ($replicated_flag) {
            $this->log_debug("Order #{$order_id} already replicated, skipping new order replication");
            return;
        }
        
        try {
            // 等待一下让订单完全创建
            usleep(500000); // 等待0.5秒
            
            // 重新获取订单确保最新状态
            $order = wc_get_order($order_id);
            
            // 检查订单是否有足够的内容进行复制
            $line_items = $order->get_items('line_item');
            $total_items = count($line_items);
            $order_total = $order->get_total();
            
            if ($total_items === 0 || $order_total <= 0) {
                $this->log_debug("Order #{$order_id} not ready for new order replication (items: {$total_items}, total: {$order_total})");
                return;
            }
            
            $this->log_debug("Order #{$order_id} ready for new order replication: {$total_items} items, total: \${$order_total}");
            
            // 复制订单到所有启用的数据库
            $this->replicate_order_to_all_databases($order);
            
            // 标记为已复制
            update_post_meta($order_id, '_yxjto_replicated', time());
            update_post_meta($order_id, '_yxjto_replication_strategy', 'new_order');
            
            $this->log_debug("=== NEW ORDER REPLICATION COMPLETE ===");
            
        } catch (Exception $e) {
            $this->log_error("Error in new order replication: " . $e->getMessage());
        }
    }

    /**
     * 📄 订单状态变更到pending时复制（最后备用时机）
     */
    public function replicate_on_status_pending($order_id) {
        $this->log_debug("=== PENDING STATUS REPLICATION TRIGGERED ===");
        $this->log_debug("Order status changed to pending: #{$order_id}");
        
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order #{$order_id} in pending status replication");
            return;
        }
        
        // 检查是否已经复制过
        $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
        if ($replicated_flag) {
            $this->log_debug("Order #{$order_id} already replicated, skipping pending status replication");
            return;
        }
        
        try {
            // 检查订单是否有足够的内容进行复制
            $line_items = $order->get_items('line_item');
            $total_items = count($line_items);
            $order_total = $order->get_total();
            
            if ($total_items === 0 || $order_total <= 0) {
                $this->log_debug("Order #{$order_id} not ready for pending status replication (items: {$total_items}, total: {$order_total})");
                return;
            }
            
            $this->log_debug("Order #{$order_id} ready for pending status replication: {$total_items} items, total: \${$order_total}");
            
            // 复制订单到所有启用的数据库
            $this->replicate_order_to_all_databases($order);
            
            // 标记为已复制
            update_post_meta($order_id, '_yxjto_replicated', time());
            update_post_meta($order_id, '_yxjto_replication_strategy', 'pending_status');
            
            $this->log_debug("=== PENDING STATUS REPLICATION COMPLETE ===");
            
        } catch (Exception $e) {
            $this->log_error("Error in pending status replication: " . $e->getMessage());
        }
    }

    /**
     * 同步订单更新（备注、元数据等）
     */
    private function sync_order_updates($order_id) {
        $this->log_debug("Syncing updates for order #{$order_id}");

        try {
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Order #{$order_id} not found for sync");
                return;
            }

            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                $this->log_error("Gateway instance not available for sync updates");
                return;
            }
            
            // 优先使用存储的源数据库
            $source_database = $this->get_order_source_database($order_id);
            if (empty($source_database)) {
                $source_database = $gateway->get_current_database();
            }
            $databases = WP_Multi_DB_Config_Manager::get_databases();

            // 获取当前订单的备注
            $notes = wc_get_order_notes(['order_id' => $order_id]);
            $this->log_debug("Found " . count($notes) . " notes for order #{$order_id}");

            foreach ($databases as $db_name => $db_config) {
                if ($db_name === $source_database || !$db_config['enabled']) {
                    continue;
                }

                $this->log_debug("Syncing order #{$order_id} updates to database: {$db_name}");

                try {
                    // 切换到目标数据库
                    $gateway->switch_database($db_name);

                    // 检查目标数据库中的订单是否存在
                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        $this->log_debug("Order #{$order_id} not found in {$db_name}, skipping sync");
                        continue;
                    }

                    // 获取目标数据库中的备注
                    $target_notes = wc_get_order_notes(['order_id' => $order_id]);
                    $target_note_contents = array_map(function($note) { return $note->content; }, $target_notes);

                    // 同步缺失的备注
                    foreach ($notes as $note) {
                        if (!in_array($note->content, $target_note_contents)) {
                            $this->log_debug("Adding missing note to {$db_name}: " . substr($note->content, 0, 50) . "...");

                            $target_order->add_order_note(
                                $note->content,
                                $note->customer_note,
                                false // 不发送邮件
                            );
                        }
                    }

                    $this->log_debug("Successfully synced updates for order #{$order_id} to {$db_name}");

                } catch (Exception $e) {
                    $this->log_error("Failed to sync order #{$order_id} to {$db_name}: " . $e->getMessage());
                }
            }

            // 切换回源数据库
            $gateway->switch_database($source_database);

        } catch (Exception $e) {
            $this->log_error("Error syncing order updates: " . $e->getMessage());
        }
    }

    // ==========================================
    // 订单项目同步方法
    // ==========================================

    /**
     * 订单项目添加时同步
     */
    public function sync_order_item_added($item_id, $item, $order_id) {
        $this->log_debug("Order item added: Item #{$item_id} to Order #{$order_id}");
        $this->sync_single_order_item($order_id, $item_id, 'added');
    }

    /**
     * 订单项目更新时同步
     */
    public function sync_order_item_updated($item_id, $item, $order_id) {
        $this->log_debug("Order item updated: Item #{$item_id} in Order #{$order_id}");
        $this->sync_single_order_item($order_id, $item_id, 'updated');
    }

    /**
     * 订单项目删除时同步
     */
    public function sync_order_item_deleted($item_id, $order_id) {
        $this->log_debug("Order item deleted: Item #{$item_id} from Order #{$order_id}");
        $this->sync_single_order_item($order_id, $item_id, 'deleted');
    }

    /**
     * 订单项目元数据添加时同步
     */
    public function sync_order_item_meta_added($meta_id, $item_id, $meta_key, $meta_value) {
        $order_id = $this->get_order_id_from_item($item_id);
        if ($order_id) {
            $this->log_debug("Order item meta added: Meta #{$meta_id} (key: {$meta_key}) to Item #{$item_id} in Order #{$order_id}");
            $this->sync_order_item_meta($order_id, $item_id, $meta_key, $meta_value, 'added');
        }
    }

    /**
     * 订单项目元数据更新时同步
     */
    public function sync_order_item_meta_updated($meta_id, $item_id, $meta_key, $meta_value) {
        $order_id = $this->get_order_id_from_item($item_id);
        if ($order_id) {
            $this->log_debug("Order item meta updated: Meta #{$meta_id} (key: {$meta_key}) in Item #{$item_id} of Order #{$order_id}");
            $this->sync_order_item_meta($order_id, $item_id, $meta_key, $meta_value, 'updated');
        }
    }

    /**
     * 订单项目元数据删除时同步
     */
    public function sync_order_item_meta_deleted($meta_id, $item_id, $meta_key) {
        $order_id = $this->get_order_id_from_item($item_id);
        if ($order_id) {
            $this->log_debug("Order item meta deleted: Meta #{$meta_id} (key: {$meta_key}) from Item #{$item_id} in Order #{$order_id}");
            $this->sync_order_item_meta($order_id, $item_id, $meta_key, null, 'deleted');
        }
    }

    /**
     * 订单总额同步
     */
    public function sync_order_totals($and_taxes, $order) {
        if (!$order) {
            return;
        }

        $order_id = $order->get_id();
        
        // 只对已复制的订单进行总额同步
        $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
        if (!$replicated_flag) {
            return;
        }

        $this->log_debug("Syncing order totals for Order #{$order_id}");
        $this->sync_order_totals_to_all_databases($order);
    }

    /**
     * 同步单个订单项目到所有数据库
     */
    private function sync_single_order_item($order_id, $item_id, $action) {
        try {
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Order #{$order_id} not found for item sync");
                return;
            }

            // 只对已复制的订单进行项目同步
            $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
            if (!$replicated_flag) {
                $this->log_debug("Order #{$order_id} not yet replicated, skipping item sync");
                return;
            }

            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                $this->log_error("Gateway instance not available for item sync");
                return;
            }
            
            // 优先使用存储的源数据库
            $source_database = $this->get_order_source_database($order_id);
            if (empty($source_database)) {
                $source_database = $gateway->get_current_database();
            }
            $databases = WP_Multi_DB_Config_Manager::get_databases();

            // 获取源项目数据
            $source_item_data = null;
            if ($action !== 'deleted') {
                $source_item = $order->get_item($item_id);
                if ($source_item) {
                    $source_item_data = $this->extract_single_item_data($source_item);
                }
            }

            foreach ($databases as $db_name => $db_config) {
                if ($db_name === $source_database || !$db_config['enabled']) {
                    continue;
                }

                $this->log_debug("Syncing item #{$item_id} ({$action}) to database: {$db_name}");

                try {
                    // 切换到目标数据库
                    $gateway->switch_database($db_name);

                    // 检查目标数据库中的订单是否存在
                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        $this->log_debug("Order #{$order_id} not found in {$db_name}, skipping item sync");
                        continue;
                    }

                    if ($action === 'deleted') {
                        // 删除目标数据库中对应的项目
                        $this->delete_order_item_in_target_db($target_order, $item_id, $db_name);
                    } else {
                        // 添加或更新目标数据库中的项目
                        $this->sync_order_item_to_target_db($target_order, $source_item_data, $item_id, $action, $db_name);
                    }

                    // 重新计算订单总额
                    $target_order->calculate_totals();
                    $target_order->save();

                } catch (Exception $e) {
                    $this->log_error("Failed to sync item #{$item_id} to {$db_name}: " . $e->getMessage());
                }
            }

            // 切换回源数据库
            $gateway->switch_database($source_database);

        } catch (Exception $e) {
            $this->log_error("Error syncing order item: " . $e->getMessage());
        }
    }

    /**
     * 同步订单项目元数据到所有数据库
     */
    private function sync_order_item_meta($order_id, $item_id, $meta_key, $meta_value, $action) {
        try {
            // 只对已复制的订单进行元数据同步
            $replicated_flag = get_post_meta($order_id, '_yxjto_replicated', true);
            if (!$replicated_flag) {
                return;
            }

            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                return;
            }
            
            // 优先使用存储的源数据库
            $source_database = $this->get_order_source_database($order_id);
            if (empty($source_database)) {
                $source_database = $gateway->get_current_database();
            }
            $databases = WP_Multi_DB_Config_Manager::get_databases();

            foreach ($databases as $db_name => $db_config) {
                if ($db_name === $source_database || !$db_config['enabled']) {
                    continue;
                }

                try {
                    // 切换到目标数据库
                    $gateway->switch_database($db_name);

                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        continue;
                    }

                    $target_item = $target_order->get_item($item_id);
                    if (!$target_item) {
                        continue;
                    }

                    if ($action === 'deleted') {
                        $target_item->delete_meta_data($meta_key);
                    } else {
                        $target_item->update_meta_data($meta_key, $meta_value);
                    }

                    $target_item->save();

                } catch (Exception $e) {
                    $this->log_error("Failed to sync item meta to {$db_name}: " . $e->getMessage());
                }
            }

            // 切换回源数据库
            $gateway->switch_database($source_database);

        } catch (Exception $e) {
            $this->log_error("Error syncing order item meta: " . $e->getMessage());
        }
    }

    /**
     * 同步订单总额到所有数据库
     */
    private function sync_order_totals_to_all_databases($order) {
        try {
            $order_id = $order->get_id();
            
            $gateway = $this->get_gateway_instance();
            if (!$gateway) {
                return;
            }
            
            // 优先使用存储的源数据库
            $source_database = $this->get_order_source_database($order_id);
            if (empty($source_database)) {
                $source_database = $gateway->get_current_database();
            }
            $databases = WP_Multi_DB_Config_Manager::get_databases();

            // 获取源订单的总额信息
            $totals_data = [
                'total' => $order->get_total(),
                'subtotal' => $order->get_subtotal(),
                'tax_total' => $order->get_total_tax(),
                'shipping_total' => $order->get_shipping_total(),
                'discount_total' => $order->get_total_discount(),
            ];

            foreach ($databases as $db_name => $db_config) {
                if ($db_name === $source_database || !$db_config['enabled']) {
                    continue;
                }

                try {
                    // 切换到目标数据库
                    $gateway->switch_database($db_name);

                    $target_order = wc_get_order($order_id);
                    if (!$target_order) {
                        continue;
                    }

                    // 更新目标订单的总额
                    $target_order->set_total($totals_data['total']);
                    $target_order->set_subtotal($totals_data['subtotal']);
                    $target_order->set_total_tax($totals_data['tax_total']);
                    $target_order->set_shipping_total($totals_data['shipping_total']);
                    $target_order->set_discount_total($totals_data['discount_total']);
                    $target_order->save();

                    $this->log_debug("Synced order totals for Order #{$order_id} to {$db_name}");

                } catch (Exception $e) {
                    $this->log_error("Failed to sync order totals to {$db_name}: " . $e->getMessage());
                }
            }

            // 切换回源数据库
            $gateway->switch_database($source_database);

        } catch (Exception $e) {
            $this->log_error("Error syncing order totals: " . $e->getMessage());
        }
    }

    /**
     * 提取单个项目数据
     */
    private function extract_single_item_data($item) {
        $item_data = [
            'type' => $item->get_type(),
            'name' => $item->get_name(),
            'quantity' => $item->get_quantity(),
            'total' => $item->get_total(),
            'subtotal' => $item->get_subtotal(),
            'tax_total' => $item->get_total_tax(),
            'meta_data' => $item->get_meta_data(),
        ];

        // 如果是商品项目，获取商品ID和变体ID
        if ($item->get_type() === 'line_item') {
            $item_data['product_id'] = $item->get_product_id();
            $item_data['variation_id'] = $item->get_variation_id();
        }

        return $item_data;
    }

    /**
     * 在目标数据库中同步订单项目
     */
    private function sync_order_item_to_target_db($target_order, $source_item_data, $item_id, $action, $db_name) {
        if (!$source_item_data) {
            return;
        }

        // 检查目标订单中是否已存在该项目
        $existing_item = $target_order->get_item($item_id);

        if ($action === 'added' || !$existing_item) {
            // 添加新项目到目标订单
            if ($source_item_data['type'] === 'line_item') {
                // 应用商品替换逻辑
                $replaced_item_data = $this->apply_product_replacement_for_sync($source_item_data, $db_name);
                
                $product = wc_get_product($replaced_item_data['product_id']);
                if ($product) {
                    $item = new WC_Order_Item_Product();
                    $item->set_product($product);
                    $item->set_name($replaced_item_data['name']);
                    $item->set_quantity($replaced_item_data['quantity']);
                    $item->set_total($replaced_item_data['total']);
                    $item->set_subtotal($replaced_item_data['subtotal']);
                    
                    $target_order->add_item($item);
                    $this->log_debug("Added line item to target order in {$db_name}");
                }
            } elseif ($source_item_data['type'] === 'fee') {
                // 添加费用项目
                $fee = new WC_Order_Item_Fee();
                $fee->set_name($source_item_data['name']);
                $fee->set_total($source_item_data['total']);
                $fee->set_total_tax($source_item_data['tax_total']);
                
                $target_order->add_item($fee);
                $this->log_debug("Added fee item to target order in {$db_name}");
            } elseif ($source_item_data['type'] === 'shipping') {
                // 添加运费项目
                $shipping = new WC_Order_Item_Shipping();
                $shipping->set_method_title($source_item_data['name']);
                $shipping->set_total($source_item_data['total']);
                $shipping->set_total_tax($source_item_data['tax_total']);

                $target_order->add_item($shipping);
                $this->log_debug("Added shipping item to target order in {$db_name}");
            } elseif ($source_item_data['type'] === 'coupon') {
                // 添加优惠券项目
                $coupon = new WC_Order_Item_Coupon();
                $coupon->set_code($source_item_data['code']);
                $coupon->set_discount($source_item_data['discount_amount']);
                $coupon->set_discount_tax($source_item_data['discount_tax']);

                $target_order->add_item($coupon);
                $this->log_debug("Added coupon item '{$source_item_data['code']}' to target order in {$db_name} (discount: {$source_item_data['discount_amount']})");
            }
        } else {
            // 更新现有项目
            $existing_item->set_quantity($source_item_data['quantity']);
            $existing_item->set_total($source_item_data['total']);
            $existing_item->set_subtotal($source_item_data['subtotal']);
            $existing_item->save();
            
            $this->log_debug("Updated existing item in target order in {$db_name}");
        }
    }

    /**
     * 在目标数据库中删除订单项目
     */
    private function delete_order_item_in_target_db($target_order, $item_id, $db_name) {
        $existing_item = $target_order->get_item($item_id);
        if ($existing_item) {
            $target_order->remove_item($item_id);
            $this->log_debug("Deleted item #{$item_id} from target order in {$db_name}");
        }
    }

    /**
     * 为同步应用商品替换
     */
    private function apply_product_replacement_for_sync($item_data, $target_db_name) {
        // 这里可以复用现有的商品替换逻辑，但需要适配单个项目
        // 保持价格不变的原则
        
        $replaced_data = $item_data; // 默认保持原始数据
        
        // 尝试找到替换商品
        try {
            $gateway = $this->get_gateway_instance();
            $connection = $gateway->get_database_connection($target_db_name);
            
            if ($connection) {
                $replacement_mode = $this->settings['product_replacement_mode'] ?? 'same_price';
                
                if ($replacement_mode === 'same_price') {
                    $unit_price = $item_data['total'] / $item_data['quantity'];
                    $replacement = $this->find_product_by_price_enhanced_no_duplicate($unit_price, $connection, []);
                } else {
                    $replacement = $this->find_product_by_id_enhanced_no_duplicate(
                        $item_data['product_id'], 
                        $item_data['variation_id'], 
                        $connection, 
                        []
                    );
                }
                
                if ($replacement) {
                    $replaced_data['product_id'] = $replacement['product_id'];
                    $replaced_data['variation_id'] = $replacement['variation_id'];
                    $replaced_data['name'] = $replacement['name'];
                    // 价格保持不变
                    
                    $this->log_debug("✅ SYNC REPLACEMENT: Product {$item_data['product_id']} → {$replacement['product_id']} in {$target_db_name}");
                }
            }
        } catch (Exception $e) {
            $this->log_error("Failed to apply replacement for sync: " . $e->getMessage());
        }
        
        return $replaced_data;
    }

    /**
     * 从订单项目ID获取订单ID
     */
    private function get_order_id_from_item($item_id) {
        global $wpdb;
        
        $order_id = $wpdb->get_var($wpdb->prepare(
            "SELECT order_id FROM {$wpdb->prefix}woocommerce_order_items WHERE order_item_id = %d",
            $item_id
        ));
        
        return $order_id ? intval($order_id) : null;
    }

    // ==========================================
    // 现有的订单复制方法
    // ==========================================

    /**
     * 复制订单到所有启用的数据库
     */
    private function replicate_order_to_all_databases($order) {
        $order_id = $order->get_id();
        
        // 获取并设置源数据库（第一次复制前设置，如果已存在则不重复设置）
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            // 如果没有设置过源数据库，则获取当前数据库并设置
            $current_database = YXJTO_Gateway::get_instance()->get_current_database();
            if (!empty($current_database)) {
                $this->set_order_source_database($order_id, $current_database);
                $this->log_debug("set_order_source_database #{$order_id}: {$source_database}");
                $source_database = $current_database;
            }
        } else {
            // 如果已经有源数据库记录，使用记录的源数据库
            $current_database = $source_database;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        
        $this->log_debug("Source database for order #{$order_id}: {$source_database}");
        $this->log_debug("Current database context: {$current_database}");
        $this->log_debug("Available databases: " . implode(', ', array_keys($databases)));
        
        // 重要：在源数据库中获取订单数据（在切换到目标数据库之前）
        $this->log_debug("Extracting order data from SOURCE database for order #{$order->get_id()}");
        $order_data = $this->extract_order_data($order);
        $this->log_debug("Successfully extracted order data from source database");
        foreach ($databases as $db_name => $db_config) {
            // 跳过源数据库和未启用的数据库
            if ($db_name === $source_database || !$db_config['enabled']) {
                continue;
            }
            
            $this->log_debug("Replicating order #{$order->get_id()} to database: {$db_name}");
            
            // 复制订单到目标数据库
            $result = $this->replicate_order_to_database($order_data, $db_name, $db_config);
            
            if ($result) {
                $this->log_debug("Successfully replicated order #{$order->get_id()} to {$db_name}");

                // 清除缓存并立即修复显示（只执行一次）
                $this->clear_order_cache_safe($order->get_id(), $db_name);
                $this->fix_order_display_once($order->get_id(), $db_name, $order_data);
            } else {
                $this->log_error("Failed to replicate order #{$order->get_id()} to {$db_name}");
            }
        }
    }
    
    /**
     * 复制订单到指定数据库
     */
    private function replicate_order_to_database($order_data, $target_db_name, $target_db_config) {
        try {
            $this->log_debug("Starting replication to database: {$target_db_name}");



            // 创建目标数据库连接
            $this->log_debug("Creating connection to TARGET database: {$target_db_name}");
            $target_connection = $this->create_database_connection($target_db_config);
            if (!$target_connection) {
                throw new Exception("Failed to connect to target database: {$target_db_name}");
            }
            $this->log_debug("Successfully connected to target database: {$target_db_name}");

            // 商品智能替换（在目标数据库连接上下文中进行）
            if ($this->settings['enable_smart_product_replacement']) {
                $this->log_debug("Applying smart product replacement for target database: {$target_db_name}");
                $order_data = $this->apply_smart_product_replacement($order_data, $target_connection);
                $this->log_debug("Smart product replacement completed");
            }

            // 在目标数据库中创建订单
            $this->log_debug("Creating order in TARGET database: {$target_db_name}");
            $replicated_order_id = $this->create_order_in_database($order_data, $target_connection, $target_db_config);
            
            if ($replicated_order_id) {
                $this->log_debug("Order replicated with ID: {$replicated_order_id} in database: {$target_db_name}");
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            $this->log_error("Error replicating order to {$target_db_name}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 创建数据库连接
     */
    private function create_database_connection($db_config) {
        try {
            $connection = new mysqli(
                $db_config['host'],
                $db_config['username'],
                $db_config['password'],
                $db_config['database']
            );
            
            if ($connection->connect_error) {
                throw new Exception("Connection failed: " . $connection->connect_error);
            }
            
            // 设置字符集
            $charset = $db_config['charset'] ?? 'utf8mb4';
            $connection->set_charset($charset);
            
            return $connection;
            
        } catch (Exception $e) {
            $this->log_error("Database connection error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 提取订单数据（从源数据库）
     * 重要：此方法必须在源数据库连接上下文中调用，不能在切换到目标数据库后调用
     */
    private function extract_order_data($order) {
        try {
            $order_id = $order->get_id();
            $this->log_debug("=== EXTRACTING ORDER DATA FROM SOURCE DATABASE ===");
            $this->log_debug("Source order ID: {$order_id}");

            // 获取当前数据库连接信息（源数据库）
            global $wpdb;
            $this->log_debug("Source database: {$wpdb->dbname} (table prefix: {$wpdb->prefix})");

            $order_data = [
                'id' => $order_id,
                'status' => $order->get_status(),
                'currency' => $order->get_currency() ?: 'USD',
                'date_created' => $order->get_date_created(),
                'date_modified' => $order->get_date_modified(),
                'customer_id' => $order->get_customer_id() ?: 0,
                'customer_note' => $order->get_customer_note() ?: '',
                'billing_address' => $this->extract_billing_address($order),
                'shipping_address' => $this->extract_shipping_address($order),
                'payment_method' => $order->get_payment_method() ?: '',
                'payment_method_title' => $order->get_payment_method_title() ?: '',
                'transaction_id' => $order->get_transaction_id() ?: '',
                'total' => $order->get_total() ?: 0,
                'subtotal' => $order->get_subtotal() ?: 0,
                'tax_total' => $order->get_total_tax() ?: 0,
                'shipping_total' => $order->get_shipping_total() ?: 0,
                'discount_total' => $order->get_discount_total() ?: 0,
                'order_key' => $order->get_order_key() ?: '',
                'items' => [],
                'meta_data' => [],
                'notes' => []
            ];

            $this->log_debug("Extracted basic order data from SOURCE database for order #{$order_id} (status: {$order_data['status']}, total: {$order_data['total']})");
            $this->log_debug("Source order payment method: {$order_data['payment_method']} - {$order_data['payment_method_title']}");

            // 提取订单项目（包括商品、费用、运费等）
            $all_items = [];

            $this->log_debug("Starting to extract order items from SOURCE database for order #{$order_id}");

            // 强制刷新订单对象以获取最新的项目数据
            wp_cache_delete($order_id, 'posts');
            wp_cache_delete($order_id, 'wc_orders');
            $fresh_order = wc_get_order($order_id);

            if ($fresh_order) {
                $order = $fresh_order;
                $this->log_debug("Refreshed order object to get latest item data");
            }

            // 获取商品项目 - 增强版本，包含多种获取方式
            $line_items = $order->get_items('line_item');
            $this->log_debug("Found " . count($line_items) . " line_item(s) via WooCommerce API");

            // 如果WooCommerce API没有返回项目，尝试直接数据库查询
            if (empty($line_items)) {
                $this->log_debug("WooCommerce API returned empty, trying direct database query...");
                global $wpdb;

                $db_line_items = $wpdb->get_results($wpdb->prepare("
                    SELECT order_item_id, order_item_name
                    FROM {$wpdb->prefix}woocommerce_order_items
                    WHERE order_id = %d AND order_item_type = 'line_item'
                ", $order_id));

                $this->log_debug("Direct database query found " . count($db_line_items) . " line_item(s)");

                if (!empty($db_line_items)) {
                    $this->log_debug("WARNING: Database has line items but WooCommerce API returned empty. This may indicate a data consistency issue.");

                    // 尝试重新加载订单对象
                    $this->log_debug("Attempting to reload order object...");
                    wp_cache_delete($order_id, 'posts');
                    wp_cache_delete($order_id, 'wc_orders');
                    $order = wc_get_order($order_id);

                    if ($order) {
                        $line_items = $order->get_items('line_item');
                        $this->log_debug("After reload: Found " . count($line_items) . " line_item(s)");
                    }
                }
            }

            foreach ($line_items as $item_id => $item) {
                $product_id = $item->get_product_id();
                $variation_id = $item->get_variation_id();
                $quantity = $item->get_quantity();
                $name = $item->get_name();
                $total = $item->get_total();
                $subtotal = $item->get_subtotal();
                $tax_total = $item->get_total_tax();
                $meta_data = $item->get_meta_data();

                $all_items[] = [
                    'type' => 'line_item',
                    'product_id' => $product_id,
                    'variation_id' => $variation_id,
                    'quantity' => $quantity,
                    'name' => $name,
                    'total' => $total,
                    'subtotal' => $subtotal,
                    'tax_total' => $tax_total,
                    'meta_data' => $meta_data
                ];

                $this->log_debug("Extracted line_item #{$item_id}: '{$name}' (product_id: {$product_id}, qty: {$quantity}, total: {$total}, meta_count: " . count($meta_data) . ")");
            }

            // 获取费用项目 - 增强版本，再次刷新以确保获取最新数据
            wp_cache_delete($order_id, 'posts');
            wp_cache_delete($order_id, 'wc_orders');
            $fresh_order_for_fees = wc_get_order($order_id);

            if ($fresh_order_for_fees) {
                $order = $fresh_order_for_fees;
                $this->log_debug("Refreshed order object again before extracting fees and shipping");
            }

            $fee_items = $order->get_items('fee');
            $this->log_debug("Found " . count($fee_items) . " fee item(s) via WooCommerce API");

            // 如果API没有返回费用项目，尝试直接数据库查询
            if (empty($fee_items)) {
                $db_fee_items = $wpdb->get_results($wpdb->prepare("
                    SELECT order_item_id, order_item_name
                    FROM {$wpdb->prefix}woocommerce_order_items
                    WHERE order_id = %d AND order_item_type = 'fee'
                ", $order_id));

                $this->log_debug("Direct database query found " . count($db_fee_items) . " fee item(s)");

                if (!empty($db_fee_items)) {
                    $this->log_debug("WARNING: Database has fee items but WooCommerce API returned empty.");
                }
            }

            foreach ($fee_items as $item_id => $item) {
                $name = $item->get_name();
                $total = $item->get_total();
                $tax_total = $item->get_total_tax();
                $meta_data = $item->get_meta_data();

                // 获取费用特有的属性
                $tax_class = $item->get_tax_class();
                $tax_status = $item->get_tax_status();

                $all_items[] = [
                    'type' => 'fee',
                    'product_id' => 0, // 费用项目没有商品ID
                    'variation_id' => 0,
                    'quantity' => 1,
                    'name' => $name,
                    'total' => $total,
                    'subtotal' => $total,
                    'tax_total' => $tax_total,
                    'tax_class' => $tax_class,
                    'tax_status' => $tax_status,
                    'tax_data' => $item->get_taxes(),
                    'meta_data' => $meta_data
                ];

                $this->log_debug("Extracted fee #{$item_id}: '{$name}' (total: {$total}, tax: {$tax_total}, tax_class: {$tax_class}, tax_status: {$tax_status}, meta_count: " . count($meta_data) . ")");
            }

            // 获取运费项目 - 增强版本
            $shipping_items = $order->get_items('shipping');
            $this->log_debug("Found " . count($shipping_items) . " shipping item(s) via WooCommerce API");

            // 如果API没有返回运费项目，尝试直接数据库查询
            if (empty($shipping_items)) {
                $db_shipping_items = $wpdb->get_results($wpdb->prepare("
                    SELECT order_item_id, order_item_name
                    FROM {$wpdb->prefix}woocommerce_order_items
                    WHERE order_id = %d AND order_item_type = 'shipping'
                ", $order_id));

                $this->log_debug("Direct database query found " . count($db_shipping_items) . " shipping item(s)");

                if (!empty($db_shipping_items)) {
                    $this->log_debug("WARNING: Database has shipping items but WooCommerce API returned empty.");
                }
            }

            foreach ($shipping_items as $item_id => $item) {
                $name = $item->get_name();
                $total = $item->get_total();
                $tax_total = $item->get_total_tax();
                $meta_data = $item->get_meta_data();

                // 获取运费特有的属性
                $method_id = $item->get_method_id();
                $instance_id = $item->get_instance_id();
                $taxes = $item->get_taxes();

                $all_items[] = [
                    'type' => 'shipping',
                    'product_id' => 0,
                    'variation_id' => 0,
                    'quantity' => 1,
                    'name' => $name,
                    'total' => $total,
                    'subtotal' => $total,
                    'tax_total' => $tax_total,
                    'method_id' => $method_id,
                    'instance_id' => $instance_id,
                    'taxes' => $taxes,
                    'meta_data' => $meta_data
                ];

                $this->log_debug("Extracted shipping #{$item_id}: '{$name}' (method: {$method_id}, total: {$total}, tax: {$tax_total}, meta_count: " . count($meta_data) . ")");
            }

            // 获取优惠券项目 - 这是之前缺失的重要部分！
            $coupon_items = $order->get_items('coupon');
            $this->log_debug("Found " . count($coupon_items) . " coupon item(s) via WooCommerce API");

            // 如果API没有返回优惠券项目，尝试直接数据库查询
            if (empty($coupon_items)) {
                $db_coupon_items = $wpdb->get_results($wpdb->prepare("
                    SELECT order_item_id, order_item_name
                    FROM {$wpdb->prefix}woocommerce_order_items
                    WHERE order_id = %d AND order_item_type = 'coupon'
                ", $order_id));

                $this->log_debug("Direct database query found " . count($db_coupon_items) . " coupon item(s)");

                if (!empty($db_coupon_items)) {
                    $this->log_debug("WARNING: Database has coupon items but WooCommerce API returned empty.");
                }
            }

            foreach ($coupon_items as $item_id => $item) {
                $code = $item->get_code();
                $discount_amount = $item->get_discount();
                $discount_tax = $item->get_discount_tax();
                $meta_data = $item->get_meta_data();

                $all_items[] = [
                    'type' => 'coupon',
                    'product_id' => 0, // 优惠券项目没有商品ID
                    'variation_id' => 0,
                    'quantity' => 1,
                    'name' => $code,
                    'code' => $code,
                    'total' => -$discount_amount, // 优惠券折扣是负数
                    'subtotal' => -$discount_amount,
                    'tax_total' => -$discount_tax,
                    'discount_amount' => $discount_amount,
                    'discount_tax' => $discount_tax,
                    'meta_data' => $meta_data
                ];

                $this->log_debug("Extracted coupon #{$item_id}: '{$code}' (discount: {$discount_amount}, tax: {$discount_tax}, meta_count: " . count($meta_data) . ")");
            }

            $order_data['items'] = $all_items;

            $total_items = count($all_items);
            $line_count = count($line_items);
            $fee_count = count($fee_items);
            $shipping_count = count($shipping_items);
            $coupon_count = count($coupon_items);

            $this->log_debug("ITEMS EXTRACTION COMPLETE from SOURCE database: Total {$total_items} items extracted ({$line_count} line_items + {$fee_count} fees + {$shipping_count} shipping + {$coupon_count} coupons)");

            // 提取元数据（从源数据库）
            $this->log_debug("Extracting order meta data from SOURCE database...");
            foreach ($order->get_meta_data() as $meta) {
                $order_data['meta_data'][] = [
                    'key' => $meta->key,
                    'value' => $meta->value
                ];
            }

            $this->log_debug("Extracted " . count($order_data['meta_data']) . " meta data items from SOURCE database");

            // 提取订单备注（从源数据库）
            $this->log_debug("Extracting order notes from SOURCE database...");
            $notes = wc_get_order_notes(['order_id' => $order_id]);
            foreach ($notes as $note) {
                $note_data = [
                    'content' => $note->content ?: '',
                    'customer_note' => $note->customer_note ? 1 : 0,
                    'added_by' => $note->added_by ?: 'system',
                    'date_created' => $note->date_created
                ];

                $order_data['notes'][] = $note_data;
                $this->log_debug("Extracted note: '{$note_data['content']}' (customer_note: {$note_data['customer_note']}, added_by: {$note_data['added_by']})");
            }

            $this->log_debug("Extracted " . count($order_data['notes']) . " order notes from SOURCE database");
            $this->log_debug("=== SOURCE DATABASE EXTRACTION COMPLETED ===");

            return $order_data;

        } catch (Exception $e) {
            $this->log_error("Error extracting order data: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 商品智能替换 - 增强版本，支持变体商品，避免重复替换
     */
    private function apply_smart_product_replacement($order_data, $target_connection) {
        if (!$this->settings['enable_smart_product_replacement']) {
            return $order_data;
        }

        $replacement_mode = $this->settings['product_replacement_mode'];
        $this->log_debug("Applying smart product replacement with mode: {$replacement_mode} (with duplicate avoidance)");

        $successful_replacements = 0;
        $failed_replacements = 0;
        $kept_originals = 0;
        
        // 追踪已使用的替换商品，避免重复使用
        $used_replacements = [];

        foreach ($order_data['items'] as &$item) {
            // 只对商品项目进行替换，跳过费用、运费等
            if (($item['type'] ?? 'line_item') !== 'line_item' || $item['product_id'] <= 0) {
                $this->log_debug("Skipping non-product item: {$item['name']} (type: " . ($item['type'] ?? 'line_item') . ")");
                continue;
            }

            $original_product_id = $item['product_id'];
            $original_variation_id = $item['variation_id'] ?? 0;
            $original_price = $item['quantity'] > 0 ? ($item['total'] / $item['quantity']) : 0; // 单价

            $this->log_debug("Processing item replacement: Product ID {$original_product_id}, Variation ID {$original_variation_id}, Price {$original_price}");

            // 先尝试智能替换（避免重复）
            $replacement_product = null;
            if ($replacement_mode === 'same_price') {
                // Same Price Random Products 模式：
                // 用其他数据库同样价格的主商品或者变体商品进行随机替换
                $replacement_product = $this->find_product_by_price_enhanced_no_duplicate($original_price, $target_connection, $used_replacements);
            } elseif ($replacement_mode === 'same_id') {
                // Same ID Products 模式：
                // 1. 优先用其他数据库同一个商品ID
                // 2. 如果同一个商品ID有变体，随机选择一个变体
                // 3. 如果没有变体就用主商品
                // 4. 如果其他数据库没有同一个商品ID，回退到Same Price Random Products模式
                $replacement_product = $this->find_product_by_id_enhanced_no_duplicate($original_product_id, $original_variation_id, $target_connection, $used_replacements);
                
                // 如果按ID找不到，回退到按价格查找
                if (!$replacement_product) {
                    $this->log_debug("Same ID not found, falling back to Same Price Random Products mode for product {$original_product_id}");
                    $replacement_product = $this->find_product_by_price_enhanced_no_duplicate($original_price, $target_connection, $used_replacements);
                }
            } else {
                // 默认使用Same Price模式
                $replacement_product = $this->find_product_by_price_enhanced_no_duplicate($original_price, $target_connection, $used_replacements);
            }

            if ($replacement_product) {
                // 成功找到替换商品
                $original_quantity = $item['quantity'];
                $original_total = $item['total'];
                $original_subtotal = $item['subtotal'];
                $original_tax_total = $item['tax_total'];
                $original_meta_data = $item['meta_data'] ?? [];
                $original_unit_price = $original_quantity > 0 ? ($original_total / $original_quantity) : 0;

                // 替换商品ID、变体ID和名称
                $item['product_id'] = $replacement_product['product_id'];
                $item['variation_id'] = $replacement_product['variation_id'];
                $item['name'] = $replacement_product['name'];

                // 严格保持源数据库的价格、数量和总价格不变
                // 无论替换商品在目标数据库中的实际价格是多少，都使用源数据库的价格
                $item['quantity'] = $original_quantity;
                $item['total'] = $original_total;
                $item['subtotal'] = $original_subtotal;
                $item['tax_total'] = $original_tax_total;
                
                $this->log_debug("✅ PRICE PRESERVATION: Keeping original pricing - Mode: {$replacement_mode}");
                $this->log_debug("   Original unit price: {$original_unit_price}, Total: {$original_total}, Quantity: {$original_quantity}");
                $this->log_debug("   Target product price in DB: {$replacement_product['price']} (IGNORED - using original price)");

                // 处理变体属性元数据
                if ($replacement_product['variation_id'] > 0 && isset($replacement_product['variation_attributes'])) {
                    $item['meta_data'] = $this->merge_variation_attributes($original_meta_data, $replacement_product['variation_attributes']);
                } else {
                    $item['meta_data'] = $original_meta_data;
                }

                // 记录已使用的替换商品（防止后续重复使用）
                $replacement_key = $replacement_product['product_id'] . '_' . $replacement_product['variation_id'];
                $used_replacements[$replacement_key] = [
                    'product_id' => $replacement_product['product_id'],
                    'variation_id' => $replacement_product['variation_id'],
                    'name' => $replacement_product['name'],
                    'original_price' => $original_unit_price,
                    'target_price' => $replacement_product['price']
                ];

                $log_message = "✅ REPLACED: Product {$original_product_id}";
                if ($original_variation_id > 0) {
                    $log_message .= " (variation {$original_variation_id})";
                }
                $log_message .= " with {$replacement_product['product_id']}";
                if ($replacement_product['variation_id'] > 0) {
                    $log_message .= " (variation {$replacement_product['variation_id']})";
                }
                $log_message .= " ('{$replacement_product['name']}'), qty={$item['quantity']}, total={$item['total']}";
                
                $this->log_debug($log_message);
                $successful_replacements++;
            } else {
                // 没有找到替换商品，检查原商品是否在目标数据库中存在
                $original_exists = $this->verify_product_exists_in_target($original_product_id, $original_variation_id, $target_connection);
                
                if ($original_exists) {
                    // 原商品存在，保持原样
                    $this->log_debug("✅ KEPT ORIGINAL: Product {$original_product_id} exists in target database, keeping original product");
                    $kept_originals++;
                } else {
                    // 原商品不存在，尝试找任意商品作为回退（避免重复）
                    $this->log_debug("⚠️  ORIGINAL NOT FOUND: Product {$original_product_id} does not exist in target database, trying fallback replacement (avoiding duplicates)");
                    
                    $fallback_product = $this->find_fallback_product_no_duplicate($original_price, $target_connection, $used_replacements);
                    
                    if ($fallback_product) {
                        // 使用回退商品
                        $original_quantity = $item['quantity'];
                        $original_total = $item['total'];
                        $original_subtotal = $item['subtotal'];
                        $original_tax_total = $item['tax_total'];
                        $original_meta_data = $item['meta_data'] ?? [];
                        $original_unit_price = $original_quantity > 0 ? ($original_total / $original_quantity) : 0;

                        $item['product_id'] = $fallback_product['product_id'];
                        $item['variation_id'] = $fallback_product['variation_id'];
                        $item['name'] = $fallback_product['name'];

                        // 严格保持源数据库的原价格、数量和总价格不变
                        $item['quantity'] = $original_quantity;
                        $item['total'] = $original_total;
                        $item['subtotal'] = $original_subtotal;
                        $item['tax_total'] = $original_tax_total;
                        
                        $this->log_debug("✅ FALLBACK PRICE PRESERVATION: Using original pricing");
                        $this->log_debug("   Original unit price: {$original_unit_price}, Total: {$original_total}, Quantity: {$original_quantity}");
                        $this->log_debug("   Fallback product price in DB: {$fallback_product['price']} (IGNORED - using original price)");

                        // 处理变体属性
                        if ($fallback_product['variation_id'] > 0 && isset($fallback_product['variation_attributes'])) {
                            $item['meta_data'] = $this->merge_variation_attributes($original_meta_data, $fallback_product['variation_attributes']);
                        } else {
                            $item['meta_data'] = $original_meta_data;
                        }

                        // 记录已使用的回退商品
                        $fallback_key = $fallback_product['product_id'] . '_' . $fallback_product['variation_id'];
                        $used_replacements[$fallback_key] = [
                            'product_id' => $fallback_product['product_id'],
                            'variation_id' => $fallback_product['variation_id'],
                            'name' => $fallback_product['name'],
                            'original_price' => $original_unit_price,
                            'target_price' => $fallback_product['price']
                        ];

                        $this->log_debug("🔄 FALLBACK REPLACED: Product {$original_product_id} with {$fallback_product['product_id']} ('{$fallback_product['name']}'), qty={$item['quantity']}, total={$item['total']}");
                        $successful_replacements++;
                    } else {
                        // 完全无法替换，标记为失败但保持项目
                        $this->log_debug("❌ REPLACEMENT FAILED: No replacement or fallback found for product {$original_product_id}, keeping original (may cause display issues)");
                        $failed_replacements++;
                        
                        // 添加一个标记，表示这个商品可能有问题
                        $item['_replacement_failed'] = true;
                    }
                }
            }
        }

        // 记录替换统计信息和防重复使用的情况
        $total_items = $successful_replacements + $failed_replacements + $kept_originals;
        $this->log_debug("=== PRODUCT REPLACEMENT SUMMARY (WITH DUPLICATE AVOIDANCE) ===");
        $this->log_debug("Total product items processed: {$total_items}");
        $this->log_debug("Successful replacements: {$successful_replacements}");
        $this->log_debug("Kept original products: {$kept_originals}");
        $this->log_debug("Failed replacements: {$failed_replacements}");
        $this->log_debug("Used replacements (avoiding duplicates): " . count($used_replacements));
        
        // 详细记录所有已使用的替换商品和价格保持情况
        if (!empty($used_replacements)) {
            $this->log_debug("🔒 USED REPLACEMENT PRODUCTS (price preservation details):");
            foreach ($used_replacements as $key => $used) {
                $variation_info = $used['variation_id'] > 0 ? " (variation {$used['variation_id']})" : " (main product)";
                $price_info = "";
                if (isset($used['original_price']) && isset($used['target_price'])) {
                    $price_info = " - Original price: {$used['original_price']}, Target DB price: {$used['target_price']} (USING ORIGINAL)";
                }
                $this->log_debug("   - Product {$used['product_id']}{$variation_info}: {$used['name']}{$price_info}");
            }
        }
        
        if ($failed_replacements > 0) {
            $this->log_debug("⚠️  WARNING: {$failed_replacements} products could not be replaced and may not display correctly in the target database");
        }

        return $order_data;
    }

    /**
     * 验证商品是否在目标数据库中存在
     */
    private function verify_product_exists_in_target($product_id, $variation_id, $connection) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        if ($variation_id > 0) {
            // 检查变体商品是否存在
            $sql = "SELECT COUNT(*) as count 
                    FROM {$table_prefix}posts p
                    INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                    WHERE p.ID = ? 
                    AND v.ID = ?
                    AND p.post_type = 'product'
                    AND p.post_status = 'publish'
                    AND v.post_type = 'product_variation'
                    AND v.post_status = 'publish'";

            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $stmt->bind_param('ii', $product_id, $variation_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                return $row['count'] > 0;
            }
        } else {
            // 检查主商品是否存在
            $sql = "SELECT COUNT(*) as count 
                    FROM {$table_prefix}posts 
                    WHERE ID = ? 
                    AND post_type = 'product'
                    AND post_status = 'publish'";

            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $stmt->bind_param('i', $product_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                return $row['count'] > 0;
            }
        }

        return false;
    }

    /**
     * 查找回退商品 - 在无法找到合适替换时使用
     */
    private function find_fallback_product($original_price, $connection) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        $this->log_debug("Searching for fallback product (any available product)");

        // 首先尝试找价格相近的商品（范围放宽到50%）
        $price_min = $original_price * 0.5;
        $price_max = $original_price * 1.5;

        $sql = "SELECT 
                    p.ID as product_id,
                    v.ID as variation_id,
                    v.post_title as name,
                    pm.meta_value as price,
                    'variation' as type
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                INNER JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND v.post_type = 'product_variation'
                AND v.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) BETWEEN ? AND ?
                
                UNION ALL
                
                SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) BETWEEN ? AND ?
                
                ORDER BY RAND()
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if ($stmt) {
            $stmt->bind_param('dddd', $price_min, $price_max, $price_min, $price_max);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $product_data = [
                    'product_id' => intval($row['product_id']),
                    'variation_id' => intval($row['variation_id']),
                    'name' => $row['name'],
                    'price' => floatval($row['price']),
                    'type' => $row['type']
                ];

                if ($product_data['variation_id'] > 0) {
                    $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
                }

                $this->log_debug("Found fallback product with relaxed price range: {$row['type']} - ID {$product_data['product_id']}, Price {$product_data['price']}");
                return $product_data;
            }
        }

        // 如果还是找不到，找任意一个可用的商品
        $sql = "SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type
                FROM {$table_prefix}posts p
                LEFT JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_price'
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                ORDER BY RAND()
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if ($stmt) {
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $product_data = [
                    'product_id' => intval($row['product_id']),
                    'variation_id' => 0,
                    'name' => $row['name'],
                    'price' => floatval($row['price'] ?? 0),
                    'type' => $row['type']
                ];

                $this->log_debug("Found any available fallback product: {$product_data['product_id']} - {$product_data['name']}");
                return $product_data;
            }
        }

        $this->log_debug("No fallback product found in target database");
        return null;
    }

    /**
     * 根据价格查找商品 - 增强版本，支持变体商品，真正随机选择
     */
    private function find_product_by_price_enhanced($price, $connection) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        // 查找价格相同的商品（允许小幅差异）
        $price_min = $price * 0.95; // 允许5%的价格差异
        $price_max = $price * 1.05;

        $this->log_debug("Searching for products with price between {$price_min} and {$price_max} (random selection)");

        // 使用更强的随机性：先获取所有符合条件的商品，然后随机选择
        // 这确保了真正的随机分布，不受数据库索引顺序影响
        $sql = "SELECT 
                    p.ID as product_id,
                    v.ID as variation_id,
                    v.post_title as name,
                    pm.meta_value as price,
                    'variation' as type,
                    RAND() as random_order
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                INNER JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND v.post_type = 'product_variation'
                AND v.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) BETWEEN ? AND ?
                
                UNION ALL
                
                SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type,
                    RAND() as random_order
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) BETWEEN ? AND ?
                AND p.ID NOT IN (
                    SELECT DISTINCT post_parent 
                    FROM {$table_prefix}posts 
                    WHERE post_type = 'product_variation' 
                    AND post_parent IS NOT NULL
                )
                
                ORDER BY random_order
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare enhanced price search statement: " . $connection->error);
            return null;
        }

        $stmt->bind_param('dddd', $price_min, $price_max, $price_min, $price_max);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $product_data = [
                'product_id' => intval($row['product_id']),
                'variation_id' => intval($row['variation_id']),
                'name' => $row['name'],
                'price' => floatval($row['price']),
                'type' => $row['type']
            ];

            // 如果是变体商品，获取变体属性
            if ($product_data['variation_id'] > 0) {
                $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
            }

            $this->log_debug("Found {$row['type']} product (random selection): ID {$product_data['product_id']}, Variation {$product_data['variation_id']}, Price {$product_data['price']}");
            return $product_data;
        }

        $this->log_debug("No product found with price between {$price_min} and {$price_max}");
        return null;
    }

    /**
     * 根据价格查找商品 - 兼容旧版本
     */
    private function find_product_by_price($price, $connection) {
        $enhanced_result = $this->find_product_by_price_enhanced($price, $connection);
        
        if ($enhanced_result) {
            return [
                'id' => $enhanced_result['variation_id'] > 0 ? $enhanced_result['variation_id'] : $enhanced_result['product_id'],
                'name' => $enhanced_result['name'],
                'price' => $enhanced_result['price']
            ];
        }
        
        return null;
    }

    /**
     * 根据价格查找商品 - 防重复版本，支持变体商品，真正随机选择
     * 增加三级价格回退机制：
     * 1. 查找相同价格的商品（允许5%差异）
     * 2. 如果没有相同价格，查找价格大于原价格且差价最少的商品
     * 3. 如果还没有，随机替换任意商品
     */
    private function find_product_by_price_enhanced_no_duplicate($price, $connection, $used_replacements = []) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        // 第一步：查找价格相同的商品（允许5%差异）
        $price_min = $price * 0.95;
        $price_max = $price * 1.05;

        $this->log_debug("Step 1: Searching for products with same price between {$price_min} and {$price_max} (avoiding duplicates, random selection)");

        $result = $this->search_products_by_price_range($price_min, $price_max, $connection, $used_replacements, $table_prefix);
        if ($result) {
            $this->log_debug("Found same price product: {$result['type']} - ID {$result['product_id']}, Variation {$result['variation_id']}, Price {$result['price']}");
            return $result;
        }

        // 第二步：如果没有相同价格，查找价格大于原价格且差价最少的商品
        $this->log_debug("Step 2: No same price products found, searching for products with price > {$price} and minimum price difference");

        $result = $this->search_products_with_minimum_price_difference($price, $connection, $used_replacements, $table_prefix);
        if ($result) {
            $price_diff = $result['price'] - $price;
            $this->log_debug("Found minimum price difference product: {$result['type']} - ID {$result['product_id']}, Variation {$result['variation_id']}, Price {$result['price']} (difference: +{$price_diff})");
            return $result;
        }

        // 第三步：如果还没有，随机替换任意商品
        $this->log_debug("Step 3: No higher price products found, searching for any available product");

        $result = $this->search_products_by_price_range(0, PHP_FLOAT_MAX, $connection, $used_replacements, $table_prefix);
        if ($result) {
            $this->log_debug("Found random fallback product: {$result['type']} - ID {$result['product_id']}, Variation {$result['variation_id']}, Price {$result['price']}");
            return $result;
        }

        $this->log_debug("No non-duplicate product found in any price range");
        return null;
    }

    /**
     * 查找价格大于指定价格且差价最少的商品
     */
    private function search_products_with_minimum_price_difference($min_price, $connection, $used_replacements, $table_prefix) {
        // 构建变体查询的排除条件
        $variation_exclude_conditions = [];
        $variation_exclude_params = [];
        
        // 构建简单商品查询的排除条件
        $simple_exclude_conditions = [];
        $simple_exclude_params = [];
        
        if (!empty($used_replacements)) {
            foreach ($used_replacements as $used) {
                if ($used['variation_id'] > 0) {
                    // 变体查询：排除已使用的变体
                    $variation_exclude_conditions[] = "(p.ID != ? OR v.ID != ?)";
                    $variation_exclude_params[] = $used['product_id'];
                    $variation_exclude_params[] = $used['variation_id'];
                } else {
                    // 变体查询：排除已使用的主商品
                    $variation_exclude_conditions[] = "p.ID != ?";
                    $variation_exclude_params[] = $used['product_id'];
                    
                    // 简单商品查询：排除已使用的主商品
                    $simple_exclude_conditions[] = "p.ID != ?";
                    $simple_exclude_params[] = $used['product_id'];
                }
            }
        }

        $variation_exclude_sql = !empty($variation_exclude_conditions) ? "AND (" . implode(" AND ", $variation_exclude_conditions) . ")" : "";
        $simple_exclude_sql = !empty($simple_exclude_conditions) ? "AND (" . implode(" AND ", $simple_exclude_conditions) . ")" : "";

        // 查找价格大于指定价格的商品，按价格升序排列（差价最少的在前）
        $sql = "SELECT 
                    p.ID as product_id,
                    v.ID as variation_id,
                    v.post_title as name,
                    pm.meta_value as price,
                    'variation' as type,
                    (CAST(pm.meta_value AS DECIMAL(10,2)) - ?) as price_difference
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                INNER JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND v.post_type = 'product_variation'
                AND v.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) > ?
                {$variation_exclude_sql}
                
                UNION ALL
                
                SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type,
                    (CAST(pm.meta_value AS DECIMAL(10,2)) - ?) as price_difference
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND pm.meta_key = '_price'
                AND CAST(pm.meta_value AS DECIMAL(10,2)) > ?
                AND p.ID NOT IN (
                    SELECT DISTINCT post_parent 
                    FROM {$table_prefix}posts 
                    WHERE post_type = 'product_variation' 
                    AND post_parent IS NOT NULL
                )
                {$simple_exclude_sql}
                
                ORDER BY price_difference ASC, RAND()
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare minimum price difference search statement: " . $connection->error);
            return null;
        }

        // 绑定参数：价格参数（用于计算差价和筛选） + 排除条件参数
        $all_params = array_merge(
            [$min_price, $min_price],           // 变体查询：计算差价和筛选价格
            $variation_exclude_params,          // 变体查询的排除参数
            [$min_price, $min_price],           // 简单商品查询：计算差价和筛选价格
            $simple_exclude_params              // 简单商品查询的排除参数
        );
        
        // 修复：正确计算参数类型字符串
        // 前面4个价格参数使用 'd'（double），后面的排除参数使用 'i'（integer）
        $param_types = 'dddd' . str_repeat('i', count($variation_exclude_params)) . str_repeat('i', count($simple_exclude_params));
        
        $stmt->bind_param($param_types, ...$all_params);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $product_data = [
                'product_id' => intval($row['product_id']),
                'variation_id' => intval($row['variation_id']),
                'name' => $row['name'],
                'price' => floatval($row['price']),
                'type' => $row['type'],
                'price_difference' => floatval($row['price_difference'])
            ];

            // 如果是变体商品，获取变体属性
            if ($product_data['variation_id'] > 0) {
                $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
            }

            return $product_data;
        }

        return null;
    }

    /**
     * 在指定价格范围内搜索商品，避免重复
     */
    private function search_products_by_price_range($price_min, $price_max, $connection, $used_replacements, $table_prefix) {
        // 构建变体查询的排除条件
        $variation_exclude_conditions = [];
        $variation_exclude_params = [];
        
        // 构建简单商品查询的排除条件
        $simple_exclude_conditions = [];
        $simple_exclude_params = [];
        
        if (!empty($used_replacements)) {
            foreach ($used_replacements as $used) {
                if ($used['variation_id'] > 0) {
                    // 变体查询：排除已使用的变体
                    $variation_exclude_conditions[] = "(p.ID != ? OR v.ID != ?)";
                    $variation_exclude_params[] = $used['product_id'];
                    $variation_exclude_params[] = $used['variation_id'];
                } else {
                    // 变体查询：排除已使用的主商品
                    $variation_exclude_conditions[] = "p.ID != ?";
                    $variation_exclude_params[] = $used['product_id'];
                    
                    // 简单商品查询：排除已使用的主商品
                    $simple_exclude_conditions[] = "p.ID != ?";
                    $simple_exclude_params[] = $used['product_id'];
                }
            }
        }

        $variation_exclude_sql = !empty($variation_exclude_conditions) ? "AND (" . implode(" AND ", $variation_exclude_conditions) . ")" : "";
        $simple_exclude_sql = !empty($simple_exclude_conditions) ? "AND (" . implode(" AND ", $simple_exclude_conditions) . ")" : "";

        // 构建价格条件
        $price_condition = "";
        if ($price_max == PHP_FLOAT_MAX) {
            $price_condition = "AND CAST(pm.meta_value AS DECIMAL(10,2)) >= ?";
        } else {
            $price_condition = "AND CAST(pm.meta_value AS DECIMAL(10,2)) BETWEEN ? AND ?";
        }

        // 使用更强的随机性：先获取所有符合条件的商品，然后随机选择
        $sql = "SELECT 
                    p.ID as product_id,
                    v.ID as variation_id,
                    v.post_title as name,
                    pm.meta_value as price,
                    'variation' as type,
                    RAND() as random_order
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                INNER JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND v.post_type = 'product_variation'
                AND v.post_status = 'publish'
                AND pm.meta_key = '_price'
                {$price_condition}
                {$variation_exclude_sql}
                
                UNION ALL
                
                SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type,
                    RAND() as random_order
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND pm.meta_key = '_price'
                {$price_condition}
                AND p.ID NOT IN (
                    SELECT DISTINCT post_parent 
                    FROM {$table_prefix}posts 
                    WHERE post_type = 'product_variation' 
                    AND post_parent IS NOT NULL
                )
                {$simple_exclude_sql}
                
                ORDER BY random_order
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare price range search statement: " . $connection->error);
            return null;
        }

        // 绑定参数：价格范围 + 各自的排除条件参数
        $price_params = [];
        if ($price_max == PHP_FLOAT_MAX) {
            $price_params = [$price_min, $price_min]; // 两个查询都使用相同的最小价格
        } else {
            $price_params = [$price_min, $price_max, $price_min, $price_max]; // 两个查询都使用价格范围
        }
        
        $all_params = array_merge(
            array_slice($price_params, 0, ($price_max == PHP_FLOAT_MAX) ? 1 : 2), // 变体查询的价格参数
            $variation_exclude_params,          // 变体查询的排除参数
            array_slice($price_params, ($price_max == PHP_FLOAT_MAX) ? 1 : 2),    // 简单商品查询的价格参数
            $simple_exclude_params              // 简单商品查询的排除参数
        );
        
        $price_param_count = ($price_max == PHP_FLOAT_MAX) ? 1 : 2;
        $param_types = str_repeat('d', $price_param_count) . str_repeat('i', count($variation_exclude_params)) . str_repeat('d', $price_param_count) . str_repeat('i', count($simple_exclude_params));
        
        $stmt->bind_param($param_types, ...$all_params);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $product_data = [
                'product_id' => intval($row['product_id']),
                'variation_id' => intval($row['variation_id']),
                'name' => $row['name'],
                'price' => floatval($row['price']),
                'type' => $row['type']
            ];

            // 如果是变体商品，获取变体属性
            if ($product_data['variation_id'] > 0) {
                $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
            }

            return $product_data;
        }

        return null;
    }

    /**
     * 根据ID查找商品 - 增强版本，支持变体商品智能匹配
     */
    private function find_product_by_id_enhanced($product_id, $variation_id, $connection) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        $this->log_debug("Searching for product: ID {$product_id}, Variation {$variation_id}");

        // 如果原商品有变体ID，优先查找相同的变体
        if ($variation_id > 0) {
            $this->log_debug("Searching for specific variation: {$variation_id}");
            
            $sql = "SELECT 
                        p.ID as product_id,
                        v.ID as variation_id,
                        v.post_title as name,
                        pm.meta_value as price,
                        'variation' as type
                    FROM {$table_prefix}posts p
                    INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                    LEFT JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id AND pm.meta_key = '_price'
                    WHERE v.ID = ?
                    AND p.post_type = 'product'
                    AND p.post_status = 'publish'
                    AND v.post_type = 'product_variation'
                    AND v.post_status = 'publish'";

            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $stmt->bind_param('i', $variation_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($row = $result->fetch_assoc()) {
                    $product_data = [
                        'product_id' => intval($row['product_id']),
                        'variation_id' => intval($row['variation_id']),
                        'name' => $row['name'],
                        'price' => floatval($row['price'] ?? 0),
                        'type' => $row['type']
                    ];

                    $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
                    
                    $this->log_debug("Found exact variation match: {$variation_id}");
                    return $product_data;
                }
            }

            // 如果找不到确切的变体，查找同一主商品的其他变体
            $this->log_debug("Exact variation not found, searching for other variations of product {$product_id}");
            
            $sql = "SELECT 
                        p.ID as product_id,
                        v.ID as variation_id,
                        v.post_title as name,
                        pm.meta_value as price,
                        'variation' as type
                    FROM {$table_prefix}posts p
                    INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                    LEFT JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id AND pm.meta_key = '_price'
                    WHERE p.ID = ?
                    AND p.post_type = 'product'
                    AND p.post_status = 'publish'
                    AND v.post_type = 'product_variation'
                    AND v.post_status = 'publish'
                    ORDER BY v.ID ASC
                    LIMIT 1";

            $stmt = $connection->prepare($sql);
            if ($stmt) {
                $stmt->bind_param('i', $product_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($row = $result->fetch_assoc()) {
                    $product_data = [
                        'product_id' => intval($row['product_id']),
                        'variation_id' => intval($row['variation_id']),
                        'name' => $row['name'],
                        'price' => floatval($row['price'] ?? 0),
                        'type' => $row['type']
                    ];

                    $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
                    
                    $this->log_debug("Found alternative variation for product {$product_id}: variation {$product_data['variation_id']}");
                    return $product_data;
                }
            }
        }

        // 查找主商品（无论原商品是否有变体）
        $this->log_debug("Searching for main product: {$product_id}");
        
        $sql = "SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type
                FROM {$table_prefix}posts p
                LEFT JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_price'
                WHERE p.ID = ?
                AND p.post_type = 'product'
                AND p.post_status = 'publish'";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare enhanced ID search statement: " . $connection->error);
            return null;
        }

        $stmt->bind_param('i', $product_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $product_data = [
                'product_id' => intval($row['product_id']),
                'variation_id' => 0,
                'name' => $row['name'],
                'price' => floatval($row['price'] ?? 0),
                'type' => $row['type']
            ];

            // 检查这个商品是否有变体，如果有变体但原订单没有指定变体，选择第一个变体
            if ($variation_id == 0) {
                $variation_sql = "SELECT 
                                    v.ID as variation_id,
                                    v.post_title as variation_name,
                                    pm.meta_value as variation_price
                                FROM {$table_prefix}posts v
                                LEFT JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id AND pm.meta_key = '_price'
                                WHERE v.post_parent = ?
                                AND v.post_type = 'product_variation'
                                AND v.post_status = 'publish'
                                ORDER BY v.ID ASC
                                LIMIT 1";

                $variation_stmt = $connection->prepare($variation_sql);
                if ($variation_stmt) {
                    $variation_stmt->bind_param('i', $product_id);
                    $variation_stmt->execute();
                    $variation_result = $variation_stmt->get_result();

                    if ($variation_row = $variation_result->fetch_assoc()) {
                        $product_data['variation_id'] = intval($variation_row['variation_id']);
                        $product_data['name'] = $variation_row['variation_name'];
                        $product_data['price'] = floatval($variation_row['variation_price'] ?? $product_data['price']);
                        $product_data['type'] = 'variation';
                        $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
                        
                        $this->log_debug("Product {$product_id} has variations, selected first variation: {$product_data['variation_id']}");
                    }
                }
            }

            $this->log_debug("Found product match: {$product_data['type']} - Product {$product_data['product_id']}, Variation {$product_data['variation_id']}");
            return $product_data;
        }

        $this->log_debug("No product found with ID: {$product_id}");
        return null;
    }

    /**
     * 根据ID查找商品 - 兼容旧版本
     */
    private function find_product_by_id($product_id, $connection) {
        $enhanced_result = $this->find_product_by_id_enhanced($product_id, 0, $connection);
        
        if ($enhanced_result) {
            return [
                'id' => $enhanced_result['variation_id'] > 0 ? $enhanced_result['variation_id'] : $enhanced_result['product_id'],
                'name' => $enhanced_result['name']
            ];
        }
        
        return null;
    }

    /**
     * 根据ID查找商品 - 防重复版本，支持变体商品智能匹配
     */
    /**
     * 根据ID查找商品 - 无重复版本
     * Same ID Products模式逻辑：
     * 1. 如果其他数据库有同一个商品ID的变体，随机选择一个变体
     * 2. 如果没有变体就用同一个商品ID的主商品
     * 3. 如果其他数据库没有同一个商品ID，返回null（外层会回退到Same Price模式）
     */
    private function find_product_by_id_enhanced_no_duplicate($product_id, $variation_id, $connection, $used_replacements = []) {
        $table_prefix = $this->get_table_prefix_from_connection($connection);

        $this->log_debug("Same ID Products mode: Searching for product ID {$product_id} (original variation: {$variation_id})");

        // 第一步：检查目标数据库是否存在这个商品ID
        $sql = "SELECT COUNT(*) as count 
                FROM {$table_prefix}posts 
                WHERE ID = ? 
                AND post_type = 'product' 
                AND post_status = 'publish'";
        
        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare product existence check: " . $connection->error);
            return null;
        }
        
        $stmt->bind_param('i', $product_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        if ($row['count'] == 0) {
            $this->log_debug("Product ID {$product_id} does not exist in target database, will fallback to Same Price mode");
            return null;
        }

        // 第二步：查找该商品的所有可用变体（排除已使用的）
        $exclude_variations = [];
        foreach ($used_replacements as $used) {
            if ($used['product_id'] == $product_id) {
                $exclude_variations[] = $used['variation_id'];
            }
        }

        $exclude_sql = "";
        $exclude_params = [];
        if (!empty($exclude_variations)) {
            $placeholders = implode(',', array_fill(0, count($exclude_variations), '?'));
            $exclude_sql = "AND v.ID NOT IN ({$placeholders})";
            $exclude_params = $exclude_variations;
        }

        // 查找该商品的所有变体
        $sql = "SELECT 
                    p.ID as product_id,
                    v.ID as variation_id,
                    v.post_title as name,
                    pm.meta_value as price,
                    'variation' as type
                FROM {$table_prefix}posts p
                INNER JOIN {$table_prefix}posts v ON p.ID = v.post_parent
                LEFT JOIN {$table_prefix}postmeta pm ON v.ID = pm.post_id AND pm.meta_key = '_price'
                WHERE p.ID = ?
                AND p.post_type = 'product'
                AND p.post_status = 'publish'
                AND v.post_type = 'product_variation'
                AND v.post_status = 'publish'
                {$exclude_sql}
                ORDER BY RAND()
                LIMIT 1";

        $stmt = $connection->prepare($sql);
        if ($stmt) {
            $all_params = array_merge([$product_id], $exclude_params);
            $param_types = 'i' . str_repeat('i', count($exclude_params));
            
            $stmt->bind_param($param_types, ...$all_params);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $product_data = [
                    'product_id' => intval($row['product_id']),
                    'variation_id' => intval($row['variation_id']),
                    'name' => $row['name'],
                    'price' => floatval($row['price'] ?? 0),
                    'type' => $row['type']
                ];

                $product_data['variation_attributes'] = $this->get_variation_attributes($product_data['variation_id'], $connection, $table_prefix);
                
                $this->log_debug("Same ID mode: Found available variation {$product_data['variation_id']} for product {$product_id}");
                return $product_data;
            }
        }

        // 第三步：如果没有可用变体，检查主商品是否可用（未被使用）
        $main_key = $product_id . '_0';
        if (isset($used_replacements[$main_key])) {
            $this->log_debug("Same ID mode: Main product {$product_id} already used, no alternatives available");
            return null;
        }

        // 查找主商品（确保不是变体商品的父商品）
        $sql = "SELECT 
                    p.ID as product_id,
                    0 as variation_id,
                    p.post_title as name,
                    pm.meta_value as price,
                    'simple' as type
                FROM {$table_prefix}posts p
                LEFT JOIN {$table_prefix}postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_price'
                WHERE p.ID = ?
                AND p.post_type = 'product'
                AND p.post_status = 'publish'
                AND p.ID NOT IN (
                    SELECT DISTINCT post_parent 
                    FROM {$table_prefix}posts 
                    WHERE post_type = 'product_variation' 
                    AND post_parent IS NOT NULL
                    AND post_status = 'publish'
                )";

        $stmt = $connection->prepare($sql);
        if ($stmt) {
            $stmt->bind_param('i', $product_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $product_data = [
                    'product_id' => intval($row['product_id']),
                    'variation_id' => 0,
                    'name' => $row['name'],
                    'price' => floatval($row['price'] ?? 0),
                    'type' => $row['type']
                ];
                
                $this->log_debug("Same ID mode: Found main product {$product_id} (no variations available)");
                return $product_data;
            }
        }

        $this->log_debug("Same ID mode: Product {$product_id} exists but no available variants or main product (all used or has variations)");
        return null;
    }

    /**
     * 查找回退商品 - 无重复版本 (使用Same Price模式)
     */
    private function find_fallback_product_no_duplicate($price, $connection, $used_replacements = []) {
        // 使用Same Price模式作为回退
        return $this->find_product_by_price_enhanced_no_duplicate($price, $connection, $used_replacements);
    }

    /**
     * 获取数据库连接的表前缀
     */
    private function get_table_prefix_from_connection($connection) {
        // 默认使用wp_前缀，实际应用中可能需要从配置中获取
        return 'wp_';
    }

    /**
     * 获取变体属性
     */
    private function get_variation_attributes($variation_id, $connection, $table_prefix) {
        $attributes = [];

        $sql = "SELECT meta_key, meta_value 
                FROM {$table_prefix}postmeta 
                WHERE post_id = ? 
                AND meta_key LIKE 'attribute_%'";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare variation attributes query: " . $connection->error);
            return $attributes;
        }

        $stmt->bind_param('i', $variation_id);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $attributes[$row['meta_key']] = $row['meta_value'];
        }

        $this->log_debug("Retrieved " . count($attributes) . " attributes for variation {$variation_id}");
        return $attributes;
    }

    /**
     * 合并变体属性到项目元数据
     */
    private function merge_variation_attributes($original_meta_data, $variation_attributes) {
        // 如果原始元数据是对象数组，转换为关联数组
        $meta_array = [];
        
        if (is_array($original_meta_data)) {
            foreach ($original_meta_data as $meta) {
                if (is_object($meta) && isset($meta->key) && isset($meta->value)) {
                    $meta_array[$meta->key] = $meta->value;
                } elseif (is_array($meta) && isset($meta['key']) && isset($meta['value'])) {
                    $meta_array[$meta['key']] = $meta['value'];
                }
            }
        }

        // 合并变体属性
        foreach ($variation_attributes as $key => $value) {
            $meta_array[$key] = $value;
        }

        // 转换回对象数组格式（WooCommerce期望的格式）
        $result = [];
        foreach ($meta_array as $key => $value) {
            $result[] = (object) [
                'key' => $key,
                'value' => $value
            ];
        }

        return $result;
    }

    /**
     * 在目标数据库中创建订单
     */
    private function create_order_in_database($order_data, $target_connection, $target_db_config) {
        try {
            $table_prefix = $this->get_table_prefix_from_connection($target_connection);

            // 开始事务
            $target_connection->autocommit(false);

            // 1. 插入到wc_orders表（HPOS主表）
            $order_id = $this->insert_order_to_hpos_table($order_data, $target_connection, $table_prefix);
            if (!$order_id) {
                throw new Exception("Failed to insert order to wc_orders table");
            }

            // 2. 插入订单项目
            $this->insert_order_items($order_id, $order_data['items'], $target_connection, $table_prefix);

            // 3. 插入元数据
            $this->insert_order_meta($order_id, $order_data['meta_data'], $target_connection, $table_prefix);

            // 4. 插入订单备注
            $this->insert_order_notes($order_id, $order_data['notes'], $target_connection, $table_prefix);

            // 5. 插入地址信息
            $this->insert_order_addresses($order_id, $order_data, $target_connection, $table_prefix);

            // 提交事务
            $target_connection->commit();
            $target_connection->autocommit(true);

            // 6. 重新计算订单总额以确保折扣正确应用
            $this->recalculate_order_totals_after_creation($order_id, $order_data);

            $this->log_debug("Successfully created order {$order_id} in target database");
            return $order_id;

        } catch (Exception $e) {
            // 回滚事务
            $target_connection->rollback();
            $target_connection->autocommit(true);

            $this->log_error("Failed to create order in target database: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 重新计算订单总额以确保折扣正确应用
     */
    private function recalculate_order_totals_after_creation($order_id, $order_data) {
        try {
            // 获取刚创建的订单对象
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Cannot get order object for recalculation: {$order_id}");
                return;
            }

            $this->log_debug("Recalculating totals for order #{$order_id} to ensure discount is properly applied");

            // 保存原始总额用于比较
            $original_total = $order->get_total();
            $original_discount = $order->get_total_discount();

            // 重新计算总额 - 这会确保优惠券折扣被正确计算
            $order->calculate_totals();

            // 获取重新计算后的总额
            $new_total = $order->get_total();
            $new_discount = $order->get_total_discount();

            // 如果总额或折扣发生变化，保存订单
            if (abs($original_total - $new_total) > 0.01 || abs($original_discount - $new_discount) > 0.01) {
                $order->save();
                $this->log_debug("Order totals recalculated and saved: total {$original_total} -> {$new_total}, discount {$original_discount} -> {$new_discount}");
            } else {
                $this->log_debug("Order totals are already correct: total={$new_total}, discount={$new_discount}");
            }

        } catch (Exception $e) {
            $this->log_error("Failed to recalculate order totals for order #{$order_id}: " . $e->getMessage());
        }
    }

    /**
     * 插入订单到HPOS主表
     */
    private function insert_order_to_hpos_table($order_data, $connection, $table_prefix) {
        $order_id = $order_data['id'];

        // 检查订单是否已存在
        $this->log_debug("Checking if order #{$order_id} already exists in target database...");
        $check_sql = "SELECT COUNT(*) as count FROM {$table_prefix}wc_orders WHERE id = ?";
        $check_stmt = $connection->prepare($check_sql);

        if ($check_stmt) {
            $check_stmt->bind_param('i', $order_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] > 0) {
                $this->log_debug("WARNING: Order #{$order_id} already exists in target database. Skipping insertion to avoid duplicate key error.");
                $this->log_debug("If you want to replace the existing order, please delete it first using the cleanup tools.");
                return $order_id; // 返回现有订单ID，表示"成功"
            }

            $check_stmt->close();
        }

        $this->log_debug("Order #{$order_id} does not exist in target database. Proceeding with insertion...");

        $sql = "INSERT INTO {$table_prefix}wc_orders (
            id, status, type, currency, tax_amount, total_amount, customer_id,
            billing_email, date_created_gmt, date_updated_gmt, parent_order_id,
            payment_method, payment_method_title, transaction_id, customer_note
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            throw new Exception("Failed to prepare order insert statement: " . $connection->error);
        }

        $status = 'wc-' . $order_data['status'];
        $date_created = $order_data['date_created'] ? $order_data['date_created']->format('Y-m-d H:i:s') : current_time('mysql', true);
        $date_modified = $order_data['date_modified'] ? $order_data['date_modified']->format('Y-m-d H:i:s') : current_time('mysql', true);
        $billing_email = $order_data['billing_address']['email'] ?? '';
        $order_type = 'shop_order';
        $parent_order_id = 0;

        $stmt->bind_param(
            'isssddisssissss',
            $order_data['id'],
            $status,
            $order_type,
            $order_data['currency'],
            $order_data['tax_total'],
            $order_data['total'],
            $order_data['customer_id'],
            $billing_email,
            $date_created,
            $date_modified,
            $parent_order_id,
            $order_data['payment_method'],
            $order_data['payment_method_title'],
            $order_data['transaction_id'],
            $order_data['customer_note']
        );

        if (!$stmt->execute()) {
            throw new Exception("Failed to execute order insert: " . $stmt->error);
        }

        return $order_data['id']; // 使用原订单ID保持一致
    }

    /**
     * 插入订单项目
     */
    private function insert_order_items($order_id, $items, $connection, $table_prefix) {
        $this->log_debug("Starting to insert order items for order #{$order_id}. Total items: " . count($items));

        // 检查订单是否已有项目 - 改进版本：按类型检查，避免阻止缺失类型的项目插入
        $existing_items_by_type = [];
        $check_sql = "SELECT order_item_type, COUNT(*) as count FROM {$table_prefix}woocommerce_order_items WHERE order_id = ? GROUP BY order_item_type";
        $check_stmt = $connection->prepare($check_sql);

        if ($check_stmt) {
            $check_stmt->bind_param('i', $order_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $existing_items_by_type[$row['order_item_type']] = $row['count'];
            }
            $check_stmt->close();
        }

        $total_existing_items = array_sum($existing_items_by_type);
        if ($total_existing_items > 0) {
            $this->log_debug("Order #{$order_id} already has {$total_existing_items} items in target database:");
            foreach ($existing_items_by_type as $type => $count) {
                $this->log_debug("  - {$type}: {$count} items");
            }
            $this->log_debug("Will only insert missing item types to avoid duplicates.");
        } else {
            $this->log_debug("Order #{$order_id} has no existing items in target database. Proceeding with all item insertion...");
        }

        foreach ($items as $item_index => $item) {
            $item_type = $item['type'] ?? 'line_item';

            $this->log_debug("Processing item #{$item_index}: {$item['name']} (type: {$item_type})");

            // 检查此类型的项目是否已存在，如果存在则跳过
            if (isset($existing_items_by_type[$item_type]) && $existing_items_by_type[$item_type] > 0) {
                $this->log_debug("Skipping item #{$item_index} ({$item_type}): {$existing_items_by_type[$item_type]} items of this type already exist in target database");
                continue;
            }

            // 插入到woocommerce_order_items表
            $sql = "INSERT INTO {$table_prefix}woocommerce_order_items (
                order_item_name, order_item_type, order_id
            ) VALUES (?, ?, ?)";

            $stmt = $connection->prepare($sql);
            if (!$stmt) {
                $this->log_debug("ERROR: Failed to prepare woocommerce_order_items insert: " . $connection->error);
                throw new Exception("Failed to prepare order item insert: " . $connection->error);
            }

            $stmt->bind_param('ssi', $item['name'], $item_type, $order_id);
            if (!$stmt->execute()) {
                $this->log_debug("ERROR: Failed to execute woocommerce_order_items insert: " . $stmt->error);
                throw new Exception("Failed to insert order item: " . $stmt->error);
            }

            $item_id = $connection->insert_id;
            $this->log_debug("SUCCESS: Inserted into woocommerce_order_items - Item ID: {$item_id}, Name: '{$item['name']}', Type: {$item_type}");

            // 根据项目类型插入不同的元数据
            if ($item_type === 'line_item') {
                // 商品项目元数据 - 包含所有WooCommerce需要的字段
                $item_meta = [
                    '_product_id' => $item['product_id'],
                    '_variation_id' => $item['variation_id'] ?? 0,
                    '_qty' => $item['quantity'],
                    '_line_total' => $item['total']?? 0,
                    '_line_subtotal' => $item['subtotal']?? 0,
                    '_line_tax' => $item['tax_total']?? 0,
                    '_line_subtotal_tax' => 0, // 通常为0，除非有复杂税务
                    '_line_tax_data' => serialize([]), // 税务数据
                    '_reduced_stock' => $item['quantity'] // 库存减少数量
                ];

                $this->log_debug("Prepared line_item metadata for item {$item_id}: product_id={$item['product_id']}, qty={$item['quantity']}, total={$item['total']}, subtotal={$item['subtotal']}");
            } elseif ($item_type === 'fee') {
                // 费用项目元数据 - 包含完整的费用相关元数据
                $tax_data = $item['tax_data'] ?? [];
                $item_meta = [
                    '_fee_amount' => $item['total'] ?? 0,
                    '_tax_class' => $item['tax_class'] ?? '',
                    '_tax_status' => $item['tax_status'] ?? 'taxable',
                    '_line_total' => $item['total'] ?? 0,
                    '_line_tax' => $item['tax_total'] ?? 0,
                    '_line_tax_data' => is_array($tax_data) ? $tax_data : []
                ];

                $this->log_debug("Prepared fee metadata for item {$item_id}: amount={$item['total']}, tax={$item['tax_total']}, tax_status=" . ($item['tax_status'] ?? 'taxable'));
            } elseif ($item_type === 'shipping') {
                // 运费项目元数据 - 包含完整的运费相关元数据
                $taxes = $item['taxes'] ?? [];
                $item_meta = [
                    'method_id' => $item['method_id'] ?? 'flat_rate',
                    'instance_id' => $item['instance_id'] ?? '',
                    'cost' => $item['total'] ?? 0,
                    'total_tax' => $item['tax_total'] ?? 0,
                    'taxes' => is_array($taxes) ? $taxes : []
                ];

                $this->log_debug("Prepared shipping metadata for item {$item_id}: method=" . ($item['method_id'] ?? 'flat_rate') . ", cost={$item['total']}, tax={$item['tax_total']}");
            } elseif ($item_type === 'coupon') {
                // 优惠券项目元数据 - 包含完整的优惠券相关元数据
                $item_meta = [
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'discount_amount_tax' => $item['discount_tax'] ?? 0,
                    'coupon_data' => serialize([
                        'code' => $item['code'] ?? $item['name'],
                        'discount_type' => 'fixed_cart', // 默认类型，实际应该从优惠券对象获取
                        'amount' => $item['discount_amount'] ?? 0
                    ])
                ];

                $this->log_debug("Prepared coupon metadata for item {$item_id}: code=" . ($item['code'] ?? $item['name']) . ", discount={$item['discount_amount']}, tax={$item['discount_tax']}");
            } else {
                // 其他类型项目的基本元数据
                $item_meta = [
                    '_line_total' => $item['total'],
                    '_line_tax' => $item['tax_total']
                ];

                $this->log_debug("Prepared generic metadata for item {$item_id} (type: {$item_type}): total={$item['total']}");
            }

            $meta_count = 0;
            foreach ($item_meta as $meta_key => $meta_value) {
                $this->insert_order_item_meta($item_id, $meta_key, $meta_value, $connection, $table_prefix);
                $meta_count++;

                // 安全地记录元数据值（处理数组情况）
                $log_value = is_array($meta_value) ? json_encode($meta_value) : $meta_value;
                $this->log_debug("Inserted meta: {$meta_key} = {$log_value} for item {$item_id}");
            }

            // 插入自定义元数据
            $custom_meta_count = 0;
            if (isset($item['meta_data']) && is_array($item['meta_data'])) {
                foreach ($item['meta_data'] as $meta) {
                    if (is_object($meta) && isset($meta->key) && isset($meta->value)) {
                        $this->insert_order_item_meta($item_id, $meta->key, $meta->value, $connection, $table_prefix);
                        $custom_meta_count++;
                        $this->log_debug("Inserted custom meta: {$meta->key} = {$meta->value} for item {$item_id}");
                    }
                }
            }

            $total_meta = $meta_count + $custom_meta_count;
            $this->log_debug("COMPLETED: Item {$item_id} ({$item_type}): '{$item['name']}' - {$meta_count} standard meta + {$custom_meta_count} custom meta = {$total_meta} total meta entries");
        }

        $this->log_debug("FINISHED: Successfully inserted all " . count($items) . " order items for order #{$order_id}");
    }

    /**
     * 插入订单项目元数据
     */
    private function insert_order_item_meta($item_id, $meta_key, $meta_value, $connection, $table_prefix) {
        // 详细的调试日志
        $original_value = $meta_value;
        $value_type = gettype($meta_value);

        $sql = "INSERT INTO {$table_prefix}woocommerce_order_itemmeta (
            order_item_id, meta_key, meta_value
        ) VALUES (?, ?, ?)";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_debug("ERROR: Failed to prepare woocommerce_order_itemmeta insert for item {$item_id}, key '{$meta_key}': " . $connection->error);
            throw new Exception("Failed to prepare order item meta insert: " . $connection->error);
        }

        // 处理不同类型的值
        if (is_array($meta_value) || is_object($meta_value)) {
            $meta_value_str = serialize($meta_value);
            $this->log_debug("Serialized {$value_type} value for meta key '{$meta_key}' (item {$item_id}): " . substr($meta_value_str, 0, 100) . (strlen($meta_value_str) > 100 ? '...' : ''));
        } else {
            $meta_value_str = (string)$meta_value;
            $this->log_debug("Converting {$value_type} to string for meta key '{$meta_key}' (item {$item_id}): '{$meta_value_str}'");
        }

        $stmt->bind_param('iss', $item_id, $meta_key, $meta_value_str);

        if (!$stmt->execute()) {
            $this->log_debug("ERROR: Failed to execute woocommerce_order_itemmeta insert for item {$item_id}, key '{$meta_key}': " . $stmt->error);
            throw new Exception("Failed to insert order item meta: " . $stmt->error);
        }

        $meta_id = $connection->insert_id;
        $this->log_debug("SUCCESS: Inserted into woocommerce_order_itemmeta - Meta ID: {$meta_id}, Item ID: {$item_id}, Key: '{$meta_key}', Value length: " . strlen($meta_value_str) . " chars");

        // 对重要的元数据进行特别记录
        if (in_array($meta_key, ['_product_id', '_qty', '_line_total', '_line_subtotal'])) {
            $this->log_debug("IMPORTANT META: {$meta_key} = {$meta_value_str} for item {$item_id}");
        }
    }

    /**
     * 插入订单元数据
     */
    private function insert_order_meta($order_id, $meta_data, $connection, $table_prefix) {
        foreach ($meta_data as $meta) {
            $sql = "INSERT INTO {$table_prefix}wc_orders_meta (
                order_id, meta_key, meta_value
            ) VALUES (?, ?, ?)";

            $stmt = $connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Failed to prepare order meta insert: " . $connection->error);
            }

            $meta_value_str = is_array($meta['value']) || is_object($meta['value']) ? serialize($meta['value']) : (string)$meta['value'];
            $stmt->bind_param('iss', $order_id, $meta['key'], $meta_value_str);

            if (!$stmt->execute()) {
                throw new Exception("Failed to insert order meta: " . $stmt->error);
            }
        }
    }

    /**
     * 插入订单备注
     */
    private function insert_order_notes($order_id, $notes, $connection, $table_prefix) {
        foreach ($notes as $note) {
            $sql = "INSERT INTO {$table_prefix}comments (
                comment_post_ID, comment_author, comment_author_email, comment_author_url,
                comment_author_IP, comment_date, comment_date_gmt, comment_content,
                comment_karma, comment_approved, comment_agent, comment_type, comment_parent
            ) VALUES (?, ?, '', '', '', ?, ?, ?, 0, 1, '', 'order_note', 0)";

            $stmt = $connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Failed to prepare order note insert: " . $connection->error);
            }

            $author = $note['added_by'] ?: 'system';

            // 处理日期格式 - 支持多种日期格式
            if ($note['date_created']) {
                if (is_object($note['date_created']) && method_exists($note['date_created'], 'format')) {
                    // WC_DateTime 对象
                    $date = $note['date_created']->format('Y-m-d H:i:s');
                    $date_gmt = $note['date_created']->format('Y-m-d H:i:s');
                } elseif (is_string($note['date_created'])) {
                    // 字符串格式
                    $date = $note['date_created'];
                    $date_gmt = gmdate('Y-m-d H:i:s', strtotime($note['date_created']));
                } else {
                    // 其他格式，使用当前时间
                    $date = current_time('mysql');
                    $date_gmt = current_time('mysql', true);
                }
            } else {
                $date = current_time('mysql');
                $date_gmt = current_time('mysql', true);
            }

            $stmt->bind_param('issss', $order_id, $author, $date, $date_gmt, $note['content']);

            if (!$stmt->execute()) {
                throw new Exception("Failed to insert order note: " . $stmt->error);
            }

            $comment_id = $connection->insert_id;

            // 添加备注元数据
            $this->insert_comment_meta($comment_id, 'is_customer_note', $note['customer_note'] ? 1 : 0, $connection, $table_prefix);
        }
    }

    /**
     * 插入评论元数据
     */
    private function insert_comment_meta($comment_id, $meta_key, $meta_value, $connection, $table_prefix) {
        $sql = "INSERT INTO {$table_prefix}commentmeta (
            comment_id, meta_key, meta_value
        ) VALUES (?, ?, ?)";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            throw new Exception("Failed to prepare comment meta insert: " . $connection->error);
        }

        $meta_value_str = (string)$meta_value;
        $stmt->bind_param('iss', $comment_id, $meta_key, $meta_value_str);

        if (!$stmt->execute()) {
            throw new Exception("Failed to insert comment meta: " . $stmt->error);
        }
    }

    /**
     * 插入订单地址信息
     */
    private function insert_order_addresses($order_id, $order_data, $connection, $table_prefix) {
        $this->log_debug("Inserting address data for order #{$order_id}");

        // 插入账单地址 - 包括空值，确保完整性
        if (isset($order_data['billing_address']) && is_array($order_data['billing_address'])) {
            $this->log_debug("Inserting billing address with " . count($order_data['billing_address']) . " fields");
            foreach ($order_data['billing_address'] as $key => $value) {
                $meta_key = '_billing_' . $key;
                // 确保所有字段都被插入，包括空值
                $this->insert_order_meta_direct($order_id, $meta_key, $value, $connection, $table_prefix);
                $this->log_debug("Inserted billing field: {$meta_key} = " . (is_string($value) ? substr($value, 0, 50) : $value));
            }
        } else {
            $this->log_warning("No billing address data found for order #{$order_id}");
        }

        // 插入收货地址 - 包括空值，确保完整性
        if (isset($order_data['shipping_address']) && is_array($order_data['shipping_address'])) {
            $this->log_debug("Inserting shipping address with " . count($order_data['shipping_address']) . " fields");
            foreach ($order_data['shipping_address'] as $key => $value) {
                $meta_key = '_shipping_' . $key;
                // 确保所有字段都被插入，包括空值
                $this->insert_order_meta_direct($order_id, $meta_key, $value, $connection, $table_prefix);
                $this->log_debug("Inserted shipping field: {$meta_key} = " . (is_string($value) ? substr($value, 0, 50) : $value));
            }
        } else {
            $this->log_warning("No shipping address data found for order #{$order_id}");
        }

        // 插入其他重要元数据
        $important_meta = [
            '_order_key' => $order_data['order_key'],
            '_order_total' => $order_data['total'],
            '_order_tax' => $order_data['tax_total'],
            '_order_shipping' => $order_data['shipping_total'],
            '_order_discount' => $order_data['discount_total'],
            '_payment_method' => $order_data['payment_method'],
            '_payment_method_title' => $order_data['payment_method_title'],
            '_transaction_id' => $order_data['transaction_id']
        ];

        foreach ($important_meta as $key => $value) {
            if ($value !== null && $value !== '') {
                $this->insert_order_meta_direct($order_id, $key, $value, $connection, $table_prefix);
            }
        }
    }

    /**
     * 直接插入订单元数据
     */
    private function insert_order_meta_direct($order_id, $meta_key, $meta_value, $connection, $table_prefix) {
        // 首先检查是否已存在，如果存在则更新
        $check_sql = "SELECT meta_id FROM {$table_prefix}wc_orders_meta WHERE order_id = ? AND meta_key = ?";
        $check_stmt = $connection->prepare($check_sql);

        if ($check_stmt) {
            $check_stmt->bind_param('is', $order_id, $meta_key);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                // 记录已存在，执行更新
                $check_stmt->close();
                $this->update_order_meta_direct($order_id, $meta_key, $meta_value, $connection, $table_prefix);
                return;
            }
            $check_stmt->close();
        }

        // 插入新记录
        $sql = "INSERT INTO {$table_prefix}wc_orders_meta (
            order_id, meta_key, meta_value
        ) VALUES (?, ?, ?)";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare order meta insert for {$meta_key}: " . $connection->error);
            return;
        }

        // 处理不同类型的值，确保空值也能正确插入
        if (is_array($meta_value) || is_object($meta_value)) {
            $meta_value_str = serialize($meta_value);
        } elseif (is_null($meta_value)) {
            $meta_value_str = '';
        } else {
            $meta_value_str = (string)$meta_value;
        }

        $stmt->bind_param('iss', $order_id, $meta_key, $meta_value_str);

        if (!$stmt->execute()) {
            $this->log_error("Failed to insert order meta {$meta_key}: " . $stmt->error);
        } else {
            $this->log_debug("Successfully inserted order meta: {$meta_key} = " . substr($meta_value_str, 0, 50));
        }

        $stmt->close();
    }

    /**
     * 直接更新订单元数据
     */
    private function update_order_meta_direct($order_id, $meta_key, $meta_value, $connection, $table_prefix) {
        $sql = "UPDATE {$table_prefix}wc_orders_meta SET meta_value = ? WHERE order_id = ? AND meta_key = ?";

        $stmt = $connection->prepare($sql);
        if (!$stmt) {
            $this->log_error("Failed to prepare order meta update for {$meta_key}: " . $connection->error);
            return;
        }

        // 处理不同类型的值
        if (is_array($meta_value) || is_object($meta_value)) {
            $meta_value_str = serialize($meta_value);
        } elseif (is_null($meta_value)) {
            $meta_value_str = '';
        } else {
            $meta_value_str = (string)$meta_value;
        }

        $stmt->bind_param('sis', $meta_value_str, $order_id, $meta_key);

        if (!$stmt->execute()) {
            $this->log_error("Failed to update order meta {$meta_key}: " . $stmt->error);
        } else {
            $this->log_debug("Successfully updated order meta: {$meta_key} = " . substr($meta_value_str, 0, 50));
        }

        $stmt->close();
    }

    /**
     * 复制订单状态变更
     */
    public function replicate_order_status($order_id, $from_status, $to_status, $order) {
        $this->log_debug("Replicating status change for order #{$order_id}: {$from_status} -> {$to_status}");

        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = YXJTO_Gateway::get_instance()->get_current_database();
        }
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        foreach ($databases as $db_name => $db_config) {
            if ($db_name === $source_database || !$db_config['enabled']) {
                continue;
            }

            $this->update_order_status_in_database($order_id, $to_status, $db_name, $db_config);
        }
    }

    /**
     * 在指定数据库中更新订单状态
     */
    private function update_order_status_in_database($order_id, $status, $db_name, $db_config) {
        try {
            $connection = $this->create_database_connection($db_config);
            if (!$connection) {
                throw new Exception("Failed to connect to database: {$db_name}");
            }

            $table_prefix = $this->get_table_prefix_from_connection($connection);
            $status_with_prefix = 'wc-' . ltrim($status, 'wc-');

            // 更新HPOS表中的状态
            $sql = "UPDATE {$table_prefix}wc_orders SET status = ?, date_updated_gmt = ? WHERE id = ?";
            $stmt = $connection->prepare($sql);

            if (!$stmt) {
                throw new Exception("Failed to prepare status update: " . $connection->error);
            }

            $current_time = current_time('mysql', true);
            $stmt->bind_param('ssi', $status_with_prefix, $current_time, $order_id);

            if (!$stmt->execute()) {
                throw new Exception("Failed to update order status: " . $stmt->error);
            }

            $this->log_debug("Successfully updated order #{$order_id} status to {$status} in {$db_name}");

        } catch (Exception $e) {
            $this->log_error("Failed to update order status in {$db_name}: " . $e->getMessage());
        }
    }

    /**
     * 复制支付信息
     */
    public function replicate_payment_info($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Invalid order ID for payment info replication: {$order_id}");
            return;
        }

        $this->log_debug("Replicating payment info for order #{$order_id}");

        $payment_data = [
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'transaction_id' => $order->get_transaction_id(),
            'date_paid' => $order->get_date_paid(),
            'meta_data' => []
        ];

        // 获取支付相关的元数据
        $payment_meta_keys = [
            '_paypal_multi_gateway_account_id',
            '_paypal_multi_gateway_payment_id',
            '_paypal_multi_gateway_payment_url',
            '_paypal_multi_gateway_account_type',
            '_paypal_multi_gateway_testmode',
            '_paypal_multi_gateway_created_at',
            '_stripe_source_id',
            '_stripe_charge_id'
        ];

        foreach ($payment_meta_keys as $meta_key) {
            $meta_value = $order->get_meta($meta_key);
            if ($meta_value) {
                $payment_data['meta_data'][$meta_key] = $meta_value;
            }
        }

        // 特殊处理支付令牌 - 使用WooCommerce正确的方法
        try {
            $payment_tokens = $order->get_payment_tokens();
            if (!empty($payment_tokens)) {
                $payment_data['meta_data']['_payment_tokens'] = $payment_tokens;
            }
        } catch (Exception $e) {
            $this->log_error("Failed to get payment tokens for order #{$order_id}: " . $e->getMessage());
        }

        $this->replicate_payment_data_to_all_databases($order_id, $payment_data);
    }

    /**
     * 复制支付数据到所有数据库
     */
    private function replicate_payment_data_to_all_databases($order_id, $payment_data) {
        $gateway = $this->get_gateway_instance();
        if (!$gateway) {
            $this->log_error("Gateway instance not available for payment data replication");
            return;
        }
        
        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = $gateway->get_current_database();
        }
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        foreach ($databases as $db_name => $db_config) {
            if ($db_name === $source_database || !$db_config['enabled']) {
                continue;
            }

            $this->update_payment_info_in_database($order_id, $payment_data, $db_name, $db_config);
        }
    }

    /**
     * 在指定数据库中更新支付信息
     */
    private function update_payment_info_in_database($order_id, $payment_data, $db_name, $db_config) {
        try {
            $connection = $this->create_database_connection($db_config);
            if (!$connection) {
                throw new Exception("Failed to connect to database: {$db_name}");
            }

            $table_prefix = $this->get_table_prefix_from_connection($connection);

            // 更新订单表中的支付信息
            $sql = "UPDATE {$table_prefix}wc_orders SET
                    payment_method = ?,
                    payment_method_title = ?,
                    transaction_id = ?,
                    date_updated_gmt = ?
                    WHERE id = ?";

            $stmt = $connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Failed to prepare payment update: " . $connection->error);
            }

            $current_time = current_time('mysql', true);
            $stmt->bind_param(
                'ssssi',
                $payment_data['payment_method'],
                $payment_data['payment_method_title'],
                $payment_data['transaction_id'],
                $current_time,
                $order_id
            );

            if (!$stmt->execute()) {
                throw new Exception("Failed to update payment info: " . $stmt->error);
            }

            // 更新支付相关的元数据
            foreach ($payment_data['meta_data'] as $meta_key => $meta_value) {
                $this->update_order_meta_in_database($order_id, $meta_key, $meta_value, $connection, $table_prefix);
            }

            $this->log_debug("Successfully updated payment info for order #{$order_id} in {$db_name}");

        } catch (Exception $e) {
            $this->log_error("Failed to update payment info in {$db_name}: " . $e->getMessage());
        }
    }

    /**
     * 在数据库中更新订单元数据
     */
    private function update_order_meta_in_database($order_id, $meta_key, $meta_value, $connection, $table_prefix) {
        // 先尝试更新
        $sql = "UPDATE {$table_prefix}wc_orders_meta SET meta_value = ? WHERE order_id = ? AND meta_key = ?";
        $stmt = $connection->prepare($sql);

        if (!$stmt) {
            $this->log_error("Failed to prepare meta update for {$meta_key}: " . $connection->error);
            return;
        }

        $meta_value_str = is_array($meta_value) || is_object($meta_value) ? serialize($meta_value) : (string)$meta_value;
        $stmt->bind_param('sis', $meta_value_str, $order_id, $meta_key);

        if (!$stmt->execute()) {
            $this->log_error("Failed to update meta {$meta_key}: " . $stmt->error);
            return;
        }

        // 如果没有更新任何行，则插入新记录
        if ($stmt->affected_rows === 0) {
            $this->insert_order_meta_direct($order_id, $meta_key, $meta_value, $connection, $table_prefix);
        }
    }

    /**
     * 支付跳转前验证订单并切换到默认数据库
     */
    public function verify_order_before_payment($payment_url, $order) {
        if (!is_a($order, 'WC_Order')) {
            $this->log_debug("Invalid order object passed to verify_order_before_payment");
            return $payment_url;
        }

        $order_id = $order->get_id();
        $this->log_debug("=== PAYMENT VERIFICATION STARTED ===");
        $this->log_debug("Verifying order #{$order_id} before payment and switching to default database");

        // 安全获取当前数据库
        $gateway = $this->get_gateway_instance();
        if (!$gateway) {
            $this->log_error("Gateway instance not available for payment verification, skipping database switch");
            return $payment_url;
        }
        
        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = $gateway->get_current_database();
        }
        $this->log_debug("Source database for order #{$order_id}: {$source_database}");

        // 检查订单是否存在于默认数据库
        $default_order_exists = $this->get_order_from_default_database($order_id);

        if (!$default_order_exists) {
            $this->log_error("Order #{$order_id} not found in default database, payment verification failed");
            wp_die(__('Order verification failed. Please try again.', 'yxjto-gateway'));
        }

        $this->log_debug("Order #{$order_id} found in default database, proceeding with payment verification");

        // 验证订单总价是否一致（如果启用了总价验证）
        if (class_exists('YXJTO_Payment_Database_Switch_Config')) {
            $config = YXJTO_Payment_Database_Switch_Config::get_instance();
            if ($config && $config->is_totals_verification_enabled()) {
                $totals_match = $this->verify_order_totals_match($order_id, $order);
                if (!$totals_match) {
                    $this->log_error("Order #{$order_id} totals mismatch between source and default database, payment verification failed");
                    wp_die(__('Order verification failed: Price mismatch detected. Please try again.', 'yxjto-gateway'));
                }
                $this->log_debug("Order totals verification passed for order #{$order_id}");
            } else {
                $this->log_debug("Order totals verification is disabled, skipping totals check for order #{$order_id}");
            }
        }

        // 强制切换到默认数据库进行支付处理
        if ($source_database !== 'default') {
            $this->log_debug("Source database is '{$source_database}', FORCING switch to default database for payment");

            // 切换到默认数据库
            $switch_result = $gateway->switch_database('default');

            if (!$switch_result) {
                $this->log_error("Failed to switch to default database for payment processing");
                wp_die(__('Database switch failed. Please try again.', 'yxjto-gateway'));
            }

            // 验证切换是否成功
            $new_current_database = $gateway->get_current_database();
            if ($new_current_database !== 'default') {
                $this->log_error("Database switch verification failed: Expected 'default', got '{$new_current_database}'");
                wp_die(__('Database switch verification failed. Please try again.', 'yxjto-gateway'));
            }

            $this->log_debug("Successfully switched to default database for payment processing (verified: {$new_current_database})");
        } else {
            $this->log_debug("Already using default database for payment processing");
        }

        // 设置一个标记，表示我们为了支付而切换了数据库
        // 使用WordPress的transient API代替session，避免header问题
        if ($current_database !== 'default') {
            $switch_data = [
                'original_database' => $current_database,
                'switched_for_payment' => true,
                'order_id' => $order_id,
                'timestamp' => time()
            ];

            // 使用订单ID作为唯一标识符，使用配置的超时时间
            $timeout = HOUR_IN_SECONDS; // 默认1小时
            if (class_exists('YXJTO_Payment_Database_Switch_Config')) {
                $config = YXJTO_Payment_Database_Switch_Config::get_instance();
                if ($config) {
                    $timeout = $config->get_transient_timeout();
                }
            }
            set_transient('yxjto_payment_db_switch_' . $order_id, $switch_data, $timeout);
            $this->log_debug("Transient set for post-payment database switch back to '{$current_database}'");
        }

        $this->log_debug("=== PAYMENT VERIFICATION COMPLETED ===");
        $this->log_debug("Order #{$order_id} verified successfully and using default database for payment");

        return $payment_url;
    }

    /**
     * 支付验证过滤器 - 用于PayPal等支付网关，确保使用默认数据库
     */
    public function verify_payment_filter($verification_result, $order) {
        if (!is_a($order, 'WC_Order')) {
            $this->log_debug("Invalid order object in verify_payment_filter");
            return false;
        }

        $order_id = $order->get_id();
        $this->log_debug("=== PAYMENT VERIFICATION FILTER STARTED ===");
        $this->log_debug("Payment verification filter for order #{$order_id}");

        // 安全获取当前数据库
        $gateway = $this->get_gateway_instance();
        if (!$gateway) {
            $this->log_error("Gateway instance not available for payment verification filter");
            return false;
        }
        
        // 获取当前数据库状态
        $current_database = $gateway->get_current_database();

        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = $current_database;
        }
        $this->log_debug("Current database: {$current_database}");
        $this->log_debug("Source database for order #{$order_id} in payment filter: {$source_database}");

        // 检查订单是否存在于默认数据库
        $default_order_exists = $this->get_order_from_default_database($order_id);

        if (!$default_order_exists) {
            $this->log_error("Order #{$order_id} not found in default database, payment verification failed");
            return false;
        }

        $this->log_debug("Order #{$order_id} confirmed in default database");

        // 验证订单总价是否一致（在支付处理阶段再次验证）
        if (class_exists('YXJTO_Payment_Database_Switch_Config')) {
            $config = YXJTO_Payment_Database_Switch_Config::get_instance();
            if ($config && $config->is_totals_verification_enabled()) {
                $totals_match = $this->verify_order_totals_match($order_id, $order);
                if (!$totals_match) {
                    $this->log_error("Order #{$order_id} totals mismatch during payment processing, verification failed");
                    return false;
                }
                $this->log_debug("Order totals verification passed during payment processing for order #{$order_id}");
            } else {
                $this->log_debug("Order totals verification is disabled, skipping totals check during payment processing for order #{$order_id}");
            }
        }

        // 强制确保当前使用的是默认数据库
        if ($current_database !== 'default') {
            $this->log_debug("Payment processing not in default database, FORCING switch to default");

            $switch_result = $gateway->switch_database('default');

            if (!$switch_result) {
                $this->log_error("Failed to switch to default database during payment verification filter");
                return false;
            }

            // 验证切换是否成功
            $new_current_database = $gateway->get_current_database();
            if ($new_current_database !== 'default') {
                $this->log_error("Database switch verification failed in payment filter: Expected 'default', got '{$new_current_database}'");
                return false;
            }

            $this->log_debug("Successfully switched to default database for payment verification (verified: {$new_current_database})");
        } else {
            $this->log_debug("Already using default database for payment processing");
        }

        $this->log_debug("=== PAYMENT VERIFICATION FILTER COMPLETED ===");
        $this->log_debug("Order #{$order_id} verified successfully and using default database for payment processing");
        return true;
    }

    /**
     * 从默认数据库获取订单并验证总价
     */
    private function get_order_from_default_database($order_id) {
        $gateway = $this->get_gateway_instance();
        if (!$gateway) {
            $this->log_error("Gateway instance not available for default database order verification");
            return false;
        }
        
        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = $gateway->get_current_database();
        }

        // 如果源数据库就是默认数据库，直接返回
        if ($source_database === 'default') {
            return wc_get_order($order_id);
        }

        // 切换到默认数据库查询
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        if (!isset($databases['default']) || !$databases['default']['enabled']) {
            $this->log_error("Default database not configured or disabled");
            return false;
        }

        try {
            $connection = $this->create_database_connection($databases['default']);
            if (!$connection) {
                throw new Exception("Failed to connect to default database");
            }

            $table_prefix = $this->get_table_prefix_from_connection($connection);

            // 查询订单是否存在
            $sql = "SELECT id FROM {$table_prefix}wc_orders WHERE id = ? LIMIT 1";
            $stmt = $connection->prepare($sql);

            if (!$stmt) {
                throw new Exception("Failed to prepare order verification query: " . $connection->error);
            }

            $stmt->bind_param('i', $order_id);
            $stmt->execute();
            $result = $stmt->get_result();

            return $result->num_rows > 0;

        } catch (Exception $e) {
            $this->log_error("Error verifying order in default database: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证默认数据库和源数据库订单总价是否一致
     */
    private function verify_order_totals_match($order_id, $source_order) {
        $gateway = YXJTO_Gateway::get_instance();

        // 获取当前数据库状态
        $current_database = $gateway->get_current_database();

        // 优先使用存储的源数据库
        $source_database = $this->get_order_source_database($order_id);
        if (empty($source_database)) {
            $source_database = $current_database;
        }

        $this->log_debug("Starting order totals verification for order #{$order_id}");
        $this->log_debug("Current database: {$current_database}");
        $this->log_debug("Source database: {$source_database}");

        // 获取源订单的详细信息
        $source_totals = $this->extract_order_totals($source_order);
        $this->log_debug("Source order totals: " . json_encode($source_totals));

        // 如果源数据库就是默认数据库，直接比较
        if ($source_database === 'default') {
            $default_order = wc_get_order($order_id);
            if (!$default_order) {
                $this->log_error("Order #{$order_id} not found in default database");
                return false;
            }

            $default_totals = $this->extract_order_totals($default_order);
            return $this->compare_order_totals($source_totals, $default_totals, $order_id);
        }

        // 需要连接到默认数据库获取订单详情
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        if (!isset($databases['default']) || !$databases['default']['enabled']) {
            $this->log_error("Default database not configured for totals verification");
            return false;
        }

        try {
            // 临时切换到默认数据库获取订单详情
            $switch_result = $gateway->switch_database('default');
            if (!$switch_result) {
                throw new Exception("Failed to switch to default database for totals verification");
            }

            $default_order = wc_get_order($order_id);
            if (!$default_order) {
                throw new Exception("Order #{$order_id} not found in default database");
            }

            $default_totals = $this->extract_order_totals($default_order);
            $this->log_debug("Default order totals: " . json_encode($default_totals));

            // 切换回源数据库
            $gateway->switch_database($current_database);

            // 比较总价
            return $this->compare_order_totals($source_totals, $default_totals, $order_id);

        } catch (Exception $e) {
            $this->log_error("Error during order totals verification: " . $e->getMessage());

            // 确保切换回源数据库
            try {
                $gateway->switch_database($current_database);
            } catch (Exception $switch_error) {
                $this->log_error("Failed to switch back to source database: " . $switch_error->getMessage());
            }

            return false;
        }
    }

    /**
     * 处理支付回调 - 简化版本，仅保留基本日志记录
     */
    public function handle_payment_callback($order_id) {
        $this->log_debug("=== PAYMENT CALLBACK STARTED ===");
        $this->log_debug("Handling payment callback for order #{$order_id} - using default WooCommerce behavior");

        // 使用当前数据库的订单信息显示回调页面，不进行数据库切换
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->log_error("Order #{$order_id} not found for callback handling");
            return;
        }

        $this->log_debug("Payment callback completed for order #{$order_id} - order found and displayed with default behavior");
        $this->log_debug("=== PAYMENT CALLBACK COMPLETED ===");
    }

    /**
     * 提取订单总价信息
     */
    private function extract_order_totals($order) {
        if (!$order || !is_a($order, 'WC_Order')) {
            return false;
        }

        $totals = array(
            'order_id' => $order->get_id(),
            'currency' => $order->get_currency(),
            'subtotal' => 0,
            'tax_total' => 0,
            'shipping_total' => 0,
            'discount_total' => 0,
            'fee_total' => 0,
            'total' => $order->get_total(),
            'items' => array(),
            'shipping_items' => array(),
            'fee_items' => array(),
            'tax_items' => array()
        );

        // 提取商品项目
        foreach ($order->get_items() as $item_id => $item) {
            $totals['items'][] = array(
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'subtotal' => $item->get_subtotal(),
                'total' => $item->get_total(),
                'tax' => $item->get_subtotal_tax() + $item->get_total_tax(),
                'product_id' => $item->get_product_id(),
                'variation_id' => $item->get_variation_id()
            );
            $totals['subtotal'] += $item->get_total();
        }

        // 提取运费项目
        foreach ($order->get_items('shipping') as $item_id => $item) {
            $totals['shipping_items'][] = array(
                'name' => $item->get_name(),
                'total' => $item->get_total(),
                'tax' => $item->get_total_tax(),
                'method_id' => $item->get_method_id()
            );
            $totals['shipping_total'] += $item->get_total();
        }

        // 提取费用项目
        foreach ($order->get_items('fee') as $item_id => $item) {
            $totals['fee_items'][] = array(
                'name' => $item->get_name(),
                'total' => $item->get_total(),
                'tax' => $item->get_total_tax()
            );
            $totals['fee_total'] += $item->get_total();
        }

        // 提取税费项目
        foreach ($order->get_items('tax') as $item_id => $item) {
            $totals['tax_items'][] = array(
                'name' => $item->get_name(),
                'tax_total' => $item->get_tax_total(),
                'shipping_tax_total' => $item->get_shipping_tax_total(),
                'rate_id' => $item->get_rate_id()
            );
            $totals['tax_total'] += $item->get_tax_total() + $item->get_shipping_tax_total();
        }

        // 提取优惠券折扣
        $totals['discount_total'] = $order->get_total_discount();

        return $totals;
    }

    /**
     * 比较两个订单的总价信息
     */
    private function compare_order_totals($source_totals, $default_totals, $order_id) {
        if (!$source_totals || !$default_totals) {
            $this->log_error("Invalid totals data for comparison (Order #{$order_id})");
            return false;
        }

        $this->log_debug("Comparing order totals for order #{$order_id}");

        // 获取配置
        $config = null;
        $precision = 0.01;
        $verify_items = true;
        $verify_shipping = true;
        $verify_tax = true;
        $verify_discounts = true;

        if (class_exists('YXJTO_Payment_Database_Switch_Config')) {
            $config = YXJTO_Payment_Database_Switch_Config::get_instance();
            $precision = $config->get_totals_precision();
            $verify_items = $config->should_verify_item_details();
            $verify_shipping = $config->should_verify_shipping_fees();
            $verify_tax = $config->should_verify_tax_amounts();
            $verify_discounts = $config->should_verify_discounts();
        }

        // 🔧 智能商品替换兼容性修复：
        // 如果启用了智能商品替换，跳过商品详细验证，因为商品ID会发生变化
        // 但总价验证仍然有效，确保订单金额正确
        if ($this->settings['enable_smart_product_replacement'] ?? false) {
            $verify_items = false;
            $this->log_debug("Smart product replacement is enabled, skipping item details verification for order #{$order_id}");
        }

        // 比较基本信息
        if ($source_totals['currency'] !== $default_totals['currency']) {
            $this->log_error("Currency mismatch: source={$source_totals['currency']}, default={$default_totals['currency']} (Order #{$order_id})");
            return false;
        }

        // 比较总价（使用配置的精度）
        $total_diff = abs($source_totals['total'] - $default_totals['total']);
        if ($total_diff > $precision) {
            $this->log_error("Total amount mismatch: source={$source_totals['total']}, default={$default_totals['total']}, diff={$total_diff}, precision={$precision} (Order #{$order_id})");
            return false;
        }

        // 比较商品项目（如果启用了商品详细验证）
        if ($verify_items) {
            if (count($source_totals['items']) !== count($default_totals['items'])) {
                $this->log_error("Item count mismatch: source=" . count($source_totals['items']) . ", default=" . count($default_totals['items']) . " (Order #{$order_id})");
                return false;
            }

            // 比较每个商品项目
            $source_items_by_product = array();
            foreach ($source_totals['items'] as $item) {
                $key = $item['product_id'] . '_' . $item['variation_id'];
                if (!isset($source_items_by_product[$key])) {
                    $source_items_by_product[$key] = array();
                }
                $source_items_by_product[$key][] = $item;
            }

            $default_items_by_product = array();
            foreach ($default_totals['items'] as $item) {
                $key = $item['product_id'] . '_' . $item['variation_id'];
                if (!isset($default_items_by_product[$key])) {
                    $default_items_by_product[$key] = array();
                }
                $default_items_by_product[$key][] = $item;
            }

            // 验证商品项目匹配
            foreach ($source_items_by_product as $product_key => $source_items) {
                if (!isset($default_items_by_product[$product_key])) {
                    $this->log_error("Product missing in default database: {$product_key} (Order #{$order_id})");
                    return false;
                }

                $default_items = $default_items_by_product[$product_key];

                if (count($source_items) !== count($default_items)) {
                    $this->log_error("Product quantity mismatch for {$product_key}: source=" . count($source_items) . ", default=" . count($default_items) . " (Order #{$order_id})");
                    return false;
                }

                // 比较每个商品的详细信息
                for ($i = 0; $i < count($source_items); $i++) {
                    $source_item = $source_items[$i];
                    $default_item = $default_items[$i];

                    if ($source_item['quantity'] !== $default_item['quantity']) {
                        $this->log_error("Item quantity mismatch for {$product_key}: source={$source_item['quantity']}, default={$default_item['quantity']} (Order #{$order_id})");
                        return false;
                    }

                    $item_total_diff = abs($source_item['total'] - $default_item['total']);
                    if ($item_total_diff > $precision) {
                        $this->log_error("Item total mismatch for {$product_key}: source={$source_item['total']}, default={$default_item['total']}, diff={$item_total_diff}, precision={$precision} (Order #{$order_id})");
                        return false;
                    }
                }
            }
        } else {
            $this->log_debug("Item details verification is disabled for order #{$order_id}");
        }

        // 比较运费总额（如果启用了运费验证）
        if ($verify_shipping) {
            $shipping_diff = abs($source_totals['shipping_total'] - $default_totals['shipping_total']);
            if ($shipping_diff > $precision) {
                $this->log_error("Shipping total mismatch: source={$source_totals['shipping_total']}, default={$default_totals['shipping_total']}, diff={$shipping_diff}, precision={$precision} (Order #{$order_id})");
                return false;
            }
        } else {
            $this->log_debug("Shipping verification is disabled for order #{$order_id}");
        }

        // 比较费用总额（始终验证，因为费用通常是重要的）
        $fee_diff = abs($source_totals['fee_total'] - $default_totals['fee_total']);
        if ($fee_diff > $precision) {
            $this->log_error("Fee total mismatch: source={$source_totals['fee_total']}, default={$default_totals['fee_total']}, diff={$fee_diff}, precision={$precision} (Order #{$order_id})");
            return false;
        }

        // 比较税费总额（如果启用了税费验证）
        if ($verify_tax) {
            $tax_diff = abs($source_totals['tax_total'] - $default_totals['tax_total']);
            if ($tax_diff > $precision) {
                $this->log_error("Tax total mismatch: source={$source_totals['tax_total']}, default={$default_totals['tax_total']}, diff={$tax_diff}, precision={$precision} (Order #{$order_id})");
                return false;
            }
        } else {
            $this->log_debug("Tax verification is disabled for order #{$order_id}");
        }

        // 比较折扣总额（如果启用了折扣验证）
        if ($verify_discounts) {
            $discount_diff = abs($source_totals['discount_total'] - $default_totals['discount_total']);
            if ($discount_diff > $precision) {
                $this->log_error("Discount total mismatch: source={$source_totals['discount_total']}, default={$default_totals['discount_total']}, diff={$discount_diff}, precision={$precision} (Order #{$order_id})");
                return false;
            }
        } else {
            $this->log_debug("Discount verification is disabled for order #{$order_id}");
        }

        $this->log_debug("Order totals verification passed for order #{$order_id}");
        return true;
    }

    /**
     * 处理支付后的数据库切换
     */
    public function handle_post_payment_database_switch($order_id) {
        // 检查是否有支付时的数据库切换记录（使用transient API）
        $switch_info = get_transient('yxjto_payment_db_switch_' . $order_id);

        if ($switch_info && is_array($switch_info)) {
            // 验证是否是同一个订单的支付
            if ($switch_info['order_id'] == $order_id && $switch_info['switched_for_payment']) {
                $original_database = $switch_info['original_database'];

                $this->log_debug("Payment completed for order #{$order_id}, considering switch back to '{$original_database}'");

                // 根据配置决定是否切换回原数据库
                $should_switch_back = apply_filters('yxjto_order_replication_switch_back_after_payment', false, $order_id, $original_database);

                if ($should_switch_back && $original_database !== 'default') {
                    $gateway = YXJTO_Gateway::get_instance();
                    $switch_result = $gateway->switch_database($original_database);

                    if ($switch_result) {
                        $this->log_debug("Successfully switched back to '{$original_database}' after payment completion");
                    } else {
                        $this->log_error("Failed to switch back to '{$original_database}' after payment completion");
                    }
                }

                // 清除transient记录
                delete_transient('yxjto_payment_db_switch_' . $order_id);
            }
        }
    }

    /**
     * 复制订单备注
     */
    public function replicate_order_note($note_id, $order) {
        // 处理订单参数 - 可能是订单对象或订单ID
        if (is_object($order)) {
            $order_id = $order->get_id();
            $this->log_debug("Replicating order note #{$note_id} for order object #{$order_id}");
        } else {
            $order_id = intval($order);
            $this->log_debug("Replicating order note #{$note_id} for order #{$order_id}");
        }

        // 备注复制功能独立运行，不依赖于订单复制设置
        $this->log_debug("Processing order note replication (independent of order replication setting)");

        // 获取备注信息
        $note = get_comment($note_id);
        if (!$note) {
            $this->log_error("Note #{$note_id} not found");
            return;
        }

        // 确保这是订单备注
        if ($note->comment_type !== 'order_note') {
            $this->log_debug("Comment #{$note_id} is not an order note, skipping");
            return;
        }

        $note_data = [
            'content' => $note->comment_content ?: '',
            'customer_note' => get_comment_meta($note_id, 'is_customer_note', true) ? 1 : 0,
            'added_by' => $note->comment_author ?: 'system',
            'date_created' => $note->comment_date
        ];

        $this->log_debug("Note data: content='" . substr($note_data['content'], 0, 50) . "...', customer_note={$note_data['customer_note']}, added_by={$note_data['added_by']}");

        $this->replicate_note_to_all_databases($order_id, $note_data);
    }

    /**
     * 复制备注到所有数据库
     */
    private function replicate_note_to_all_databases($order_id, $note_data) {
        // 确保 order_id 是整数
        if (is_object($order_id) && method_exists($order_id, 'get_id')) {
            $order_id = $order_id->get_id();
        } else {
            $order_id = intval($order_id);
        }

        if ($order_id <= 0) {
            $this->log_error("Invalid order ID for note replication: {$order_id}");
            return;
        }

        $current_database = YXJTO_Gateway::get_instance()->get_current_database();
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        $success_count = 0;
        $total_count = 0;

        foreach ($databases as $db_name => $db_config) {
            if ($db_name === $current_database) {
                $this->log_debug("Skipping current database: {$db_name}");
                continue;
            }

            if (!$db_config['enabled']) {
                $this->log_debug("Skipping disabled database: {$db_name}");
                continue;
            }

            $total_count++;
            $this->log_debug("Replicating note to database: {$db_name}");

            if ($this->insert_note_in_database($order_id, $note_data, $db_name, $db_config)) {
                $success_count++;
            }
        }

        $this->log_debug("Note replication completed: {$success_count}/{$total_count} databases successful");
    }

    /**
     * 在指定数据库中插入备注
     */
    private function insert_note_in_database($order_id, $note_data, $db_name, $db_config) {
        try {
            // 确保 order_id 是整数
            $order_id = intval($order_id);
            if ($order_id <= 0) {
                throw new Exception("Invalid order ID: {$order_id}");
            }

            // 首先检查订单是否存在于目标数据库
            $connection = $this->create_database_connection($db_config);
            if (!$connection) {
                throw new Exception("Failed to connect to database: {$db_name}");
            }

            $table_prefix = $this->get_table_prefix_from_connection($connection);

            // 检查订单是否存在 - 支持HPOS和传统模式
            $order_exists = false;

            // 首先尝试HPOS表
            $hpos_check_sql = "SELECT id FROM {$table_prefix}wc_orders WHERE id = ?";
            $hpos_stmt = $connection->prepare($hpos_check_sql);
            if ($hpos_stmt) {
                $hpos_stmt->bind_param('i', $order_id);
                $hpos_stmt->execute();
                $hpos_result = $hpos_stmt->get_result();
                if ($hpos_result->num_rows > 0) {
                    $order_exists = true;
                }
                $hpos_stmt->close();
            }

            // 如果HPOS中没有找到，尝试传统posts表
            if (!$order_exists) {
                $posts_check_sql = "SELECT ID FROM {$table_prefix}posts WHERE ID = ? AND post_type = 'shop_order'";
                $posts_stmt = $connection->prepare($posts_check_sql);
                if ($posts_stmt) {
                    $posts_stmt->bind_param('i', $order_id);
                    $posts_stmt->execute();
                    $posts_result = $posts_stmt->get_result();
                    if ($posts_result->num_rows > 0) {
                        $order_exists = true;
                    }
                    $posts_stmt->close();
                }
            }

            if (!$order_exists) {
                throw new Exception("Order #{$order_id} does not exist in database {$db_name}");
            }

            // 检查备注是否已经存在（避免重复）
            $note_check_sql = "SELECT comment_ID FROM {$table_prefix}comments
                              WHERE comment_post_ID = ? AND comment_content = ? AND comment_type = 'order_note'";
            $note_stmt = $connection->prepare($note_check_sql);
            if (!$note_stmt) {
                throw new Exception("Failed to prepare note check: " . $connection->error);
            }

            $note_stmt->bind_param('is', $order_id, $note_data['content']);
            $note_stmt->execute();
            $note_result = $note_stmt->get_result();

            if ($note_result->num_rows > 0) {
                $this->log_debug("Note already exists in {$db_name} for order #{$order_id}, skipping");
                return true; // 已存在，视为成功
            }

            // 插入备注
            $sql = "INSERT INTO {$table_prefix}comments (
                comment_post_ID, comment_author, comment_author_email, comment_author_url,
                comment_author_IP, comment_date, comment_date_gmt, comment_content,
                comment_karma, comment_approved, comment_agent, comment_type, comment_parent
            ) VALUES (?, ?, '', '', '', ?, ?, ?, 0, 1, '', 'order_note', 0)";

            $stmt = $connection->prepare($sql);
            if (!$stmt) {
                throw new Exception("Failed to prepare note insert: " . $connection->error);
            }

            $date_gmt = gmdate('Y-m-d H:i:s', strtotime($note_data['date_created']));

            $stmt->bind_param(
                'issss',
                $order_id,
                $note_data['added_by'],
                $note_data['date_created'],
                $date_gmt,
                $note_data['content']
            );

            if (!$stmt->execute()) {
                throw new Exception("Failed to insert note: " . $stmt->error);
            }

            $comment_id = $connection->insert_id;

            // 插入备注元数据
            $this->insert_comment_meta($comment_id, 'is_customer_note', $note_data['customer_note'], $connection, $table_prefix);

            $this->log_debug("Successfully replicated note for order #{$order_id} to {$db_name} (comment_id: {$comment_id})");
            return true;

        } catch (Exception $e) {
            $this->log_error("Failed to replicate note to {$db_name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 复制订单元数据更新
     */
    public function replicate_order_meta($meta_id, $object_id, $meta_key, $meta_value) {
        // 检查是否是订单相关的元数据
        if (!$this->is_order_meta($object_id, $meta_key)) {
            return;
        }

        $this->log_debug("Replicating meta update for order #{$object_id}: {$meta_key}");

        $current_database = YXJTO_Gateway::get_instance()->get_current_database();
        $databases = WP_Multi_DB_Config_Manager::get_databases();

        foreach ($databases as $db_name => $db_config) {
            if ($db_name === $current_database || !$db_config['enabled']) {
                continue;
            }

            $this->update_meta_in_database($object_id, $meta_key, $meta_value, $db_name, $db_config);
        }
    }

    /**
     * 检查是否是订单元数据
     */
    private function is_order_meta($object_id, $meta_key) {
        // 检查对象是否是订单
        $order = wc_get_order($object_id);
        if (!$order) {
            return false;
        }

        // 检查是否是需要复制的元数据键
        $replicated_meta_keys = [
            '_payment_method',
            '_payment_method_title',
            '_transaction_id',
            '_order_total',
            '_order_tax',
            '_order_shipping',
            '_billing_',
            '_shipping_',
            '_paypal_multi_gateway_'
        ];

        foreach ($replicated_meta_keys as $key_prefix) {
            if (strpos($meta_key, $key_prefix) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 在指定数据库中更新元数据
     */
    private function update_meta_in_database($order_id, $meta_key, $meta_value, $db_name, $db_config) {
        try {
            $connection = $this->create_database_connection($db_config);
            if (!$connection) {
                throw new Exception("Failed to connect to database: {$db_name}");
            }

            $table_prefix = $this->get_table_prefix_from_connection($connection);

            $this->update_order_meta_in_database($order_id, $meta_key, $meta_value, $connection, $table_prefix);

            $this->log_debug("Successfully updated meta {$meta_key} for order #{$order_id} in {$db_name}");

        } catch (Exception $e) {
            $this->log_error("Failed to update meta in {$db_name}: " . $e->getMessage());
        }
    }

    /**
     * 记录调试信息
     */
    private function log_debug($message) {
        if ($this->debug && YXJTO_GATEWAY_DEBUG) {
            // 处理各种数据类型，确保转换为字符串
            if (is_array($message) || is_object($message)) {
                $message = json_encode($message, JSON_UNESCAPED_UNICODE);
            } elseif (!is_string($message)) {
                $message = (string) $message;
            }
            error_log("[YXJTO Order Replication DEBUG] " . $message);
        }
    }

    /**
     * 记录错误信息
     */
    private function log_error($message) {
        // 处理各种数据类型，确保转换为字符串
        if (is_array($message) || is_object($message)) {
            $message = json_encode($message, JSON_UNESCAPED_UNICODE);
        } elseif (!is_string($message)) {
            $message = (string) $message;
        }
        error_log("[YXJTO Order Replication ERROR] " . $message);
    }

    /**
     * 提取账单地址信息
     */
    private function extract_billing_address($order) {
        $billing_address = [];

        // 使用getter方法确保获取正确的数据
        $billing_address['first_name'] = $order->get_billing_first_name();
        $billing_address['last_name'] = $order->get_billing_last_name();
        $billing_address['company'] = $order->get_billing_company();
        $billing_address['address_1'] = $order->get_billing_address_1();
        $billing_address['address_2'] = $order->get_billing_address_2();
        $billing_address['city'] = $order->get_billing_city();
        $billing_address['state'] = $order->get_billing_state();
        $billing_address['postcode'] = $order->get_billing_postcode();
        $billing_address['country'] = $order->get_billing_country();
        $billing_address['email'] = $order->get_billing_email();
        $billing_address['phone'] = $order->get_billing_phone();

        $this->log_debug("Extracted billing address: email=" . $billing_address['email'] . ", phone=" . $billing_address['phone']);

        return $billing_address;
    }

    /**
     * 提取配送地址信息
     */
    private function extract_shipping_address($order) {
        $shipping_address = [];

        // 使用getter方法确保获取正确的数据
        $shipping_address['first_name'] = $order->get_shipping_first_name();
        $shipping_address['last_name'] = $order->get_shipping_last_name();
        $shipping_address['company'] = $order->get_shipping_company();
        $shipping_address['address_1'] = $order->get_shipping_address_1();
        $shipping_address['address_2'] = $order->get_shipping_address_2();
        $shipping_address['city'] = $order->get_shipping_city();
        $shipping_address['state'] = $order->get_shipping_state();
        $shipping_address['postcode'] = $order->get_shipping_postcode();
        $shipping_address['country'] = $order->get_shipping_country();
        $shipping_address['phone'] = $order->get_shipping_phone();

        $this->log_debug("Extracted shipping address: address_1=" . $shipping_address['address_1'] . ", city=" . $shipping_address['city'] . ", phone=" . $shipping_address['phone']);

        return $shipping_address;
    }

    /**
     * 安全清除订单缓存，不触发任何钩子
     */
    private function clear_order_cache_safe($order_id, $db_name) {
        try {
            $this->log_debug("Safely clearing cache for order #{$order_id} in database {$db_name}");

            // 保存当前数据库
            $current_database = YXJTO_Gateway::get_instance()->get_current_database();

            // 切换到目标数据库
            YXJTO_Gateway::get_instance()->switch_database($db_name);

            // 只清除缓存，不做任何其他操作
            wc_delete_shop_order_transients($order_id);
            wp_cache_delete($order_id, 'post_meta');
            wp_cache_delete($order_id, 'posts');

            // 清除WooCommerce对象缓存
            if (function_exists('WC_Cache_Helper')) {
                $cache_key = WC_Cache_Helper::get_cache_prefix('orders') . 'order_' . $order_id;
                wp_cache_delete($cache_key, 'orders');
            }

            // 如果使用HPOS，清除HPOS相关缓存
            if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
                Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {
                wp_cache_delete('order_' . $order_id, 'wc_orders');
            }

            $this->log_debug("Successfully cleared cache for order #{$order_id} in database {$db_name}");

            // 切换回原数据库
            YXJTO_Gateway::get_instance()->switch_database($current_database);

            return true;

        } catch (Exception $e) {
            $this->log_error("Failed to refresh order #{$order_id} in database {$db_name}: " . $e->getMessage());

            // 确保切换回原数据库
            if (isset($current_database)) {
                YXJTO_Gateway::get_instance()->switch_database($current_database);
            }

            return false;
        }
    }

    /**
     * 立即修复订单显示（只执行一次，避免循环）
     */
    private function fix_order_display_once($order_id, $db_name, $order_data) {
        // 使用静态变量确保每个订单只修复一次
        static $fixed_orders = [];
        $fix_key = "{$order_id}_{$db_name}";

        if (isset($fixed_orders[$fix_key])) {
            $this->log_debug("Order #{$order_id} in {$db_name} already fixed, skipping");
            return;
        }

        try {
            $this->log_debug("Fixing display for order #{$order_id} in {$db_name}");

            // 保存当前数据库
            $current_database = YXJTO_Gateway::get_instance()->get_current_database();

            // 切换到目标数据库
            YXJTO_Gateway::get_instance()->switch_database($db_name);

            // 获取订单对象
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_error("Order #{$order_id} not found in {$db_name} during display fix");
                YXJTO_Gateway::get_instance()->switch_database($current_database);
                return;
            }

            // 临时移除复制钩子避免循环
            $removed_hooks = [];
            if (has_action('woocommerce_update_order', [$this, 'on_order_updated'])) {
                remove_action('woocommerce_update_order', [$this, 'on_order_updated']);
                $removed_hooks[] = 'woocommerce_update_order';
            }
            if (has_action('save_post', [$this, 'on_order_saved'])) {
                remove_action('save_post', [$this, 'on_order_saved']);
                $removed_hooks[] = 'save_post';
            }

            // 使用setter方法设置完整的地址信息
            $updated = false;

            if (isset($order_data['billing_address']) && is_array($order_data['billing_address'])) {
                $billing = $order_data['billing_address'];

                // 设置所有账单地址字段
                if (isset($billing['first_name']) && $order->get_billing_first_name() !== $billing['first_name']) {
                    $order->set_billing_first_name($billing['first_name']);
                    $updated = true;
                }
                if (isset($billing['last_name']) && $order->get_billing_last_name() !== $billing['last_name']) {
                    $order->set_billing_last_name($billing['last_name']);
                    $updated = true;
                }
                if (isset($billing['company']) && $order->get_billing_company() !== $billing['company']) {
                    $order->set_billing_company($billing['company']);
                    $updated = true;
                }
                if (isset($billing['address_1']) && $order->get_billing_address_1() !== $billing['address_1']) {
                    $order->set_billing_address_1($billing['address_1']);
                    $updated = true;
                }
                if (isset($billing['address_2']) && $order->get_billing_address_2() !== $billing['address_2']) {
                    $order->set_billing_address_2($billing['address_2']);
                    $updated = true;
                }
                if (isset($billing['city']) && $order->get_billing_city() !== $billing['city']) {
                    $order->set_billing_city($billing['city']);
                    $updated = true;
                }
                if (isset($billing['state']) && $order->get_billing_state() !== $billing['state']) {
                    $order->set_billing_state($billing['state']);
                    $updated = true;
                }
                if (isset($billing['postcode']) && $order->get_billing_postcode() !== $billing['postcode']) {
                    $order->set_billing_postcode($billing['postcode']);
                    $updated = true;
                }
                if (isset($billing['country']) && $order->get_billing_country() !== $billing['country']) {
                    $order->set_billing_country($billing['country']);
                    $updated = true;
                }
                if (isset($billing['email']) && $order->get_billing_email() !== $billing['email']) {
                    $order->set_billing_email($billing['email']);
                    $updated = true;
                }
                if (isset($billing['phone']) && $order->get_billing_phone() !== $billing['phone']) {
                    $order->set_billing_phone($billing['phone']);
                    $updated = true;
                }
            }

            if (isset($order_data['shipping_address']) && is_array($order_data['shipping_address'])) {
                $shipping = $order_data['shipping_address'];

                // 设置所有配送地址字段
                if (isset($shipping['first_name']) && $order->get_shipping_first_name() !== $shipping['first_name']) {
                    $order->set_shipping_first_name($shipping['first_name']);
                    $updated = true;
                }
                if (isset($shipping['last_name']) && $order->get_shipping_last_name() !== $shipping['last_name']) {
                    $order->set_shipping_last_name($shipping['last_name']);
                    $updated = true;
                }
                if (isset($shipping['company']) && $order->get_shipping_company() !== $shipping['company']) {
                    $order->set_shipping_company($shipping['company']);
                    $updated = true;
                }
                if (isset($shipping['address_1']) && $order->get_shipping_address_1() !== $shipping['address_1']) {
                    $order->set_shipping_address_1($shipping['address_1']);
                    $updated = true;
                }
                if (isset($shipping['address_2']) && $order->get_shipping_address_2() !== $shipping['address_2']) {
                    $order->set_shipping_address_2($shipping['address_2']);
                    $updated = true;
                }
                if (isset($shipping['city']) && $order->get_shipping_city() !== $shipping['city']) {
                    $order->set_shipping_city($shipping['city']);
                    $updated = true;
                }
                if (isset($shipping['state']) && $order->get_shipping_state() !== $shipping['state']) {
                    $order->set_shipping_state($shipping['state']);
                    $updated = true;
                }
                if (isset($shipping['postcode']) && $order->get_shipping_postcode() !== $shipping['postcode']) {
                    $order->set_shipping_postcode($shipping['postcode']);
                    $updated = true;
                }
                if (isset($shipping['country']) && $order->get_shipping_country() !== $shipping['country']) {
                    $order->set_shipping_country($shipping['country']);
                    $updated = true;
                }
                if (isset($shipping['phone']) && $order->get_shipping_phone() !== $shipping['phone']) {
                    $order->set_shipping_phone($shipping['phone']);
                    $updated = true;
                }
            }

            // 只有在确实需要更新时才保存
            if ($updated) {
                $order->save();
                $this->log_debug("Fixed and saved display for order #{$order_id} in {$db_name}");
            }

            // 恢复移除的钩子
            foreach ($removed_hooks as $hook_name) {
                if ($hook_name === 'woocommerce_update_order') {
                    add_action('woocommerce_update_order', [$this, 'on_order_updated']);
                } elseif ($hook_name === 'save_post') {
                    add_action('save_post', [$this, 'on_order_saved']);
                }
            }

            // 标记为已修复
            $fixed_orders[$fix_key] = true;

            // 切换回原数据库
            YXJTO_Gateway::get_instance()->switch_database($current_database);

        } catch (Exception $e) {
            $this->log_error("Failed to fix display for order #{$order_id} in {$db_name}: " . $e->getMessage());

            // 确保切换回原数据库
            if (isset($current_database)) {
                YXJTO_Gateway::get_instance()->switch_database($current_database);
            }
        }
    }
    
    /**
     * 订单创建后清空购物车（主要方法）
     * 在 woocommerce_checkout_order_processed 钩子中调用
     */
    public function clear_cart_after_order_creation($order_id) {
        if (!function_exists('WC') || !WC()->cart) {
            return;
        }
        
        // 检查购物车是否已经为空
        if (WC()->cart->is_empty()) {
            $this->log_debug("Cart is already empty for order #{$order_id}");
            return;
        }
        
        try {
            WC()->cart->empty_cart();
            $this->log_debug("SUCCESS: Cart cleared after order creation for order #{$order_id}");
            
            // 设置一个标志，表示购物车已被清空
            if (!headers_sent()) {
                setcookie('cart_cleared_for_order_' . $order_id, '1', time() + 3600, '/');
            }
            
        } catch (Exception $e) {
            $this->log_error("Failed to clear cart after order creation for order #{$order_id}: " . $e->getMessage());
        }
    }
    
    /**
     * 新订单创建后清空购物车（备用方法）
     * 在 woocommerce_new_order 钩子中调用，作为备用机制
     */
    public function clear_cart_after_new_order($order_id) {
        if (!function_exists('WC') || !WC()->cart) {
            return;
        }
        
        // 检查是否已经通过其他钩子清空过购物车
        if (isset($_COOKIE['cart_cleared_for_order_' . $order_id])) {
            $this->log_debug("Cart already cleared for order #{$order_id}, skipping backup clear");
            return;
        }
        
        // 检查购物车是否已经为空
        if (WC()->cart->is_empty()) {
            $this->log_debug("Cart is already empty for new order #{$order_id}");
            return;
        }
        
        try {
            WC()->cart->empty_cart();
            $this->log_debug("SUCCESS: Cart cleared after new order creation for order #{$order_id} (backup mechanism)");
            
        } catch (Exception $e) {
            $this->log_error("Failed to clear cart after new order creation for order #{$order_id}: " . $e->getMessage());
        }
    }
}
