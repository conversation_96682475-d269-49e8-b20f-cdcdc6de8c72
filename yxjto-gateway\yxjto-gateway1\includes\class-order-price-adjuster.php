<?php
/**
 * 订单价格调整器
 * 负责在支付前调整订单价格，确保价格一致性和合理性
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 订单价格调整器类
 */
class YXJTO_Order_Price_Adjuster {
    
    /**
     * 价格精度（允许的差异）
     */
    const PRICE_PRECISION = 0.01;
    
    /**
     * 最小商品价格（防止负价格）
     */
    const MIN_ITEM_PRICE = 0.01;
    
    /**
     * 日志记录器
     */
    private $logger;

    /**
     * 价格调整策略
     */
    private $strategy;

    /**
     * 配置选项
     */
    private $config;

    /**
     * 构造函数
     */
    public function __construct($logger = null, $config = []) {
        $this->logger = $logger;
        $this->config = array_merge([
            'strategy' => 'auto', // auto, proportional, equal_distribution, highest_price_first
            'enable_validation' => true,
            'enable_recalculation' => true,
            'min_adjustment_threshold' => self::PRICE_PRECISION
        ], $config);

        // 加载策略接口
        $this->load_strategy_interface();
    }

    /**
     * 加载策略接口
     */
    private function load_strategy_interface() {
        $strategy_file = dirname(__FILE__) . '/interfaces/interface-price-adjustment-strategy.php';
        if (file_exists($strategy_file)) {
            require_once $strategy_file;
        }
    }
    
    /**
     * 调整订单价格主入口
     * 
     * @param WC_Order $order 订单对象
     * @return bool 是否进行了调整
     */
    public function adjust_order_pricing($order) {
        try {
            $order_id = $order->get_id();
            $this->log_debug("Price adjustment started for order #{$order_id}");
            
            // 1. 获取订单各项金额
            $amounts = $this->extract_order_amounts($order);
            $this->log_debug("Original amounts extracted", $amounts);
            
            // 2. 验证运费、折扣和税费数据
            $validated_amounts = $this->validate_order_amounts($order, $amounts);
            $this->log_debug("Amounts after validation", $validated_amounts);
            
            // 3. 计算总额差异
            $difference = $this->calculate_total_difference($order, $validated_amounts);
            $this->log_debug("Calculated difference: {$difference}");
            
            // 4. 如有差异，调整商品价格
            if (abs($difference) > self::PRICE_PRECISION) {
                return $this->adjust_item_prices($order, $difference);
            }
            
            $this->log_debug("No price adjustment needed for order #{$order_id}");
            return false;
            
        } catch (Exception $e) {
            $this->log_error("Price adjustment error for order #{$order->get_id()}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 提取订单各项金额
     * 
     * @param WC_Order $order 订单对象
     * @return array 金额数组
     */
    private function extract_order_amounts($order) {
        return [
            'subtotal' => $order->get_subtotal(),
            'tax_total' => $order->get_total_tax(),
            'shipping_total' => $order->get_shipping_total(),
            'discount_total' => $order->get_total_discount(),
            'order_total' => $order->get_total()
        ];
    }
    
    /**
     * 验证订单各项金额的准确性
     * 
     * @param WC_Order $order 订单对象
     * @param array $amounts 原始金额
     * @return array 验证后的金额
     */
    private function validate_order_amounts($order, $amounts) {
        $validated = $amounts;
        
        // 验证运费数据
        $validated['shipping_total'] = $this->validate_shipping_amount($order, $amounts['shipping_total']);
        
        // 验证折扣数据
        $validated['discount_total'] = $this->validate_discount_amount($order, $amounts['discount_total']);
        
        // 验证税费数据（可选，通常税费计算比较准确）
        $validated['tax_total'] = $this->validate_tax_amount($order, $amounts['tax_total']);
        
        return $validated;
    }
    
    /**
     * 验证运费金额
     * 
     * @param WC_Order $order 订单对象
     * @param float $original_shipping 原始运费
     * @return float 验证后的运费
     */
    private function validate_shipping_amount($order, $original_shipping) {
        $shipping_items = $order->get_items('shipping');
        $calculated_shipping = 0;
        
        foreach ($shipping_items as $shipping_item) {
            $calculated_shipping += $shipping_item->get_total();
        }
        
        // 如果计算出的运费与原始运费差异较大，使用计算值
        if (abs($original_shipping - $calculated_shipping) > self::PRICE_PRECISION && $calculated_shipping > 0) {
            $this->log_debug("Shipping amount adjusted: {$original_shipping} -> {$calculated_shipping}");
            return $calculated_shipping;
        }
        
        return $original_shipping;
    }
    
    /**
     * 验证折扣金额
     * 
     * @param WC_Order $order 订单对象
     * @param float $original_discount 原始折扣
     * @return float 验证后的折扣
     */
    private function validate_discount_amount($order, $original_discount) {
        $coupon_items = $order->get_items('coupon');
        $calculated_discount = 0;
        
        foreach ($coupon_items as $coupon_item) {
            $calculated_discount += $coupon_item->get_discount();
        }
        
        // 如果计算出的折扣与原始折扣差异较大，使用计算值
        if (abs($original_discount - $calculated_discount) > self::PRICE_PRECISION && $calculated_discount > 0) {
            $this->log_debug("Discount amount adjusted: {$original_discount} -> {$calculated_discount}");
            return $calculated_discount;
        }
        
        return $original_discount;
    }
    
    /**
     * 验证税费金额
     * 
     * @param WC_Order $order 订单对象
     * @param float $original_tax 原始税费
     * @return float 验证后的税费
     */
    private function validate_tax_amount($order, $original_tax) {
        // 税费验证逻辑（可根据需要扩展）
        $tax_items = $order->get_items('tax');
        $calculated_tax = 0;
        
        foreach ($tax_items as $tax_item) {
            $calculated_tax += $tax_item->get_tax_total();
        }
        
        // 如果计算出的税费与原始税费差异较大，记录但不调整（税费通常由系统准确计算）
        if (abs($original_tax - $calculated_tax) > self::PRICE_PRECISION) {
            $this->log_debug("Tax amount difference detected: original={$original_tax}, calculated={$calculated_tax}");
        }
        
        return $original_tax;
    }
    
    /**
     * 计算总额差异
     * 
     * @param WC_Order $order 订单对象
     * @param array $validated_amounts 验证后的金额
     * @return float 差异金额
     */
    private function calculate_total_difference($order, $validated_amounts) {
        $calculated_total = $validated_amounts['subtotal'] 
                          + $validated_amounts['tax_total'] 
                          + $validated_amounts['shipping_total'] 
                          - $validated_amounts['discount_total'];
        
        $difference = $validated_amounts['order_total'] - $calculated_total;
        
        $this->log_debug("Total calculation: subtotal({$validated_amounts['subtotal']}) + tax({$validated_amounts['tax_total']}) + shipping({$validated_amounts['shipping_total']}) - discount({$validated_amounts['discount_total']}) = {$calculated_total}");
        $this->log_debug("Order total: {$validated_amounts['order_total']}, Calculated total: {$calculated_total}, Difference: {$difference}");
        
        return $difference;
    }
    
    /**
     * 调整商品价格以匹配订单总额
     * 
     * @param WC_Order $order 订单对象
     * @param float $difference 需要调整的差异
     * @return bool 是否调整成功
     */
    private function adjust_item_prices($order, $difference) {
        $order_id = $order->get_id();
        $items = $order->get_items();
        
        if (empty($items)) {
            $this->log_debug("No items found for price adjustment in order #{$order_id}");
            return false;
        }
        
        $this->log_debug("Adjusting item prices for order #{$order_id}, difference: {$difference}");
        
        // 提取商品数据
        $item_data = $this->extract_item_data($items);
        
        // 计算目标商品总价
        $current_items_total = array_sum(array_column($item_data, 'original_total'));
        $target_items_total = $current_items_total + $difference;
        
        // 验证目标总价的合理性
        if (!$this->validate_target_total($target_items_total, $item_data)) {
            return false;
        }
        
        // 按比例分配价格调整
        return $this->distribute_price_adjustment($item_data, $target_items_total);
    }
    
    /**
     * 提取商品数据
     * 
     * @param array $items 订单商品项目
     * @return array 商品数据数组
     */
    private function extract_item_data($items) {
        $item_data = [];
        
        foreach ($items as $item_id => $item) {
            $item_total = $item->get_total();
            $quantity = $item->get_quantity();
            
            $item_data[] = [
                'item_id' => $item_id,
                'item' => $item,
                'original_total' => $item_total,
                'quantity' => $quantity,
                'unit_price' => $quantity > 0 ? $item_total / $quantity : $item_total,
                'name' => $item->get_name()
            ];
        }
        
        return $item_data;
    }
    
    /**
     * 验证目标总价的合理性
     * 
     * @param float $target_total 目标总价
     * @param array $item_data 商品数据
     * @return bool 是否合理
     */
    private function validate_target_total($target_total, $item_data) {
        if ($target_total <= 0) {
            $this->log_debug("Target items total is zero or negative ({$target_total}), skipping adjustment");
            return false;
        }
        
        // 检查是否会导致商品价格过低
        $min_required_total = count($item_data) * self::MIN_ITEM_PRICE;
        if ($target_total < $min_required_total) {
            $this->log_debug("Target total ({$target_total}) would result in prices below minimum ({$min_required_total}), skipping adjustment");
            return false;
        }
        
        return true;
    }
    
    /**
     * 按策略分配价格调整
     *
     * @param array $item_data 商品数据
     * @param float $target_total 目标总价
     * @return bool 是否调整成功
     */
    private function distribute_price_adjustment($item_data, $target_total) {
        $current_total = array_sum(array_column($item_data, 'original_total'));
        $difference = $target_total - $current_total;

        // 选择调整策略
        $strategy = $this->get_adjustment_strategy($item_data, $difference);
        if (!$strategy) {
            $this->log_error("No suitable price adjustment strategy found");
            return false;
        }

        $this->log_debug("Using price adjustment strategy: " . $strategy->get_strategy_name());
        $this->log_debug("Current items total: {$current_total}, Target items total: {$target_total}, Difference: {$difference}");

        // 使用策略调整价格
        $adjusted_items = $strategy->adjust_prices($item_data, $target_total, $current_total);

        // 应用价格调整
        return $this->apply_price_adjustments($adjusted_items);
    }

    /**
     * 获取价格调整策略
     *
     * @param array $item_data 商品数据
     * @param float $difference 价格差异
     * @return YXJTO_Price_Adjustment_Strategy_Interface|null 调整策略
     */
    private function get_adjustment_strategy($item_data, $difference) {
        if (!class_exists('YXJTO_Price_Adjustment_Strategy_Factory')) {
            $this->log_error("Price adjustment strategy factory not available");
            return null;
        }

        if ($this->config['strategy'] === 'auto') {
            // 自动选择最适合的策略
            return YXJTO_Price_Adjustment_Strategy_Factory::get_best_strategy($item_data, $difference);
        } else {
            // 使用指定的策略
            return YXJTO_Price_Adjustment_Strategy_Factory::create_strategy($this->config['strategy']);
        }
    }

    /**
     * 应用价格调整
     *
     * @param array $adjusted_items 调整后的商品数据
     * @return bool 是否应用成功
     */
    private function apply_price_adjustments($adjusted_items) {
        $success = true;
        $order = null;

        foreach ($adjusted_items as $item_info) {
            try {
                $this->update_item_price($item_info['item'], $item_info['new_total']);
                $this->log_debug("Item '{$item_info['name']}': {$item_info['original_total']} -> {$item_info['new_total']} (unit: {$item_info['new_unit_price']})");

                if (!$order) {
                    $order = $item_info['item']->get_order();
                }
            } catch (Exception $e) {
                $this->log_error("Failed to update price for item '{$item_info['name']}': " . $e->getMessage());
                $success = false;
            }
        }

        // 重新计算订单总额
        if ($order && $this->config['enable_recalculation']) {
            $this->recalculate_order_totals($order);
        }

        return $success;
    }
    
    /**
     * 更新商品价格
     * 
     * @param WC_Order_Item_Product $item 商品项目
     * @param float $new_total 新的总价
     */
    private function update_item_price($item, $new_total) {
        $item->set_total($new_total);
        $item->set_subtotal($new_total);
        $item->save();
    }
    
    /**
     * 重新计算订单总额
     * 
     * @param WC_Order $order 订单对象
     */
    private function recalculate_order_totals($order) {
        $order->calculate_totals();
        $order->save();
        
        $new_total = $order->get_total();
        $this->log_debug("Order totals recalculated. New total: {$new_total}");
    }
    
    /**
     * 记录调试日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    private function log_debug($message, $context = []) {
        try {
            if ($this->logger) {
                if (!empty($context)) {
                    $message .= ' - ' . json_encode($context);
                }

                // 检查logger是否有log_debug方法
                if (method_exists($this->logger, 'log_debug')) {
                    $this->logger->log_debug($message);
                } elseif (is_callable([$this->logger, 'log_debug'])) {
                    call_user_func([$this->logger, 'log_debug'], $message);
                } else {
                    // 回退到基本的错误日志
                    error_log("Price Adjuster [Debug]: " . $message);
                }
            }
        } catch (Exception $e) {
            // 如果日志记录失败，使用基本的错误日志
            error_log("Price Adjuster [Debug Log Error]: " . $e->getMessage() . " - Original message: " . $message);
        }
    }

    /**
     * 记录错误日志
     *
     * @param string $message 错误消息
     */
    private function log_error($message) {
        try {
            if ($this->logger) {
                // 检查logger是否有log_error方法
                if (method_exists($this->logger, 'log_error')) {
                    $this->logger->log_error($message);
                } elseif (is_callable([$this->logger, 'log_error'])) {
                    call_user_func([$this->logger, 'log_error'], $message);
                } else {
                    // 回退到基本的错误日志
                    error_log("Price Adjuster [Error]: " . $message);
                }
            } else {
                // 没有logger时直接使用错误日志
                error_log("Price Adjuster [Error]: " . $message);
            }
        } catch (Exception $e) {
            // 如果日志记录失败，使用基本的错误日志
            error_log("Price Adjuster [Error Log Error]: " . $e->getMessage() . " - Original message: " . $message);
        }
    }
}
