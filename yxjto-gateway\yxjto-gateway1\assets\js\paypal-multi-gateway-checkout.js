/**
 * YXJTO PayPal Multi Gateway Checkout JavaScript
 * 参考 WooCommerce PayPal Payments 的前端实现
 */

(function($) {
    'use strict';

    var YXJTOPayPalMultiGatewayCheckout = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.updatePaymentMethod();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;

            // 监听支付方式变化
            $(document.body).on('change', 'input[name="payment_method"]', function() {
                self.updatePaymentMethod();
            });

            // 监听结账表单提交
            $(document.body).on('checkout_place_order_yxjto_paypal_multi_gateway', function() {
                return self.validateOrder();
            });

            // 监听 WooCommerce 更新结账事件
            $(document.body).on('updated_checkout', function() {
                self.updatePaymentMethod();
            });

            // 处理支付按钮点击
            $(document).on('click', '.yxjto-paypal-multi-gateway-button', function(e) {
                e.preventDefault();
                self.processPayment();
            });

            // 处理账户切换按钮
            $(document).on('click', '.yxjto-paypal-switch-account-btn', function(e) {
                e.preventDefault();
                self.switchAccount();
            });
        },

        /**
         * Update payment method display
         */
        updatePaymentMethod: function() {
            var selectedMethod = $('input[name="payment_method"]:checked').val();
            
            if (selectedMethod === 'yxjto_paypal_multi_gateway') {
                this.showPayPalOptions();
            } else {
                this.hidePayPalOptions();
            }
        },

        /**
         * Show PayPal payment options
         */
        showPayPalOptions: function() {
            var $paymentBox = $('.payment_method_yxjto_paypal_multi_gateway');
            
            if ($paymentBox.length === 0) {
                return;
            }

            // 添加 PayPal 特定的样式类
            $paymentBox.addClass('yxjto-paypal-multi-gateway-active');

            // 如果还没有添加额外的支付信息，则添加
            if ($paymentBox.find('.yxjto-paypal-payment-info').length === 0) {
                this.addPaymentInfo($paymentBox);
            }

            // 显示加载动画（如果需要）
            this.checkAccountStatus();
        },

        /**
         * Hide PayPal payment options
         */
        hidePayPalOptions: function() {
            $('.payment_method_yxjto_paypal_multi_gateway').removeClass('yxjto-paypal-multi-gateway-active');
        },

        /**
         * Add payment information
         */
        addPaymentInfo: function($container) {
            var infoHtml = '<div class="yxjto-paypal-payment-info">';
            
            // 添加安全提示
            infoHtml += '<div class="yxjto-paypal-security-info">';
            infoHtml += '<span class="yxjto-paypal-security-icon">🔒</span>';
            infoHtml += '<span>' + (yxjto_paypal_multi_gateway_params.strings.security_info || 'Your payment information is securely protected by PayPal') + '</span>';
            infoHtml += '</div>';

            // 添加处理信息
            infoHtml += '<div class="yxjto-paypal-process-info">';
            infoHtml += '<p>' + (yxjto_paypal_multi_gateway_params.strings.payment_process_info || 'After clicking "Pay Now", you will be redirected to PayPal to complete payment.') + '</p>';
            infoHtml += '</div>';

            // 添加账户状态区域
            infoHtml += '<div class="yxjto-paypal-account-status" id="yxjto-paypal-account-status"></div>';

            infoHtml += '</div>';

            $container.find('.payment_box').append(infoHtml);
        },

        /**
         * Check account status
         */
        checkAccountStatus: function() {
            var self = this;
            var $statusContainer = $('#yxjto-paypal-account-status');

            if ($statusContainer.length === 0) {
                return;
            }

            // 显示检查状态
            $statusContainer.html('<div class="yxjto-paypal-status-warning">' +
                '<span class="yxjto-paypal-spinner"></span>' +
                '<span>' + (yxjto_paypal_multi_gateway_params.strings.checking_status || 'Checking PayPal status...') + '</span>' +
                '</div>');

            $.ajax({
                url: yxjto_paypal_multi_gateway_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'yxjto_paypal_multi_gateway_check_status',
                    nonce: yxjto_paypal_multi_gateway_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var accountsCount = response.data.accounts_count || 0;
                        
                        if (accountsCount > 0) {
                            var statusHtml = '<div class="yxjto-paypal-status-success">';
                            statusHtml += '<span>✓</span>';
                            statusHtml += '<span>' + yxjto_paypal_multi_gateway_params.strings.accounts_available.replace('%d', accountsCount) + '</span>';
                            statusHtml += '</div>';
                            
                            // 添加账户切换按钮（如果有多个账户）
                            if (accountsCount > 1) {
                                statusHtml += '<div class="yxjto-paypal-account-switcher">';
                                statusHtml += '<button type="button" class="yxjto-paypal-switch-account-btn">';
                                statusHtml += yxjto_paypal_multi_gateway_params.strings.switch_account || '🔄 Switch Account';
                                statusHtml += '</button>';
                                statusHtml += '</div>';
                            }
                            
                            $statusContainer.html(statusHtml);
                        } else {
                            $statusContainer.html('<div class="yxjto-paypal-status-error">' +
                                '<span>⚠</span>' +
                                '<span>' + (yxjto_paypal_multi_gateway_params.strings.no_accounts_available || 'No PayPal accounts available') + '</span>' +
                                '</div>');
                        }
                    } else {
                        $statusContainer.html('<div class="yxjto-paypal-status-error">' +
                            '<span>⚠</span>' +
                            '<span>' + (yxjto_paypal_multi_gateway_params.strings.status_check_failed || 'Unable to check PayPal account status') + '</span>' +
                            '</div>');
                    }
                },
                error: function() {
                    $statusContainer.html('<div class="yxjto-paypal-status-error">' +
                        '<span>⚠</span>' +
                        '<span>' + (yxjto_paypal_multi_gateway_params.strings.network_error || 'Network error, please try again') + '</span>' +
                        '</div>');
                }
            });
        },

        /**
         * Switch account
         */
        switchAccount: function() {
            var self = this;
            var $button = $('.yxjto-paypal-switch-account-btn');
            var originalText = $button.text();

            $button.prop('disabled', true).html('<span class="yxjto-paypal-spinner"></span>' + 
                (yxjto_paypal_multi_gateway_params.strings.switching || '🔄 Switching...'));

            $.ajax({
                url: yxjto_paypal_multi_gateway_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'yxjto_paypal_multi_gateway_simulate_checkout',
                    nonce: yxjto_paypal_multi_gateway_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // 显示切换成功消息
                        var $statusContainer = $('#yxjto-paypal-account-status');
                        var successHtml = '<div class="yxjto-paypal-status-success">';
                        successHtml += '<span>✓</span>';
                        successHtml += '<span>' + (yxjto_paypal_multi_gateway_params.strings.switch_success || 'Switch successful!') + '</span>';
                        successHtml += '</div>';
                        
                        $statusContainer.html(successHtml);
                        
                        // 2秒后恢复原状态
                        setTimeout(function() {
                            self.checkAccountStatus();
                        }, 2000);
                    } else {
                        self.showError(yxjto_paypal_multi_gateway_params.strings.switch_failed + (response.data || ''));
                    }
                },
                error: function() {
                    self.showError(yxjto_paypal_multi_gateway_params.strings.network_error);
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        /**
         * Validate order before submission
         */
        validateOrder: function() {
            // 检查必填字段
            var requiredFields = ['billing_first_name', 'billing_last_name', 'billing_email'];
            var missingFields = [];

            requiredFields.forEach(function(field) {
                var $field = $('#' + field);
                if ($field.length && !$field.val().trim()) {
                    missingFields.push($field.closest('.form-row').find('label').text().replace('*', '').trim());
                }
            });

            if (missingFields.length > 0) {
                this.showError(yxjto_paypal_multi_gateway_params.strings.required_fields);
                return false;
            }

            // 检查货币支持
            var currency = yxjto_paypal_multi_gateway_params.currency || 'USD';
            var supportedCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];
            
            if (supportedCurrencies.indexOf(currency) === -1) {
                this.showError(yxjto_paypal_multi_gateway_params.strings.currency_not_supported);
                return false;
            }

            return true;
        },

        /**
         * Process payment
         */
        processPayment: function() {
            // 这个方法通常由 WooCommerce 的结账流程处理
            // 这里可以添加额外的客户端验证或处理逻辑
            console.log('YXJTO PayPal Multi Gateway: Processing payment...');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            var $container = $('.woocommerce-notices-wrapper').first();
            if ($container.length === 0) {
                $container = $('.woocommerce').first();
            }

            var errorHtml = '<div class="woocommerce-error yxjto-paypal-error-message">' + message + '</div>';
            $container.prepend(errorHtml);

            // 滚动到错误消息
            $('html, body').animate({
                scrollTop: $container.offset().top - 100
            }, 500);

            // 5秒后自动隐藏
            setTimeout(function() {
                $('.yxjto-paypal-error-message').fadeOut();
            }, 5000);
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            var $container = $('.woocommerce-notices-wrapper').first();
            if ($container.length === 0) {
                $container = $('.woocommerce').first();
            }

            var successHtml = '<div class="woocommerce-message yxjto-paypal-success-message">' + message + '</div>';
            $container.prepend(successHtml);

            // 3秒后自动隐藏
            setTimeout(function() {
                $('.yxjto-paypal-success-message').fadeOut();
            }, 3000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        YXJTOPayPalMultiGatewayCheckout.init();
    });

    // Make globally available
    window.YXJTOPayPalMultiGatewayCheckout = YXJTOPayPalMultiGatewayCheckout;

})(jQuery);
