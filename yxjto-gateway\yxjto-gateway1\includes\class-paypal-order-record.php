<?php
/**
 * PayPal Order Record Manager
 * 
 * 专门用于记录和检索订单的支付账号和源数据库信息
 * 使用文件存储确保数据的可靠性和独立性
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class YXJTO_PayPal_Order_Record {
    
    private static $instance = null;
    private $record_file_path;
    private $records = array();
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $upload_dir = wp_upload_dir();
        $this->record_file_path = WP_CONTENT_DIR . '/yxjto-paypal-order-records.json';
        $this->load_records();
    }
    
    /**
     * 从文件加载记录
     */
    private function load_records() {
        if (file_exists($this->record_file_path)) {
            $content = file_get_contents($this->record_file_path);
            if ($content !== false) {
                $decoded = json_decode($content, true);
                if (is_array($decoded)) {
                    $this->records = $decoded;
                }
            }
        }
    }
    
    /**
     * 保存记录到文件
     */
    private function save_records() {
        // 确保目录存在
        $dir = dirname($this->record_file_path);
        if (!file_exists($dir)) {
            wp_mkdir_p($dir);
        }
        
        // 保存记录
        $json_content = json_encode($this->records, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        file_put_contents($this->record_file_path, $json_content, LOCK_EX);
    }
    
    /**
     * 记录订单信息
     * 
     * @param string $order_id 订单号
     * @param string $payment_account_id 支付账号ID
     * @param string $source_database 源数据库
     * @param array $additional_data 额外数据
     */
    public function record_order($order_id, $payment_account_id, $source_database, $additional_data = array()) {
        $record = array(
            'order_id' => $order_id,
            'payment_account_id' => $payment_account_id,
            'source_database' => $source_database,
            'timestamp' => current_time('timestamp'),
            'created_at' => current_time('Y-m-d H:i:s'),
            'additional_data' => $additional_data
        );
        
        $this->records[$order_id] = $record;
        $this->save_records();
        
        // 记录日志
        error_log("PayPal Order Record: Recorded order #{$order_id} with account {$payment_account_id} from database {$source_database}");
    }
    
    /**
     * 获取订单记录
     * 
     * @param string $order_id 订单号
     * @return array|null 订单记录或null
     */
    public function get_order_record($order_id) {
        if (isset($this->records[$order_id])) {
            return $this->records[$order_id];
        }
        return null;
    }
    
    /**
     * 获取订单的支付账号
     * 
     * @param string $order_id 订单号
     * @return string|null 支付账号ID或null
     */
    public function get_payment_account($order_id) {
        $record = $this->get_order_record($order_id);
        return $record ? $record['payment_account_id'] : null;
    }
    
    /**
     * 获取订单的源数据库
     * 
     * @param string $order_id 订单号
     * @return string|null 源数据库或null
     */
    public function get_source_database($order_id) {
        $record = $this->get_order_record($order_id);
        return $record ? $record['source_database'] : null;
    }
    
    /**
     * 检查订单是否已记录
     * 
     * @param string $order_id 订单号
     * @return bool
     */
    public function has_order_record($order_id) {
        return isset($this->records[$order_id]);
    }
    
    /**
     * 删除订单记录
     * 
     * @param string $order_id 订单号
     * @return bool 是否成功删除
     */
    public function delete_order_record($order_id) {
        if (isset($this->records[$order_id])) {
            unset($this->records[$order_id]);
            $this->save_records();
            error_log("PayPal Order Record: Deleted record for order #{$order_id}");
            return true;
        }
        return false;
    }
    
    /**
     * 清理过期记录（超过30天的记录）
     */
    public function cleanup_old_records() {
        $thirty_days_ago = current_time('timestamp') - (30 * 24 * 60 * 60);
        $deleted_count = 0;
        
        foreach ($this->records as $order_id => $record) {
            if (isset($record['timestamp']) && $record['timestamp'] < $thirty_days_ago) {
                unset($this->records[$order_id]);
                $deleted_count++;
            }
        }
        
        if ($deleted_count > 0) {
            $this->save_records();
            error_log("PayPal Order Record: Cleaned up {$deleted_count} old records");
        }
        
        return $deleted_count;
    }
    
    /**
     * 获取所有记录（用于调试）
     * 
     * @return array
     */
    public function get_all_records() {
        return $this->records;
    }
    
    /**
     * 获取记录统计信息
     * 
     * @return array
     */
    public function get_statistics() {
        return array(
            'total_records' => count($this->records),
            'file_path' => $this->record_file_path,
            'file_exists' => file_exists($this->record_file_path),
            'file_size' => file_exists($this->record_file_path) ? filesize($this->record_file_path) : 0
        );
    }
}
