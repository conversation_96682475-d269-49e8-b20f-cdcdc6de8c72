<?php
/**
 * PayPal交易日志记录器
 * 专门用于记录PayPal交易到数据库表
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Transaction_Logger {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 日志表名
     */
    private $table_name;
    
    /**
     * 构造函数
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 记录交易日志
     */
    public function log_transaction($transaction_id, $account_id, $order_id, $amount, $currency, $status, $gateway_response = null) {
        global $wpdb;
        
        $data = array(
            'transaction_id' => sanitize_text_field($transaction_id),
            'account_id' => sanitize_text_field($account_id),
            'order_id' => intval($order_id),
            'amount' => floatval($amount),
            'currency' => sanitize_text_field($currency),
            'status' => sanitize_text_field($status),
            'gateway_response' => is_array($gateway_response) ? wp_json_encode($gateway_response) : $gateway_response,
            'created_at' => current_time('mysql')
        );
        
        $formats = array(
            '%s', // transaction_id
            '%s', // account_id
            '%d', // order_id
            '%f', // amount
            '%s', // currency
            '%s', // status
            '%s', // gateway_response
            '%s'  // created_at
        );
        
        $result = $wpdb->insert($this->table_name, $data, $formats);
        
        if ($result === false) {
            error_log('PayPal Transaction Logger: Failed to insert transaction log - ' . $wpdb->last_error);
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * 更新交易状态
     */
    public function update_transaction_status($transaction_id, $status, $gateway_response = null) {
        global $wpdb;
        
        $data = array(
            'status' => sanitize_text_field($status),
            'updated_at' => current_time('mysql')
        );
        
        $formats = array('%s', '%s');
        
        if ($gateway_response !== null) {
            $data['gateway_response'] = is_array($gateway_response) ? wp_json_encode($gateway_response) : $gateway_response;
            $formats[] = '%s';
        }
        
        $result = $wpdb->update(
            $this->table_name,
            $data,
            array('transaction_id' => $transaction_id),
            $formats,
            array('%s')
        );
        
        return $result !== false;
    }
    
    /**
     * 获取交易日志
     */
    public function get_transaction($transaction_id) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE transaction_id = %s",
            $transaction_id
        );
        
        return $wpdb->get_row($sql);
    }
    
    /**
     * 获取订单的交易日志
     */
    public function get_order_transactions($order_id) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE order_id = %d ORDER BY created_at DESC",
            $order_id
        );
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * 获取账户的交易日志
     */
    public function get_account_transactions($account_id, $limit = 50) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE account_id = %s ORDER BY created_at DESC LIMIT %d",
            $account_id,
            $limit
        );
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * 获取交易统计
     */
    public function get_transaction_stats($account_id = null, $days = 30) {
        global $wpdb;
        
        $where_clause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)";
        $params = array($days);
        
        if ($account_id) {
            $where_clause .= " AND account_id = %s";
            $params[] = $account_id;
        }
        
        $sql = "SELECT 
                    status,
                    COUNT(*) as count,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount
                FROM {$this->table_name} 
                {$where_clause}
                GROUP BY status";
        
        $prepared_sql = $wpdb->prepare($sql, $params);
        
        return $wpdb->get_results($prepared_sql);
    }
    
    /**
     * 清理旧日志
     */
    public function cleanup_old_logs($days = 90) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days
        );
        
        $deleted = $wpdb->query($sql);
        
        if ($deleted !== false) {
            error_log("PayPal Transaction Logger: Cleaned up {$deleted} old log entries");
        }
        
        return $deleted;
    }
    
    /**
     * 获取最近的交易
     */
    public function get_recent_transactions($limit = 20) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} ORDER BY created_at DESC LIMIT %d",
            $limit
        );
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * 检查表是否存在
     */
    public function table_exists() {
        global $wpdb;
        
        $table_name = $this->table_name;
        $result = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
        
        return $result === $table_name;
    }
    
    /**
     * 获取表状态信息
     */
    public function get_table_info() {
        global $wpdb;
        
        if (!$this->table_exists()) {
            return null;
        }
        
        // 获取记录数量
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        
        // 获取最新记录时间
        $latest = $wpdb->get_var("SELECT MAX(created_at) FROM {$this->table_name}");
        
        // 获取最旧记录时间
        $oldest = $wpdb->get_var("SELECT MIN(created_at) FROM {$this->table_name}");
        
        // 获取表大小信息
        $table_status = $wpdb->get_row($wpdb->prepare(
            "SELECT 
                table_rows,
                data_length,
                index_length,
                (data_length + index_length) as total_size
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s",
            DB_NAME,
            $this->table_name
        ));
        
        return array(
            'exists' => true,
            'record_count' => intval($count),
            'latest_record' => $latest,
            'oldest_record' => $oldest,
            'table_rows' => $table_status ? intval($table_status->table_rows) : 0,
            'data_size' => $table_status ? intval($table_status->data_length) : 0,
            'index_size' => $table_status ? intval($table_status->index_length) : 0,
            'total_size' => $table_status ? intval($table_status->total_size) : 0
        );
    }
}

// 全局函数，方便调用
if (!function_exists('yxjto_log_paypal_transaction')) {
    function yxjto_log_paypal_transaction($transaction_id, $account_id, $order_id, $amount, $currency, $status, $gateway_response = null) {
        $logger = YXJTO_PayPal_Transaction_Logger::get_instance();
        return $logger->log_transaction($transaction_id, $account_id, $order_id, $amount, $currency, $status, $gateway_response);
    }
}

if (!function_exists('yxjto_update_paypal_transaction_status')) {
    function yxjto_update_paypal_transaction_status($transaction_id, $status, $gateway_response = null) {
        $logger = YXJTO_PayPal_Transaction_Logger::get_instance();
        return $logger->update_transaction_status($transaction_id, $status, $gateway_response);
    }
}
