<?php
/**
 * 价格调整配置管理器
 * 管理价格调整相关的配置选项
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 价格调整配置管理器类
 */
class YXJTO_Price_Adjustment_Config {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 配置选项键名
     */
    const OPTION_KEY = 'yxjto_price_adjustment_config';
    
    /**
     * 默认配置
     */
    private $default_config = [
        'enabled' => true,
        'strategy' => 'auto',
        'enable_validation' => true,
        'enable_recalculation' => true,
        'min_adjustment_threshold' => 0.01,
        'max_adjustment_percentage' => 50, // 最大调整百分比
        'enable_logging' => true,
        'fallback_to_legacy' => true,
        'validation_rules' => [
            'validate_shipping' => true,
            'validate_discount' => true,
            'validate_tax' => false // 税费通常由系统准确计算
        ],
        'strategy_preferences' => [
            'small_difference_threshold' => 5.00, // 小差异阈值
            'large_difference_threshold' => 50.00, // 大差异阈值
            'prefer_proportional_for_small' => true,
            'prefer_highest_first_for_large' => true
        ]
    ];
    
    /**
     * 当前配置
     */
    private $config;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 私有构造函数
     */
    private function __construct() {
        $this->load_config();
    }
    
    /**
     * 加载配置
     */
    private function load_config() {
        $saved_config = get_option(self::OPTION_KEY, []);
        $this->config = $this->merge_config($this->default_config, $saved_config);
    }
    
    /**
     * 递归合并配置
     * 
     * @param array $default 默认配置
     * @param array $saved 保存的配置
     * @return array 合并后的配置
     */
    private function merge_config($default, $saved) {
        foreach ($saved as $key => $value) {
            if (is_array($value) && isset($default[$key]) && is_array($default[$key])) {
                $default[$key] = $this->merge_config($default[$key], $value);
            } else {
                $default[$key] = $value;
            }
        }
        return $default;
    }
    
    /**
     * 获取配置值
     * 
     * @param string $key 配置键名，支持点号分隔的嵌套键
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    public function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (is_array($value) && isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
    
    /**
     * 设置配置值
     * 
     * @param string $key 配置键名，支持点号分隔的嵌套键
     * @param mixed $value 配置值
     */
    public function set($key, $value) {
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * 获取所有配置
     * 
     * @return array 配置数组
     */
    public function get_all() {
        return $this->config;
    }
    
    /**
     * 保存配置
     * 
     * @return bool 是否保存成功
     */
    public function save() {
        return update_option(self::OPTION_KEY, $this->config);
    }
    
    /**
     * 重置为默认配置
     * 
     * @return bool 是否重置成功
     */
    public function reset() {
        $this->config = $this->default_config;
        return $this->save();
    }
    
    /**
     * 验证配置
     * 
     * @param array $config 要验证的配置
     * @return array 验证结果 ['valid' => bool, 'errors' => array]
     */
    public function validate_config($config) {
        $errors = [];
        
        // 验证策略
        if (isset($config['strategy'])) {
            $valid_strategies = ['auto', 'proportional', 'equal_distribution', 'highest_price_first'];
            if (!in_array($config['strategy'], $valid_strategies)) {
                $errors[] = "Invalid strategy: {$config['strategy']}";
            }
        }
        
        // 验证阈值
        if (isset($config['min_adjustment_threshold'])) {
            if (!is_numeric($config['min_adjustment_threshold']) || $config['min_adjustment_threshold'] < 0) {
                $errors[] = "Invalid min_adjustment_threshold: must be a positive number";
            }
        }
        
        // 验证最大调整百分比
        if (isset($config['max_adjustment_percentage'])) {
            if (!is_numeric($config['max_adjustment_percentage']) || $config['max_adjustment_percentage'] <= 0 || $config['max_adjustment_percentage'] > 100) {
                $errors[] = "Invalid max_adjustment_percentage: must be between 1 and 100";
            }
        }
        
        // 验证策略偏好阈值
        if (isset($config['strategy_preferences'])) {
            $prefs = $config['strategy_preferences'];
            if (isset($prefs['small_difference_threshold'], $prefs['large_difference_threshold'])) {
                if ($prefs['small_difference_threshold'] >= $prefs['large_difference_threshold']) {
                    $errors[] = "small_difference_threshold must be less than large_difference_threshold";
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 获取推荐的策略
     * 
     * @param float $difference 价格差异
     * @param int $item_count 商品数量
     * @return string 推荐的策略
     */
    public function get_recommended_strategy($difference, $item_count) {
        $abs_difference = abs($difference);
        $small_threshold = $this->get('strategy_preferences.small_difference_threshold', 5.00);
        $large_threshold = $this->get('strategy_preferences.large_difference_threshold', 50.00);
        
        if ($abs_difference <= $small_threshold) {
            // 小差异，推荐平均分配或比例分配
            if ($this->get('strategy_preferences.prefer_proportional_for_small', true)) {
                return 'proportional';
            } else {
                return 'equal_distribution';
            }
        } elseif ($abs_difference >= $large_threshold && $item_count > 1) {
            // 大差异，推荐最高价优先
            if ($this->get('strategy_preferences.prefer_highest_first_for_large', true)) {
                return 'highest_price_first';
            } else {
                return 'proportional';
            }
        } else {
            // 中等差异，使用比例分配
            return 'proportional';
        }
    }
    
    /**
     * 检查是否启用价格调整
     * 
     * @return bool 是否启用
     */
    public function is_enabled() {
        return $this->get('enabled', true);
    }
    
    /**
     * 检查是否启用日志记录
     * 
     * @return bool 是否启用日志
     */
    public function is_logging_enabled() {
        return $this->get('enable_logging', true);
    }
    
    /**
     * 检查是否启用验证
     * 
     * @return bool 是否启用验证
     */
    public function is_validation_enabled() {
        return $this->get('enable_validation', true);
    }
    
    /**
     * 检查是否启用重新计算
     * 
     * @return bool 是否启用重新计算
     */
    public function is_recalculation_enabled() {
        return $this->get('enable_recalculation', true);
    }
    
    /**
     * 获取最小调整阈值
     * 
     * @return float 最小调整阈值
     */
    public function get_min_threshold() {
        return $this->get('min_adjustment_threshold', 0.01);
    }
    
    /**
     * 获取最大调整百分比
     * 
     * @return float 最大调整百分比
     */
    public function get_max_adjustment_percentage() {
        return $this->get('max_adjustment_percentage', 50);
    }
    
    /**
     * 检查调整是否在允许范围内
     * 
     * @param float $original_total 原始总价
     * @param float $target_total 目标总价
     * @return bool 是否在允许范围内
     */
    public function is_adjustment_within_limits($original_total, $target_total) {
        if ($original_total <= 0) {
            return false;
        }
        
        $difference = abs($target_total - $original_total);
        $percentage = ($difference / $original_total) * 100;
        $max_percentage = $this->get_max_adjustment_percentage();
        
        return $percentage <= $max_percentage;
    }
    
    /**
     * 导出配置
     * 
     * @return string JSON格式的配置
     */
    public function export_config() {
        return json_encode($this->config, JSON_PRETTY_PRINT);
    }
    
    /**
     * 导入配置
     * 
     * @param string $json_config JSON格式的配置
     * @return bool 是否导入成功
     */
    public function import_config($json_config) {
        $config = json_decode($json_config, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        
        $validation = $this->validate_config($config);
        if (!$validation['valid']) {
            return false;
        }
        
        $this->config = $this->merge_config($this->default_config, $config);
        return $this->save();
    }
}
