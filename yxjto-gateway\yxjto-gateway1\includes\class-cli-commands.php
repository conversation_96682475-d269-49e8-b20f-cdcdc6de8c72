<?php
/**
 * WP-CLI 命令来运行订单比较测试
 * 使用方法: wp yxjto test order-comparison
 */

if (!defined('WP_CLI') || !WP_CLI) {
    return;
}

class YXJTO_CLI_Commands {
    
    /**
     * 运行YXJTO相关测试
     *
     * ## OPTIONS
     *
     * <test_type>
     * : 要运行的测试类型
     * ---
     * default: order-comparison
     * options:
     *   - order-comparison
     *   - database-connections
     *   - database-switching
     *   - order-search
     * ---
     *
     * [--verbose]
     * : 显示详细输出
     *
     * ## EXAMPLES
     *
     *     wp yxjto test order-comparison
     *     wp yxjto test database-connections --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function test($args, $assoc_args) {
        $test_type = $args[0] ?? 'order-comparison';
        $verbose = isset($assoc_args['verbose']);
        
        WP_CLI::line("🧪 开始运行 YXJTO 测试: $test_type");
        WP_CLI::line("");
        
        switch ($test_type) {
            case 'order-comparison':
                $this->run_order_comparison_test($verbose);
                break;
            case 'database-connections':
                $this->test_database_connections($verbose);
                break;
            case 'database-switching':
                $this->test_database_switching($verbose);
                break;
            case 'order-search':
                $this->test_order_search($verbose);
                break;
            default:
                WP_CLI::error("未知的测试类型: $test_type");
        }
    }
    
    /**
     * 运行完整的订单比较测试
     */
    private function run_order_comparison_test($verbose) {
        try {
            // 包含测试脚本
            $test_file = WP_PLUGIN_DIR . '/yxjto-gateway/test/order-comparison-test.php';
            
            if (!file_exists($test_file)) {
                WP_CLI::error("测试文件不存在: $test_file");
                return;
            }
            
            require_once $test_file;
            
            if (!class_exists('YXJTO_Order_Comparison_Test')) {
                WP_CLI::error("YXJTO_Order_Comparison_Test 类未找到");
                return;
            }
            
            // 捕获输出
            ob_start();
            
            $test = new YXJTO_Order_Comparison_Test();
            $test->run_tests();
            
            $output = ob_get_clean();
            
            // 处理输出
            $lines = explode("\n", $output);
            foreach ($lines as $line) {
                if (empty(trim($line))) continue;
                
                if (strpos($line, '✅') !== false) {
                    WP_CLI::success($line);
                } elseif (strpos($line, '❌') !== false) {
                    WP_CLI::error($line, false);
                } elseif (strpos($line, '⚠️') !== false) {
                    WP_CLI::warning($line);
                } else {
                    WP_CLI::line($line);
                }
            }
            
        } catch (Exception $e) {
            WP_CLI::error("测试运行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function test_database_connections($verbose) {
        WP_CLI::line("🔗 测试数据库连接...");
        
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            WP_CLI::error("WP_Multi_DB_Config_Manager 类不存在");
            return;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        
        if (empty($databases)) {
            WP_CLI::warning("没有配置任何数据库");
            return;
        }
        
        $success_count = 0;
        $total_count = count($databases);
        
        foreach ($databases as $db_key => $db_config) {
            WP_CLI::line("测试数据库: $db_key");
            
            $connection = WP_Multi_DB_Config_Manager::get_connection($db_key);
            if ($connection) {
                WP_CLI::success("$db_key 连接成功");
                $success_count++;
                
                if ($verbose) {
                    WP_CLI::line("  - 主机: " . $db_config['host']);
                    WP_CLI::line("  - 数据库: " . $db_config['database']);
                }
            } else {
                WP_CLI::error("$db_key 连接失败", false);
            }
        }
        
        WP_CLI::line("");
        WP_CLI::line("总结: $success_count/$total_count 数据库连接成功");
    }
    
    /**
     * 测试数据库切换
     */
    private function test_database_switching($verbose) {
        WP_CLI::line("🔄 测试数据库切换...");
        
        if (!class_exists('YXJTO_Gateway')) {
            WP_CLI::error("YXJTO_Gateway 类不存在");
            return;
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        WP_CLI::line("当前数据库: $original_db");
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $switch_tests = 0;
        $successful_switches = 0;
        
        foreach ($databases as $db_key => $db_config) {
            if ($db_key === $original_db) {
                continue;
            }
            
            $switch_tests++;
            WP_CLI::line("切换到数据库: $db_key");
            
            $result = $gateway->switch_database($db_key);
            
            if ($result) {
                // 验证切换
                $current = $gateway->get_current_database();
                if ($current === $db_key) {
                    WP_CLI::success("切换到 $db_key 成功并验证");
                    $successful_switches++;
                } else {
                    WP_CLI::error("切换到 $db_key 后验证失败", false);
                }
            } else {
                WP_CLI::error("切换到 $db_key 失败", false);
            }
        }
        
        // 恢复原始数据库
        $gateway->switch_database($original_db);
        WP_CLI::line("恢复到原始数据库: $original_db");
        
        WP_CLI::line("");
        WP_CLI::line("总结: $successful_switches/$switch_tests 数据库切换成功");
    }
    
    /**
     * 测试订单搜索
     */
    private function test_order_search($verbose) {
        WP_CLI::line("🔍 测试订单搜索...");
        
        if (!class_exists('YXJTO_Order_Comparison')) {
            WP_CLI::error("YXJTO_Order_Comparison 类不存在");
            return;
        }
        
        // 获取最近的几个订单ID进行测试
        $recent_orders = wc_get_orders([
            'limit' => 3,
            'orderby' => 'date',
            'order' => 'DESC',
            'return' => 'ids'
        ]);
        
        if (empty($recent_orders)) {
            WP_CLI::warning("没有找到任何订单用于测试");
            return;
        }
        
        $comparison = new YXJTO_Order_Comparison();
        $found_orders = 0;
        
        foreach ($recent_orders as $order_id) {
            WP_CLI::line("搜索订单 ID: $order_id");
            
            $results = $comparison->search_order_across_databases($order_id);
            
            if (!empty($results)) {
                WP_CLI::success("在 " . count($results) . " 个数据库中找到订单 $order_id");
                $found_orders++;
                
                if ($verbose) {
                    foreach ($results as $db_key => $order_data) {
                        WP_CLI::line("  - 数据库: $db_key, 状态: {$order_data['status']}");
                    }
                }
            } else {
                WP_CLI::warning("订单 $order_id 在其他数据库中未找到");
            }
        }
        
        WP_CLI::line("");
        WP_CLI::line("总结: 在多数据库中找到 $found_orders/" . count($recent_orders) . " 个订单");
    }
}

// 注册WP-CLI命令
WP_CLI::add_command('yxjto', 'YXJTO_CLI_Commands');
