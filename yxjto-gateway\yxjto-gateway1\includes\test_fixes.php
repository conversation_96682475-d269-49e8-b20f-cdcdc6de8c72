<?php
/**
 * 测试 WooCommerce 合规性修复
 * 验证关键方法是否正常工作
 */

// 模拟测试环境
if (!defined('ABSPATH')) {
    define('ABSPATH', '/fake/path/');
}

// 检查语法错误
echo "=== WooCommerce 合规性修复测试 ===\n\n";

echo "1. 语法检查...\n";
$syntax_check = shell_exec('php -l class-order-replication.php 2>&1');
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "✅ 语法检查通过\n";
} else {
    echo "❌ 语法错误:\n" . $syntax_check . "\n";
}

echo "\n2. 检查关键方法修复状态...\n";

$file_content = file_get_contents('class-order-replication.php');

// 检查是否添加了安全的 gateway 获取方法
if (strpos($file_content, 'private function get_gateway_instance()') !== false) {
    echo "✅ 安全的 Gateway 实例获取方法已添加\n";
} else {
    echo "❌ 缺少安全的 Gateway 实例获取方法\n";
}

// 检查 payment_tokens 的修复
if (strpos($file_content, '$order->get_payment_tokens()') !== false) {
    echo "✅ WooCommerce 支付令牌访问已修复\n";
} else {
    echo "❌ 支付令牌访问未修复\n";
}

// 检查是否还有直接的 YXJTO_Gateway::get_instance() 调用
$direct_calls = preg_match_all('/YXJTO_Gateway::get_instance\(\)/', $file_content);
echo "\n3. 剩余待修复的直接调用: " . $direct_calls . " 个\n";

if ($direct_calls > 5) {
    echo "⚠️  建议使用批量修复脚本处理剩余调用\n";
} elseif ($direct_calls > 0) {
    echo "⚠️  少量剩余调用，可手动修复\n";
} else {
    echo "✅ 所有直接调用已修复\n";
}

echo "\n=== 测试完成 ===\n";
?>
