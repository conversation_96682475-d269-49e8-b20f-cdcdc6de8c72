<?php
/**
 * 价格调整管理界面
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 价格调整管理界面类
 */
class YXJTO_Price_Adjustment_Admin {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 配置管理器
     */
    private $config;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 私有构造函数
     */
    private function __construct() {
        // 加载配置管理器
        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        $this->config = YXJTO_Price_Adjustment_Config::get_instance();
        
        // 注册AJAX处理器
        add_action('wp_ajax_yxjto_save_price_adjustment_config', [$this, 'ajax_save_config']);
        add_action('wp_ajax_yxjto_reset_price_adjustment_config', [$this, 'ajax_reset_config']);
        add_action('wp_ajax_yxjto_test_price_adjustment', [$this, 'ajax_test_adjustment']);
        add_action('wp_ajax_yxjto_export_price_adjustment_config', [$this, 'ajax_export_config']);
        add_action('wp_ajax_yxjto_import_price_adjustment_config', [$this, 'ajax_import_config']);
    }
    
    /**
     * 渲染价格调整配置页面
     */
    public function render_config_page() {
        $config = $this->config->get_all();
        ?>
        <div class="price-adjustment-config">
            <h3><?php _e('Price Adjustment Settings', 'yxjto-gateway'); ?></h3>
            
            <form id="price-adjustment-config-form">
                <?php wp_nonce_field('yxjto_price_adjustment_config', 'price_adjustment_nonce'); ?>
                
                <!-- 基本设置 -->
                <div class="config-section">
                    <h4><?php _e('Basic Settings', 'yxjto-gateway'); ?></h4>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Price Adjustment', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enabled" value="1" <?php checked($config['enabled']); ?>>
                                    <?php _e('Enable automatic price adjustment before payment', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Adjustment Strategy', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="strategy">
                                    <option value="auto" <?php selected($config['strategy'], 'auto'); ?>><?php _e('Auto (Recommended)', 'yxjto-gateway'); ?></option>
                                    <option value="proportional" <?php selected($config['strategy'], 'proportional'); ?>><?php _e('Proportional Distribution', 'yxjto-gateway'); ?></option>
                                    <option value="equal_distribution" <?php selected($config['strategy'], 'equal_distribution'); ?>><?php _e('Equal Distribution', 'yxjto-gateway'); ?></option>
                                    <option value="highest_price_first" <?php selected($config['strategy'], 'highest_price_first'); ?>><?php _e('Highest Price First', 'yxjto-gateway'); ?></option>
                                </select>
                                <p class="description"><?php _e('Choose how price differences should be distributed among items.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Minimum Adjustment Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="min_adjustment_threshold" value="<?php echo esc_attr($config['min_adjustment_threshold']); ?>" step="0.01" min="0">
                                <p class="description"><?php _e('Minimum price difference to trigger adjustment (in currency units).', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Maximum Adjustment Percentage', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="max_adjustment_percentage" value="<?php echo esc_attr($config['max_adjustment_percentage']); ?>" min="1" max="100">%
                                <p class="description"><?php _e('Maximum allowed adjustment as percentage of original total.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- 验证设置 -->
                <div class="config-section">
                    <h4><?php _e('Validation Settings', 'yxjto-gateway'); ?></h4>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Validation', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_validation" value="1" <?php checked($config['enable_validation']); ?>>
                                    <?php _e('Validate shipping, discount, and tax amounts before adjustment', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Validation Rules', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="validation_rules[validate_shipping]" value="1" <?php checked($config['validation_rules']['validate_shipping']); ?>>
                                    <?php _e('Validate shipping amounts', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="validation_rules[validate_discount]" value="1" <?php checked($config['validation_rules']['validate_discount']); ?>>
                                    <?php _e('Validate discount amounts', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="validation_rules[validate_tax]" value="1" <?php checked($config['validation_rules']['validate_tax']); ?>>
                                    <?php _e('Validate tax amounts', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- 策略偏好设置 -->
                <div class="config-section">
                    <h4><?php _e('Strategy Preferences', 'yxjto-gateway'); ?></h4>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Small Difference Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="strategy_preferences[small_difference_threshold]" value="<?php echo esc_attr($config['strategy_preferences']['small_difference_threshold']); ?>" step="0.01" min="0">
                                <p class="description"><?php _e('Threshold for considering a price difference as "small".', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Large Difference Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="strategy_preferences[large_difference_threshold]" value="<?php echo esc_attr($config['strategy_preferences']['large_difference_threshold']); ?>" step="0.01" min="0">
                                <p class="description"><?php _e('Threshold for considering a price difference as "large".', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Strategy Preferences', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="strategy_preferences[prefer_proportional_for_small]" value="1" <?php checked($config['strategy_preferences']['prefer_proportional_for_small']); ?>>
                                    <?php _e('Prefer proportional distribution for small differences', 'yxjto-gateway'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="strategy_preferences[prefer_highest_first_for_large]" value="1" <?php checked($config['strategy_preferences']['prefer_highest_first_for_large']); ?>>
                                    <?php _e('Prefer highest price first for large differences', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- 其他设置 -->
                <div class="config-section">
                    <h4><?php _e('Other Settings', 'yxjto-gateway'); ?></h4>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Logging', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_logging" value="1" <?php checked($config['enable_logging']); ?>>
                                    <?php _e('Log price adjustment activities for debugging', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Enable Recalculation', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_recalculation" value="1" <?php checked($config['enable_recalculation']); ?>>
                                    <?php _e('Recalculate order totals after price adjustment', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Fallback to Legacy', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="fallback_to_legacy" value="1" <?php checked($config['fallback_to_legacy']); ?>>
                                    <?php _e('Use legacy price adjustment method if new method fails', 'yxjto-gateway'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- 操作按钮 -->
                <div class="config-actions">
                    <button type="submit" class="button-primary"><?php _e('Save Settings', 'yxjto-gateway'); ?></button>
                    <button type="button" id="reset-config" class="button"><?php _e('Reset to Defaults', 'yxjto-gateway'); ?></button>
                    <button type="button" id="test-adjustment" class="button"><?php _e('Test Adjustment', 'yxjto-gateway'); ?></button>
                    <button type="button" id="export-config" class="button"><?php _e('Export Config', 'yxjto-gateway'); ?></button>
                    <button type="button" id="import-config" class="button"><?php _e('Import Config', 'yxjto-gateway'); ?></button>
                </div>
            </form>
            
            <!-- 测试结果区域 -->
            <div id="test-results" style="display: none;">
                <h4><?php _e('Test Results', 'yxjto-gateway'); ?></h4>
                <div id="test-output"></div>
            </div>
            
            <!-- 导入配置区域 -->
            <div id="import-config-area" style="display: none;">
                <h4><?php _e('Import Configuration', 'yxjto-gateway'); ?></h4>
                <textarea id="import-config-text" rows="10" cols="80" placeholder="<?php _e('Paste configuration JSON here...', 'yxjto-gateway'); ?>"></textarea><br>
                <button type="button" id="do-import" class="button"><?php _e('Import', 'yxjto-gateway'); ?></button>
                <button type="button" id="cancel-import" class="button"><?php _e('Cancel', 'yxjto-gateway'); ?></button>
            </div>
        </div>
        
        <style>
        .price-adjustment-config .config-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border: 1px solid #c3c4c7;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        
        .price-adjustment-config .config-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #23282d;
        }
        
        .price-adjustment-config .config-actions {
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid #c3c4c7;
        }
        
        .price-adjustment-config .config-actions .button {
            margin-right: 10px;
        }
        
        #test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f0f0f1;
            border: 1px solid #c3c4c7;
        }
        
        #test-output {
            font-family: monospace;
            white-space: pre-wrap;
            background: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            max-height: 300px;
            overflow-y: auto;
        }
        
        #import-config-area {
            margin-top: 20px;
            padding: 15px;
            background: #f0f0f1;
            border: 1px solid #c3c4c7;
        }
        
        #import-config-text {
            width: 100%;
            font-family: monospace;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // 保存配置
            $('#price-adjustment-config-form').on('submit', function(e) {
                e.preventDefault();
                
                var formData = $(this).serialize();
                formData += '&action=yxjto_save_price_adjustment_config';
                
                $.post(ajaxurl, formData, function(response) {
                    if (response.success) {
                        alert('<?php _e('Settings saved successfully!', 'yxjto-gateway'); ?>');
                    } else {
                        alert('<?php _e('Failed to save settings: ', 'yxjto-gateway'); ?>' + response.data);
                    }
                });
            });
            
            // 重置配置
            $('#reset-config').on('click', function() {
                if (confirm('<?php _e('Are you sure you want to reset all settings to defaults?', 'yxjto-gateway'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'yxjto_reset_price_adjustment_config',
                        nonce: $('[name="price_adjustment_nonce"]').val()
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('<?php _e('Failed to reset settings: ', 'yxjto-gateway'); ?>' + response.data);
                        }
                    });
                }
            });
            
            // 测试调整
            $('#test-adjustment').on('click', function() {
                $.post(ajaxurl, {
                    action: 'yxjto_test_price_adjustment',
                    nonce: $('[name="price_adjustment_nonce"]').val()
                }, function(response) {
                    $('#test-output').text(response.data);
                    $('#test-results').show();
                });
            });
            
            // 导出配置
            $('#export-config').on('click', function() {
                $.post(ajaxurl, {
                    action: 'yxjto_export_price_adjustment_config',
                    nonce: $('[name="price_adjustment_nonce"]').val()
                }, function(response) {
                    if (response.success) {
                        var blob = new Blob([response.data], {type: 'application/json'});
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = 'price-adjustment-config.json';
                        a.click();
                        window.URL.revokeObjectURL(url);
                    }
                });
            });
            
            // 显示导入区域
            $('#import-config').on('click', function() {
                $('#import-config-area').show();
            });
            
            // 执行导入
            $('#do-import').on('click', function() {
                var configText = $('#import-config-text').val();
                if (!configText.trim()) {
                    alert('<?php _e('Please paste configuration JSON.', 'yxjto-gateway'); ?>');
                    return;
                }
                
                $.post(ajaxurl, {
                    action: 'yxjto_import_price_adjustment_config',
                    nonce: $('[name="price_adjustment_nonce"]').val(),
                    config: configText
                }, function(response) {
                    if (response.success) {
                        alert('<?php _e('Configuration imported successfully!', 'yxjto-gateway'); ?>');
                        location.reload();
                    } else {
                        alert('<?php _e('Failed to import configuration: ', 'yxjto-gateway'); ?>' + response.data);
                    }
                });
            });
            
            // 取消导入
            $('#cancel-import').on('click', function() {
                $('#import-config-area').hide();
                $('#import-config-text').val('');
            });
        });
        </script>
        <?php
    }
    
    /**
     * AJAX: 保存配置
     */
    public function ajax_save_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'price_adjustment_nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }
        
        // 处理表单数据
        $config = [];
        $config['enabled'] = isset($_POST['enabled']);
        $config['strategy'] = sanitize_text_field($_POST['strategy'] ?? 'auto');
        $config['enable_validation'] = isset($_POST['enable_validation']);
        $config['enable_recalculation'] = isset($_POST['enable_recalculation']);
        $config['enable_logging'] = isset($_POST['enable_logging']);
        $config['fallback_to_legacy'] = isset($_POST['fallback_to_legacy']);
        $config['min_adjustment_threshold'] = floatval($_POST['min_adjustment_threshold'] ?? 0.01);
        $config['max_adjustment_percentage'] = intval($_POST['max_adjustment_percentage'] ?? 50);
        
        // 处理验证规则
        $config['validation_rules'] = [
            'validate_shipping' => isset($_POST['validation_rules']['validate_shipping']),
            'validate_discount' => isset($_POST['validation_rules']['validate_discount']),
            'validate_tax' => isset($_POST['validation_rules']['validate_tax'])
        ];
        
        // 处理策略偏好
        $config['strategy_preferences'] = [
            'small_difference_threshold' => floatval($_POST['strategy_preferences']['small_difference_threshold'] ?? 5.00),
            'large_difference_threshold' => floatval($_POST['strategy_preferences']['large_difference_threshold'] ?? 50.00),
            'prefer_proportional_for_small' => isset($_POST['strategy_preferences']['prefer_proportional_for_small']),
            'prefer_highest_first_for_large' => isset($_POST['strategy_preferences']['prefer_highest_first_for_large'])
        ];
        
        // 验证配置
        $validation = $this->config->validate_config($config);
        if (!$validation['valid']) {
            wp_send_json_error(implode(', ', $validation['errors']));
        }
        
        // 保存配置
        foreach ($config as $key => $value) {
            $this->config->set($key, $value);
        }
        
        if ($this->config->save()) {
            wp_send_json_success(__('Settings saved successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to save settings.', 'yxjto-gateway'));
        }
    }
    
    /**
     * AJAX: 重置配置
     */
    public function ajax_reset_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }
        
        if ($this->config->reset()) {
            wp_send_json_success(__('Settings reset successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to reset settings.', 'yxjto-gateway'));
        }
    }
    
    /**
     * AJAX: 测试价格调整
     */
    public function ajax_test_adjustment() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }
        
        // 运行测试脚本
        ob_start();
        
        // 这里可以运行一些测试用例
        echo "Price Adjustment Test Results:\n";
        echo "=============================\n\n";
        echo "Current Configuration:\n";
        echo "- Enabled: " . ($this->config->is_enabled() ? 'Yes' : 'No') . "\n";
        echo "- Strategy: " . $this->config->get('strategy') . "\n";
        echo "- Min Threshold: " . $this->config->get_min_threshold() . "\n";
        echo "- Max Adjustment: " . $this->config->get_max_adjustment_percentage() . "%\n\n";
        
        echo "Strategy Recommendations:\n";
        echo "- Small difference (2.00): " . $this->config->get_recommended_strategy(2.0, 3) . "\n";
        echo "- Medium difference (15.00): " . $this->config->get_recommended_strategy(15.0, 3) . "\n";
        echo "- Large difference (60.00): " . $this->config->get_recommended_strategy(60.0, 3) . "\n\n";
        
        echo "Adjustment Limits:\n";
        echo "- 100 to 110: " . ($this->config->is_adjustment_within_limits(100, 110) ? 'Allowed' : 'Exceeded') . "\n";
        echo "- 100 to 200: " . ($this->config->is_adjustment_within_limits(100, 200) ? 'Allowed' : 'Exceeded') . "\n";
        
        $output = ob_get_clean();
        wp_send_json_success($output);
    }
    
    /**
     * AJAX: 导出配置
     */
    public function ajax_export_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }
        
        $config_json = $this->config->export_config();
        wp_send_json_success($config_json);
    }
    
    /**
     * AJAX: 导入配置
     */
    public function ajax_import_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }
        
        $config_json = sanitize_textarea_field($_POST['config'] ?? '');
        
        if ($this->config->import_config($config_json)) {
            wp_send_json_success(__('Configuration imported successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to import configuration. Please check the JSON format.', 'yxjto-gateway'));
        }
    }
}
