/**
 * WordPress Multi Database Admin JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // 调试：检查税收按钮是否存在
    console.log('Tax buttons check:');
    console.log('tax-sync-now:', $('#tax-sync-now').length);
    console.log('tax-validate-data:', $('#tax-validate-data').length);
    console.log('tax-backup-data:', $('#tax-backup-data').length);
    console.log('tax-cleanup-data:', $('#tax-cleanup-data').length);
    console.log('tax-sync-result-message:', $('#tax-sync-result-message').length);
    
    // 检查yxjtoGateway对象
    if (typeof yxjtoGateway !== 'undefined') {
        console.log('yxjtoGateway object:', yxjtoGateway);
    } else {
        console.error('yxjtoGateway object not found!');
    }
    
    // 使用事件委托来确保按钮绑定
    $(document).on('click', '#tax-sync-now', function() {
        console.log('税收同步按钮点击 (事件委托)');
        handleTaxSync();
    });
    
    $(document).on('click', '#tax-validate-data', function() {
        console.log('税收验证按钮点击 (事件委托)');
        handleTaxValidate();
    });
    
    $(document).on('click', '#tax-backup-data', function() {
        console.log('税收备份按钮点击 (事件委托)');
        handleTaxBackup();
    });
    
    $(document).on('click', '#tax-cleanup-data', function() {
        console.log('税收清理按钮点击 (事件委托)');
        handleTaxCleanup();
    });
    
    // 税收同步处理函数
    function handleTaxSync() {
        var $button = $('#tax-sync-now');
        var $resultMessage = $('#tax-sync-result-message');
        
        console.log('Tax sync button clicked');
        console.log('AJAX URL:', yxjtoGateway.ajaxUrl);
        console.log('Nonce:', yxjtoGateway.nonce);
        
        $button.prop('disabled', true).text('同步中...');
        $resultMessage.hide();
        
        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'yxjto_tax_sync',
                security: yxjtoGateway.nonce
            },
            beforeSend: function() {
                console.log('Sending tax sync AJAX request...');
            },
            success: function(response) {
                console.log('Tax sync response:', response);
                if (response && response.success) {
                    $resultMessage.removeClass('notice-error').addClass('notice notice-success')
                        .html('<p>' + response.data.message + '</p>').show();
                } else {
                    var message = (response && response.data && response.data.message) ? 
                        response.data.message : '同步失败';
                    $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                        .html('<p>' + message + '</p>').show();
                }
            },
            error: function(xhr, status, error) {
                console.error('Tax sync error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                    .html('<p>同步请求失败: ' + error + '</p>').show();
            },
            complete: function() {
                console.log('Tax sync request completed');
                $button.prop('disabled', false).text('立即同步税收数据');
            }
        });
    }
    
    // 税收验证处理函数
    function handleTaxValidate() {
        var $button = $('#tax-validate-data');
        var $resultMessage = $('#tax-sync-result-message');
        
        console.log('Tax validate button clicked');
        
        $button.prop('disabled', true).text('验证中...');
        $resultMessage.hide();
        
        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'yxjto_tax_validate',
                security: yxjtoGateway.nonce
            },
            beforeSend: function() {
                console.log('Sending tax validate AJAX request...');
            },
            success: function(response) {
                console.log('Tax validate response:', response);
                if (response && response.success) {
                    var validation = response.data.validation_result;
                    var message = '<h4>验证结果：</h4>';
                    
                    if (validation.success) {
                        message += '<p style="color: green;">✓ 税收数据验证通过</p>';
                        message += '<ul>';
                        if (validation.checks.tax_rates) {
                            message += '<li>税率记录数：' + validation.checks.tax_rates.record_count + '</li>';
                        }
                        if (validation.checks.tax_rate_locations) {
                            message += '<li>税率位置记录数：' + validation.checks.tax_rate_locations.record_count + '</li>';
                        }
                        if (validation.checks.tax_options) {
                            message += '<li>税收选项数：' + validation.checks.tax_options.total_options + '</li>';
                            if (validation.checks.tax_options.missing_options.length > 0) {
                                message += '<li style="color: orange;">缺失选项：' + validation.checks.tax_options.missing_options.join(', ') + '</li>';
                            }
                        }
                        if (validation.checks.data_integrity) {
                            if (validation.checks.data_integrity.orphaned_locations > 0) {
                                message += '<li style="color: orange;">发现 ' + validation.checks.data_integrity.orphaned_locations + ' 个孤立的位置记录</li>';
                            } else {
                                message += '<li style="color: green;">数据完整性检查通过</li>';
                            }
                        }
                        message += '</ul>';
                    } else {
                        message += '<p style="color: red;">✗ 验证发现问题：' + (validation.error || '未知错误') + '</p>';
                    }
                    
                    $resultMessage.removeClass('notice-error').addClass('notice notice-success')
                        .html('<p>' + message + '</p>').show();
                } else {
                    var message = (response && response.data && response.data.message) ? 
                        response.data.message : '验证失败';
                    $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                        .html('<p>' + message + '</p>').show();
                }
            },
            error: function(xhr, status, error) {
                console.error('Tax validation error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                    .html('<p>验证请求失败: ' + error + '</p>').show();
            },
            complete: function() {
                console.log('Tax validate request completed');
                $button.prop('disabled', false).text('验证税收数据');
            }
        });
    }
    
    // 税收备份处理函数
    function handleTaxBackup() {
        var $button = $('#tax-backup-data');
        var $resultMessage = $('#tax-sync-result-message');
        
        console.log('Tax backup button clicked');
        
        $button.prop('disabled', true).text('备份中...');
        $resultMessage.hide();
        
        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'yxjto_tax_backup',
                security: yxjtoGateway.nonce
            },
            beforeSend: function() {
                console.log('Sending tax backup AJAX request...');
            },
            success: function(response) {
                console.log('Tax backup response:', response);
                if (response && response.success) {
                    $resultMessage.removeClass('notice-error').addClass('notice notice-success')
                        .html('<p>' + response.data.message + '</p>').show();
                } else {
                    var message = (response && response.data && response.data.message) ? 
                        response.data.message : '备份失败';
                    $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                        .html('<p>' + message + '</p>').show();
                }
            },
            error: function(xhr, status, error) {
                console.error('Tax backup error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                    .html('<p>备份请求失败: ' + error + '</p>').show();
            },
            complete: function() {
                console.log('Tax backup request completed');
                $button.prop('disabled', false).text('备份税收数据');
            }
        });
    }
    
    // 税收清理处理函数
    function handleTaxCleanup() {
        var $button = $('#tax-cleanup-data');
        var $resultMessage = $('#tax-sync-result-message');
        
        console.log('Tax cleanup button clicked');
        
        if (!confirm('确定要清理孤立的税收数据吗？此操作不可逆。')) {
            return;
        }
        
        $button.prop('disabled', true).text('清理中...');
        $resultMessage.hide();
        
        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'yxjto_tax_cleanup',
                security: yxjtoGateway.nonce
            },
            beforeSend: function() {
                console.log('Sending tax cleanup AJAX request...');
            },
            success: function(response) {
                console.log('Tax cleanup response:', response);
                if (response && response.success) {
                    $resultMessage.removeClass('notice-error').addClass('notice notice-success')
                        .html('<p>清理完成，删除了 ' + (response.data.cleaned_items || 0) + ' 个孤立记录</p>').show();
                } else {
                    var message = (response && response.data && response.data.message) ? 
                        response.data.message : '清理失败';
                    $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                        .html('<p>' + message + '</p>').show();
                }
            },
            error: function(xhr, status, error) {
                console.error('Tax cleanup error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                $resultMessage.removeClass('notice-success').addClass('notice notice-error')
                    .html('<p>清理请求失败: ' + error + '</p>').show();
            },
            complete: function() {
                console.log('Tax cleanup request completed');
                $button.prop('disabled', false).text('清理孤立数据');
            }
        });
    }
    
    // 标签页切换
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        
        var target = $(this).attr('href');
        
        // 更新标签页状态
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // 显示对应内容
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // 测试数据库连接
    $(document).on('click', '.test-connection', function() {
        var $button = $(this);
        var key = $button.data('key');
        var $status = $('#status_' + key);

        // 确保状态显示区域存在
        if ($status.length === 0) {
            console.error('Status element not found for key:', key);
            alert('Error: Status display element not found');
            return;
        }

        var config = {
            host: $('#db_host_' + key).val(),
            database: $('#db_database_' + key).val(),
            username: $('#db_username_' + key).val(),
            password: $('#db_password_' + key).val(),
            charset: $('#db_charset_' + key).val() || 'utf8mb4'
        };

        // 验证必填字段
        if (!config.host || !config.database || !config.username) {
            $status.html('<span class="notice notice-error inline"><p>请填写主机、数据库名和用户名</p></span>');
            return;
        }

        $button.prop('disabled', true).text(yxjtoGateway.strings.testing || 'Testing...');
        $status.html('<span class="spinner is-active"></span>');

        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'yxjto_gateway_test_connection',
                nonce: yxjtoGateway.nonce,
                host: config.host,
                database: config.database,
                username: config.username,
                password: config.password,
                charset: config.charset
            },
            success: function(response) {
                console.log('AJAX Response:', response);
                if (response && response.success) {
                    $status.html('<span class="notice notice-success inline"><p>' + response.message + '</p></span>');
                } else {
                    var message = (response && response.message) ? response.message : '连接失败';
                    $status.html('<span class="notice notice-error inline"><p>' + message + '</p></span>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error);
                var errorMsg = '连接测试失败';
                if (xhr.responseText) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMsg = response.message;
                        }
                    } catch (e) {
                        errorMsg += ': ' + xhr.responseText.substring(0, 100);
                    }
                }
                $status.html('<span class="notice notice-error inline"><p>' + errorMsg + '</p></span>');
            },
            complete: function() {
                $button.prop('disabled', false).text(yxjtoGateway.strings.testConnection || 'Test Connection');
            }
        });
    });
    
    // 切换数据库
    $('.switch-database').on('click', function() {
        var $button = $(this);
        var database = $button.data('key');
        
        if (!confirm(yxjtoGateway.strings.confirmSwitch || 'Are you sure you want to switch to this database?')) {
            return;
        }
        
        $button.prop('disabled', true).text(yxjtoGateway.strings.switching);
        
        $.ajax({
            url: yxjtoGateway.ajaxUrl,
            type: 'POST',
            data: {
                action: 'yxjto_gateway_switch_database',
                nonce: yxjtoGateway.nonce,
                database: database
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert(yxjtoGateway.strings.switchFailed);
            },
            complete: function() {
                $button.prop('disabled', false).text(yxjtoGateway.strings.switchToThis || 'Switch to This');
            }
        });
    });
    
    // 添加新数据库
    $('.add-database').on('click', function() {
        var timestamp = Date.now();
        var key = 'new_' + timestamp;
        
        var template = `
            <div class="database-config" data-key="${key}">
                <h3>
                    ${yxjtoGateway.strings.newDatabase || 'New Database'}
                    <button type="button" class="button test-connection" data-key="${key}">${yxjtoGateway.strings.testConnection || 'Test Connection'}</button>
                    <button type="button" class="button switch-database" data-key="${key}">${yxjtoGateway.strings.switchToThis || 'Switch to This'}</button>
                    <button type="button" class="button button-secondary remove-database" data-key="${key}">${yxjtoGateway.strings.remove || 'Remove'}</button>
                </h3>
                
                <table class="form-table">
                    <tr>
                        <th><label for="db_name_${key}">Name</label></th>
                        <td><input type="text" id="db_name_${key}" name="databases[${key}][name]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_host_${key}">Host</label></th>
                        <td><input type="text" id="db_host_${key}" name="databases[${key}][host]" value="localhost" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_database_${key}">Database</label></th>
                        <td><input type="text" id="db_database_${key}" name="databases[${key}][database]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_username_${key}">Username</label></th>
                        <td><input type="text" id="db_username_${key}" name="databases[${key}][username]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_password_${key}">Password</label></th>
                        <td><input type="password" id="db_password_${key}" name="databases[${key}][password]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_charset_${key}">Charset</label></th>
                        <td><input type="text" id="db_charset_${key}" name="databases[${key}][charset]" value="utf8mb4" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="db_enabled_${key}">Enabled</label></th>
                        <td><input type="checkbox" id="db_enabled_${key}" name="databases[${key}][enabled]" value="1" checked /></td>
                    </tr>
                </table>
                
                <div class="connection-status" id="status_${key}"></div>
            </div>
        `;
        
        $('.database-configs').append(template);
    });
    
    // 删除数据库配置
    $(document).on('click', '.remove-database', function() {
        if (confirm(yxjtoGateway.strings.confirmRemoveDatabase || 'Are you sure you want to remove this database configuration?')) {
            $(this).closest('.database-config').remove();
        }
    });
    
    // 添加IP规则
    $('.add-ip-rule').on('click', function() {
        var index = $('.ip-rule').length;
        
        var template = `
            <div class="ip-rule" data-index="${index}">
                <h3>
                    Rule ${index + 1}
                    <button type="button" class="button button-secondary remove-ip-rule" data-index="${index}">Remove</button>
                </h3>
                
                <table class="form-table">
                    <tr>
                        <th><label for="ip_range_${index}">IP Range</label></th>
                        <td>
                            <input type="text" id="ip_range_${index}" name="ip_rules[${index}][ip_range]" value="" class="regular-text" />
                            <p class="description">Examples: ***********, ***********/24, ***********-***********00</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="ip_database_${index}">Database</label></th>
                        <td>
                            <select id="ip_database_${index}" name="ip_rules[${index}][database]">
                                <option value="">${yxjtoGateway.strings.selectDatabase || 'Select Database'}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="ip_enabled_${index}">Enabled</label></th>
                        <td><input type="checkbox" id="ip_enabled_${index}" name="ip_rules[${index}][enabled]" value="1" checked /></td>
                    </tr>
                </table>
            </div>
        `;
        
        $('.ip-rules').append(template);
        
        // 填充数据库选项
        populateDatabaseOptions($('#ip_database_' + index));
    });
    
    // 删除IP规则
    $(document).on('click', '.remove-ip-rule', function() {
        if (confirm(yxjtoGateway.strings.confirmRemoveIpRule || 'Are you sure you want to remove this IP rule?')) {
            $(this).closest('.ip-rule').remove();
            updateRuleIndexes('.ip-rule', 'ip_rules');
        }
    });
    
    // 添加URL规则
    $('.add-url-rule').on('click', function() {
        var index = $('.url-rule').length;

        var template = `
            <div class="url-rule" data-index="${index}">
                <h3>
                    Rule ${index + 1}
                    <button type="button" class="button button-secondary remove-url-rule" data-index="${index}">Remove</button>
                </h3>
                
                <table class="form-table">
                    <tr>
                        <th><label for="url_parameter_${index}">Parameter Name</label></th>
                        <td><input type="text" id="url_parameter_${index}" name="url_rules[${index}][parameter]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="url_value_${index}">Parameter Value</label></th>
                        <td><input type="text" id="url_value_${index}" name="url_rules[${index}][value]" value="" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th><label for="url_database_${index}">Database</label></th>
                        <td>
                            <select id="url_database_${index}" name="url_rules[${index}][database]">
                                <option value="">${yxjtoGateway.strings.selectDatabase || 'Select Database'}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="url_enabled_${index}">Enabled</label></th>
                        <td><input type="checkbox" id="url_enabled_${index}" name="url_rules[${index}][enabled]" value="1" checked /></td>
                    </tr>
                </table>
            </div>
        `;
        
        $('.url-rules').append(template);

        // 填充数据库选项
        populateDatabaseOptions($('#url_database_' + index));
    });
    
    // 删除URL规则
    $(document).on('click', '.remove-url-rule', function() {
        if (confirm(yxjtoGateway.strings.confirmRemoveUrlRule || 'Are you sure you want to remove this URL rule?')) {
            $(this).closest('.url-rule').remove();
            updateRuleIndexes('.url-rule', 'url_rules');
        }
    });
    
    // 填充数据库选项
    function populateDatabaseOptions($select) {
        $select.empty().append(`<option value="">${yxjtoGateway.strings.selectDatabase || 'Select Database'}</option>`);

        $('.database-config').each(function() {
            var key = $(this).data('key');
            var name = $(this).find('input[name*="[name]"]').val() || key;
            $select.append(`<option value="${key}">${name}</option>`);
        });
    }
    
    // 更新规则索引
    function updateRuleIndexes(selector, prefix) {
        $(selector).each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('h3').first().text('Rule ' + (index + 1));
            
            // 更新所有输入字段的name属性
            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                    $(this).attr('name', newName);
                    
                    var id = $(this).attr('id');
                    if (id) {
                        var newId = id.replace(/_\d+$/, '_' + index);
                        $(this).attr('id', newId);
                        
                        // 更新对应的label
                        $('label[for="' + id + '"]').attr('for', newId);
                    }
                }
            });
        });
    }
});
