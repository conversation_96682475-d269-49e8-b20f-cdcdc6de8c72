#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的代码替换脚本
用于修复 class-order-replication.php 中的 YXJTO_Gateway::get_instance() 调用
"""

import re
import os

def safe_replace_gateway_calls(file_path):
    """安全替换 Gateway 调用"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 替换模式 1: $current_database = YXJTO_Gateway::get_instance()->get_current_database();
    pattern1 = r'(\s+)\$current_database = YXJTO_Gateway::get_instance\(\)->get_current_database\(\);'
    replacement1 = r'\1$gateway = $this->get_gateway_instance();\1if (!$gateway) {\1    $this->log_error("Gateway instance not available");\1    return;\1}\1$current_database = $gateway->get_current_database();'
    
    content = re.sub(pattern1, replacement1, content)
    
    # 替换模式 2: $gateway = YXJTO_Gateway::get_instance();
    pattern2 = r'(\s+)\$gateway = YXJTO_Gateway::get_instance\(\);'
    replacement2 = r'\1$gateway = $this->get_gateway_instance();\1if (!$gateway) {\1    $this->log_error("Gateway instance not available");\1    return;\1}'
    
    content = re.sub(pattern2, replacement2, content)
    
    # 替换模式 3: 其他直接调用 (但跳过 get_gateway_instance 方法内部的调用)
    # 检查是否在 get_gateway_instance 方法内部
    def replacement_func(match):
        full_match = match.group(0)
        preceding_text = content[:match.start()]
        
        # 检查是否在 get_gateway_instance 方法内部
        method_start = preceding_text.rfind('private function get_gateway_instance()')
        if method_start != -1:
            method_end = content.find('}', match.end())
            next_method = content.find('function ', match.end())
            if next_method == -1 or method_end < next_method:
                # 在 get_gateway_instance 方法内部，保持不变
                return full_match
        
        # 不在 get_gateway_instance 方法内部，进行替换
        indent = match.group(1)
        return f'{indent}$gateway = $this->get_gateway_instance();{indent}if (!$gateway) {{{indent}    $this->log_error("Gateway instance not available");{indent}    return;{indent}}}{indent}$gateway'
    
    pattern3 = r'(\s+)YXJTO_Gateway::get_instance\(\)'
    content = re.sub(pattern3, replacement_func, content)
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"文件已更新: {file_path}")
    print(f"备份已创建: {backup_path}")

if __name__ == "__main__":
    file_path = "class-order-replication.php"
    if os.path.exists(file_path):
        safe_replace_gateway_calls(file_path)
    else:
        print(f"文件不存在: {file_path}")
