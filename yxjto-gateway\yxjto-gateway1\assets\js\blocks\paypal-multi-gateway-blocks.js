/**
 * YXJTO PayPal Multi Gateway WooCommerce Blocks Support
 */

const { registerPaymentMethod } = window.wc.wcBlocksRegistry;
const { createElement, useState, useEffect, useRef } = window.wp.element;
const { __ } = window.wp.i18n;
const { decodeEntities } = window.wp.htmlEntities;

/**
 * 获取安全的图标 URL
 */
const getIconUrl = (iconPath) => {
    const baseUrl = window.yxjtoPaypalMultiGatewayData?.plugin_url || '';
    return baseUrl + iconPath;
};

/**
 * YXJTO PayPal Multi Gateway Payment Method Component
 */
const YXJTOPayPalMultiGatewayComponent = ({ billing, shippingData, eventRegistration, emitResponse }) => {
    const [accountStatus, setAccountStatus] = useState({
        loading: true,
        available: false,
        message: __('Checking...', 'yxjto-paypal-multi-gateway')
    });

    // Use ref to track log output, avoid duplicates
    const loggedAccountRef = useRef(null);
    const lastStatusCheckRef = useRef(null);

    const { onPaymentSetup, onCheckoutValidation } = eventRegistration;

    // Check account status
    useEffect(() => {
        // Debug info
        console.log('YXJTO PayPal Multi Gateway Blocks: Initializing component');
        console.log('Available global variables:', {
            wc: typeof window.wc,
            yxjtoPaypalMultiGatewayData: typeof window.yxjtoPaypalMultiGatewayData,
            ajaxurl: typeof window.ajaxurl
        });

        if (window.yxjtoPaypalMultiGatewayData) {
            console.log('YXJTO PayPal Data:', window.yxjtoPaypalMultiGatewayData);
        }

        checkAccountStatus();
    }, []);

    // Payment setup handling
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            if (!accountStatus.available) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: accountStatus.message || __('Payment method not available', 'yxjto-paypal-multi-gateway')
                };
            }

            // Get currently selected account ID
            const selectedAccountId = accountStatus.selected_account ? accountStatus.selected_account.id : null;

            // 调试日志：记录 Blocks 中选择的账号ID
            console.log('YXJTO PayPal Multi Gateway [Blocks]: Selected Account ID for payment:', selectedAccountId);
            console.log('YXJTO PayPal Multi Gateway [Blocks]: Account Status:', accountStatus);
            console.log('YXJTO PayPal Multi Gateway [Blocks]: Selected Account Object:', accountStatus.selected_account);

            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        yxjto_paypal_multi_gateway: true,
                        paypal_account_id: selectedAccountId
                    }
                }
            };
        });

        return unsubscribe;
    }, [onPaymentSetup, accountStatus, emitResponse]);

    // Checkout validation handling
    useEffect(() => {
        const unsubscribe = onCheckoutValidation(() => {
            if (!accountStatus.available) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: accountStatus.message || __('Payment method not available', 'yxjto-paypal-multi-gateway')
                };
            }

            return {
                type: emitResponse.responseTypes.SUCCESS
            };
        });

        return unsubscribe;
    }, [onCheckoutValidation, accountStatus, emitResponse]);

    /**
     * Check account status
     */
    const checkAccountStatus = async () => {
        try {
            // Get correct AJAX URL - try multiple sources
            const ajaxUrl = window.yxjtoPaypalMultiGatewayData?.admin_ajax_url ||
                           window.wc?.wcSettings?.ajax_url ||
                           window.yxjtoPaypalMultiGatewayData?.ajax_url ||
                           window.ajaxurl ||
                           '/wp-admin/admin-ajax.php';

            console.log('YXJTO PayPal Multi Gateway: Using AJAX URL:', ajaxUrl);
            console.log('YXJTO PayPal Multi Gateway: Using Nonce:', window.yxjtoPaypalMultiGatewayData?.nonce);

            // 使用标准WordPress AJAX
            const requestData = {
                action: 'yxjto_paypal_multi_gateway_check_status',
                nonce: window.yxjtoPaypalMultiGatewayData?.nonce || ''
            };

            console.log('YXJTO PayPal Multi Gateway: Sending request data:', requestData);

            const response = await fetch(ajaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(requestData)
            });

            const data = await response.json();

            // Only output detailed logs when status actually changes
            const statusKey = data.success ?
                `success-${data.data.has_valid_accounts}-${data.data.selected_account?.type}` :
                `error-${data.data}`;

            if (lastStatusCheckRef.current !== statusKey) {
                lastStatusCheckRef.current = statusKey;
                console.log('YXJTO PayPal Multi Gateway: Response status:', response.status, response.statusText);
                console.log('YXJTO PayPal Multi Gateway: Response data:', data);
            }

            if (data.success) {
                if (lastStatusCheckRef.current !== statusKey) {
                    console.log('YXJTO PayPal Multi Gateway: Status check successful');
                }
                setAccountStatus({
                    loading: false,
                    available: data.data.gateway_available && data.data.has_valid_accounts,
                    selected_account: data.data.selected_account,
                    message: data.data.message
                });
            } else {
                if (lastStatusCheckRef.current !== statusKey) {
                    console.log('YXJTO PayPal Multi Gateway: Status check failed:', data.data);
                }
                setAccountStatus({
                    loading: false,
                    available: false,
                    message: data.data || __('Check failed', 'yxjto-paypal-multi-gateway')
                });
            }
        } catch (error) {
            console.error('YXJTO PayPal Multi Gateway: Request exception:', error);
            setAccountStatus({
                loading: false,
                available: false,
                message: __('Network error: ', 'yxjto-paypal-multi-gateway') + error.message
            });
        }
    };

    /**
     * Render status indicator
     */
    const renderStatusIndicator = () => {
        if (accountStatus.loading) {
            return createElement('div', {
                className: 'yxjto-paypal-status-loading',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '10px',
                    background: '#e3f2fd',
                    border: '1px solid #bbdefb',
                    borderRadius: '4px',
                    color: '#1976d2',
                    fontSize: '14px'
                }
            }, [
                createElement('div', {
                    key: 'spinner',
                    style: {
                        width: '16px',
                        height: '16px',
                        border: '2px solid #1976d2',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                    }
                }),
                __('Checking PayPal status...', 'yxjto-paypal-multi-gateway')
            ]);
        }

        if (accountStatus.available) {
            // Get selected account info (load balancing selection)
            const selectedAccount = accountStatus.selected_account;

            // Only output debug logs when account changes, avoid duplicates
            const currentAccountKey = selectedAccount ?
                `${selectedAccount.type}-${selectedAccount.display}` : 'none';

            if (loggedAccountRef.current !== currentAccountKey) {
                loggedAccountRef.current = currentAccountKey;

                console.log('YXJTO PayPal Blocks - Selected account debug:', {
                    accountStatus: accountStatus,
                    selectedAccount: selectedAccount,
                    selectedAccountExists: !!selectedAccount,
                    selectedAccountType: typeof selectedAccount,
                    selectedAccountIcon: selectedAccount?.icon,
                    selectedAccountDisplay: selectedAccount?.display
                });
            }

            return createElement('div', {
                className: 'yxjto-paypal-status-success',
                style: {
                    padding: '12px',
                    background: '#d4edda',
                    border: '1px solid #c3e6cb',
                    borderRadius: '6px',
                    color: '#155724',
                    fontSize: '14px',
                    marginBottom: '10px'
                }
            }, [
                // Main title
                createElement('div', {
                    key: 'title',
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        marginBottom: selectedAccount ? '10px' : '0',
                        fontWeight: '500'
                    }
                }, [
                    createElement('div', {
                        key: 'icon',
                        style: {
                            width: '16px',
                            height: '16px',
                            background: '#28a745',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '10px'
                        }
                    }, '✓'),
                    __('Verification completed', 'yxjto-paypal-multi-gateway')
                ]),

                // Display selected account info (if available)
                ...(selectedAccount && selectedAccount.display ? [createElement('div', {
                    key: 'selected-account',
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        fontSize: '13px',
                        color: '#0c5460',
                        padding: '6px 8px',
                        background: 'rgba(255, 255, 255, 0.3)',
                        borderRadius: '4px',
                        marginBottom: '8px'
                    }
                }, [
                    createElement('span', {
                        key: 'account-text',
                        style: {
                            flex: '1'
                        }
                    }, selectedAccount.display)
                ])] : [])
            ]);
        }

        return createElement('div', {
            className: 'yxjto-paypal-status-error',
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                background: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '4px',
                color: '#721c24',
                fontSize: '14px'
            }
        }, [
            createElement('div', {
                key: 'icon',
                style: {
                    width: '16px',
                    height: '16px',
                    background: '#dc3545',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '10px'
                }
            }, '!'),
            accountStatus.message
        ]);
    };

    /**
     * Render payment info
     */
    const renderPaymentInfo = () => {
        return createElement('div', {
            className: 'yxjto-paypal-payment-info',
            style: {
                marginTop: '15px',
                padding: '12px',
                background: '#f8f9fa',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
            }
        }, [
            // Payment notice
            createElement('div', {
                key: 'notice',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    fontSize: '13px',
                    color: '#495057',
                    marginBottom: '8px'
                }
            }, [
                createElement('div', {
                    key: 'icon',
                    style: {
                        width: '16px',
                        height: '16px',
                        background: '#007cba',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '10px'
                    }
                }, 'i'),
                __('You will be redirected to PayPal to complete your payment securely.', 'yxjto-paypal-multi-gateway')
            ]),

            // Security info
            createElement('div', {
                key: 'security',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    color: '#6c757d',
                    fontSize: '12px'
                }
            }, [
                createElement('span', {
                    key: 'icon',
                    style: {
                        fontSize: '12px'
                    }
                }, '🔒'),
                __('Secure payment powered by PayPal', 'yxjto-paypal-multi-gateway')
            ]),

            // Test mode notice
            window.yxjtoPaypalMultiGatewayData?.testmode === 'yes' && createElement('div', {
                key: 'testmode',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginTop: '10px',
                    padding: '8px 12px',
                    background: '#fff3cd',
                    border: '1px solid #ffeaa7',
                    borderRadius: '4px',
                    color: '#856404',
                    fontSize: '13px'
                }
            }, [
                createElement('div', {
                    key: 'icon',
                    style: {
                        width: '14px',
                        height: '14px',
                        background: '#ffc107',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '8px'
                    }
                }, 'T'),
                __('Test mode - No real transactions will be processed', 'yxjto-paypal-multi-gateway')
            ])
        ]);
    };

    return createElement('div', {
        className: 'yxjto-paypal-multi-gateway-blocks'
    }, [
        renderStatusIndicator(),
        renderPaymentInfo()
    ]);
};

/**
 * YXJTO PayPal Multi Gateway Payment Method Label Component
 */
const YXJTOPayPalMultiGatewayLabel = ({ components }) => {
    const { PaymentMethodLabel } = components;

    // Create label with icon
    const labelContent = createElement('div', {
        style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
        }
    }, [
        // PayPal icon
        createElement('img', {
            key: 'paypal-icon',
            src: getIconUrl('assets/images/paypal-logo.svg'),
            alt: 'PayPal',
            style: {
                maxHeight: '24px',
                verticalAlign: 'middle'
            },
            onError: function(e) {
                // Hide icon if loading fails
                e.target.style.display = 'none';
            }
        }),
        // Payment method text
        createElement('span', {
            key: 'label-text'
        }, window.yxjtoPaypalMultiGatewayData?.title || 'YXJTO PayPal')
    ]);

    return createElement(PaymentMethodLabel, {
        text: labelContent
    });
};

/**
 * Register payment method
 */
const yxjtoPaypalMultiGatewayPaymentMethod = {
    name: 'yxjto_paypal_multi_gateway',
    label: createElement(YXJTOPayPalMultiGatewayLabel),
    content: createElement(YXJTOPayPalMultiGatewayComponent),
    edit: createElement(YXJTOPayPalMultiGatewayComponent),
    canMakePayment: () => {
        // 检查基本可用性
        return window.yxjtoPaypalMultiGatewayData?.enabled === 'yes';
    },
    ariaLabel: window.yxjtoPaypalMultiGatewayData?.title || 'YXJTO PayPal',
    supports: {
        features: window.yxjtoPaypalMultiGatewayData?.supports || ['products']
    }
};

// 注册支付方式
registerPaymentMethod(yxjtoPaypalMultiGatewayPaymentMethod);

// 添加 CSS 动画
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .yxjto-paypal-multi-gateway-blocks {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }

    .yxjto-paypal-multi-gateway-blocks h5 {
        color: #333;
    }

    .yxjto-paypal-multi-gateway-blocks ul {
        color: #666;
    }
`;
document.head.appendChild(style);
