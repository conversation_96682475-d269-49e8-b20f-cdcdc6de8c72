<?php
/**
 * PayPal Multi Gateway Configuration Manager
 * 
 * 专门处理PHP配置文件的读取、写入和管理
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Config {

    private static $instance = null;
    private $config_file_path;
    private $config_backup_path;
    private $config_dir;
    private $config_cache_key = 'yxjto_paypal_multi_gateway_config_cache';
    private $config_cache_time = 3600; // 1小时缓存

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // 使用YXJTO主配置文件
        if (!defined('WP_CONTENT_DIR')) {
            define('WP_CONTENT_DIR', YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_DIR . '../../../wp-content');
        }

        $this->config_file_path = WP_CONTENT_DIR . '/yxjto-gateway-config.php';
        $this->config_backup_path = WP_CONTENT_DIR . '/yxjto-gateway-config-backup.php';

        // 确保配置文件存在
        if (!file_exists($this->config_file_path)) {
            $this->create_default_config();
        }
    }

    /**
     * 确保配置目录存在
     */
    private function ensure_config_directory() {
        if (!file_exists($this->config_dir)) {
            wp_mkdir_p($this->config_dir);
            
            // 创建.htaccess文件保护配置目录
            $htaccess_content = "# PayPal Multi Gateway Config Protection\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "<Files ~ \"\\.(php)$\">\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</Files>\n";
            
            file_put_contents($this->config_dir . '.htaccess', $htaccess_content);
            
            // 创建index.php文件防止目录浏览
            file_put_contents($this->config_dir . 'index.php', '<?php // Silence is golden');
        }
    }

    /**
     * 初始化配置文件
     */
    private function initialize_config_file() {
        if (!file_exists($this->config_file_path)) {
            $this->create_default_config();
        }
    }

    /**
     * 创建默认配置文件
     */
    private function create_default_config() {
        $default_config = array(
            'databases' => array(),
            'ip_rules' => array(),
            'url_rules' => array(),
            'settings' => array(),
            'paypal_multi_gateway' => array(
                'version' => YXJTO_PAYPAL_MULTI_GATEWAY_VERSION,
                'last_updated' => current_time('mysql'),
                'settings' => array(
                    'load_balancing' => 'random',
                    'retry_count' => 1,
                    'timeout' => 30,
                    'test_mode' => 'no'
                ),
                'accounts' => array()
            )
        );

        $this->write_config_file($default_config);
    }

    /**
     * 读取配置
     */
    public function get_config() {
        // 尝试从缓存获取
        $cached_config = get_transient($this->config_cache_key);
        if ($cached_config !== false) {
            return $cached_config;
        }

        // 从YXJTO主配置文件读取
        $main_config = $this->read_config_file();

        if ($main_config === false) {
            // 配置文件损坏，尝试从备份恢复
            $main_config = $this->restore_from_backup();

            if ($main_config === false) {
                // 备份也失败，创建默认配置
                $this->create_default_config();
                $main_config = $this->read_config_file();
            }
        }

        // 提取PayPal Multi Gateway配置部分
        $paypal_config = isset($main_config['paypal_multi_gateway']) ? $main_config['paypal_multi_gateway'] : array(
            'settings' => array(),
            'accounts' => array()
        );

        // 缓存配置
        if ($paypal_config !== false) {
            set_transient($this->config_cache_key, $paypal_config, $this->config_cache_time);
        }

        return $paypal_config;
    }

    /**
     * 从文件读取配置
     */
    private function read_config_file() {
        if (!file_exists($this->config_file_path)) {
            return false;
        }

        try {
            $config = include $this->config_file_path;
            
            // 验证配置格式 - YXJTO主配置文件应该是数组
            if (!is_array($config)) {
                error_log('PayPal Multi Gateway: Invalid YXJTO config file format');
                return false;
            }

            return $config;
        } catch (Exception $e) {
            error_log('PayPal Multi Gateway: Error reading config file - ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证配置结构
     */
    private function validate_config_structure($config) {
        $required_keys = array('version', 'last_updated', 'settings', 'accounts');
        
        foreach ($required_keys as $key) {
            if (!isset($config[$key])) {
                return false;
            }
        }

        // 验证settings结构
        if (!is_array($config['settings'])) {
            return false;
        }

        // 验证accounts结构
        if (!is_array($config['accounts'])) {
            return false;
        }

        $required_account_types = array('email_accounts', 'api_accounts', 'paypal_me_accounts');
        foreach ($required_account_types as $type) {
            if (!isset($config['accounts'][$type]) || !is_array($config['accounts'][$type])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取插件设置
     */
    public function get_settings() {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $paypal_config = WP_Multi_DB_Config_Manager::get_paypal_multi_gateway_settings();
            return isset($paypal_config['settings']) ? $paypal_config['settings'] : array();
        }

        // 使用本地配置文件
        $config = $this->get_config();
        return $config ? $config['settings'] : array();
    }

    /**
     * 更新插件设置
     */
    public function update_settings($new_settings) {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $paypal_config = WP_Multi_DB_Config_Manager::get_paypal_multi_gateway_settings();
            $paypal_config['settings'] = array_merge($paypal_config['settings'], $new_settings);
            return WP_Multi_DB_Config_Manager::save_paypal_multi_gateway_settings($paypal_config);
        }

        // 使用本地配置文件
        $config = $this->get_config();
        if (!$config) {
            $config = array(
                'settings' => array(),
                'accounts' => array()
            );
        }

        // 合并新设置
        $config['settings'] = array_merge($config['settings'], $new_settings);
        $config['last_updated'] = current_time('mysql');

        return $this->save_config($config);
    }

    /**
     * 获取所有账户
     */
    public function get_accounts() {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            return WP_Multi_DB_Config_Manager::get_paypal_accounts();
        }

        // 使用本地配置文件
        $config = $this->get_config();
        return $config ? $config['accounts'] : array();
    }

    /**
     * 保存所有账户
     */
    public function save_accounts($accounts) {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            return WP_Multi_DB_Config_Manager::save_paypal_accounts($accounts);
        }

        // 使用本地配置文件
        $config = $this->get_config();

        if (!$config) {
            $config = array(
                'settings' => array(),
                'accounts' => array()
            );
        }

        $config['accounts'] = $accounts;

        return $this->save_config($config);
    }

    /**
     * 保存配置到YXJTO主配置文件
     */
    private function save_config($paypal_config) {
        // 读取YXJTO主配置文件
        $main_config = $this->read_config_file();

        if ($main_config === false) {
            // 如果主配置文件不存在，创建基本结构
            $main_config = array();
        }

        // 更新PayPal Multi Gateway配置部分
        $main_config['paypal_multi_gateway'] = $paypal_config;

        // 保存到主配置文件
        $result = $this->write_config_file($main_config);

        if ($result) {
            // 清除缓存
            delete_transient($this->config_cache_key);
        }

        return $result;
    }



    /**
     * 写入配置到YXJTO主配置文件
     */
    private function write_config_file($config) {
        try {
            // 创建备份
            if (file_exists($this->config_file_path)) {
                copy($this->config_file_path, $this->config_backup_path);
            }

            // 生成YXJTO配置文件内容
            $config_content = "<?php\n";
            $config_content .= "/**\n";
            $config_content .= " * YXJTO Gateway Configuration\n";
            $config_content .= " * Generated on " . current_time('Y-m-d H:i:s') . "\n";
            $config_content .= " */\n\n";
            $config_content .= "return " . var_export($config, true) . ";\n";

            // 使用临时文件确保原子写入
            $temp_file = $this->config_file_path . '.tmp';

            if (file_put_contents($temp_file, $config_content, LOCK_EX) === false) {
                return false;
            }

            // 原子性地移动临时文件到目标位置
            if (!rename($temp_file, $this->config_file_path)) {
                unlink($temp_file);
                return false;
            }

            return true;
        } catch (Exception $e) {
            error_log('PayPal Multi Gateway: Error writing YXJTO config file: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 添加账户
     */
    public function add_account($account_type, $account_data) {
        $config = $this->get_config();
        if (!$config) {
            return false;
        }

        $account_key = $account_type . '_accounts';
        if (!isset($config['accounts'][$account_key])) {
            $config['accounts'][$account_key] = array();
        }

        // 生成唯一ID
        $account_data['id'] = $this->generate_account_id($account_type, $account_data);
        $account_data['created_at'] = current_time('mysql');
        $account_data['status'] = 'active';

        $config['accounts'][$account_key][] = $account_data;
        $config['last_updated'] = current_time('mysql');

        if ($this->write_config($config)) {
            return $account_data['id'];
        }

        return false;
    }

    /**
     * 更新账户
     */
    public function update_account($account_id, $account_data) {
        $config = $this->get_config();
        if (!$config) {
            return false;
        }

        foreach ($config['accounts'] as $type => &$accounts) {
            foreach ($accounts as &$account) {
                if ($account['id'] === $account_id) {
                    $account = array_merge($account, $account_data);
                    $account['updated_at'] = current_time('mysql');
                    $config['last_updated'] = current_time('mysql');
                    
                    return $this->write_config($config);
                }
            }
        }

        return false;
    }

    /**
     * 删除账户
     */
    public function delete_account($account_id) {
        $config = $this->get_config();
        if (!$config) {
            return false;
        }

        foreach ($config['accounts'] as $type => &$accounts) {
            foreach ($accounts as $index => $account) {
                if ($account['id'] === $account_id) {
                    unset($accounts[$index]);
                    $accounts = array_values($accounts); // 重新索引
                    $config['last_updated'] = current_time('mysql');
                    
                    return $this->write_config($config);
                }
            }
        }

        return false;
    }

    /**
     * 生成账户ID
     */
    private function generate_account_id($account_type, $account_data) {
        $identifier = '';

        switch ($account_type) {
            case 'email':
                $identifier = $account_data['email'];
                break;
            case 'paypal_me':
                $identifier = $account_data['paypal_me_url'];
                break;
            case 'api':
                $identifier = $account_data['client_id'];
                break;
        }

        return $account_type . '_' . md5($identifier . time());
    }

    /**
     * 写入配置到文件
     */
    private function write_config($config) {
        try {
            // 创建备份
            $this->create_backup();

            // 生成配置文件内容
            $config_content = $this->generate_config_file_content($config);

            // 使用临时文件确保原子写入
            $temp_file = $this->config_file_path . '.tmp';

            if (file_put_contents($temp_file, $config_content, LOCK_EX) === false) {
                error_log('PayPal Multi Gateway: Failed to write temp config file');
                return false;
            }

            // 原子重命名
            if (!rename($temp_file, $this->config_file_path)) {
                error_log('PayPal Multi Gateway: Failed to rename temp config file');
                unlink($temp_file);
                return false;
            }

            // 设置文件权限
            chmod($this->config_file_path, 0644);

            // 清除缓存
            delete_transient($this->config_cache_key);

            return true;

        } catch (Exception $e) {
            error_log('PayPal Multi Gateway: Error writing config file - ' . $e->getMessage());

            // 清理临时文件
            if (isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }

            return false;
        }
    }

    /**
     * 生成配置文件内容
     */
    private function generate_config_file_content($config) {
        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * PayPal Multi Gateway Configuration\n";
        $content .= " * Generated on: " . current_time('Y-m-d H:i:s') . "\n";
        $content .= " * Do not edit this file manually unless you know what you're doing.\n";
        $content .= " */\n\n";
        $content .= "if (!defined('ABSPATH')) {\n";
        $content .= "    exit; // 防止直接访问\n";
        $content .= "}\n\n";
        $content .= "return " . $this->var_export_formatted($config) . ";\n";

        return $content;
    }

    /**
     * 格式化的var_export
     */
    private function var_export_formatted($var, $indent = 0) {
        $spaces = str_repeat('    ', $indent);

        if (is_array($var)) {
            $output = "array(\n";
            foreach ($var as $key => $value) {
                $output .= $spaces . '    ' . var_export($key, true) . ' => ';
                if (is_array($value)) {
                    $output .= $this->var_export_formatted($value, $indent + 1);
                } else {
                    $output .= var_export($value, true);
                }
                $output .= ",\n";
            }
            $output .= $spaces . ')';
            return $output;
        } else {
            return var_export($var, true);
        }
    }

    /**
     * 创建备份
     */
    private function create_backup() {
        if (file_exists($this->config_file_path)) {
            copy($this->config_file_path, $this->config_backup_path);
        }
    }

    /**
     * 从备份恢复
     */
    private function restore_from_backup() {
        if (file_exists($this->config_backup_path)) {
            try {
                $config = include $this->config_backup_path;

                if (is_array($config) && $this->validate_config_structure($config)) {
                    // 恢复备份到主配置文件
                    copy($this->config_backup_path, $this->config_file_path);
                    return $config;
                }
            } catch (Exception $e) {
                error_log('PayPal Multi Gateway: Error restoring from backup - ' . $e->getMessage());
            }
        }

        return false;
    }

    /**
     * 获取配置文件信息
     */
    public function get_config_file_info() {
        $info = array(
            'config_file_exists' => file_exists($this->config_file_path),
            'backup_file_exists' => file_exists($this->config_backup_path),
            'config_file_path' => $this->config_file_path,
            'backup_file_path' => $this->config_backup_path,
            'config_file_size' => file_exists($this->config_file_path) ? filesize($this->config_file_path) : 0,
            'config_file_modified' => file_exists($this->config_file_path) ? filemtime($this->config_file_path) : 0,
            'config_writable' => is_writable($this->config_dir)
        );

        if ($info['config_file_exists']) {
            $config = $this->get_config();
            $info['config_version'] = isset($config['version']) ? $config['version'] : 'unknown';
            $info['last_updated'] = isset($config['last_updated']) ? $config['last_updated'] : 'unknown';
            $info['total_accounts'] = 0;

            if (isset($config['accounts'])) {
                foreach ($config['accounts'] as $accounts) {
                    $info['total_accounts'] += count($accounts);
                }
            }
        }

        return $info;
    }

    /**
     * 验证配置文件完整性
     */
    public function validate_config_file() {
        $config = $this->read_config_file();

        if ($config === false) {
            return array(
                'valid' => false,
                'error' => 'Cannot read config file'
            );
        }

        if (!$this->validate_config_structure($config)) {
            return array(
                'valid' => false,
                'error' => 'Invalid config structure'
            );
        }

        return array(
            'valid' => true,
            'config' => $config
        );
    }

    /**
     * 重置配置文件
     */
    public function reset_config() {
        // 创建备份
        $this->create_backup();

        // 删除现有配置文件
        if (file_exists($this->config_file_path)) {
            unlink($this->config_file_path);
        }

        // 清除缓存
        delete_transient($this->config_cache_key);

        // 创建默认配置
        $this->create_default_config();

        return true;
    }

    /**
     * 清除配置缓存
     */
    public function clear_cache() {
        delete_transient($this->config_cache_key);
    }
}
