/**
 * YXJTO PayPal Multi Gateway Admin JavaScript
 */

(function($) {
    'use strict';

    var YXJTOPayPalMultiGateway = {
        
        init: function() {
            this.bindEvents();
            this.initModal();
        },
        
        bindEvents: function() {
            var self = this;

            // Add account button
            $(document).on('click', '#add-account-btn', function(e) {
                self.showAddAccountModal(e);
            });

            // Edit account button
            $(document).on('click', '.edit-account-btn', function(e) {
                self.showEditAccountModal.call(this, e);
            });

            // Delete account button
            $(document).on('click', '.delete-account-btn', function(e) {
                self.deleteAccount.call(this, e);
            });

            // Enable account button
            $(document).on('click', '.enable-account-btn', function(e) {
                self.toggleAccount(e, 'enable', this);
            });

            // Disable account button
            $(document).on('click', '.disable-account-btn', function(e) {
                self.toggleAccount(e, 'disable', this);
            });

            // Modal events
            $(document).on('click', '.yxjto-paypal-modal-close, #cancel-account-btn', function(e) {
                self.hideModal();
            });

            $(document).on('click', '#save-account-btn', function(e) {
                self.saveAccount(e);
            });

            // Account type change
            $(document).on('change', '#account-type', function() {
                self.toggleAccountFields.call(this);
            });

            // Close modal on outside click
            $(document).on('click', '.yxjto-paypal-modal', function(e) {
                if (e.target === this) {
                    self.hideModal();
                }
            });

            // Escape key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) {
                    self.hideModal();
                }
            });
        },
        
        initModal: function() {
            // Create modal if it doesn't exist
            if ($('#account-modal').length === 0) {
                // Modal is already in the HTML, so we don't need to create it
            }
        },
        
        showAddAccountModal: function(e) {
            e.preventDefault();

            // Reset form
            $('#account-form')[0].reset();
            $('#account-id').val('');
            $('#modal-title').text(yxjto_paypal_multi_gateway_admin.strings.add_new_account);

            // Hide all account type fields
            $('.account-type-fields').hide();

            // Reset account type dropdown
            $('#account-type').val('');

            // Show modal
            $('#account-modal').show();
        },
        
        showEditAccountModal: function(e) {
            e.preventDefault();
            
            var accountId = $(this).data('account-id');
            var self = YXJTOPayPalMultiGateway;
            
            if (!accountId) {
                self.showMessage(yxjto_paypal_multi_gateway_admin.strings.invalid_account_id, 'error');
                return;
            }

            // Get account data
            $.ajax({
                url: yxjto_paypal_multi_gateway_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'yxjto_paypal_multi_gateway_get_account',
                    nonce: yxjto_paypal_multi_gateway_admin.nonce,
                    account_id: accountId
                },
                beforeSend: function() {
                    $(e.target).prop('disabled', true).text(yxjto_paypal_multi_gateway_admin.strings.loading || 'Loading...');
                },
                success: function(response) {
                    if (response.success) {
                        self.populateEditForm(accountId, response.data);
                        $('#modal-title').text(yxjto_paypal_multi_gateway_admin.strings.edit_account);
                        $('#account-modal').show();
                    } else {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.get_account_data_failed + ': ' + response.data, 'error');
                    }
                },
                error: function() {
                    self.showMessage(yxjto_paypal_multi_gateway_admin.strings.network_error, 'error');
                },
                complete: function() {
                    $(e.target).prop('disabled', false).text(yxjto_paypal_multi_gateway_admin.strings.edit_account || 'Edit');
                }
            });
        },
        
        populateEditForm: function(accountId, data) {
            $('#account-id').val(accountId);
            $('#account-type').val(data.account_type);
            $('#account-weight').val(data.weight);
            
            // Hide all fields first
            $('.account-type-fields').hide();
            
            // Show relevant fields and populate data
            switch (data.account_type) {
                case 'email':
                    $('#email-fields').show();
                    $('#paypal-email').val(data.account_data.email);
                    break;
                case 'paypal_me':
                    $('#paypal-me-fields').show();
                    $('#paypal-me-url').val(data.account_data.paypal_me_url);
                    break;
                case 'api':
                    $('#api-fields').show();
                    $('#client-id').val(data.account_data.client_id);
                    $('#client-secret').val(data.account_data.client_secret);
                    $('#sandbox-mode').prop('checked', data.account_data.sandbox);
                    break;
            }
        },
        
        deleteAccount: function(e) {
            e.preventDefault();
            
            var accountId = $(this).data('account-id');
            var self = YXJTOPayPalMultiGateway;
            
            if (!accountId) {
                self.showMessage(yxjto_paypal_multi_gateway_admin.strings.invalid_account_id, 'error');
                return;
            }

            if (!confirm(yxjto_paypal_multi_gateway_admin.strings.confirm_delete)) {
                return;
            }

            $.ajax({
                url: yxjto_paypal_multi_gateway_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'yxjto_paypal_multi_gateway_delete_account',
                    nonce: yxjto_paypal_multi_gateway_admin.nonce,
                    account_id: accountId
                },
                beforeSend: function() {
                    $(e.target).prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.account_deleted, 'success');
                        // Remove the row from table
                        $('tr[data-account-id="' + accountId + '"]').fadeOut(function() {
                            $(this).remove();
                        });
                    } else {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.delete_account_failed + ' ' + response.data, 'error');
                    }
                },
                error: function() {
                    self.showMessage(yxjto_paypal_multi_gateway_admin.strings.network_error, 'error');
                },
                complete: function() {
                    $(e.target).prop('disabled', false);
                }
            });
        },
        
        toggleAccount: function(e, action, element) {
            e.preventDefault();
            
            var accountId = $(element).data('account-id');
            var self = YXJTOPayPalMultiGateway;
            var newStatus = action === 'enable' ? 'active' : 'inactive';
            var confirmMessage = action === 'enable' ? 
                yxjto_paypal_multi_gateway_admin.strings.confirm_enable : 
                yxjto_paypal_multi_gateway_admin.strings.confirm_disable;
            
            if (!accountId) {
                self.showMessage(yxjto_paypal_multi_gateway_admin.strings.invalid_account_id, 'error');
                return;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            $.ajax({
                url: yxjto_paypal_multi_gateway_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'yxjto_paypal_multi_gateway_toggle_account',
                    nonce: yxjto_paypal_multi_gateway_admin.nonce,
                    account_id: accountId,
                    status: newStatus
                },
                beforeSend: function() {
                    $(element).prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        // Update the UI
                        var $row = $('tr[data-account-id="' + accountId + '"]');
                        var $statusCell = $row.find('.status');
                        var $actionCell = $row.find('td:last-child');
                        
                        // Update status
                        $statusCell.removeClass('status-active status-inactive')
                                  .addClass('status-' + newStatus)
                                  .text(newStatus === 'active' ? 
                                        yxjto_paypal_multi_gateway_admin.strings.active_status : 
                                        yxjto_paypal_multi_gateway_admin.strings.inactive_status);
                        
                        // Update buttons
                        if (newStatus === 'active') {
                            $actionCell.find('.enable-account-btn')
                                      .removeClass('enable-account-btn button-primary')
                                      .addClass('disable-account-btn button-secondary')
                                      .text(yxjto_paypal_multi_gateway_admin.strings.disable_account)
                                      .attr('title', yxjto_paypal_multi_gateway_admin.strings.disable_account_title);
                        } else {
                            $actionCell.find('.disable-account-btn')
                                      .removeClass('disable-account-btn button-secondary')
                                      .addClass('enable-account-btn button-primary')
                                      .text(yxjto_paypal_multi_gateway_admin.strings.enable_account)
                                      .attr('title', yxjto_paypal_multi_gateway_admin.strings.enable_account_title);
                        }
                        
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.account_saved, 'success');
                    } else {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.operation_failed + ': ' + response.data, 'error');
                    }
                },
                error: function() {
                    self.showMessage(yxjto_paypal_multi_gateway_admin.strings.request_failed, 'error');
                },
                complete: function() {
                    $(element).prop('disabled', false);
                }
            });
        },
        
        saveAccount: function(e) {
            e.preventDefault();
            
            var self = this;
            var formData = this.getFormData();
            
            if (!this.validateForm(formData)) {
                return;
            }

            var action = formData.account_id ? 'yxjto_paypal_multi_gateway_update_account' : 'yxjto_paypal_multi_gateway_add_account';

            $.ajax({
                url: yxjto_paypal_multi_gateway_admin.ajax_url,
                type: 'POST',
                data: $.extend({
                    action: action,
                    nonce: yxjto_paypal_multi_gateway_admin.nonce
                }, formData),
                beforeSend: function() {
                    $('#save-account-btn').prop('disabled', true).text(yxjto_paypal_multi_gateway_admin.strings.saving);
                },
                success: function(response) {
                    if (response.success) {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.account_saved, 'success');
                        self.hideModal();
                        // Reload the page to show updated data
                        location.reload();
                    } else {
                        self.showMessage(yxjto_paypal_multi_gateway_admin.strings.save_account_failed + ' ' + response.data, 'error');
                    }
                },
                error: function() {
                    self.showMessage(yxjto_paypal_multi_gateway_admin.strings.network_error, 'error');
                },
                complete: function() {
                    $('#save-account-btn').prop('disabled', false).text(yxjto_paypal_multi_gateway_admin.strings.save_account || 'Save Account');
                }
            });
        },

        getFormData: function() {
            var formData = {
                account_id: $('#account-id').val(),
                account_type: $('#account-type').val(),
                weight: $('#account-weight').val()
            };

            switch (formData.account_type) {
                case 'email':
                    formData.paypal_email = $('#paypal-email').val();
                    break;
                case 'paypal_me':
                    formData.paypal_me_url = $('#paypal-me-url').val();
                    break;
                case 'api':
                    formData.client_id = $('#client-id').val();
                    formData.client_secret = $('#client-secret').val();
                    formData.sandbox_mode = $('#sandbox-mode').is(':checked') ? '1' : '0';
                    break;
            }

            return formData;
        },

        validateForm: function(formData) {
            var errors = [];

            if (!formData.account_type) {
                errors.push(yxjto_paypal_multi_gateway_admin.strings.select_account_type);
            }

            switch (formData.account_type) {
                case 'email':
                    if (!formData.paypal_email || !this.isValidEmail(formData.paypal_email)) {
                        errors.push(yxjto_paypal_multi_gateway_admin.strings.enter_valid_email);
                    }
                    break;
                case 'paypal_me':
                    if (!formData.paypal_me_url || !this.isValidUrl(formData.paypal_me_url)) {
                        errors.push(yxjto_paypal_multi_gateway_admin.strings.enter_valid_url);
                    }
                    break;
                case 'api':
                    if (!formData.client_id) {
                        errors.push(yxjto_paypal_multi_gateway_admin.strings.enter_client_id);
                    }
                    if (!formData.client_secret) {
                        errors.push(yxjto_paypal_multi_gateway_admin.strings.enter_client_secret);
                    }
                    break;
            }

            var weight = parseInt(formData.weight);
            if (!weight || weight < 1 || weight > 100) {
                errors.push(yxjto_paypal_multi_gateway_admin.strings.weight_range_error);
            }

            if (errors.length > 0) {
                this.showMessage(errors.join(' '), 'error');
                return false;
            }

            return true;
        },

        toggleAccountFields: function() {
            var accountType = $(this).val();

            // Hide all fields first
            $('.account-type-fields').hide();

            // Show relevant fields
            if (accountType) {
                var fieldId = accountType.replace('_', '-') + '-fields';
                $('#' + fieldId).show();
            }
        },

        isValidEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        isValidUrl: function(url) {
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        },

        hideModal: function() {
            $('#account-modal').hide();
        },

        showMessage: function(message, type) {
            var messageClass = 'yxjto-paypal-message yxjto-paypal-message-' + (type || 'info');
            var messageElement = $('<div class="' + messageClass + '">' + message + '</div>');

            // Remove existing messages
            $('.yxjto-paypal-message').remove();

            // Add new message
            $('.wrap h1').after(messageElement);

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    messageElement.fadeOut();
                }, 3000);
            }

            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 300);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        YXJTOPayPalMultiGateway.init();
    });

    // Make YXJTOPayPalMultiGateway globally available
    window.YXJTOPayPalMultiGateway = YXJTOPayPalMultiGateway;

})(jQuery);
