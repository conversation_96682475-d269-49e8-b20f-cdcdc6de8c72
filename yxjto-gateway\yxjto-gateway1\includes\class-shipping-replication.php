<?php
/**
 * YXJTO 配送复制类
 * 处理配送区域、运输设置、类别、本地自提等的多数据库复制
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 * @since 2025-07-26
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Shipping_Replication {
    
    private static $instance = null;
    private $config;
    private $source_db;
    private $target_dbs = [];
    private $log_file;
    
    /**
     * 单例模式获取实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->config = WP_Multi_DB_Config_Manager::get_config('shipping_replication');
        
        // 确保配置不为空，设置默认值
        if (empty($this->config) || !is_array($this->config)) {
            $this->config = [
                'enable_shipping_replication' => false,
                'sync_mode' => 'manual',
                'enable_sync_logging' => true,
                'last_sync' => '',
                'enable_shipping_zones_sync' => false,
                'enable_shipping_methods_sync' => false,
                'enable_shipping_classes_sync' => false,
                'enable_local_pickup_sync' => false
            ];
        }
        
        // 设置日志文件路径，使用安全的目录
        $this->log_file = $this->get_safe_log_file_path();
        
        // 延迟初始化数据库连接，确保 WordPress 完全加载
        add_action('wp_loaded', [$this, 'delayed_init'], 10);
    }
    
    /**
     * 延迟初始化方法
     */
    public function delayed_init() {
        // 初始化数据库连接
        $this->init_database_connections();
        
        // 注册WooCommerce钩子
        $this->register_hooks();
    }
    
    /**
     * 安全获取源数据库实例
     */
    private function get_source_db() {
        if (!$this->source_db) {
            global $wpdb;
            if ($wpdb) {
                $this->source_db = $wpdb;
            } else {
                throw new Exception('WordPress 数据库对象不可用');
            }
        }
        return $this->source_db;
    }
    
    /**
     * 获取安全的日志文件路径
     */
    private function get_safe_log_file_path() {
        // 尝试使用 wp-content/uploads 目录
        if (defined('WP_CONTENT_DIR')) {
            $uploads_dir = WP_CONTENT_DIR . '/uploads';
            if (is_dir($uploads_dir) && is_writable($uploads_dir)) {
                return $uploads_dir . '/yxjto-shipping-replication.log';
            }
        }
        
        // 如果 wp-content/uploads 不可用，使用插件目录
        $plugin_log_dir = dirname(__FILE__) . '/../logs';
        if (!is_dir($plugin_log_dir)) {
            wp_mkdir_p($plugin_log_dir);
        }
        
        return $plugin_log_dir . '/yxjto-shipping-replication.log';
    }
    
    /**
     * 初始化数据库连接
     */
    private function init_database_connections() {
        $db_config = WP_Multi_DB_Config_Manager::get_config('databases');
        
        // 确保 WordPress 已经加载并且 wpdb 可用
        global $wpdb;
        if (!$wpdb) {
            $this->log_message("WordPress 数据库对象不可用，跳过数据库连接初始化", 'warning');
            return;
        }
        
        // 设置源数据库（当前活动数据库）
        $this->source_db = $wpdb;
        
        // 初始化目标数据库连接
        if (!is_array($db_config) || empty($db_config)) {
            $this->log_message("数据库配置为空，跳过目标数据库连接初始化", 'warning');
            return;
        }
        
        // 获取当前数据库标识
        $current_database_key = null;
        if (class_exists('YXJTO_Gateway')) {
            try {
                $current_database_key = YXJTO_Gateway::get_instance()->get_current_database();
            } catch (Exception $e) {
                $this->log_message("无法获取当前数据库标识: " . $e->getMessage(), 'warning');
            }
        }
        
        foreach ($db_config as $db_name => $db_info) {
            // 跳过当前数据库
            if ($current_database_key && $db_name === $current_database_key) {
                $this->log_message("跳过当前数据库: {$db_name}");
                continue;
            }
            
            if (!empty($db_info['host']) && !empty($db_info['database'])) {
                try {
                    // 确保所有必需字段都存在且不为空
                    $username = isset($db_info['username']) ? $db_info['username'] : '';
                    $password = isset($db_info['password']) ? $db_info['password'] : '';
                    $database = isset($db_info['database']) ? $db_info['database'] : '';
                    $host = isset($db_info['host']) ? $db_info['host'] : 'localhost';
                    $prefix = isset($db_info['prefix']) ? $db_info['prefix'] : 'wp_';
                    
                    // 使用完整的类名来创建 wpdb 实例
                    $this->target_dbs[$db_name] = new \wpdb(
                        $username,
                        $password,
                        $database,
                        $host
                    );
                    
                    // 设置表前缀，确保不为空
                    if (!empty($prefix)) {
                        $this->target_dbs[$db_name]->set_prefix($prefix);
                    }
                    
                    // 测试连接
                    $test_result = $this->target_dbs[$db_name]->get_var("SELECT 1");
                    if ($test_result !== '1') {
                        unset($this->target_dbs[$db_name]);
                        $this->log_message("数据库连接测试失败 {$db_name}", 'error');
                    } else {
                        $this->log_message("数据库连接成功 {$db_name}", 'info');
                    }
                } catch (Exception $e) {
                    $this->log_message("数据库连接失败 {$db_name}: " . $e->getMessage(), 'error');
                }
            }
        }
    }
    
    /**
     * 注册WooCommerce钩子
     */
    private function register_hooks() {
        // 确保配置存在且启用配送复制
        if (empty($this->config) || !isset($this->config['enable_shipping_replication']) || !$this->config['enable_shipping_replication']) {
            return;
        }
        
        // 配送区域相关钩子
        if (isset($this->config['enable_shipping_zones_sync']) && $this->config['enable_shipping_zones_sync']) {
            add_action('woocommerce_shipping_zone_method_added', [$this, 'sync_shipping_zone_method'], 10, 3);
            add_action('woocommerce_shipping_zone_method_deleted', [$this, 'sync_shipping_zone_method_deletion'], 10, 3);
            add_action('woocommerce_shipping_zone_method_status_toggled', [$this, 'sync_shipping_zone_method_status'], 10, 4);

            // 配送区域创建、更新、删除钩子
            add_action('woocommerce_shipping_zone_saved', [$this, 'sync_shipping_zone_saved'], 10, 1);
            add_action('woocommerce_delete_shipping_zone', [$this, 'sync_shipping_zone_deletion'], 10, 1);

            // 监听配送区域数据库操作
            add_action('deleted_post', [$this, 'handle_shipping_zone_deletion'], 10, 1);

            // 添加更多WooCommerce配送相关钩子
            add_action('woocommerce_shipping_zone_method_saved', [$this, 'sync_shipping_zone_method_saved'], 10, 2);
            add_action('woocommerce_shipping_zone_location_saved', [$this, 'sync_shipping_zone_location_saved'], 10, 2);

            // 监听AJAX保存操作
            add_action('wp_ajax_woocommerce_shipping_zone_add_method', [$this, 'monitor_zone_method_ajax'], 1);
            add_action('wp_ajax_woocommerce_shipping_zone_methods_save_settings', [$this, 'monitor_zone_method_settings_ajax'], 1);
            add_action('wp_ajax_woocommerce_shipping_zones_save_changes', [$this, 'monitor_zone_changes_ajax'], 1);

            // 添加更多配送方式相关的钩子
            add_action('woocommerce_shipping_zone_method_set_enabled', [$this, 'sync_method_enabled_status'], 10, 3);
            add_action('woocommerce_shipping_zone_method_set_disabled', [$this, 'sync_method_disabled_status'], 10, 3);

            // 监听配送方式设置更新
            add_action('update_option', [$this, 'monitor_shipping_method_option_update'], 10, 3);
            add_action('add_option', [$this, 'monitor_shipping_method_option_add'], 10, 2);

            // 监听WordPress admin-ajax.php的所有配送相关请求
            add_action('wp_ajax_woocommerce_shipping_zone_methods_save_changes', [$this, 'monitor_method_changes_ajax'], 1);

            // 监听数据库操作
            add_action('wp_loaded', [$this, 'setup_database_monitoring']);

            // 监听WooCommerce设置保存
            add_action('woocommerce_settings_saved', [$this, 'handle_woocommerce_settings_saved']);
            add_action('woocommerce_update_options_shipping', [$this, 'handle_shipping_options_update']);
        }
        
        // 配送方法相关钩子
        if (isset($this->config['enable_shipping_methods_sync']) && $this->config['enable_shipping_methods_sync']) {
            add_action('woocommerce_shipping_init', [$this, 'init_shipping_methods_sync']);
        }
        
        // 配送类别相关钩子
        if (isset($this->config['enable_shipping_classes_sync']) && $this->config['enable_shipping_classes_sync']) {
            add_action('created_product_shipping_class', [$this, 'sync_shipping_class_creation'], 10, 2);
            add_action('edited_product_shipping_class', [$this, 'sync_shipping_class_update'], 10, 2);
            add_action('delete_product_shipping_class', [$this, 'sync_shipping_class_deletion'], 10, 4);
        }
        
        // 本地自提相关钩子
        if (isset($this->config['enable_local_pickup_sync']) && $this->config['enable_local_pickup_sync']) {
            add_action('woocommerce_shipping_local_pickup_settings_saved', [$this, 'sync_local_pickup_settings']);
        }

        // 延迟同步钩子
        add_action('yxjto_delayed_method_sync', [$this, 'handle_delayed_method_sync'], 10, 2);
        add_action('yxjto_delayed_method_settings_sync', [$this, 'handle_delayed_method_settings_sync'], 10, 2);
        add_action('yxjto_delayed_zone_sync', [$this, 'handle_delayed_zone_sync'], 10, 1);
        add_action('yxjto_delayed_option_sync', [$this, 'handle_delayed_option_sync'], 10, 2);
        add_action('yxjto_delayed_full_shipping_sync', [$this, 'handle_delayed_full_shipping_sync'], 10);
        add_action('yxjto_delayed_full_shipping_sync', [$this, 'handle_delayed_full_shipping_sync'], 10);
    }
    
    /**
     * 初始化配送方法同步
     */
    public function init_shipping_methods_sync() {
        // 这个方法在 WooCommerce 配送系统初始化时被调用
        // 可以在这里设置配送方法相关的钩子或进行初始化工作
        
        if (empty($this->config) || !isset($this->config['enable_shipping_methods_sync']) || !$this->config['enable_shipping_methods_sync']) {
            return;
        }
        
        $this->log_message("配送方法同步系统已初始化");
        
        // 这里可以添加配送方法相关的钩子
        // 例如：监听配送方法的创建、更新、删除等事件
        
        // 注册配送方法设置更新钩子
        add_action('woocommerce_update_option', [$this, 'sync_shipping_method_option'], 10, 1);
        add_action('update_option', [$this, 'sync_shipping_method_option_generic'], 10, 3);
    }
    
    /**
     * 同步配送方法选项
     */
    public function sync_shipping_method_option($option) {
        // 检查是否是配送方法相关的选项
        if (strpos($option['id'], 'woocommerce_') === 0 && 
            (strpos($option['id'], '_shipping') !== false || strpos($option['id'], '_delivery') !== false)) {
            
            $this->log_message("检测到配送方法选项更新: " . $option['id']);
            
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $option_value = get_option($option['id']);
                $this->replicate_option($target_db, $option['id'], $option_value);
            }
        }
    }
    
    /**
     * 通用配送方法选项同步
     */
    public function sync_shipping_method_option_generic($option_name, $old_value, $new_value) {
        // 检查是否是配送方法相关的选项
        if (strpos($option_name, 'woocommerce_') === 0 && 
            (strpos($option_name, '_shipping') !== false || strpos($option_name, '_delivery') !== false)) {
            
            if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
                return;
            }
            
            $this->log_message("检测到配送方法选项更新: " . $option_name);
            
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->replicate_option($target_db, $option_name, $new_value);
            }
        }
    }
    
    /**
     * 同步配送区域方法
     */
    public function sync_shipping_zone_method($instance_id, $method_id, $zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送区域方法: Zone {$zone_id}, Method {$method_id}, Instance {$instance_id}");
        
        try {
            // 获取源数据
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            $methods = $zone->get_shipping_methods();
            $method_data = null;
            
            foreach ($methods as $method) {
                if ($method->get_instance_id() == $instance_id) {
                    // 安全获取配送方法设置
                    $settings = [];
                    if (method_exists($method, 'get_instance_options')) {
                        $settings = $method->get_instance_options();
                    } elseif (method_exists($method, 'get_options')) {
                        $settings = $method->get_options();
                    } elseif (isset($method->instance_settings)) {
                        $settings = $method->instance_settings;
                    }
                    
                    $method_data = [
                        'instance_id' => $method->get_instance_id(),
                        'method_id' => $method->id, // 使用 id 属性而不是 get_id() 方法
                        'method_order' => method_exists($method, 'get_method_order') ? $method->get_method_order() : 0,
                        'enabled' => $method->is_enabled() ? 'yes' : 'no',
                        'settings' => $settings
                    ];
                    break;
                }
            }
            
            if (!$method_data) {
                $this->log_message("未找到配送方法数据: Instance {$instance_id}", 'error');
                return;
            }
            
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->replicate_shipping_zone_method($target_db, $zone_id, $method_data);
            }
            
            $this->log_message("配送区域方法同步完成: Instance {$instance_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送区域方法同步失败: " . $e->getMessage(), 'error');
            $this->record_sync_failure("配送区域方法同步失败: " . $e->getMessage());
        }
    }
    
    /**
     * 同步配送区域方法删除
     */
    public function sync_shipping_zone_method_deletion($instance_id, $method_id, $zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送区域方法删除: Zone {$zone_id}, Method {$method_id}, Instance {$instance_id}");
        
        try {
            // 从所有目标数据库删除
            foreach ($this->target_dbs as $db_name => $target_db) {
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['instance_id' => $instance_id],
                    ['%d']
                );
                
                // 删除相关设置
                $target_db->delete(
                    $target_db->prefix . 'options',
                    ['option_name' => 'woocommerce_' . $method_id . '_' . $instance_id . '_settings'],
                    ['%s']
                );
            }
            
            $this->log_message("配送区域方法删除同步完成: Instance {$instance_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送区域方法删除同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 同步配送区域方法状态切换
     */
    public function sync_shipping_zone_method_status($instance_id, $method_id, $zone_id, $is_enabled) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送区域方法状态: Zone {$zone_id}, Method {$method_id}, Instance {$instance_id}, Enabled: " . ($is_enabled ? 'Yes' : 'No'));
        
        try {
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $target_db->update(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['is_enabled' => $is_enabled ? 1 : 0],
                    ['instance_id' => $instance_id],
                    ['%d'],
                    ['%d']
                );
            }
            
            $this->log_message("配送区域方法状态同步完成: Instance {$instance_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送区域方法状态同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 同步配送区域保存
     */
    public function sync_shipping_zone_saved($zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送区域保存: Zone {$zone_id}");

        try {
            // 获取区域数据
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            if (!$zone) {
                $this->log_message("无法获取配送区域数据: Zone {$zone_id}", 'error');
                return;
            }

            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->replicate_shipping_zone($target_db, $zone_id);
                $this->log_message("配送区域同步到数据库 {$db_name}: Zone {$zone_id}");
            }

            $this->log_message("配送区域保存同步完成: Zone {$zone_id}");

        } catch (Exception $e) {
            $this->log_message("配送区域保存同步失败: " . $e->getMessage(), 'error');
            $this->record_sync_failure("配送区域保存同步失败: " . $e->getMessage());
        }
    }

    /**
     * 同步配送区域删除
     */
    public function sync_shipping_zone_deletion($zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送区域删除: Zone {$zone_id}");

        try {
            // 从所有目标数据库删除
            foreach ($this->target_dbs as $db_name => $target_db) {
                // 删除区域的配送方法
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['zone_id' => $zone_id],
                    ['%d']
                );

                // 删除区域位置
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_locations',
                    ['zone_id' => $zone_id],
                    ['%d']
                );

                // 删除区域本身
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zones',
                    ['zone_id' => $zone_id],
                    ['%d']
                );

                $this->log_message("配送区域从数据库 {$db_name} 删除完成: Zone {$zone_id}");
            }

            $this->log_message("配送区域删除同步完成: Zone {$zone_id}");

        } catch (Exception $e) {
            $this->log_message("配送区域删除同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理配送区域删除（通过post删除）
     */
    public function handle_shipping_zone_deletion($post_id) {
        // 检查是否是配送区域相关的删除
        global $wpdb;

        // 检查是否有配送区域被删除
        $deleted_zones = $wpdb->get_results("
            SELECT zone_id FROM {$wpdb->prefix}woocommerce_shipping_zones
            WHERE zone_id NOT IN (
                SELECT DISTINCT zone_id FROM {$wpdb->prefix}woocommerce_shipping_zone_methods
                UNION
                SELECT DISTINCT zone_id FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            )
        ");

        foreach ($deleted_zones as $zone) {
            $this->sync_shipping_zone_deletion($zone->zone_id);
        }
    }

    /**
     * 同步配送区域方法保存
     */
    public function sync_shipping_zone_method_saved($instance_id, $zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送区域方法保存: Zone {$zone_id}, Instance {$instance_id}");

        try {
            // 获取方法数据
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            $methods = $zone->get_shipping_methods();

            foreach ($methods as $method) {
                if ($method->get_instance_id() == $instance_id) {
                    $method_data = [
                        'instance_id' => $method->get_instance_id(),
                        'method_id' => $method->id,
                        'method_order' => method_exists($method, 'get_method_order') ? $method->get_method_order() : 0,
                        'enabled' => $method->is_enabled() ? 'yes' : 'no',
                        'settings' => method_exists($method, 'get_instance_options') ? $method->get_instance_options() : []
                    ];

                    // 同步到所有目标数据库
                    foreach ($this->target_dbs as $db_name => $target_db) {
                        $this->replicate_shipping_zone_method($target_db, $zone_id, $method_data);
                    }
                    break;
                }
            }

            $this->log_message("配送区域方法保存同步完成: Zone {$zone_id}, Instance {$instance_id}");

        } catch (Exception $e) {
            $this->log_message("配送区域方法保存同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 同步配送区域位置保存
     */
    public function sync_shipping_zone_location_saved($zone_id, $location_data) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送区域位置保存: Zone {$zone_id}");

        try {
            // 同步区域位置到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->sync_shipping_zone_locations($target_db, $zone_id, $db_name);
            }

            $this->log_message("配送区域位置保存同步完成: Zone {$zone_id}");

        } catch (Exception $e) {
            $this->log_message("配送区域位置保存同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 同步配送方法启用状态
     */
    public function sync_method_enabled_status($instance_id, $method_id, $zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送方法启用状态: Zone {$zone_id}, Method {$method_id}, Instance {$instance_id}");

        try {
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $target_db->update(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['is_enabled' => 1],
                    ['instance_id' => $instance_id, 'zone_id' => $zone_id],
                    ['%d'],
                    ['%d', '%d']
                );
            }

            $this->log_message("配送方法启用状态同步完成: Instance {$instance_id}");

        } catch (Exception $e) {
            $this->log_message("配送方法启用状态同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 同步配送方法禁用状态
     */
    public function sync_method_disabled_status($instance_id, $method_id, $zone_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }

        $this->log_message("开始同步配送方法禁用状态: Zone {$zone_id}, Method {$method_id}, Instance {$instance_id}");

        try {
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $target_db->update(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['is_enabled' => 0],
                    ['instance_id' => $instance_id, 'zone_id' => $zone_id],
                    ['%d'],
                    ['%d', '%d']
                );
            }

            $this->log_message("配送方法禁用状态同步完成: Instance {$instance_id}");

        } catch (Exception $e) {
            $this->log_message("配送方法禁用状态同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 监听配送方法选项更新
     */
    public function monitor_shipping_method_option_update($option_name, $old_value, $new_value) {
        // 检查是否是配送方法相关的选项
        if (strpos($option_name, 'woocommerce_') === 0 &&
            (strpos($option_name, '_settings') !== false ||
             strpos($option_name, 'shipping_') !== false ||
             strpos($option_name, '_instance_') !== false)) {

            $this->log_message("检测到配送方法选项更新: {$option_name}");

            // 延迟同步以确保所有相关选项都已更新
            wp_schedule_single_event(time() + 3, 'yxjto_delayed_option_sync', [$option_name, $new_value]);
        }
    }

    /**
     * 监听配送方法选项添加
     */
    public function monitor_shipping_method_option_add($option_name, $option_value) {
        // 检查是否是配送方法相关的选项
        if (strpos($option_name, 'woocommerce_') === 0 &&
            (strpos($option_name, '_settings') !== false ||
             strpos($option_name, 'shipping_') !== false ||
             strpos($option_name, '_instance_') !== false)) {

            $this->log_message("检测到配送方法选项添加: {$option_name}");

            // 延迟同步以确保所有相关选项都已添加
            wp_schedule_single_event(time() + 3, 'yxjto_delayed_option_sync', [$option_name, $option_value]);
        }
    }

    /**
     * 监听配送方法变更AJAX
     */
    public function monitor_method_changes_ajax() {
        // 在AJAX处理完成后执行同步
        add_action('wp_ajax_woocommerce_shipping_zone_methods_save_changes', [$this, 'sync_after_method_changes_ajax'], 999);
    }

    /**
     * 在方法变更AJAX后同步
     */
    public function sync_after_method_changes_ajax() {
        if (isset($_POST['zone_id'])) {
            $zone_id = intval($_POST['zone_id']);

            // 延迟执行以确保数据已保存
            wp_schedule_single_event(time() + 2, 'yxjto_delayed_zone_sync', [$zone_id]);

            $this->log_message("安排延迟区域同步: Zone {$zone_id}");
        }
    }

    /**
     * 监听配送区域方法AJAX添加
     */
    public function monitor_zone_method_ajax() {
        // 在AJAX处理完成后执行同步
        add_action('wp_ajax_woocommerce_shipping_zone_add_method', [$this, 'sync_after_method_ajax'], 999);
    }

    /**
     * 监听配送区域方法设置AJAX保存
     */
    public function monitor_zone_method_settings_ajax() {
        // 在AJAX处理完成后执行同步
        add_action('wp_ajax_woocommerce_shipping_zone_methods_save_settings', [$this, 'sync_after_method_settings_ajax'], 999);
    }

    /**
     * 监听配送区域变更AJAX保存
     */
    public function monitor_zone_changes_ajax() {
        // 在AJAX处理完成后执行同步
        add_action('wp_ajax_woocommerce_shipping_zones_save_changes', [$this, 'sync_after_zone_changes_ajax'], 999);
    }

    /**
     * 在方法AJAX后同步
     */
    public function sync_after_method_ajax() {
        if (isset($_POST['zone_id']) && isset($_POST['method_id'])) {
            $zone_id = intval($_POST['zone_id']);
            $method_id = sanitize_text_field($_POST['method_id']);

            // 延迟执行以确保数据已保存
            wp_schedule_single_event(time() + 2, 'yxjto_delayed_method_sync', [$zone_id, $method_id]);
        }
    }

    /**
     * 在方法设置AJAX后同步
     */
    public function sync_after_method_settings_ajax() {
        if (isset($_POST['instance_id']) && isset($_POST['zone_id'])) {
            $instance_id = intval($_POST['instance_id']);
            $zone_id = intval($_POST['zone_id']);

            // 延迟执行以确保数据已保存
            wp_schedule_single_event(time() + 2, 'yxjto_delayed_method_settings_sync', [$zone_id, $instance_id]);
        }
    }

    /**
     * 在区域变更AJAX后同步
     */
    public function sync_after_zone_changes_ajax() {
        if (isset($_POST['zone_id'])) {
            $zone_id = intval($_POST['zone_id']);

            // 延迟执行以确保数据已保存
            wp_schedule_single_event(time() + 2, 'yxjto_delayed_zone_sync', [$zone_id]);
        }
    }

    /**
     * 处理延迟方法同步
     */
    public function handle_delayed_method_sync($zone_id, $method_id) {
        $this->log_message("执行延迟方法同步: Zone {$zone_id}, Method {$method_id}");

        try {
            // 获取最新的方法数据并同步
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            $methods = $zone->get_shipping_methods();

            foreach ($methods as $method) {
                if ($method->id === $method_id) {
                    $method_data = [
                        'instance_id' => $method->get_instance_id(),
                        'method_id' => $method->id,
                        'method_order' => method_exists($method, 'get_method_order') ? $method->get_method_order() : 0,
                        'enabled' => $method->is_enabled() ? 'yes' : 'no',
                        'settings' => method_exists($method, 'get_instance_options') ? $method->get_instance_options() : []
                    ];

                    // 同步到所有目标数据库
                    foreach ($this->target_dbs as $db_name => $target_db) {
                        $this->replicate_shipping_zone_method($target_db, $zone_id, $method_data);
                    }
                    break;
                }
            }

        } catch (Exception $e) {
            $this->log_message("延迟方法同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理延迟方法设置同步
     */
    public function handle_delayed_method_settings_sync($zone_id, $instance_id) {
        $this->log_message("执行延迟方法设置同步: Zone {$zone_id}, Instance {$instance_id}");

        try {
            // 获取最新的方法数据并同步
            $zone = WC_Shipping_Zones::get_zone($zone_id);
            $methods = $zone->get_shipping_methods();

            foreach ($methods as $method) {
                if ($method->get_instance_id() == $instance_id) {
                    $method_data = [
                        'instance_id' => $method->get_instance_id(),
                        'method_id' => $method->id,
                        'method_order' => method_exists($method, 'get_method_order') ? $method->get_method_order() : 0,
                        'enabled' => $method->is_enabled() ? 'yes' : 'no',
                        'settings' => method_exists($method, 'get_instance_options') ? $method->get_instance_options() : []
                    ];

                    // 同步到所有目标数据库
                    foreach ($this->target_dbs as $db_name => $target_db) {
                        $this->replicate_shipping_zone_method($target_db, $zone_id, $method_data);
                    }
                    break;
                }
            }

        } catch (Exception $e) {
            $this->log_message("延迟方法设置同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理延迟区域同步
     */
    public function handle_delayed_zone_sync($zone_id) {
        $this->log_message("执行延迟区域同步: Zone {$zone_id}");

        try {
            // 同步整个区域
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->sync_single_shipping_zone($target_db, ['zone_id' => $zone_id], $db_name);
            }

        } catch (Exception $e) {
            $this->log_message("延迟区域同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理延迟选项同步
     */
    public function handle_delayed_option_sync($option_name, $option_value) {
        $this->log_message("执行延迟选项同步: {$option_name}");

        try {
            // 同步选项到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $result = $target_db->query($target_db->prepare(
                    "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
                    $option_name,
                    maybe_serialize($option_value),
                    'yes'
                ));

                if ($result === false) {
                    $this->log_message("选项同步到数据库 {$db_name} 失败: {$option_name} - " . $target_db->last_error, 'error');
                } else {
                    $this->log_message("选项同步到数据库 {$db_name} 成功: {$option_name}");
                }
            }

        } catch (Exception $e) {
            $this->log_message("延迟选项同步失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 设置数据库监听
     */
    public function setup_database_monitoring() {
        global $wpdb;

        // 监听配送区域方法表的变化
        add_action('wp_insert_post_data', [$this, 'monitor_post_data_changes'], 10, 2);

        // 使用WordPress的query钩子监听数据库操作
        add_filter('query', [$this, 'monitor_database_queries']);
    }

    /**
     * 监听数据库查询
     */
    public function monitor_database_queries($query) {
        global $wpdb;

        // 只在管理后台监听
        if (!is_admin()) {
            return $query;
        }

        // 检查是否是配送相关的INSERT/UPDATE查询
        if (preg_match('/(?:INSERT|UPDATE|REPLACE).*woocommerce_shipping_zone_methods/i', $query)) {
            $this->log_message("检测到配送方法数据库操作: " . substr($query, 0, 200) . "...");

            // 延迟执行完整同步
            wp_schedule_single_event(time() + 5, 'yxjto_delayed_full_shipping_sync');
        }

        return $query;
    }

    /**
     * 处理WooCommerce设置保存
     */
    public function handle_woocommerce_settings_saved() {
        $this->log_message("检测到WooCommerce设置保存");

        // 延迟执行完整同步
        wp_schedule_single_event(time() + 3, 'yxjto_delayed_full_shipping_sync');
    }

    /**
     * 处理配送选项更新
     */
    public function handle_shipping_options_update() {
        $this->log_message("检测到配送选项更新");

        // 延迟执行完整同步
        wp_schedule_single_event(time() + 3, 'yxjto_delayed_full_shipping_sync');
    }

    /**
     * 监听文章数据变化
     */
    public function monitor_post_data_changes($data, $postarr) {
        // 这里可以监听与配送相关的文章类型变化
        return $data;
    }

    /**
     * 处理延迟完整配送同步
     */
    public function handle_delayed_full_shipping_sync() {
        $this->log_message("执行延迟完整配送同步");

        try {
            // 执行完整的配送区域同步
            $this->sync_all_shipping_zones();

            $this->log_message("延迟完整配送同步完成");
            $this->record_sync_success();

        } catch (Exception $e) {
            $this->log_message("延迟完整配送同步失败: " . $e->getMessage(), 'error');
            $this->record_sync_failure("延迟完整配送同步失败: " . $e->getMessage());
        }
    }

    /**
     * 复制配送区域方法到目标数据库
     */
    private function replicate_shipping_zone_method($target_db, $zone_id, $method_data) {
        // 检查配送区域是否存在
        $zone_exists = $target_db->get_var($target_db->prepare(
            "SELECT zone_id FROM {$target_db->prefix}woocommerce_shipping_zones WHERE zone_id = %d",
            $zone_id
        ));
        
        if (!$zone_exists) {
            // 复制配送区域
            $this->replicate_shipping_zone($target_db, $zone_id);
        }
        
        // 使用 REPLACE INTO 来避免重复键错误
        $result = $target_db->query($target_db->prepare(
            "REPLACE INTO {$target_db->prefix}woocommerce_shipping_zone_methods 
            (instance_id, zone_id, method_id, method_order, is_enabled) 
            VALUES (%d, %d, %s, %d, %d)",
            $method_data['instance_id'],
            $zone_id,
            $method_data['method_id'],
            $method_data['method_order'],
            ($method_data['enabled'] === 'yes') ? 1 : 0
        ));
        
        if ($result === false) {
            $this->log_message("配送区域方法复制失败: Instance {$method_data['instance_id']} - " . $target_db->last_error, 'error');
        } else {
            $this->log_message("配送区域方法复制成功: Instance {$method_data['instance_id']}, Method {$method_data['method_id']}");
        }
        
        // 同步方法设置
        if (!empty($method_data['settings'])) {
            $this->replicate_shipping_method_settings($target_db, $method_data['instance_id'], $method_data['settings']);
        }

        // 同步配送方法的完整选项数据
        $this->replicate_shipping_method_complete_options($target_db, $method_data['instance_id'], $method_data['method_id']);
    }
    
    /**
     * 复制配送区域到目标数据库
     */
    private function replicate_shipping_zone($target_db, $zone_id) {
        // 获取源配送区域数据
        $source_db = $this->get_source_db();
        $zone_data = $source_db->get_row($source_db->prepare(
            "SELECT * FROM {$source_db->prefix}woocommerce_shipping_zones WHERE zone_id = %d",
            $zone_id
        ), ARRAY_A);
        
        if (!$zone_data) {
            return;
        }
        
        // 使用 REPLACE INTO 来避免重复键错误
        $result = $target_db->query($target_db->prepare(
            "REPLACE INTO {$target_db->prefix}woocommerce_shipping_zones 
            (zone_id, zone_name, zone_order) VALUES (%d, %s, %d)",
            $zone_data['zone_id'],
            $zone_data['zone_name'],
            $zone_data['zone_order']
        ));
        
        if ($result === false) {
            $this->log_message("配送区域复制失败: Zone {$zone_id} - " . $target_db->last_error, 'error');
            return;
        } else {
            $this->log_message("配送区域复制成功: Zone {$zone_id} - {$zone_data['zone_name']}");
        }
        
        // 复制配送区域位置
        // 首先删除现有的区域位置以避免重复
        $target_db->delete(
            $target_db->prefix . 'woocommerce_shipping_zone_locations',
            ['zone_id' => $zone_id],
            ['%d']
        );
        
        $zone_locations = $source_db->get_results($source_db->prepare(
            "SELECT * FROM {$source_db->prefix}woocommerce_shipping_zone_locations WHERE zone_id = %d",
            $zone_id
        ), ARRAY_A);
        
        foreach ($zone_locations as $location) {
            $location_result = $target_db->insert(
                $target_db->prefix . 'woocommerce_shipping_zone_locations',
                $location,
                ['%d', '%d', '%s', '%s']
            );
            
            if ($location_result === false) {
                $this->log_message("配送区域位置复制失败: Zone {$zone_id}, Location {$location['location_code']} - " . $target_db->last_error, 'error');
            }
        }
        
        $this->log_message("配送区域 {$zone_id} 复制了 " . count($zone_locations) . " 个位置");
    }
    
    /**
     * 复制配送方法设置
     */
    private function replicate_shipping_method_settings($target_db, $instance_id, $settings) {
        foreach ($settings as $key => $value) {
            $option_name = "woocommerce_shipping_instance_{$instance_id}_{$key}";
            $serialized_value = maybe_serialize($value);
            
            // 使用 REPLACE INTO 来避免重复键错误
            $result = $target_db->query($target_db->prepare(
                "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
                $option_name,
                $serialized_value,
                'yes'
            ));
            
            if ($result === false) {
                $this->log_message("配送方法设置复制失败: {$option_name} - " . $target_db->last_error, 'error');
            } else {
                $this->log_message("配送方法设置复制成功: {$option_name}");
            }
        }
    }

    /**
     * 复制配送方法的完整选项数据
     */
    private function replicate_shipping_method_complete_options($target_db, $instance_id, $method_id) {
        global $wpdb;

        // 获取所有与此配送方法实例相关的选项
        $option_patterns = [
            "woocommerce_{$method_id}_{$instance_id}_%",
            "woocommerce_shipping_instance_{$instance_id}_%",
            "woocommerce_{$method_id}_settings"
        ];

        foreach ($option_patterns as $pattern) {
            $options = $wpdb->get_results($wpdb->prepare(
                "SELECT option_name, option_value, autoload FROM {$wpdb->prefix}options WHERE option_name LIKE %s",
                $pattern
            ));

            foreach ($options as $option) {
                // 复制选项到目标数据库
                $result = $target_db->query($target_db->prepare(
                    "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
                    $option->option_name,
                    $option->option_value,
                    $option->autoload
                ));

                if ($result === false) {
                    $this->log_message("配送方法完整选项复制失败: {$option->option_name} - " . $target_db->last_error, 'error');
                } else {
                    $this->log_message("配送方法完整选项复制成功: {$option->option_name}");
                }
            }
        }

        $this->log_message("配送方法完整数据复制完成: Instance {$instance_id}, Method {$method_id}");
    }

    /**
     * 同步配送类别创建
     */
    public function sync_shipping_class_creation($term_id, $tt_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送类别创建: Term {$term_id}");
        
        try {
            // 获取源配送类别数据
            $term_data = get_term($term_id, 'product_shipping_class', ARRAY_A);
            if (is_wp_error($term_data) || !$term_data) {
                $this->log_message("获取配送类别数据失败: Term {$term_id}", 'error');
                return;
            }
            
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->replicate_shipping_class($target_db, $term_data);
            }
            
            $this->log_message("配送类别创建同步完成: Term {$term_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送类别创建同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 同步配送类别更新
     */
    public function sync_shipping_class_update($term_id, $tt_id) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送类别更新: Term {$term_id}");
        
        try {
            // 获取源配送类别数据
            $term_data = get_term($term_id, 'product_shipping_class', ARRAY_A);
            if (!$term_data || is_wp_error($term_data)) {
                $this->log_message("获取配送类别数据失败: Term {$term_id}", 'error');
                return;
            }
            
            // 同步到所有目标数据库
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->update_shipping_class($target_db, $term_data);
            }
            
            $this->log_message("配送类别更新同步完成: Term {$term_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送类别更新同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 同步配送类别删除
     */
    public function sync_shipping_class_deletion($term_id, $tt_id, $deleted_term, $object_ids) {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步配送类别删除: Term {$term_id}");
        
        try {
            // 从所有目标数据库删除
            foreach ($this->target_dbs as $db_name => $target_db) {
                // 删除术语
                $target_db->delete(
                    $target_db->prefix . 'terms',
                    ['term_id' => $term_id],
                    ['%d']
                );
                
                // 删除术语分类
                $target_db->delete(
                    $target_db->prefix . 'term_taxonomy',
                    ['term_id' => $term_id],
                    ['%d']
                );
                
                // 删除术语关系
                $target_db->delete(
                    $target_db->prefix . 'term_relationships',
                    ['term_taxonomy_id' => $tt_id],
                    ['%d']
                );
            }
            
            $this->log_message("配送类别删除同步完成: Term {$term_id}");
            
        } catch (Exception $e) {
            $this->log_message("配送类别删除同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 复制配送类别到目标数据库
     */
    private function replicate_shipping_class($target_db, $term_data) {
        // 使用 REPLACE INTO 来避免重复键错误
        $result = $target_db->query($target_db->prepare(
            "REPLACE INTO {$target_db->prefix}terms 
            (term_id, name, slug, term_group) VALUES (%d, %s, %s, %d)",
            $term_data['term_id'],
            $term_data['name'],
            $term_data['slug'],
            $term_data['term_group']
        ));
        
        if ($result === false) {
            $this->log_message("配送类别术语复制失败: Term {$term_data['term_id']} - " . $target_db->last_error, 'error');
            return;
        }
        
        // 复制term_taxonomy记录
        $source_db = $this->get_source_db();
        $taxonomy_data = $source_db->get_row($source_db->prepare(
            "SELECT * FROM {$source_db->prefix}term_taxonomy WHERE term_id = %d AND taxonomy = 'product_shipping_class'",
            $term_data['term_id']
        ), ARRAY_A);
        
        if ($taxonomy_data) {
            // 使用 REPLACE INTO 来避免重复键错误
            $result = $target_db->query($target_db->prepare(
                "REPLACE INTO {$target_db->prefix}term_taxonomy 
                (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
                VALUES (%d, %d, %s, %s, %d, %d)",
                $taxonomy_data['term_taxonomy_id'],
                $taxonomy_data['term_id'],
                $taxonomy_data['taxonomy'],
                $taxonomy_data['description'],
                $taxonomy_data['parent'],
                $taxonomy_data['count']
            ));
            
            if ($result === false) {
                $this->log_message("配送类别分类复制失败: Term {$term_data['term_id']} - " . $target_db->last_error, 'error');
            }
        }
    }
    
    /**
     * 更新配送类别到目标数据库
     */
    private function update_shipping_class($target_db, $term_data) {
        // 检查术语是否存在
        $existing_term = $target_db->get_row($target_db->prepare(
            "SELECT * FROM {$target_db->prefix}terms WHERE term_id = %d",
            $term_data['term_id']
        ));
        
        if ($existing_term) {
            // 更新术语
            $target_db->update(
                $target_db->prefix . 'terms',
                [
                    'name' => $term_data['name'],
                    'slug' => $term_data['slug']
                ],
                ['term_id' => $term_data['term_id']],
                ['%s', '%s'],
                ['%d']
            );
            
            // 获取并更新术语分类数据
            $source_db = $this->get_source_db();
            $taxonomy_data = $source_db->get_row($source_db->prepare(
                "SELECT * FROM {$source_db->prefix}term_taxonomy WHERE term_id = %d AND taxonomy = 'product_shipping_class'",
                $term_data['term_id']
            ), ARRAY_A);
            
            if ($taxonomy_data) {
                $target_db->update(
                    $target_db->prefix . 'term_taxonomy',
                    [
                        'description' => $taxonomy_data['description'],
                        'parent' => $taxonomy_data['parent'],
                        'count' => $taxonomy_data['count']
                    ],
                    [
                        'term_id' => $term_data['term_id'],
                        'taxonomy' => 'product_shipping_class'
                    ],
                    ['%s', '%d', '%d'],
                    ['%d', '%s']
                );
            }
        } else {
            // 如果不存在，则创建新的
            $this->replicate_shipping_class($target_db, $term_data);
        }
    }
    
    /**
     * 同步本地自提设置
     */
    public function sync_local_pickup_settings() {
        if (empty($this->config) || !isset($this->config['sync_mode']) || $this->config['sync_mode'] !== 'realtime') {
            return;
        }
        
        $this->log_message("开始同步本地自提设置");
        
        try {
            // 获取本地自提相关的所有设置
            $pickup_options = [
                'woocommerce_local_pickup_settings',
                'woocommerce_local_pickup_plus_settings',
                'woocommerce_pickup_locations'
            ];
            
            foreach ($pickup_options as $option_name) {
                $option_value = get_option($option_name);
                if ($option_value !== false) {
                    // 同步到所有目标数据库
                    foreach ($this->target_dbs as $db_name => $target_db) {
                        $this->replicate_option($target_db, $option_name, $option_value);
                    }
                }
            }
            
            $this->log_message("本地自提设置同步完成");
            
        } catch (Exception $e) {
            $this->log_message("本地自提设置同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 复制选项到目标数据库
     */
    private function replicate_option($target_db, $option_name, $option_value) {
        $serialized_value = maybe_serialize($option_value);
        
        // 使用 REPLACE INTO 来避免重复键错误
        $result = $target_db->query($target_db->prepare(
            "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
            $option_name,
            $serialized_value,
            'yes'
        ));
        
        if ($result === false) {
            $this->log_message("选项复制失败: {$option_name} - " . $target_db->last_error, 'error');
        }
    }
    
    /**
     * 批量同步所有配送设置
     */
    public function batch_sync_all_shipping() {
        try {
            $this->log_message("开始批量同步所有配送设置");

            // 运行调试状态检查
            $this->debug_sync_status();
        
        // 强制检查并创建测试数据
        $this->log_message("=== 数据检查和创建阶段 ===");
        
        // 检查是否有配送数据，如果没有则创建测试数据
        global $wpdb;
        
        // 确保数据库表存在
        $zones_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}woocommerce_shipping_zones'");
        if (!$zones_table_exists) {
            $this->log_message("配送区域表不存在，WooCommerce可能未正确安装", 'error');
            throw new Exception("WooCommerce配送区域表不存在。请确保WooCommerce已正确安装并激活。");
        }
        
        $zones_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zones");
        $classes_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}term_taxonomy WHERE taxonomy = 'product_shipping_class'");
        
        $this->log_message("数据库中现有配送区域: {$zones_count}");
        $this->log_message("数据库中现有配送类别: {$classes_count}");
        
        // 总是尝试创建基础数据，即使只缺少一种类型
        if ($zones_count == 0 || $classes_count == 0) {
            $this->log_message("检测到配送数据不完整，强制创建测试数据...");
            $created = $this->create_test_shipping_data();
            
            if ($created) {
                $this->log_message("测试数据创建完成，重新检查数据状态");
                
                // 重新检查数据
                $zones_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zones");
                $classes_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}term_taxonomy WHERE taxonomy = 'product_shipping_class'");
                $this->log_message("创建后配送区域: {$zones_count}, 配送类别: {$classes_count}");
                
            } else {
                $this->log_message("测试数据创建失败，尝试继续同步流程", 'warning');
            }
        } else {
            $this->log_message("检测到现有完整数据，跳过自动创建 (区域: {$zones_count}, 类别: {$classes_count})");
        }
        
        $this->log_message("=== 数据检查完成，开始同步流程 ===");
        
        // 检查目标数据库连接
        if (empty($this->target_dbs)) {
            $this->log_message("没有找到目标数据库连接，同步失败", 'error');
            throw new Exception("没有找到目标数据库连接。请检查多数据库配置。");
        }
        
        $this->log_message("找到 " . count($this->target_dbs) . " 个目标数据库: " . implode(', ', array_keys($this->target_dbs)));

        // 检查配置状态
        $this->log_message("配送复制配置状态: " . json_encode($this->config));

        // 同步配送区域
        if (isset($this->config['enable_shipping_zones_sync']) && $this->config['enable_shipping_zones_sync']) {
            $this->log_message("开始同步配送区域...");
            $this->batch_sync_shipping_zones();
            $this->log_message("配送区域同步完成");
        } else {
            $this->log_message("配送区域同步已禁用", 'warning');
        }

        // 同步配送类别
        if (isset($this->config['enable_shipping_classes_sync']) && $this->config['enable_shipping_classes_sync']) {
            $this->log_message("开始同步配送类别...");
            $this->batch_sync_shipping_classes();
            $this->log_message("配送类别同步完成");
        } else {
            $this->log_message("配送类别同步已禁用", 'warning');
        }

        // 同步本地自提设置
        if (isset($this->config['enable_local_pickup_sync']) && $this->config['enable_local_pickup_sync']) {
            $this->log_message("开始同步本地自提设置...");
            $this->sync_local_pickup_settings();
            $this->log_message("本地自提设置同步完成");
        } else {
            $this->log_message("本地自提同步已禁用", 'warning');
        }

        // 同步详细的配送配置
        $this->log_message("开始同步详细配送配置...");
        $this->sync_detailed_shipping_settings();
        $this->log_message("详细配送配置同步完成");

        // 同步配送方法实例设置
        $this->log_message("开始同步配送方法实例设置...");
        $this->sync_shipping_method_instances();
        $this->log_message("配送方法实例设置同步完成");

        // 同步全局配送设置
        $this->log_message("开始同步全局配送设置...");
        $this->sync_global_shipping_settings();
        $this->log_message("全局配送设置同步完成");

        // 更新最后同步时间
        WP_Multi_DB_Config_Manager::update_config('shipping_replication', 'last_sync', current_time('mysql'));

        $this->log_message("批量同步完成");

        return true; // 明确返回成功状态

        } catch (Exception $e) {
            $this->log_message("批量同步失败: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * 批量同步配送区域（完整CRUD操作）
     */
    private function batch_sync_shipping_zones() {
        // 确保WooCommerce已完全加载
        if (!function_exists('WC') || !class_exists('WC_Shipping_Zones')) {
            $this->log_message("WooCommerce未完全加载，无法获取配送区域", 'error');
            return;
        }
        
        // 多种方法尝试获取配送区域
        $zones = [];
        
        // 方法1: 使用WC_Shipping_Zones::get_zones()
        try {
            $zones = WC_Shipping_Zones::get_zones();
            $this->log_message("方法1获取到 " . count($zones) . " 个配送区域");
        } catch (Exception $e) {
            $this->log_message("方法1获取配送区域失败: " . $e->getMessage(), 'warning');
        }
        
        // 方法2: 如果方法1失败，直接从数据库获取
        if (empty($zones)) {
            global $wpdb;
            try {
                $db_zones = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}woocommerce_shipping_zones ORDER BY zone_order");
                $this->log_message("方法2从数据库获取到 " . count($db_zones) . " 个配送区域");
                
                // 转换为WC_Shipping_Zones::get_zones()格式
                foreach ($db_zones as $zone_data) {
                    $zones[] = [
                        'zone_id' => $zone_data->zone_id,
                        'zone_name' => $zone_data->zone_name,
                        'zone_order' => $zone_data->zone_order
                    ];
                }
            } catch (Exception $e) {
                $this->log_message("方法2从数据库获取配送区域失败: " . $e->getMessage(), 'error');
            }
        }
        
        // 方法3: 检查"任何地方"区域(zone_id = 0)
        try {
            $rest_of_world = new WC_Shipping_Zone(0);
            $rest_methods = $rest_of_world->get_shipping_methods();
            if (!empty($rest_methods)) {
                $zones[] = [
                    'zone_id' => 0,
                    'zone_name' => '任何地方',
                    'zone_order' => 0
                ];
                $this->log_message("添加'任何地方'区域，有 " . count($rest_methods) . " 个配送方法");
            }
        } catch (Exception $e) {
            $this->log_message("获取'任何地方'区域失败: " . $e->getMessage(), 'warning');
        }
        
        $this->log_message("最终获取到 " . count($zones) . " 个配送区域");

        // 获取源数据库中的所有区域ID
        $source_zone_ids = array_column($zones, 'zone_id');

        // 对每个目标数据库执行完整同步
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("开始完整同步数据库 {$db_name} 的配送区域");

            // 1. 删除目标数据库中不存在于源数据库的区域
            $this->cleanup_obsolete_shipping_zones($target_db, $source_zone_ids, $db_name);

            // 2. 同步/更新现有区域
            foreach ($zones as $zone_data) {
                $this->sync_single_shipping_zone($target_db, $zone_data, $db_name);
            }
        }
    }

    /**
     * 清理目标数据库中过时的配送区域
     */
    private function cleanup_obsolete_shipping_zones($target_db, $source_zone_ids, $db_name) {
        // 获取目标数据库中的所有区域
        $target_zones = $target_db->get_results("SELECT zone_id, zone_name FROM {$target_db->prefix}woocommerce_shipping_zones");

        foreach ($target_zones as $target_zone) {
            if (!in_array($target_zone->zone_id, $source_zone_ids)) {
                $this->log_message("删除数据库 {$db_name} 中的过时配送区域: {$target_zone->zone_id} - {$target_zone->zone_name}");

                // 删除区域的配送方法
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    ['zone_id' => $target_zone->zone_id],
                    ['%d']
                );

                // 删除区域位置
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_locations',
                    ['zone_id' => $target_zone->zone_id],
                    ['%d']
                );

                // 删除区域本身
                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zones',
                    ['zone_id' => $target_zone->zone_id],
                    ['%d']
                );
            }
        }
    }

    /**
     * 同步单个配送区域
     */
    private function sync_single_shipping_zone($target_db, $zone_data, $db_name) {
        $zone_id = $zone_data['zone_id'];
        $this->log_message("同步配送区域到数据库 {$db_name}: {$zone_id} - {$zone_data['zone_name']}");

        // 复制/更新区域基本信息
        $this->replicate_shipping_zone($target_db, $zone_id);

        // 获取源区域的配送方法
        $zone = WC_Shipping_Zones::get_zone($zone_id);
        $methods = $zone->get_shipping_methods();
        $source_method_ids = [];

        foreach ($methods as $method) {
            // 安全获取配送方法设置
            $settings = [];
            if (method_exists($method, 'get_instance_options')) {
                $settings = $method->get_instance_options();
            } elseif (method_exists($method, 'get_options')) {
                $settings = $method->get_options();
            } elseif (isset($method->instance_settings)) {
                $settings = $method->instance_settings;
            }

            $method_data = [
                'instance_id' => $method->get_instance_id(),
                'method_id' => $method->id,
                'method_order' => method_exists($method, 'get_method_order') ? $method->get_method_order() : 0,
                'enabled' => $method->is_enabled() ? 'yes' : 'no',
                'settings' => $settings
            ];

            $source_method_ids[] = $method_data['instance_id'];

            $this->log_message("同步配送方法到数据库 {$db_name}: Instance {$method_data['instance_id']}, Method {$method_data['method_id']}");
            $this->replicate_shipping_zone_method($target_db, $zone_id, $method_data);
        }

        // 删除目标数据库中不存在于源数据库的配送方法
        $this->cleanup_obsolete_shipping_methods($target_db, $zone_id, $source_method_ids, $db_name);

        // 同步区域位置
        $this->sync_shipping_zone_locations($target_db, $zone_id, $db_name);
    }

    /**
     * 清理过时的配送方法
     */
    private function cleanup_obsolete_shipping_methods($target_db, $zone_id, $source_method_ids, $db_name) {
        $target_methods = $target_db->get_results($target_db->prepare(
            "SELECT instance_id, method_id FROM {$target_db->prefix}woocommerce_shipping_zone_methods WHERE zone_id = %d",
            $zone_id
        ));

        foreach ($target_methods as $target_method) {
            if (!in_array($target_method->instance_id, $source_method_ids)) {
                $this->log_message("删除数据库 {$db_name} 中的过时配送方法: Zone {$zone_id}, Instance {$target_method->instance_id}, Method {$target_method->method_id}");

                $target_db->delete(
                    $target_db->prefix . 'woocommerce_shipping_zone_methods',
                    [
                        'zone_id' => $zone_id,
                        'instance_id' => $target_method->instance_id
                    ],
                    ['%d', '%d']
                );
            }
        }
    }

    /**
     * 同步配送区域位置
     */
    private function sync_shipping_zone_locations($target_db, $zone_id, $db_name) {
        global $wpdb;

        // 获取源数据库的区域位置
        $source_locations = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}woocommerce_shipping_zone_locations WHERE zone_id = %d",
            $zone_id
        ), ARRAY_A);

        // 删除目标数据库中的现有位置
        $target_db->delete(
            $target_db->prefix . 'woocommerce_shipping_zone_locations',
            ['zone_id' => $zone_id],
            ['%d']
        );

        // 插入新的位置数据
        foreach ($source_locations as $location) {
            unset($location['location_id']); // 移除自增ID
            $target_db->insert(
                $target_db->prefix . 'woocommerce_shipping_zone_locations',
                $location
            );
        }

        $this->log_message("同步了数据库 {$db_name} 中区域 {$zone_id} 的 " . count($source_locations) . " 个位置");
    }
    
    /**
     * 批量同步配送类别（完整CRUD操作）
     */
    private function batch_sync_shipping_classes() {
        // 多种方法尝试获取配送类别
        $shipping_classes = [];

        // 方法1: 使用get_terms
        $classes_terms = get_terms([
            'taxonomy' => 'product_shipping_class',
            'hide_empty' => false
        ]);

        if (is_wp_error($classes_terms)) {
            $this->log_message("方法1获取配送类别失败: " . $classes_terms->get_error_message(), 'warning');
        } else {
            $shipping_classes = $classes_terms;
            $this->log_message("方法1获取到 " . count($shipping_classes) . " 个配送类别");
        }
        
        // 方法2: 如果方法1失败，使用WC()->shipping->get_shipping_classes()
        if (empty($shipping_classes) && function_exists('WC') && WC() && WC()->shipping) {
            try {
                $wc_classes = WC()->shipping->get_shipping_classes();
                $shipping_classes = $wc_classes;
                $this->log_message("方法2获取到 " . count($shipping_classes) . " 个配送类别");
            } catch (Exception $e) {
                $this->log_message("方法2获取配送类别失败: " . $e->getMessage(), 'warning');
            }
        }
        
        // 方法3: 如果前两种方法都失败，直接从数据库获取
        if (empty($shipping_classes)) {
            global $wpdb;
            try {
                $db_classes = $wpdb->get_results("
                    SELECT t.*, tt.* 
                    FROM {$wpdb->terms} t 
                    INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id 
                    WHERE tt.taxonomy = 'product_shipping_class'
                ");
                
                $shipping_classes = $db_classes;
                $this->log_message("方法3从数据库获取到 " . count($shipping_classes) . " 个配送类别");
            } catch (Exception $e) {
                $this->log_message("方法3从数据库获取配送类别失败: " . $e->getMessage(), 'error');
            }
        }
        
        $this->log_message("最终获取到 " . count($shipping_classes) . " 个配送类别");

        // 获取源数据库中的所有配送类别ID
        $source_class_ids = [];
        foreach ($shipping_classes as $class) {
            $source_class_ids[] = $class->term_id;
        }

        // 对每个目标数据库执行完整同步
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("开始完整同步数据库 {$db_name} 的配送类别");

            // 1. 删除目标数据库中不存在于源数据库的配送类别
            $this->cleanup_obsolete_shipping_classes($target_db, $source_class_ids, $db_name);

            // 2. 同步/更新现有配送类别
            foreach ($shipping_classes as $class) {
                $term_data = (array) $class;
                $this->log_message("同步配送类别到数据库 {$db_name}: {$term_data['term_id']} - {$term_data['name']}");
                $this->replicate_shipping_class($target_db, $term_data);
            }
        }
    }

    /**
     * 清理目标数据库中过时的配送类别
     */
    private function cleanup_obsolete_shipping_classes($target_db, $source_class_ids, $db_name) {
        // 获取目标数据库中的所有配送类别
        $target_classes = $target_db->get_results("
            SELECT t.term_id, t.name
            FROM {$target_db->prefix}terms t
            INNER JOIN {$target_db->prefix}term_taxonomy tt ON t.term_id = tt.term_id
            WHERE tt.taxonomy = 'product_shipping_class'
        ");

        foreach ($target_classes as $target_class) {
            if (!in_array($target_class->term_id, $source_class_ids)) {
                $this->log_message("删除数据库 {$db_name} 中的过时配送类别: {$target_class->term_id} - {$target_class->name}");

                // 删除term_taxonomy记录
                $target_db->delete(
                    $target_db->prefix . 'term_taxonomy',
                    [
                        'term_id' => $target_class->term_id,
                        'taxonomy' => 'product_shipping_class'
                    ],
                    ['%d', '%s']
                );

                // 删除term记录
                $target_db->delete(
                    $target_db->prefix . 'terms',
                    ['term_id' => $target_class->term_id],
                    ['%d']
                );

                // 删除term_meta记录
                $target_db->delete(
                    $target_db->prefix . 'termmeta',
                    ['term_id' => $target_class->term_id],
                    ['%d']
                );
            }
        }
    }
    
    /**
     * 记录日志
     */
    private function log_message($message, $level = 'info') {
        // 确保配置存在且启用日志记录
        if (empty($this->config) || !isset($this->config['enable_sync_logging']) || !$this->config['enable_sync_logging']) {
            return;
        }
        
        // 确保消息不为空
        if (empty($message)) {
            return;
        }
        
        $timestamp = current_time('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        
        // 如果是错误，也记录到WordPress日志
        if ($level === 'error') {
            error_log("YXJTO Shipping Replication Error: {$message}");
        }
    }
    
    /**
     * 获取同步状态
     */
    public function get_sync_status() {
        return [
            'enabled' => isset($this->config['enable_shipping_replication']) ? $this->config['enable_shipping_replication'] : false,
            'last_sync' => isset($this->config['last_sync']) ? $this->config['last_sync'] : '',
            'target_databases' => count($this->target_dbs),
            'sync_mode' => isset($this->config['sync_mode']) ? $this->config['sync_mode'] : 'manual',
            'log_file_size' => file_exists($this->log_file) ? filesize($this->log_file) : 0
        ];
    }
    
    /**
     * 调试配送同步状态
     */
    public function debug_sync_status() {
        $this->log_message("=== 配送同步调试信息 ===");
        $this->log_message("配置状态: " . json_encode($this->config));
        $this->log_message("目标数据库数量: " . count($this->target_dbs));
        $this->log_message("目标数据库列表: " . implode(', ', array_keys($this->target_dbs)));
        
        // 测试每个数据库连接
        foreach ($this->target_dbs as $db_name => $target_db) {
            $test_result = $target_db->get_var("SELECT 1");
            $connection_status = ($test_result === '1') ? '连接正常' : '连接失败';
            $this->log_message("数据库 {$db_name}: {$connection_status}");
            
            if ($test_result !== '1') {
                $this->log_message("数据库 {$db_name} 错误: " . $target_db->last_error, 'error');
            }
        }
        
        // 检查配送区域 - 使用与同步相同的多种方法
        $zone_count = 0;
        if (function_exists('WC') && class_exists('WC_Shipping_Zones')) {
            try {
                $zones = WC_Shipping_Zones::get_zones();
                $zone_count = count($zones);
                
                // 如果WC方法没有返回结果，尝试直接从数据库获取
                if ($zone_count == 0) {
                    global $wpdb;
                    $db_zones = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}woocommerce_shipping_zones");
                    $zone_count = count($db_zones);
                    $this->log_message("从数据库直接获取配送区域数量: " . $zone_count);
                }
                
                // 检查"任何地方"区域
                $rest_of_world = new WC_Shipping_Zone(0);
                $rest_methods = $rest_of_world->get_shipping_methods();
                if (!empty($rest_methods)) {
                    $zone_count += 1; // 添加"任何地方"区域
                    $this->log_message("'任何地方'区域有 " . count($rest_methods) . " 个配送方法");
                }
            } catch (Exception $e) {
                $this->log_message("检查配送区域时出错: " . $e->getMessage(), 'warning');
            }
        } else {
            $this->log_message("WooCommerce或配送区域类不可用", 'warning');
        }
        $this->log_message("源数据库配送区域数量: " . $zone_count);
        
        // 检查配送类别 - 使用与同步相同的多种方法
        $class_count = 0;
        
        // 方法1: get_terms
        $shipping_classes = get_terms([
            'taxonomy' => 'product_shipping_class',
            'hide_empty' => false
        ]);
        
        if (!is_wp_error($shipping_classes)) {
            $class_count = count($shipping_classes);
        } else {
            $this->log_message("get_terms获取配送类别失败: " . $shipping_classes->get_error_message(), 'warning');
            
            // 方法2: 直接从数据库获取
            global $wpdb;
            try {
                $db_classes = $wpdb->get_results("
                    SELECT COUNT(*) as count FROM {$wpdb->term_taxonomy} 
                    WHERE taxonomy = 'product_shipping_class'
                ");
                $class_count = isset($db_classes[0]->count) ? $db_classes[0]->count : 0;
                $this->log_message("从数据库直接获取配送类别数量: " . $class_count);
            } catch (Exception $e) {
                $this->log_message("从数据库获取配送类别失败: " . $e->getMessage(), 'error');
            }
        }
        $this->log_message("源数据库配送类别数量: " . $class_count);
        
        // 如果没有数据，提供创建建议
        if ($zone_count == 0 && $class_count == 0) {
            $this->log_message("⚠ 检测到没有配送数据，建议创建测试数据", 'warning');
            $this->log_message("可以调用 create_test_shipping_data() 方法创建测试数据", 'info');
        }
        
        $this->log_message("=== 调试信息结束 ===");
    }

    /**
     * 获取调试信息
     */
    public function get_debug_info() {
        global $wpdb;

        $debug_info = [
            'woocommerce_active' => class_exists('WooCommerce'),
            'shipping_zones_table_exists' => (bool)$wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}woocommerce_shipping_zones'"),
            'shipping_methods_table_exists' => (bool)$wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}woocommerce_shipping_zone_methods'"),
            'zones_count' => (int)$wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zones"),
            'methods_count' => (int)$wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zone_methods"),
            'classes_count' => (int)$wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->term_taxonomy} WHERE taxonomy = 'product_shipping_class'"),
            'target_databases' => count($this->target_dbs),
            'config' => $this->config,
            'last_sync' => $this->config['last_sync'] ?? 'Never'
        ];

        return $debug_info;
    }

    /**
     * 验证配送数据完整性
     */
    public function verify_shipping_data_completeness() {
        $this->log_message("开始验证配送数据完整性");

        $completeness_report = [
            'shipping_zones' => $this->verify_shipping_zones_completeness(),
            'shipping_methods' => $this->verify_shipping_methods_completeness(),
            'shipping_locations' => $this->verify_shipping_locations_completeness(),
            'shipping_classes' => $this->verify_shipping_classes_completeness(),
            'shipping_settings' => $this->verify_shipping_settings_completeness(),
            'global_settings' => $this->verify_global_settings_completeness()
        ];

        $this->log_message("配送数据完整性验证完成");
        return $completeness_report;
    }

    /**
     * 验证配送区域完整性
     */
    private function verify_shipping_zones_completeness() {
        global $wpdb;

        $source_zones = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zones");
        $results = ['source_count' => $source_zones, 'target_counts' => []];

        foreach ($this->target_dbs as $db_name => $target_db) {
            $target_count = $target_db->get_var("SELECT COUNT(*) FROM {$target_db->prefix}woocommerce_shipping_zones");
            $results['target_counts'][$db_name] = $target_count;
        }

        return $results;
    }

    /**
     * 验证配送方法完整性
     */
    private function verify_shipping_methods_completeness() {
        global $wpdb;

        $source_methods = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zone_methods");
        $results = ['source_count' => $source_methods, 'target_counts' => []];

        foreach ($this->target_dbs as $db_name => $target_db) {
            $target_count = $target_db->get_var("SELECT COUNT(*) FROM {$target_db->prefix}woocommerce_shipping_zone_methods");
            $results['target_counts'][$db_name] = $target_count;
        }

        return $results;
    }

    /**
     * 验证配送位置完整性
     */
    private function verify_shipping_locations_completeness() {
        global $wpdb;

        $source_locations = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}woocommerce_shipping_zone_locations");
        $results = ['source_count' => $source_locations, 'target_counts' => []];

        foreach ($this->target_dbs as $db_name => $target_db) {
            $target_count = $target_db->get_var("SELECT COUNT(*) FROM {$target_db->prefix}woocommerce_shipping_zone_locations");
            $results['target_counts'][$db_name] = $target_count;
        }

        return $results;
    }

    /**
     * 验证配送类别完整性
     */
    private function verify_shipping_classes_completeness() {
        global $wpdb;

        $source_classes = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->term_taxonomy} WHERE taxonomy = 'product_shipping_class'");
        $results = ['source_count' => $source_classes, 'target_counts' => []];

        foreach ($this->target_dbs as $db_name => $target_db) {
            $target_count = $target_db->get_var("SELECT COUNT(*) FROM {$target_db->prefix}term_taxonomy WHERE taxonomy = 'product_shipping_class'");
            $results['target_counts'][$db_name] = $target_count;
        }

        return $results;
    }

    /**
     * 验证配送设置完整性
     */
    private function verify_shipping_settings_completeness() {
        global $wpdb;

        $shipping_options = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}options
            WHERE option_name LIKE 'woocommerce_%_settings'
            OR option_name LIKE 'woocommerce_shipping_%'
        ");

        $results = ['source_count' => $shipping_options, 'target_counts' => []];

        foreach ($this->target_dbs as $db_name => $target_db) {
            $target_count = $target_db->get_var("
                SELECT COUNT(*) FROM {$target_db->prefix}options
                WHERE option_name LIKE 'woocommerce_%_settings'
                OR option_name LIKE 'woocommerce_shipping_%'
            ");
            $results['target_counts'][$db_name] = $target_count;
        }

        return $results;
    }

    /**
     * 验证全局设置完整性
     */
    private function verify_global_settings_completeness() {
        $global_options = [
            'woocommerce_shipping_enabled',
            'woocommerce_ship_to_countries',
            'woocommerce_shipping_cost_requires_address',
            'woocommerce_hide_shipping_when_free'
        ];

        $results = ['checked_options' => count($global_options), 'sync_status' => []];

        foreach ($global_options as $option_name) {
            $source_value = get_option($option_name);
            $option_status = ['source_exists' => $source_value !== false, 'targets' => []];

            foreach ($this->target_dbs as $db_name => $target_db) {
                $target_value = $target_db->get_var($target_db->prepare(
                    "SELECT option_value FROM {$target_db->prefix}options WHERE option_name = %s",
                    $option_name
                ));
                $option_status['targets'][$db_name] = [
                    'exists' => $target_value !== null,
                    'matches' => $target_value === $source_value
                ];
            }

            $results['sync_status'][$option_name] = $option_status;
        }

        return $results;
    }

    /**
     * 删除所有数据库中的配送数据
     */
    public function delete_all_shipping_data() {
        try {
            $this->log_message("开始删除所有数据库中的配送数据");

            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $total_deleted = 0;
            $results = [];

            foreach ($databases as $db_key => $db_config) {
                if (!$db_config['enabled']) {
                    continue;
                }

                try {
                    // 连接到目标数据库
                    $target_db = new wpdb(
                        $db_config['username'],
                        $db_config['password'],
                        $db_config['database'],
                        $db_config['host']
                    );

                    if ($target_db->last_error) {
                        $results[$db_key] = [
                            'success' => false,
                            'error' => "Database connection failed: " . $target_db->last_error
                        ];
                        continue;
                    }

                    // 获取表前缀
                    $table_prefix = $db_config['table_prefix'] ?? 'wp_';

                    $deleted_items = 0;

                    // 删除配送区域方法
                    $methods_count = $target_db->get_var("SELECT COUNT(*) FROM {$table_prefix}woocommerce_shipping_zone_methods");
                    $target_db->query("DELETE FROM {$table_prefix}woocommerce_shipping_zone_methods");
                    $deleted_items += $methods_count;

                    // 删除配送区域位置
                    $locations_count = $target_db->get_var("SELECT COUNT(*) FROM {$table_prefix}woocommerce_shipping_zone_locations");
                    $target_db->query("DELETE FROM {$table_prefix}woocommerce_shipping_zone_locations");
                    $deleted_items += $locations_count;

                    // 删除配送区域
                    $zones_count = $target_db->get_var("SELECT COUNT(*) FROM {$table_prefix}woocommerce_shipping_zones");
                    $target_db->query("DELETE FROM {$table_prefix}woocommerce_shipping_zones");
                    $deleted_items += $zones_count;

                    // 删除配送类别
                    $classes_count = $target_db->get_var("
                        SELECT COUNT(*) FROM {$table_prefix}term_taxonomy
                        WHERE taxonomy = 'product_shipping_class'
                    ");

                    // 获取配送类别的term_id
                    $class_term_ids = $target_db->get_col("
                        SELECT term_id FROM {$table_prefix}term_taxonomy
                        WHERE taxonomy = 'product_shipping_class'
                    ");

                    if (!empty($class_term_ids)) {
                        $ids_string = implode(',', array_map('intval', $class_term_ids));

                        // 删除term_taxonomy记录
                        $target_db->query("DELETE FROM {$table_prefix}term_taxonomy WHERE taxonomy = 'product_shipping_class'");

                        // 删除term记录
                        $target_db->query("DELETE FROM {$table_prefix}terms WHERE term_id IN ($ids_string)");

                        // 删除term_meta记录
                        $target_db->query("DELETE FROM {$table_prefix}termmeta WHERE term_id IN ($ids_string)");
                    }

                    $deleted_items += $classes_count;
                    $total_deleted += $deleted_items;

                    $results[$db_key] = [
                        'success' => true,
                        'deleted_items' => $deleted_items,
                        'zones' => $zones_count,
                        'methods' => $methods_count,
                        'locations' => $locations_count,
                        'classes' => $classes_count
                    ];

                    $this->log_message("从数据库 {$db_key} 删除了 {$deleted_items} 个配送相关项目");

                } catch (Exception $e) {
                    $results[$db_key] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    $this->log_message("从数据库 {$db_key} 删除配送数据失败: " . $e->getMessage(), 'error');
                }
            }

            return [
                'success' => true,
                'total_deleted' => $total_deleted,
                'results' => $results
            ];

        } catch (Exception $e) {
            $this->log_message("删除所有配送数据失败: " . $e->getMessage(), 'error');
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建测试配送数据
     */
    public function create_test_shipping_data() {
        global $wpdb;
        
        $this->log_message("=== 开始创建测试配送数据 ===");
        
        $created_data = false;
        
        try {
            // 检查并创建配送区域
            $zones_table = $wpdb->prefix . 'woocommerce_shipping_zones';
            $methods_table = $wpdb->prefix . 'woocommerce_shipping_zone_methods';
            
            $existing_zones = $wpdb->get_var("SELECT COUNT(*) FROM {$zones_table}");
            
            if ($existing_zones == 0) {
                $this->log_message("创建测试配送区域...");
                
                $zone_result = $wpdb->insert(
                    $zones_table,
                    [
                        'zone_name' => '测试配送区域',
                        'zone_order' => 1
                    ],
                    ['%s', '%d']
                );
                
                if ($zone_result !== false) {
                    $zone_id = $wpdb->insert_id;
                    $this->log_message("✓ 配送区域创建成功，ID: {$zone_id}");
                    
                    // 创建配送方法
                    $method_result = $wpdb->insert(
                        $methods_table,
                        [
                            'zone_id' => $zone_id,
                            'instance_id' => 1,
                            'method_id' => 'flat_rate',
                            'method_order' => 1,
                            'is_enabled' => 1
                        ],
                        ['%d', '%d', '%s', '%d', '%d']
                    );
                    
                    if ($method_result !== false) {
                        $this->log_message("✓ 配送方法创建成功，Instance ID: {$wpdb->insert_id}");
                        $created_data = true;
                    } else {
                        $this->log_message("✗ 配送方法创建失败: " . $wpdb->last_error, 'error');
                    }
                } else {
                    $this->log_message("✗ 配送区域创建失败: " . $wpdb->last_error, 'error');
                }
            } else {
                $this->log_message("配送区域已存在 ({$existing_zones} 个)");
            }
            
            // 检查并创建配送类别
            $existing_classes = $wpdb->get_var("
                SELECT COUNT(*) FROM {$wpdb->prefix}term_taxonomy 
                WHERE taxonomy = 'product_shipping_class'
            ");
            
            if ($existing_classes == 0) {
                $this->log_message("创建测试配送类别...");
                
                // 创建术语
                $term_result = $wpdb->insert(
                    $wpdb->prefix . 'terms',
                    [
                        'name' => '标准配送',
                        'slug' => 'standard-shipping',
                        'term_group' => 0
                    ],
                    ['%s', '%s', '%d']
                );
                
                if ($term_result !== false) {
                    $term_id = $wpdb->insert_id;
                    $this->log_message("✓ 配送类别术语创建成功，ID: {$term_id}");
                    
                    // 创建术语分类
                    $taxonomy_result = $wpdb->insert(
                        $wpdb->prefix . 'term_taxonomy',
                        [
                            'term_id' => $term_id,
                            'taxonomy' => 'product_shipping_class',
                            'description' => '标准配送类别',
                            'parent' => 0,
                            'count' => 0
                        ],
                        ['%d', '%s', '%s', '%d', '%d']
                    );
                    
                    if ($taxonomy_result !== false) {
                        $this->log_message("✓ 配送类别分类创建成功，ID: {$wpdb->insert_id}");
                        $created_data = true;
                    } else {
                        $this->log_message("✗ 配送类别分类创建失败: " . $wpdb->last_error, 'error');
                    }
                } else {
                    $this->log_message("✗ 配送类别术语创建失败: " . $wpdb->last_error, 'error');
                }
            } else {
                $this->log_message("配送类别已存在 ({$existing_classes} 个)");
            }
            
            if ($created_data) {
                $this->log_message("✅ 测试数据创建完成！现在可以重新运行配送同步");
                
                // 验证创建的数据
                $final_zones = $wpdb->get_var("SELECT COUNT(*) FROM {$zones_table}");
                $final_methods = $wpdb->get_var("SELECT COUNT(*) FROM {$methods_table}");
                $final_classes = $wpdb->get_var("
                    SELECT COUNT(*) FROM {$wpdb->prefix}term_taxonomy 
                    WHERE taxonomy = 'product_shipping_class'
                ");
                
                $this->log_message("最终数据统计: 配送区域={$final_zones}, 配送方法={$final_methods}, 配送类别={$final_classes}");
            } else {
                $this->log_message("ℹ 数据已存在或创建完成");
            }
            
        } catch (Exception $e) {
            $this->log_message("创建测试数据时发生错误: " . $e->getMessage(), 'error');
        }
        
        $this->log_message("=== 测试数据创建结束 ===");
        
        return $created_data;
    }
    
    /**
     * 清理日志文件
     */
    public function cleanup_logs() {
        if (file_exists($this->log_file)) {
            file_put_contents($this->log_file, '');
            $this->log_message("日志文件已清理");
        }
    }

    /**
     * 获取配送统计信息
     */
    public function get_shipping_stats() {
        $stats = [
            'total_zones' => 0,
            'total_methods' => 0,
            'total_classes' => 0,
            'synced_zones' => 0,
            'synced_methods' => 0,
            'synced_classes' => 0,
            'failed_syncs' => 0,
            'last_sync' => get_option('yxjto_shipping_last_sync', __('Never', 'yxjto-gateway')),
            'next_batch_sync' => null,
            'error' => null
        ];

        try {
            // 获取配送区域数量
            if (class_exists('WC_Shipping_Zones')) {
                $zones = WC_Shipping_Zones::get_zones();
                $stats['total_zones'] = count($zones);
                
                // 计算配送方法数量
                foreach ($zones as $zone) {
                    if (isset($zone['shipping_methods'])) {
                        $stats['total_methods'] += count($zone['shipping_methods']);
                    }
                }
                
                // 检查"任何地方"区域的方法
                $rest_of_world = new WC_Shipping_Zone(0);
                $rest_methods = $rest_of_world->get_shipping_methods();
                if (!empty($rest_methods)) {
                    $stats['total_zones'] += 1;
                    $stats['total_methods'] += count($rest_methods);
                }
            }

            // 获取配送类别数量
            $shipping_classes = get_terms([
                'taxonomy' => 'product_shipping_class',
                'hide_empty' => false
            ]);
            
            if (!is_wp_error($shipping_classes)) {
                $stats['total_classes'] = count($shipping_classes);
            }

            // 假设同步的数量等于总数量（在实际应用中应该检查同步状态）
            $stats['synced_zones'] = $stats['total_zones'];
            $stats['synced_methods'] = $stats['total_methods'];
            $stats['synced_classes'] = $stats['total_classes'];

            // 获取下次计划同步时间
            $next_scheduled = wp_next_scheduled('yxjto_shipping_batch_sync');
            if ($next_scheduled) {
                $stats['next_batch_sync'] = date('Y-m-d H:i:s', $next_scheduled);
            }

            // 获取失败同步数量（从选项中读取）
            $stats['failed_syncs'] = get_option('yxjto_shipping_failed_syncs', 0);

            // 计算已同步的数量（这里可以根据实际需求调整计算逻辑）
            $stats['synced_zones'] = $stats['total_zones']; // 假设所有区域都已同步
            $stats['synced_methods'] = $stats['total_methods']; // 假设所有方法都已同步
            $stats['synced_classes'] = $stats['total_classes']; // 假设所有类别都已同步

        } catch (Exception $e) {
            $stats['error'] = $e->getMessage();
            $stats['failed_syncs'] = get_option('yxjto_shipping_failed_syncs', 0);
        }

        return $stats;
    }

    /**
     * 记录同步失败
     */
    private function record_sync_failure($error_message = '') {
        $current_failures = get_option('yxjto_shipping_failed_syncs', 0);
        $current_failures++;
        update_option('yxjto_shipping_failed_syncs', $current_failures);

        // 记录失败详情
        $failure_log = get_option('yxjto_shipping_failure_log', []);
        $failure_log[] = [
            'timestamp' => current_time('mysql'),
            'error' => $error_message
        ];

        // 只保留最近50条失败记录
        if (count($failure_log) > 50) {
            $failure_log = array_slice($failure_log, -50);
        }

        update_option('yxjto_shipping_failure_log', $failure_log);

        $this->log_message("记录同步失败 #{$current_failures}: {$error_message}", 'error');
    }

    /**
     * 记录同步成功
     */
    private function record_sync_success() {
        // 重置失败计数器
        update_option('yxjto_shipping_failed_syncs', 0);

        // 更新最后同步时间
        update_option('yxjto_shipping_last_sync', current_time('mysql'));

        $this->log_message("同步成功，重置失败计数器");
    }

    /**
     * 获取失败日志
     */
    public function get_failure_log() {
        return get_option('yxjto_shipping_failure_log', []);
    }

    /**
     * 清除失败统计
     */
    public function clear_failure_stats() {
        delete_option('yxjto_shipping_failed_syncs');
        delete_option('yxjto_shipping_failure_log');
        $this->log_message("清除失败统计完成");
    }

    /**
     * 同步特定区域
     */
    public function sync_specific_zone($zone_id) {
        $this->log_message("开始同步特定区域 ID: " . $zone_id);

        try {
            $zone = new WC_Shipping_Zone($zone_id);
            if (!$zone->get_id() && $zone_id !== 0) {
                return [
                    'success' => false,
                    'message' => __('Zone not found', 'yxjto-gateway')
                ];
            }

            if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_databases')) {
                return [
                    'success' => false,
                    'message' => 'Config Manager 不可用'
                ];
            }

            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $synced_count = 0;
            
            foreach ($databases as $key => $config) {
                if ($key === 'default' || empty($config['enabled'])) {
                    continue;
                }

                try {
                    $target_db = WP_Multi_DB_Config_Manager::get_database_connection($key);
                    if ($target_db) {
                        $this->sync_zone_to_database($zone, $target_db, $key);
                        $synced_count++;
                    }
                } catch (Exception $e) {
                    $this->log_message("同步区域到数据库 {$key} 失败: " . $e->getMessage(), 'error');
                }
            }

            $message = sprintf(__('Zone %d synced to %d databases', 'yxjto-gateway'), $zone_id, $synced_count);
            $this->log_message($message);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            $error_message = __('Failed to sync specific zone: ', 'yxjto-gateway') . $e->getMessage();
            $this->log_message($error_message, 'error');
            return [
                'success' => false,
                'message' => $error_message
            ];
        }
    }

    /**
     * 验证数据完整性
     */
    public function validate_data_integrity() {
        $report = "=== Shipping Data Integrity Validation Report ===\n\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";

        try {
            // 获取源数据库的数据
            $source_zones = WC_Shipping_Zones::get_zones();
            $source_classes = get_terms(['taxonomy' => 'product_shipping_class', 'hide_empty' => false]);
            
            $report .= "Source Database:\n";
            $report .= "- Zones: " . count($source_zones) . "\n";
            $report .= "- Classes: " . (is_wp_error($source_classes) ? 0 : count($source_classes)) . "\n\n";

            // 检查每个目标数据库
            if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_databases')) {
                return [
                    'success' => false,
                    'message' => 'Config Manager 不可用',
                    'report' => $report
                ];
            }
            
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            
            foreach ($databases as $key => $config) {
                if ($key === 'default' || empty($config['enabled'])) {
                    continue;
                }

                $report .= "Database: {$key}\n";
                
                try {
                    $target_db = WP_Multi_DB_Config_Manager::get_database_connection($key);
                    if ($target_db) {
                        // 检查区域数量
                        $zones_query = "SELECT COUNT(*) as count FROM {$config['table_prefix']}woocommerce_shipping_zones";
                        $zone_count = $target_db->get_var($zones_query);
                        
                        // 检查类别数量
                        $classes_query = "SELECT COUNT(*) as count FROM {$config['table_prefix']}term_taxonomy WHERE taxonomy = 'product_shipping_class'";
                        $class_count = $target_db->get_var($classes_query);
                        
                        $report .= "- Zones: {$zone_count}\n";
                        $report .= "- Classes: {$class_count}\n";
                        
                        // 检查数据一致性
                        if ($zone_count != count($source_zones)) {
                            $report .= "⚠ Zone count mismatch!\n";
                        }
                        if (!is_wp_error($source_classes) && $class_count != count($source_classes)) {
                            $report .= "⚠ Class count mismatch!\n";
                        }
                        
                    } else {
                        $report .= "✗ Connection failed\n";
                    }
                } catch (Exception $e) {
                    $report .= "✗ Error: " . $e->getMessage() . "\n";
                }
                
                $report .= "\n";
            }

        } catch (Exception $e) {
            $report .= "Error during validation: " . $e->getMessage() . "\n";
        }

        $report .= "=== End of Report ===";
        
        return $report;
    }

    /**
     * 清理孤立数据
     */
    public function cleanup_orphaned_data() {
        $this->log_message("开始清理孤立的配送数据");

        try {
            if (!class_exists('WP_Multi_DB_Config_Manager') || !method_exists('WP_Multi_DB_Config_Manager', 'get_databases')) {
                return [
                    'success' => false,
                    'message' => 'Config Manager 不可用'
                ];
            }
            
            $databases = WP_Multi_DB_Config_Manager::get_databases();
            $cleaned_count = 0;
            
            foreach ($databases as $key => $config) {
                if ($key === 'default' || empty($config['enabled'])) {
                    continue;
                }

                try {
                    $target_db = WP_Multi_DB_Config_Manager::get_database_connection($key);
                    if ($target_db) {
                        // 清理没有对应区域的配送方法
                        $cleanup_query = "
                            DELETE zsm FROM {$config['table_prefix']}woocommerce_shipping_zone_methods zsm
                            LEFT JOIN {$config['table_prefix']}woocommerce_shipping_zones z ON zsm.zone_id = z.zone_id
                            WHERE z.zone_id IS NULL AND zsm.zone_id != 0
                        ";
                        $affected_rows = $target_db->query($cleanup_query);
                        
                        if ($affected_rows > 0) {
                            $cleaned_count += $affected_rows;
                            $this->log_message("从数据库 {$key} 清理了 {$affected_rows} 个孤立的配送方法");
                        }
                    }
                } catch (Exception $e) {
                    $this->log_message("清理数据库 {$key} 时出错: " . $e->getMessage(), 'error');
                }
            }

            $message = sprintf(__('Cleaned up %d orphaned shipping methods', 'yxjto-gateway'), $cleaned_count);
            $this->log_message($message);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (Exception $e) {
            $error_message = __('Failed to cleanup orphaned data: ', 'yxjto-gateway') . $e->getMessage();
            $this->log_message($error_message, 'error');
            return [
                'success' => false,
                'message' => $error_message
            ];
        }
    }
    
    /**
     * 同步详细的配送配置设置
     */
    public function sync_detailed_shipping_settings() {
        $this->log_message("开始同步详细配送配置设置");
        
        try {
            // 1. 同步配送区域详细设置
            $this->sync_shipping_zone_settings();
            
            // 2. 同步运输设置
            $this->sync_transport_settings();
            
            // 3. 同步配送类别详细设置
            $this->sync_shipping_class_settings();
            
            // 4. 同步本地自提详细设置
            $this->sync_detailed_local_pickup_settings();
            
            // 5. 同步全局配送选项
            $this->sync_global_shipping_options();
            
            $this->log_message("详细配送配置同步完成");
            
        } catch (Exception $e) {
            $this->log_message("详细配送配置同步失败: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 同步配送区域详细设置
     */
    private function sync_shipping_zone_settings() {
        $this->log_message("同步配送区域详细设置");
        
        // 获取所有配送区域的详细配置
        global $wpdb;
        
        // 同步配送区域位置表的完整数据
        $zone_locations = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}woocommerce_shipping_zone_locations ORDER BY zone_id, location_type");
        
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("向数据库 {$db_name} 同步配送区域位置");
            
            // 清空目标数据库的区域位置
            $target_db->query("DELETE FROM {$target_db->prefix}woocommerce_shipping_zone_locations");
            
            // 插入完整的区域位置数据
            foreach ($zone_locations as $location) {
                $target_db->insert(
                    $target_db->prefix . 'woocommerce_shipping_zone_locations',
                    [
                        'zone_id' => $location->zone_id,
                        'location_code' => $location->location_code,
                        'location_type' => $location->location_type
                    ],
                    ['%d', '%s', '%s']
                );
            }
        }
        
        // 同步配送方法的详细实例设置
        $this->sync_shipping_method_instances();
    }
    
    /**
     * 同步配送方法实例设置
     */
    private function sync_shipping_method_instances() {
        $this->log_message("同步配送方法实例设置");
        
        global $wpdb;
        
        // 获取所有配送方法实例的选项
        $shipping_options = $wpdb->get_results("
            SELECT option_name, option_value, autoload 
            FROM {$wpdb->prefix}options 
            WHERE option_name LIKE 'woocommerce_%_settings' 
            OR option_name LIKE 'woocommerce_shipping_%'
            OR option_name LIKE 'woocommerce_%_instance_%'
        ");
        
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("向数据库 {$db_name} 同步配送方法实例设置");
            
            foreach ($shipping_options as $option) {
                $target_db->query($target_db->prepare(
                    "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
                    $option->option_name,
                    $option->option_value,
                    $option->autoload
                ));
            }
        }
    }

    /**
     * 同步全局配送设置
     */
    private function sync_global_shipping_settings() {
        $this->log_message("同步全局配送设置");

        // WooCommerce全局配送相关设置
        $global_shipping_options = [
            // 基础配送设置
            'woocommerce_shipping_enabled',
            'woocommerce_ship_to_countries',
            'woocommerce_shipping_country',
            'woocommerce_default_country',
            'woocommerce_allowed_countries',
            'woocommerce_all_except_countries',
            'woocommerce_specific_allowed_countries',
            'woocommerce_specific_ship_to_countries',

            // 配送计算设置
            'woocommerce_shipping_cost_requires_address',
            'woocommerce_shipping_debug_mode',
            'woocommerce_enable_shipping_calc',
            'woocommerce_shipping_calculator_enabled',

            // 配送税收设置
            'woocommerce_shipping_tax_class',
            'woocommerce_tax_based_on',

            // 配送显示设置
            'woocommerce_hide_shipping_when_free',
            'woocommerce_shipping_method_format',

            // 配送区域设置
            'woocommerce_default_shipping_zone',
            'woocommerce_shipping_zones_enabled'
        ];

        foreach ($global_shipping_options as $option_name) {
            $option_value = get_option($option_name);
            if ($option_value !== false) {
                // 同步到所有目标数据库
                foreach ($this->target_dbs as $db_name => $target_db) {
                    $this->replicate_option($target_db, $option_name, $option_value);
                }
                $this->log_message("同步全局设置: {$option_name}");
            }
        }

        $this->log_message("全局配送设置同步完成");
    }

    /**
     * 同步运输设置
     */
    private function sync_transport_settings() {
        $this->log_message("同步运输设置");
        
        // WooCommerce 运输相关的核心设置
        $transport_options = [
            'woocommerce_shipping_cost_requires_address',
            'woocommerce_ship_to_countries',
            'woocommerce_shipping_destination_requires_postcode',
            'woocommerce_ship_to_destination',
            'woocommerce_shipping_debug_mode',
            'woocommerce_enable_shipping_calc',
            'woocommerce_shipping_cost_requires_address',
            'woocommerce_calc_shipping',
            'woocommerce_enable_guest_checkout',
            'woocommerce_calc_taxes',
            'woocommerce_prices_include_tax'
        ];
        
        foreach ($transport_options as $option_name) {
            $option_value = get_option($option_name);
            if ($option_value !== false) {
                foreach ($this->target_dbs as $db_name => $target_db) {
                    $this->replicate_option($target_db, $option_name, $option_value);
                }
                $this->log_message("同步运输设置: {$option_name}");
            }
        }
    }
    
    /**
     * 同步配送类别详细设置
     */
    private function sync_shipping_class_settings() {
        $this->log_message("同步配送类别详细设置");
        
        global $wpdb;
        
        // 同步配送类别的术语元数据
        $class_meta = $wpdb->get_results("
            SELECT tm.* 
            FROM {$wpdb->prefix}termmeta tm
            INNER JOIN {$wpdb->prefix}term_taxonomy tt ON tm.term_id = tt.term_id
            WHERE tt.taxonomy = 'product_shipping_class'
        ");
        
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("向数据库 {$db_name} 同步配送类别元数据");
            
            // 清空目标数据库的配送类别元数据
            $target_db->query("
                DELETE tm FROM {$target_db->prefix}termmeta tm
                INNER JOIN {$target_db->prefix}term_taxonomy tt ON tm.term_id = tt.term_id
                WHERE tt.taxonomy = 'product_shipping_class'
            ");
            
            // 插入配送类别元数据
            foreach ($class_meta as $meta) {
                $target_db->insert(
                    $target_db->prefix . 'termmeta',
                    [
                        'term_id' => $meta->term_id,
                        'meta_key' => $meta->meta_key,
                        'meta_value' => $meta->meta_value
                    ],
                    ['%d', '%s', '%s']
                );
            }
        }
    }
    
    /**
     * 同步详细本地自提设置
     */
    private function sync_detailed_local_pickup_settings() {
        $this->log_message("同步详细本地自提设置");
        
        // 扩展的本地自提选项
        $pickup_options = [
            'woocommerce_local_pickup_settings',
            'woocommerce_local_pickup_plus_settings',
            'woocommerce_pickup_locations',
            'woocommerce_local_pickup_enable',
            'woocommerce_local_pickup_title',
            'woocommerce_local_pickup_cost',
            'woocommerce_local_pickup_tax_status',
            'local_pickup_plus_locations',
            'local_pickup_plus_default_location',
            'local_pickup_plus_appointment_duration',
            'local_pickup_plus_lead_time',
            'local_pickup_plus_deadline'
        ];
        
        foreach ($pickup_options as $option_name) {
            $option_value = get_option($option_name);
            if ($option_value !== false) {
                foreach ($this->target_dbs as $db_name => $target_db) {
                    $this->replicate_option($target_db, $option_name, $option_value);
                }
                $this->log_message("同步本地自提设置: {$option_name}");
            }
        }
        
        // 同步本地自提相关的文章类型数据
        $this->sync_pickup_location_posts();
    }
    
    /**
     * 同步自提地点文章数据
     */
    private function sync_pickup_location_posts() {
        global $wpdb;
        
        // 获取自提地点相关的文章
        $pickup_posts = $wpdb->get_results("
            SELECT * FROM {$wpdb->prefix}posts 
            WHERE post_type IN ('pickup_location', 'local_pickup_plus')
        ");
        
        if (!empty($pickup_posts)) {
            foreach ($this->target_dbs as $db_name => $target_db) {
                $this->log_message("向数据库 {$db_name} 同步自提地点文章");
                
                foreach ($pickup_posts as $post) {
                    $target_db->query($target_db->prepare(
                        "REPLACE INTO {$target_db->prefix}posts 
                        (ID, post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt, post_status, comment_status, ping_status, post_password, post_name, to_ping, pinged, post_modified, post_modified_gmt, post_content_filtered, post_parent, guid, menu_order, post_type, post_mime_type, comment_count)
                        VALUES (%d, %d, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %d, %s, %d, %s, %s, %d)",
                        $post->ID, $post->post_author, $post->post_date, $post->post_date_gmt, $post->post_content, $post->post_title, $post->post_excerpt, $post->post_status, $post->comment_status, $post->ping_status, $post->post_password, $post->post_name, $post->to_ping, $post->pinged, $post->post_modified, $post->post_modified_gmt, $post->post_content_filtered, $post->post_parent, $post->guid, $post->menu_order, $post->post_type, $post->post_mime_type, $post->comment_count
                    ));
                    
                    // 同步文章元数据
                    $post_meta = $wpdb->get_results($wpdb->prepare(
                        "SELECT * FROM {$wpdb->prefix}postmeta WHERE post_id = %d",
                        $post->ID
                    ));
                    
                    foreach ($post_meta as $meta) {
                        $target_db->query($target_db->prepare(
                            "REPLACE INTO {$target_db->prefix}postmeta (post_id, meta_key, meta_value) VALUES (%d, %s, %s)",
                            $meta->post_id, $meta->meta_key, $meta->meta_value
                        ));
                    }
                }
            }
        }
    }
    
    /**
     * 同步全局配送选项
     */
    private function sync_global_shipping_options() {
        $this->log_message("同步全局配送选项");
        
        global $wpdb;
        
        // 获取所有WooCommerce配送相关的选项
        $shipping_options = $wpdb->get_results("
            SELECT option_name, option_value, autoload 
            FROM {$wpdb->prefix}options 
            WHERE option_name LIKE 'woocommerce_ship%'
            OR option_name LIKE 'woocommerce_calc%'
            OR option_name LIKE 'woocommerce_enable_ship%'
            OR option_name LIKE 'woocommerce_default_ship%'
            OR option_name LIKE '%shipping%'
            OR option_name LIKE '%delivery%'
            OR option_name LIKE '%freight%'
        ");
        
        foreach ($this->target_dbs as $db_name => $target_db) {
            $this->log_message("向数据库 {$db_name} 同步全局配送选项");
            
            foreach ($shipping_options as $option) {
                // 跳过一些系统选项
                if (strpos($option->option_name, 'transient') !== false || 
                    strpos($option->option_name, '_site_transient') !== false) {
                    continue;
                }
                
                $target_db->query($target_db->prepare(
                    "REPLACE INTO {$target_db->prefix}options (option_name, option_value, autoload) VALUES (%s, %s, %s)",
                    $option->option_name,
                    $option->option_value,
                    $option->autoload
                ));
            }
        }
        
        $this->log_message("全局配送选项同步完成");
    }
}

// 初始化配送复制类
if (class_exists('WP_Multi_DB_Config_Manager')) {
    add_action('plugins_loaded', function() {
        // 确保 WordPress 和 WooCommerce 都已加载
        if (class_exists('WooCommerce') && function_exists('WC')) {
            YXJTO_Shipping_Replication::get_instance();
        }
    }, 20); // 使用较高的优先级确保在其他插件之后加载
}

/**
 * 获取配送复制实例的全局函数
 */
if (!function_exists('YXJTO_Shipping_Replication')) {
    function YXJTO_Shipping_Replication() {
        return YXJTO_Shipping_Replication::get_instance();
    }
}
