<?php
/**
 * PayPal Multi Gateway API Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_API {
    
    private $sandbox_base_url = 'https://api.sandbox.paypal.com';
    private $live_base_url = 'https://api.paypal.com';

    public function __construct() {
    }
    
    /**
     * Test account connection
     */
    public function test_account($account_type, $account_data) {
        switch ($account_type) {
            case 'email':
                return $this->test_email_account($account_data);
            case 'paypal_me':
                return $this->test_paypal_me_account($account_data);
            case 'api':
                return $this->test_api_account($account_data);
            default:
                return array(
                    'success' => false,
                    'message' => __('Invalid account type', 'yxjto-paypal-multi-gateway')
                );
        }
    }
    
    /**
     * Test email account
     */
    private function test_email_account($account_data) {
        if (empty($account_data['email']) || !is_email($account_data['email'])) {
            return array(
                'success' => false,
                'message' => __('Invalid email address', 'yxjto-paypal-multi-gateway')
            );
        }

        // For email accounts, we can only validate the format
        // Actual validation would require a test transaction
        return array(
            'success' => true,
            'message' => __('Email format is valid', 'yxjto-paypal-multi-gateway')
        );
    }
    
    /**
     * Test PayPal.me account
     */
    private function test_paypal_me_account($account_data) {
        if (empty($account_data['paypal_me_url']) || !filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL)) {
            return array(
                'success' => false,
                'message' => __('Invalid PayPal.me URL', 'yxjto-paypal-multi-gateway')
            );
        }

        // Check if URL is accessible
        $response = wp_remote_head($account_data['paypal_me_url'], array(
            'timeout' => 10,
            'user-agent' => 'PayPal Multi Gateway Test'
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => __('PayPal.me URL is not accessible', 'yxjto-paypal-multi-gateway')
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return array(
                'success' => false,
                'message' => sprintf(__('PayPal.me URL returned status code: %d', 'yxjto-paypal-multi-gateway'), $response_code)
            );
        }

        return array(
            'success' => true,
            'message' => __('PayPal.me URL is accessible', 'yxjto-paypal-multi-gateway')
        );
    }
    
    /**
     * Test API account
     */
    private function test_api_account($account_data) {
        if (empty($account_data['client_id']) || empty($account_data['client_secret'])) {
            return array(
                'success' => false,
                'message' => __('Client ID and client secret are required', 'yxjto-paypal-multi-gateway')
            );
        }

        $access_token = $this->get_access_token($account_data);

        if (!$access_token) {
            return array(
                'success' => false,
                'message' => __('Failed to get access token. Please check your credentials.', 'yxjto-paypal-multi-gateway')
            );
        }

        return array(
            'success' => true,
            'message' => __('API credentials are valid', 'yxjto-paypal-multi-gateway'),
            'access_token' => $access_token
        );
    }
    
    /**
     * Create payment
     */
    public function create_payment($order, $account) {
        // 调试日志：记录创建支付时使用的账户
        error_log("PayPal Multi Gateway [API Debug]: Creating payment for Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [API Debug]: Using account ID: {$account->account_id}");
        error_log("PayPal Multi Gateway [API Debug]: Account type: {$account->account_type}");

        $account_data = json_decode($account->account_data, true);

        $result = null;
        switch ($account->account_type) {
            case 'email':
                $result = $this->create_email_payment($order, $account_data);
                break;
            case 'paypal_me':
                $result = $this->create_paypal_me_payment($order, $account_data);
                break;
            case 'api':
                $result = $this->create_api_payment($order, $account_data);
                break;
            default:
                $result = array(
                    'success' => false,
                    'message' => __('Invalid account type', 'yxjto-paypal-multi-gateway')
                );
        }

        return $result;
    }
    
    /**
     * Create email-based payment
     */
    private function create_email_payment($order, $account_data) {
        // 调试日志：记录邮箱支付创建详情
        error_log("PayPal Multi Gateway [Email Payment Debug]: Creating email payment for Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [Email Payment Debug]: PayPal Email: {$account_data['email']}");
        error_log("PayPal Multi Gateway [Email Payment Debug]: Order Total: {$order->get_total()} {$order->get_currency()}");

        $paypal_args = array(
            'business' => $account_data['email'],
            'cmd' => '_xclick',
            'item_name' => sprintf(__('Order #%s', 'yxjto-paypal-multi-gateway'), $order->get_order_number()),
            'item_number' => $order->get_id(),
            'amount' => $order->get_total(),
            'currency_code' => $order->get_currency(),
            'return' => add_query_arg(array(
                'wc-api' => 'yxjto_paypal_multi_gateway_payment',
                'order_id' => $order->get_id(),
                'payment_id' => 'email_' . $order->get_id() . '_' . time()
            ), home_url('/')),
            'cancel_return' => add_query_arg(array(
                'wc-api' => 'yxjto_paypal_multi_gateway_payment',
                'cancel' => 'true',
                'order_id' => $order->get_id()
            ), home_url('/')),
            'notify_url' => add_query_arg('wc-api', 'paypal_multi_gateway_ipn', home_url('/')),
            'no_shipping' => $order->needs_shipping_address() ? '0' : '1',
            'no_note' => '1',
            'charset' => 'utf-8',
            'rm' => '2',  // 返回方法：POST方式返回所有支付变量
            'upload' => '1',
            'custom' => $order->get_id(), // 添加自定义字段，用于IPN识别订单
            'invoice' => 'order_' . $order->get_id() . '_' . time() // 添加发票号
        );
        
        // Add billing information
        $paypal_args['first_name'] = $order->get_billing_first_name();
        $paypal_args['last_name'] = $order->get_billing_last_name();
        $paypal_args['email'] = $order->get_billing_email();
        $paypal_args['address1'] = $order->get_billing_address_1();
        $paypal_args['address2'] = $order->get_billing_address_2();
        $paypal_args['city'] = $order->get_billing_city();
        $paypal_args['state'] = $order->get_billing_state();
        $paypal_args['zip'] = $order->get_billing_postcode();
        $paypal_args['country'] = $order->get_billing_country();
        
        // Add line items
        $item_loop = 0;
        foreach ($order->get_items() as $item) {
            $item_loop++;
            $paypal_args['item_name_' . $item_loop] = $item->get_name();
            $paypal_args['quantity_' . $item_loop] = $item->get_quantity();
            $paypal_args['amount_' . $item_loop] = $order->get_item_total($item, false);
        }
        
        // Determine PayPal URL
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $paypal_url = $settings['test_mode'] === 'yes' ? 'https://www.sandbox.paypal.com/cgi-bin/webscr' : 'https://www.paypal.com/cgi-bin/webscr';

        $approval_url = $paypal_url . '?' . http_build_query($paypal_args);
        $payment_id = 'email_' . $order->get_id() . '_' . time();

        // 详细调试日志
        error_log("PayPal Multi Gateway [Email Payment Debug]: PayPal URL: {$paypal_url}");
        error_log("PayPal Multi Gateway [Email Payment Debug]: Test Mode: {$settings['test_mode']}");
        error_log("PayPal Multi Gateway [Email Payment Debug]: Generated Payment ID: {$payment_id}");
        error_log("PayPal Multi Gateway [Email Payment Debug]: PayPal Args: " . print_r($paypal_args, true));
        error_log("PayPal Multi Gateway [Email Payment Debug]: Final Approval URL: {$approval_url}");

        return array(
            'success' => true,
            'payment_id' => $payment_id,
            'approval_url' => $approval_url
        );
    }
    
    /**
     * Create PayPal.me payment
     */
    private function create_paypal_me_payment($order, $account_data) {
        // 调试日志：记录PayPal.me支付创建详情
        error_log("PayPal Multi Gateway [PayPal.me Payment Debug]: Creating PayPal.me payment for Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [PayPal.me Payment Debug]: PayPal.me URL: {$account_data['paypal_me_url']}");
        error_log("PayPal Multi Gateway [PayPal.me Payment Debug]: Order Total: {$order->get_total()} {$order->get_currency()}");

        $paypal_me_url = rtrim($account_data['paypal_me_url'], '/');
        $amount = $order->get_total();
        $currency = $order->get_currency();
        
        // PayPal.me URL format: https://paypal.me/username/amount
        $approval_url = $paypal_me_url . '/' . $amount . $currency;
        
        // Add return URL as a parameter (not officially supported but some users add it)
        $return_url = add_query_arg(array(
            'wc-api' => 'paypal_multi_gateway_paypalme_return',
            'order_id' => $order->get_id()
        ), home_url('/'));

        $approval_url = add_query_arg('return', urlencode($return_url), $approval_url);
        $payment_id = 'paypal_me_' . $order->get_id() . '_' . time();

        // 详细调试日志
        error_log("PayPal Multi Gateway [PayPal.me Payment Debug]: Generated Payment ID: {$payment_id}");
        error_log("PayPal Multi Gateway [PayPal.me Payment Debug]: Final Approval URL: {$approval_url}");

        return array(
            'success' => true,
            'payment_id' => $payment_id,
            'approval_url' => $approval_url
        );
    }
    
    /**
     * Create API-based payment
     */
    private function create_api_payment($order, $account_data) {
        // 调试日志：记录API支付创建详情
        error_log("PayPal Multi Gateway [API Payment Debug]: Creating API payment for Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [API Payment Debug]: Client ID: {$account_data['client_id']}");
        error_log("PayPal Multi Gateway [API Payment Debug]: Sandbox Mode: " . (isset($account_data['sandbox']) && $account_data['sandbox'] ? 'Yes' : 'No'));
        error_log("PayPal Multi Gateway [API Payment Debug]: Order Total: {$order->get_total()} {$order->get_currency()}");

        $access_token = $this->get_access_token($account_data);
        
        if (!$access_token) {
            error_log("PayPal Multi Gateway [API Payment Debug]: Failed to get access token");
            return array(
                'success' => false,
                'message' => __('Failed to get access token', 'yxjto-paypal-multi-gateway')
            );
        }

        error_log("PayPal Multi Gateway [API Payment Debug]: Access token obtained successfully");
        
        $base_url = isset($account_data['sandbox']) && $account_data['sandbox'] ? $this->sandbox_base_url : $this->live_base_url;
        
        // 获取商品列表和小计（价格调整已在process_payment中完成）
        $subtotal = $order->get_subtotal();
        $tax_total = $order->get_total_tax();
        $shipping_total = $order->get_shipping_total();
        $discount_total = $order->get_total_discount();
        $order_total = $order->get_total();

        // 调试日志
        error_log("PayPal Multi Gateway [API Debug]: Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [API Debug]: Using pre-adjusted values - Subtotal: {$subtotal}, Tax: {$tax_total}, Shipping: {$shipping_total}, Discount: {$discount_total}, Total: {$order_total}");

        // 获取商品列表（不需要额外调整，因为价格已在process_payment中调整）
        $item_list = $this->get_order_items($order);

        // 计算实际的商品总价（已调整后的价格）
        $actual_items_total = 0;
        foreach ($order->get_items() as $item) {
            $actual_items_total += $item->get_total();
        }

        error_log("PayPal Multi Gateway [API Debug]: Order subtotal: {$subtotal}");
        error_log("PayPal Multi Gateway [API Debug]: Calculated items total: {$actual_items_total}");
        error_log("PayPal Multi Gateway [API Debug]: Order total: {$order_total}");

        // PayPal API要求：商品价格总和必须等于小计
        // 如果有折扣，我们需要使用折扣前的价格作为小计，然后用shipping_discount扣除折扣
        $paypal_subtotal = $actual_items_total;

        // 如果有折扣，调整小计为折扣前的金额
        if ($discount_total > 0) {
            $paypal_subtotal = $actual_items_total + $discount_total;
            error_log("PayPal Multi Gateway [API Debug]: Adjusting subtotal for discount - Original: {$actual_items_total}, Discount: {$discount_total}, Adjusted: {$paypal_subtotal}");
        }

        // 构建交易金额详情
        $amount_details = array(
            'subtotal' => number_format($paypal_subtotal, 2, '.', ''),
            'tax' => number_format($tax_total, 2, '.', ''),
            'shipping' => number_format($shipping_total, 2, '.', '')
        );

        // 添加折扣信息
        if ($discount_total > 0) {
            $amount_details['shipping_discount'] = number_format($discount_total, 2, '.', '');
            error_log("PayPal Multi Gateway [API Debug]: Added discount: {$discount_total}");
        }

        // 处理额外费用（从订单项目中获取）
        $fee_total = 0;
        foreach ($order->get_items('fee') as $fee_item) {
            $fee_total += $fee_item->get_total();
        }

        if ($fee_total > 0) {
            // PayPal API中费用通常包含在小计中，或者作为单独的处理费
            error_log("PayPal Multi Gateway [API Debug]: Order has fees: {$fee_total}");
        }

        // 处理运费税费
        $shipping_tax = $order->get_shipping_tax();
        if ($shipping_tax > 0) {
            // 运费税费通常包含在税费总额中
            error_log("PayPal Multi Gateway [API Debug]: Shipping tax: {$shipping_tax}");
        }

        // 验证金额计算是否正确
        $calculated_total = floatval($amount_details['subtotal']) + floatval($amount_details['tax']) + floatval($amount_details['shipping']);
        if (isset($amount_details['shipping_discount'])) {
            $calculated_total -= floatval($amount_details['shipping_discount']);
        }

        $order_total = $order->get_total();

        error_log("PayPal Multi Gateway [API Debug]: Amount breakdown:");
        error_log("PayPal Multi Gateway [API Debug]: - Subtotal: {$amount_details['subtotal']}");
        error_log("PayPal Multi Gateway [API Debug]: - Tax: {$amount_details['tax']}");
        error_log("PayPal Multi Gateway [API Debug]: - Shipping: {$amount_details['shipping']}");
        if (isset($amount_details['shipping_discount'])) {
            error_log("PayPal Multi Gateway [API Debug]: - Discount: {$amount_details['shipping_discount']}");
        }
        error_log("PayPal Multi Gateway [API Debug]: - Calculated Total: {$calculated_total}");
        error_log("PayPal Multi Gateway [API Debug]: - Order Total: {$order_total}");

        // 如果计算的总额与订单总额不匹配，调整小计
        if (abs($calculated_total - $order_total) > 0.01) {
            error_log("PayPal Multi Gateway [API Warning]: Amount mismatch detected, adjusting subtotal");

            // 重新计算小计以确保总额匹配
            $adjusted_subtotal = $order_total - floatval($amount_details['tax']) - floatval($amount_details['shipping']);
            if (isset($amount_details['shipping_discount'])) {
                $adjusted_subtotal += floatval($amount_details['shipping_discount']);
            }

            $amount_details['subtotal'] = number_format($adjusted_subtotal, 2, '.', '');
            error_log("PayPal Multi Gateway [API Debug]: Adjusted subtotal to: {$adjusted_subtotal}");
        }
        
        $payment_data = array(
            'intent' => 'sale',
            'payer' => array(
                'payment_method' => 'paypal'
            ),
            'transactions' => array(
                array(
                    'amount' => array(
                        'total' => number_format($order->get_total(), 2, '.', ''),
                        'currency' => $order->get_currency(),
                        'details' => $amount_details
                    ),
                    'description' => sprintf(__('Order #%s from %s', 'yxjto-paypal-multi-gateway'),
                                           $order->get_order_number(),
                                           get_bloginfo('name')),
                    'invoice_number' => $this->generate_unique_invoice_number($order),
                    'item_list' => $item_list
                )
            ),
            'redirect_urls' => array(
                'return_url' => add_query_arg(array(
                    'wc-api' => 'yxjto_paypal_multi_gateway_payment',
                    'order_id' => $order->get_id()
                ), home_url('/')),
                'cancel_url' => add_query_arg(array(
                    'wc-api' => 'yxjto_paypal_multi_gateway_payment',
                    'cancel' => 'true',
                    'order_id' => $order->get_id()
                ), home_url('/'))
            )
        );
        
        // 获取插件设置中的超时时间
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $timeout = $settings['timeout'];

        // 详细调试日志
        error_log("PayPal Multi Gateway [API Payment Debug]: Base URL: {$base_url}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Original Subtotal: {$subtotal}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Actual Items Total: {$actual_items_total}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Tax: {$tax_total}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Shipping: {$shipping_total}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Discount: {$discount_total}");
        error_log("PayPal Multi Gateway [API Amount Debug]: Order Total: {$order_total}");
        error_log("PayPal Multi Gateway [API Amount Debug]: PayPal API Calculated Total: " . ($actual_items_total + $tax_total + $shipping_total));

        // 详细显示PayPal API提交的数据结构
        error_log("PayPal Multi Gateway [API Debug]: === PayPal API Submission Details ===");
        error_log("PayPal Multi Gateway [API Debug]: Transaction Amount:");
        error_log("PayPal Multi Gateway [API Debug]: - Total: " . $payment_data['transactions'][0]['amount']['total']);
        error_log("PayPal Multi Gateway [API Debug]: - Currency: " . $payment_data['transactions'][0]['amount']['currency']);

        error_log("PayPal Multi Gateway [API Debug]: Amount Details:");
        foreach ($payment_data['transactions'][0]['amount']['details'] as $key => $value) {
            error_log("PayPal Multi Gateway [API Debug]: - {$key}: {$value}");
        }

        error_log("PayPal Multi Gateway [API Debug]: Items (" . count($payment_data['transactions'][0]['item_list']['items']) . " items):");
        foreach ($payment_data['transactions'][0]['item_list']['items'] as $index => $item) {
            $item_total = floatval($item['price']) * intval($item['quantity']);
            error_log("PayPal Multi Gateway [API Debug]: Item " . ($index + 1) . ": {$item['name']} - Qty: {$item['quantity']}, Price: {$item['price']}, Total: {$item_total}");
        }

        // 验证PayPal API数据的一致性
        $api_subtotal = floatval($payment_data['transactions'][0]['amount']['details']['subtotal']);
        $api_tax = floatval($payment_data['transactions'][0]['amount']['details']['tax']);
        $api_shipping = floatval($payment_data['transactions'][0]['amount']['details']['shipping']);
        $api_discount = isset($payment_data['transactions'][0]['amount']['details']['shipping_discount']) ?
                       floatval($payment_data['transactions'][0]['amount']['details']['shipping_discount']) : 0;

        $api_calculated_total = $api_subtotal + $api_tax + $api_shipping - $api_discount;
        $api_declared_total = floatval($payment_data['transactions'][0]['amount']['total']);

        error_log("PayPal Multi Gateway [API Debug]: PayPal API Validation:");
        error_log("PayPal Multi Gateway [API Debug]: - Calculated: {$api_calculated_total}");
        error_log("PayPal Multi Gateway [API Debug]: - Declared: {$api_declared_total}");
        error_log("PayPal Multi Gateway [API Debug]: - Match: " . (abs($api_calculated_total - $api_declared_total) < 0.01 ? "YES" : "NO"));

        error_log("PayPal Multi Gateway [API Payment Debug]: Full Payment Data: " . wp_json_encode($payment_data));

        $response = wp_remote_post($base_url . '/v1/payments/payment', array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $access_token
            ),
            'body' => wp_json_encode($payment_data),
            'timeout' => $timeout
        ));

        if (is_wp_error($response)) {
            error_log("PayPal Multi Gateway [API Payment Debug]: WP Error: " . $response->get_error_message());
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        $response_code = wp_remote_retrieve_response_code($response);

        error_log("PayPal Multi Gateway [API Payment Debug]: Response Code: {$response_code}");
        error_log("PayPal Multi Gateway [API Payment Debug]: Response Body: {$body}");

        if (isset($data['id']) && isset($data['links'])) {
            $approval_url = '';
            foreach ($data['links'] as $link) {
                if ($link['rel'] === 'approval_url') {
                    $approval_url = $link['href'];
                    break;
                }
            }

            if ($approval_url) {
                error_log("PayPal Multi Gateway [API Payment Debug]: Payment created successfully - ID: {$data['id']}, Approval URL: {$approval_url}");
                return array(
                    'success' => true,
                    'payment_id' => $data['id'],
                    'approval_url' => $approval_url
                );
            } else {
                error_log("PayPal Multi Gateway [API Payment Debug]: No approval URL found in response");
            }
        } else {
            error_log("PayPal Multi Gateway [API Payment Debug]: Invalid response structure - missing ID or links");
        }

        $error_message = isset($data['message']) ? $data['message'] : __('Unknown error occurred', 'yxjto-paypal-multi-gateway');
        error_log("PayPal Multi Gateway [API Payment Debug]: Payment creation failed: {$error_message}");

        return array(
            'success' => false,
            'message' => $error_message
        );
    }

    /**
     * Execute payment (for API-based payments)
     */
    public function execute_payment($payment_id, $payer_id, $order, $account_id = null) {
        // 调试日志：记录执行支付的详情
        error_log("PayPal Multi Gateway [Execute Payment Debug]: Payment ID: {$payment_id}, Payer ID: {$payer_id}, Order ID: {$order->get_id()}");
        error_log("PayPal Multi Gateway [Execute Payment Debug]: Provided account_id: " . ($account_id ?: 'null'));
        
        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $account = null;

        // 多重回退机制获取账户配置
        // 1. 尝试使用提供的 account_id
        if ($account_id) {
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Trying provided account_id: {$account_id}");
            $account = $accounts->get_account($account_id);
            if ($account) {
                error_log("PayPal Multi Gateway [Execute Payment Debug]: Found account using provided ID: {$account_id}");
            }
        }
        
        // 2. 尝试从订单元数据获取
        if (!$account) {
            $order_account_id = $order->get_meta('_paypal_multi_gateway_account_id');
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Account ID from order meta: " . ($order_account_id ?: 'null'));
            if ($order_account_id) {
                $account = $accounts->get_account($order_account_id);
                if ($account) {
                    error_log("PayPal Multi Gateway [Execute Payment Debug]: Found account using order meta ID: {$order_account_id}");
                }
            }
        }
        
        // 3. 尝试使用默认账户
        if (!$account) {
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Trying to get default account");
            $active_accounts = $accounts->get_active_accounts();
            if (!empty($active_accounts)) {
                $account = reset($active_accounts); // 获取第一个活跃账户
                error_log("PayPal Multi Gateway [Execute Payment Debug]: Using first available account: " . $account->account_id);
            }
        }
        
        // 4. 最后的回退：负载均衡选择
        if (!$account) {
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Trying load balancing account selection");
            $account = $accounts->select_account_for_checkout_with_load_balancing();
            if ($account) {
                error_log("PayPal Multi Gateway [Execute Payment Debug]: Load balancing selected account: " . $account->account_id);
            }
        }

        // 检查账户类型并相应处理
        if (!$account) {
            error_log("PayPal Multi Gateway [Execute Payment Debug]: No account found after all fallback attempts");
            $available_accounts = $accounts->get_active_accounts();
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Available accounts: " . print_r(array_map(function($acc) { return $acc->account_id; }, $available_accounts), true));
            return array(
                'success' => false,
                'message' => __('Account configuration not found', 'yxjto-paypal-multi-gateway')
            );
        }

        error_log("PayPal Multi Gateway [Execute Payment Debug]: Account type: {$account->account_type}");

        if ($account->account_type !== 'api') {
            // For non-API payments (email, paypal_me), handle differently
            error_log("PayPal Multi Gateway [Execute Payment Debug]: Processing non-API payment type: {$account->account_type}");
            
            if ($account->account_type === 'email') {
                // 对于邮箱类型，检查是否有PayerID（表示用户从PayPal返回）
                if ($payer_id && $payer_id !== 'N/A') {
                    // 用户从PayPal成功返回，标记支付为完成
                    error_log("PayPal Multi Gateway [Execute Payment Debug]: Email payment with valid Payer ID, marking as completed");
                    
                    // 生成一个模拟的transaction ID用于跟踪
                    $transaction_id = 'email_' . $order->get_id() . '_' . time() . '_' . substr($payer_id, 0, 8);
                    
                    return array(
                        'success' => true,
                        'transaction_id' => $transaction_id,
                        'payment_method' => 'email_return'
                    );
                } else {
                    // 没有PayerID，可能是直接返回或者取消
                    error_log("PayPal Multi Gateway [Execute Payment Debug]: Email payment without valid Payer ID");
                    return array(
                        'success' => false,
                        'message' => __('Payment was not completed or was cancelled', 'yxjto-paypal-multi-gateway')
                    );
                }
            } else {
                // PayPal.me 或其他类型
                error_log("PayPal Multi Gateway [Execute Payment Debug]: Non-email, non-API payment type");
                return array(
                    'success' => true,
                    'transaction_id' => $payment_id,
                    'payment_method' => $account->account_type
                );
            }
        }

        $account_data = json_decode($account->account_data, true);
        $access_token = $this->get_access_token($account_data);

        if (!$access_token) {
            return array(
                'success' => false,
                'message' => __('Failed to get access token', 'yxjto-paypal-multi-gateway')
            );
        }

        $base_url = isset($account_data['sandbox']) && $account_data['sandbox'] ? $this->sandbox_base_url : $this->live_base_url;

        $execute_data = array(
            'payer_id' => $payer_id
        );

        // 获取插件设置中的超时时间
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $timeout = $settings['timeout'];

        $response = wp_remote_post($base_url . '/v1/payments/payment/' . $payment_id . '/execute', array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $access_token
            ),
            'body' => wp_json_encode($execute_data),
            'timeout' => $timeout
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['state']) && $data['state'] === 'approved') {
            $transaction_id = isset($data['transactions'][0]['related_resources'][0]['sale']['id'])
                            ? $data['transactions'][0]['related_resources'][0]['sale']['id']
                            : $payment_id;

            return array(
                'success' => true,
                'transaction_id' => $transaction_id
            );
        } else {
            // 提供更详细的错误信息
            $error_message = __('Payment execution failed', 'yxjto-paypal-multi-gateway');
            $error_details = array();

            if (isset($data['message'])) {
                $error_message = $data['message'];
            }

            if (isset($data['details']) && is_array($data['details'])) {
                foreach ($data['details'] as $detail) {
                    if (isset($detail['issue'])) {
                        $error_details[] = $detail['issue'];
                    }
                }
            }

            if (isset($data['name'])) {
                $error_details[] = 'Error Code: ' . $data['name'];
            }

            if (isset($data['state'])) {
                $error_details[] = 'Payment State: ' . $data['state'];
            }

            // 如果有详细错误信息，添加到错误消息中
            if (!empty($error_details)) {
                $error_message .= ' (' . implode(', ', $error_details) . ')';
            }

            return array(
                'success' => false,
                'message' => $error_message,
                'raw_response' => $data,
                'http_code' => wp_remote_retrieve_response_code($response)
            );
        }
    }

    /**
     * Get access token for API calls
     */
    private function get_access_token($account_data) {
        $cache_key = 'yxjto_paypal_access_token_' . md5($account_data['client_id']);
        $access_token = get_transient($cache_key);

        if ($access_token) {
            return $access_token;
        }

        $base_url = isset($account_data['sandbox']) && $account_data['sandbox'] ? $this->sandbox_base_url : $this->live_base_url;

        // 获取插件设置中的超时时间
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $timeout = $settings['timeout'];

        $response = wp_remote_post($base_url . '/v1/oauth2/token', array(
            'headers' => array(
                'Accept' => 'application/json',
                'Accept-Language' => 'en_US',
                'Authorization' => 'Basic ' . base64_encode($account_data['client_id'] . ':' . $account_data['client_secret'])
            ),
            'body' => 'grant_type=client_credentials',
            'timeout' => $timeout
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['access_token'])) {
            $expires_in = isset($data['expires_in']) ? intval($data['expires_in']) - 60 : 3540; // Subtract 60 seconds for safety
            set_transient($cache_key, $data['access_token'], $expires_in);
            return $data['access_token'];
        }

        return false;
    }

    /**
     * Get order items for PayPal API
     * 确保商品价格总和等于PayPal API的小计（折扣前金额）
     */
    private function get_order_items($order) {
        $items = array();
        $order_items = $order->get_items();
        $item_count = count($order_items);

        // 计算折扣前的商品总价（PayPal API的小计）
        $actual_items_total = 0;
        foreach ($order_items as $item) {
            $actual_items_total += $item->get_total();
        }

        $discount_total = $order->get_total_discount();
        $paypal_subtotal = $actual_items_total + $discount_total; // 折扣前的总价

        error_log("PayPal Multi Gateway [API Debug]: Items calculation for PayPal API:");
        error_log("PayPal Multi Gateway [API Debug]: - Actual items total (after discount): {$actual_items_total}");
        error_log("PayPal Multi Gateway [API Debug]: - Discount total: {$discount_total}");
        error_log("PayPal Multi Gateway [API Debug]: - PayPal subtotal (before discount): {$paypal_subtotal}");

        $current_index = 0;
        $accumulated_price = 0;

        foreach ($order_items as $item_id => $item) {
            $current_index++;
            $product = $item->get_product();
            $quantity = $item->get_quantity();

            // 计算折扣前的单价
            $item_total_after_discount = $item->get_total();
            $item_discount_ratio = $discount_total > 0 ? ($paypal_subtotal / $actual_items_total) : 1;
            $item_total_before_discount = $item_total_after_discount * $item_discount_ratio;
            $unit_price_before_discount = $quantity > 0 ? $item_total_before_discount / $quantity : $item_total_before_discount;

            // 对于最后一个商品，调整单价以确保总和等于PayPal小计
            if ($current_index === $item_count) {
                $remaining_total = $paypal_subtotal - $accumulated_price;
                $adjusted_unit_price = $quantity > 0 ? $remaining_total / $quantity : $remaining_total;

                error_log("PayPal Multi Gateway [API Debug]: Last item '{$item->get_name()}': Calculated unit price={$unit_price_before_discount}, Adjusted unit price={$adjusted_unit_price}");

                $unit_price_before_discount = $adjusted_unit_price;
            }

            $formatted_unit_price = number_format($unit_price_before_discount, 2, '.', '');
            $calculated_item_total = $formatted_unit_price * $quantity;
            $accumulated_price += $calculated_item_total;

            $item_data = array(
                'name' => $item->get_name(),
                'quantity' => $quantity,
                'price' => $formatted_unit_price,
                'currency' => $order->get_currency()
            );

            // 添加SKU信息（如果有）
            if ($product && $product->get_sku()) {
                $item_data['sku'] = $product->get_sku();
            }

            error_log("PayPal Multi Gateway [API Debug]: Item '{$item->get_name()}': Qty={$quantity}, Unit Price={$formatted_unit_price}, Item Total={$calculated_item_total}");

            $items[] = $item_data;
        }

        error_log("PayPal Multi Gateway [API Debug]: Final accumulated price: {$accumulated_price}");
        error_log("PayPal Multi Gateway [API Debug]: PayPal subtotal: {$paypal_subtotal}");
        error_log("PayPal Multi Gateway [API Debug]: Price match: " . (abs($accumulated_price - $paypal_subtotal) < 0.01 ? "YES" : "NO"));

        return array('items' => $items);
    }

    /**
     * 生成唯一的发票编号
     * 确保在多数据库环境下不会重复
     */
    private function generate_unique_invoice_number($order) {
        // 获取当前数据库名称
        $current_db = 'default';
        if (class_exists('YXJTO_Gateway')) {
            $current_db = YXJTO_Gateway::get_instance()->get_current_database();
        }

        // 基本订单号
        $order_number = $order->get_order_number();

        // 添加时间戳和随机字符串确保唯一性
        $timestamp = time();
        $random = substr(md5(mt_rand()), 0, 6);

        // 格式: 订单号-数据库-时间戳-随机字符
        $invoice_number = sprintf('%s-%s-%s-%s', $order_number, $current_db, $timestamp, $random);

        // 记录生成的发票号到订单元数据
        $order->update_meta_data('_paypal_invoice_number', $invoice_number);
        $order->save();

        return $invoice_number;
    }
}
