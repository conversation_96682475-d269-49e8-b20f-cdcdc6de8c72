<?php
/**
 * WordPress多数据库插件 - 爬虫检测器
 * 检测搜索引擎爬虫和社交媒体爬虫
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 爬虫检测器类
 */
class WP_Multi_Database_Crawler_Detector {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 当前User-Agent
     */
    private $user_agent;

    /**
     * 当前客户端IP
     */
    private $client_ip;

    /**
     * 爬虫检测设置
     */
    private $settings;
    
    /**
     * 爬虫IP数据库实例
     */
    private $ip_database;

    /**
     * 默认爬虫模式
     */
    private $default_crawlers = [
        'googlebot' => [
            'name' => 'Google Bot',
            'patterns' => [
                'Googlebot',
                'Google-InspectionTool',
                'GoogleOther',
                'Google-Site-Verification'
            ],
            'description' => 'Google search engine crawler'
        ],
        'bingbot' => [
            'name' => 'Bing Bot',
            'patterns' => [
                'bingbot',
                'BingPreview',
                'msnbot'
            ],
            'description' => 'Microsoft Bing search engine crawler'
        ],
        'facebookbot' => [
            'name' => 'Facebook Bot',
            'patterns' => [
                'facebookexternalhit',
                'Facebot',
                'FacebookBot'
            ],
            'description' => 'Facebook social media crawler'
        ],
        'twitterbot' => [
            'name' => 'Twitter Bot',
            'patterns' => [
                'Twitterbot',
                'TwitterBot'
            ],
            'description' => 'Twitter social media crawler'
        ],
        'linkedinbot' => [
            'name' => 'LinkedIn Bot',
            'patterns' => [
                'LinkedInBot',
                'LinkedInBot/'
            ],
            'description' => 'LinkedIn social media crawler'
        ],
        'baidubot' => [
            'name' => 'Baidu Bot',
            'patterns' => [
                'Baiduspider',
                'BaiduSpider'
            ],
            'description' => 'Baidu search engine crawler (China)'
        ],
        'yandexbot' => [
            'name' => 'Yandex Bot',
            'patterns' => [
                'YandexBot',
                'YandexImages',
                'YandexVideo'
            ],
            'description' => 'Yandex search engine crawler (Russia)'
        ],
        'yahoobot' => [
            'name' => 'Yahoo Bot',
            'patterns' => [
                'Slurp',
                'Yahoo! Slurp'
            ],
            'description' => 'Yahoo search engine crawler'
        ],
        'duckduckbot' => [
            'name' => 'DuckDuckGo Bot',
            'patterns' => [
                'DuckDuckBot',
                'DuckDuckGo-Favicons-Bot'
            ],
            'description' => 'DuckDuckGo search engine crawler'
        ]
    ];
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $this->client_ip = $this->get_client_ip();
        $this->settings = WP_Multi_DB_Config_Manager::get_crawler_settings();

        // 加载IP数据库
        if (!class_exists('WP_Multi_Database_Crawler_IP_Database')) {
            require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/crawler-ip-database.php';
        }
        $this->ip_database = new WP_Multi_Database_Crawler_IP_Database();
    }
    
    /**
     * 检测是否为爬虫
     */
    public function is_crawler() {
        // 如果爬虫检测被禁用，返回false
        if (empty($this->settings['enabled'])) {
            return false;
        }

        // 优先检测IP地址（更可靠）
        if (!empty($this->client_ip) && !empty($this->settings['enable_ip_detection'])) {
            $ip_crawler = $this->detect_crawler_by_ip();
            if ($ip_crawler) {
                // 如果启用了反向DNS验证，进行验证
                if (!empty($this->settings['enable_reverse_dns'])) {
                    if ($this->verify_crawler_by_reverse_dns($ip_crawler['type'])) {
                        $this->log_crawler_visit($ip_crawler, 'ip_verified');
                        return true;
                    }
                } else {
                    $this->log_crawler_visit($ip_crawler, 'ip');
                    return true;
                }
            }
        }

        // 如果没有User-Agent，且IP检测未成功，不是爬虫
        if (empty($this->user_agent)) {
            return false;
        }

        // 检测User-Agent模式
        $detected_crawler = $this->detect_default_crawler();
        if ($detected_crawler) {
            // 如果同时启用了IP验证，检查IP是否匹配
            if (!empty($this->settings['enable_ip_verification']) && !empty($this->client_ip)) {
                if ($this->verify_crawler_ip($detected_crawler['type'])) {
                    $this->log_crawler_visit($detected_crawler, 'user_agent_ip_verified');
                    return true;
                } else {
                    // IP不匹配，可能是伪造的User-Agent
                    $this->log_crawler_visit([
                        'type' => 'suspicious',
                        'name' => 'Suspicious Crawler',
                        'pattern' => $detected_crawler['pattern'],
                        'user_agent' => $this->user_agent,
                        'reason' => 'User-Agent matches but IP does not'
                    ], 'suspicious');
                    return false;
                }
            } else {
                $this->log_crawler_visit($detected_crawler, 'user_agent');
                return true;
            }
        }

        // 检测自定义爬虫模式
        $custom_crawler = $this->detect_custom_crawler();
        if ($custom_crawler) {
            $this->log_crawler_visit($custom_crawler, 'custom');
            return true;
        }

        return false;
    }
    
    /**
     * 检测默认爬虫
     */
    private function detect_default_crawler() {
        $enabled_crawlers = isset($this->settings['enabled_crawlers']) ? $this->settings['enabled_crawlers'] : [];
        
        foreach ($this->default_crawlers as $key => $crawler) {
            // 如果这个爬虫被禁用，跳过
            if (!in_array($key, $enabled_crawlers)) {
                continue;
            }
            
            foreach ($crawler['patterns'] as $pattern) {
                if (stripos($this->user_agent, $pattern) !== false) {
                    return [
                        'type' => $key,
                        'name' => $crawler['name'],
                        'pattern' => $pattern,
                        'user_agent' => $this->user_agent
                    ];
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检测自定义爬虫模式
     */
    private function detect_custom_crawler() {
        $custom_patterns = isset($this->settings['custom_patterns']) ? $this->settings['custom_patterns'] : [];

        foreach ($custom_patterns as $pattern) {
            if (empty($pattern)) {
                continue;
            }

            if (stripos($this->user_agent, $pattern) !== false) {
                return [
                    'type' => 'custom',
                    'name' => 'Custom Crawler',
                    'pattern' => $pattern,
                    'user_agent' => $this->user_agent
                ];
            }
        }

        return false;
    }

    /**
     * 通过IP地址检测爬虫
     */
    private function detect_crawler_by_ip() {
        if (empty($this->client_ip)) {
            return false;
        }

        $enabled_crawlers = isset($this->settings['enabled_crawlers']) ? $this->settings['enabled_crawlers'] : [];
        $all_ip_ranges = WP_Multi_Database_Crawler_IP_Database::get_all_crawler_ip_ranges();

        foreach ($all_ip_ranges as $crawler_type => $ip_ranges) {
            // 如果这个爬虫被禁用，跳过
            if (!in_array($crawler_type, $enabled_crawlers)) {
                continue;
            }

            foreach ($ip_ranges as $ip_range) {
                if ($this->ip_in_range($this->client_ip, $ip_range)) {
                    $crawler_info = isset($this->default_crawlers[$crawler_type]) ?
                        $this->default_crawlers[$crawler_type] :
                        ['name' => ucfirst($crawler_type) . ' Bot', 'description' => 'Detected by IP'];

                    return [
                        'type' => $crawler_type,
                        'name' => $crawler_info['name'],
                        'ip_range' => $ip_range,
                        'client_ip' => $this->client_ip,
                        'user_agent' => $this->user_agent,
                        'detection_method' => 'ip_range'
                    ];
                }
            }
        }

        // 检查自定义IP段
        $custom_ip_ranges = isset($this->settings['custom_ip_ranges']) ? $this->settings['custom_ip_ranges'] : [];
        foreach ($custom_ip_ranges as $ip_range) {
            if ($this->ip_in_range($this->client_ip, $ip_range)) {
                return [
                    'type' => 'custom_ip',
                    'name' => 'Custom IP Crawler',
                    'ip_range' => $ip_range,
                    'client_ip' => $this->client_ip,
                    'user_agent' => $this->user_agent,
                    'detection_method' => 'custom_ip_range'
                ];
            }
        }

        return false;
    }

    /**
     * 验证爬虫IP是否匹配声称的类型
     */
    private function verify_crawler_ip($crawler_type) {
        if (empty($this->client_ip)) {
            return false;
        }

        return WP_Multi_Database_Crawler_IP_Database::is_crawler_ip($this->client_ip, $crawler_type);
    }

    /**
     * 通过反向DNS验证爬虫
     */
    private function verify_crawler_by_reverse_dns($crawler_type) {
        if (empty($this->client_ip)) {
            return false;
        }

        // 反向DNS查询
        $hostname = gethostbyaddr($this->client_ip);

        if ($hostname === $this->client_ip) {
            // 反向DNS查询失败
            return false;
        }

        // 获取有效域名
        $valid_domains = WP_Multi_Database_Crawler_IP_Database::get_crawler_reverse_dns_domains();

        if (!isset($valid_domains[$crawler_type])) {
            return false;
        }

        foreach ($valid_domains[$crawler_type] as $domain) {
            if (strpos($hostname, $domain) !== false) {
                // 进行正向DNS验证
                $forward_ip = gethostbyname($hostname);
                return $forward_ip === $this->client_ip;
            }
        }

        return false;
    }

    /**
     * 检查IP是否在指定范围内
     */
    private function ip_in_range($ip, $range) {
        if (strpos($range, '/') === false) {
            // 单个IP地址
            return $ip === $range;
        }

        list($subnet, $mask) = explode('/', $range);

        // 转换为长整型
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);

        if ($ip_long === false || $subnet_long === false) {
            return false;
        }

        // 计算网络掩码
        $mask_long = -1 << (32 - (int)$mask);

        // 检查IP是否在子网内
        return ($ip_long & $mask_long) === ($subnet_long & $mask_long);
    }
    
    /**
     * 记录爬虫访问日志
     */
    private function log_crawler_visit($crawler_info, $detection_type) {
        // 如果日志记录被禁用，直接返回
        if (empty($this->settings['log_visits'])) {
            return;
        }
        
        global $wpdb;
        
        // 确保日志表存在
        $table_name = $wpdb->prefix . 'multi_db_logs';
        
        $log_data = [
            'timestamp' => current_time('mysql'),
            'level' => 'INFO',
            'message' => sprintf(
                'Crawler detected: %s (Pattern: %s, Type: %s)',
                $crawler_info['name'],
                $crawler_info['pattern'],
                $detection_type
            ),
            'context' => json_encode([
                'crawler_type' => $crawler_info['type'],
                'crawler_name' => $crawler_info['name'],
                'pattern_matched' => $crawler_info['pattern'],
                'user_agent' => $crawler_info['user_agent'],
                'detection_type' => $detection_type,
                'ip_address' => $this->get_client_ip(),
                'request_uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
                'database_switched' => 'default'
            ])
        ];
        
        $wpdb->insert($table_name, $log_data);
        
        // 如果启用了控制台输出，也输出到控制台
        if (defined('YXJTO_GATEWAY_DEBUG') && YXJTO_GATEWAY_DEBUG) {
            $this->output_crawler_detection_info($crawler_info, $detection_type);
        }
    }
    
    /**
     * 输出爬虫检测信息到控制台
     */
    private function output_crawler_detection_info($crawler_info, $detection_type) {
        $settings = WP_Multi_DB_Config_Manager::get_settings();
        
        // 如果控制台输出被禁用，直接返回
        if (!empty($settings['disable_console_output'])) {
            return;
        }
        
        $output = "\n🤖 WordPress Multi Database - Crawler Detected\n";
        $output .= "  ⏰ Timestamp: " . current_time('Y-m-d H:i:s') . "\n";
        $output .= "  🕷️ Crawler: " . $crawler_info['name'] . " (" . $crawler_info['type'] . ")\n";
        $output .= "  🔍 Pattern: " . $crawler_info['pattern'] . "\n";
        $output .= "  📱 User Agent: " . substr($crawler_info['user_agent'], 0, 100) . "...\n";
        $output .= "  🌍 IP Address: " . $this->get_client_ip() . "\n";
        $output .= "  🔗 Request URI: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'N/A') . "\n";
        $output .= "  🗄️ Database: Default (Crawler Override)\n";
        $output .= "  📊 Detection Type: " . ucfirst($detection_type) . "\n";
        
        echo "<!-- " . esc_html($output) . " -->\n";
    }
    
    /**
     * 获取客户端IP地址
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Unknown';
    }
    
    /**
     * 获取支持的爬虫列表
     */
    public function get_supported_crawlers() {
        return $this->default_crawlers;
    }
    
    /**
     * 获取当前User-Agent
     */
    public function get_user_agent() {
        return $this->user_agent;
    }
    
    /**
     * 手动检测指定User-Agent是否为爬虫
     */
    public function detect_user_agent($user_agent) {
        $original_user_agent = $this->user_agent;
        $this->user_agent = $user_agent;

        $result = $this->is_crawler();

        $this->user_agent = $original_user_agent;

        return $result;
    }

    /**
     * 手动检测指定IP是否为爬虫
     */
    public function detect_ip($ip) {
        $original_ip = $this->client_ip;
        $this->client_ip = $ip;

        $result = $this->detect_crawler_by_ip();

        $this->client_ip = $original_ip;

        return $result;
    }

    /**
     * 验证IP和User-Agent的组合
     */
    public function verify_crawler_combination($ip, $user_agent) {
        $original_ip = $this->client_ip;
        $original_user_agent = $this->user_agent;

        $this->client_ip = $ip;
        $this->user_agent = $user_agent;

        $result = $this->is_crawler();

        $this->client_ip = $original_ip;
        $this->user_agent = $original_user_agent;

        return $result;
    }

    /**
     * 获取详细的爬虫检测信息
     */
    public function get_detailed_crawler_info() {
        $info = [
            'client_ip' => $this->client_ip,
            'user_agent' => $this->user_agent,
            'is_crawler' => false,
            'detection_methods' => [],
            'crawler_info' => null,
            'verification_results' => []
        ];

        // IP检测
        if (!empty($this->settings['enable_ip_detection'])) {
            $ip_result = $this->detect_crawler_by_ip();
            if ($ip_result) {
                $info['detection_methods'][] = 'ip_range';
                $info['crawler_info'] = $ip_result;
                $info['is_crawler'] = true;

                // 反向DNS验证
                if (!empty($this->settings['enable_reverse_dns'])) {
                    $dns_result = $this->verify_crawler_by_reverse_dns($ip_result['type']);
                    $info['verification_results']['reverse_dns'] = $dns_result;
                    if (!$dns_result) {
                        $info['is_crawler'] = false;
                    }
                }
            }
        }

        // User-Agent检测
        $ua_result = $this->detect_default_crawler();
        if ($ua_result) {
            $info['detection_methods'][] = 'user_agent';
            if (!$info['crawler_info']) {
                $info['crawler_info'] = $ua_result;
            }

            // IP验证
            if (!empty($this->settings['enable_ip_verification'])) {
                $ip_verification = $this->verify_crawler_ip($ua_result['type']);
                $info['verification_results']['ip_verification'] = $ip_verification;
                if (!$ip_verification && !$info['is_crawler']) {
                    $info['is_crawler'] = false;
                } else if ($ip_verification) {
                    $info['is_crawler'] = true;
                }
            } else if (!$info['is_crawler']) {
                $info['is_crawler'] = true;
            }
        }

        // 自定义模式检测
        $custom_result = $this->detect_custom_crawler();
        if ($custom_result && !$info['is_crawler']) {
            $info['detection_methods'][] = 'custom_pattern';
            $info['crawler_info'] = $custom_result;
            $info['is_crawler'] = true;
        }

        return $info;
    }

    /**
     * 批量验证IP列表
     */
    public function batch_verify_ips($ip_list) {
        $results = [];

        foreach ($ip_list as $ip) {
            $results[$ip] = $this->detect_ip($ip);
        }

        return $results;
    }

    /**
     * 获取爬虫检测统计信息
     */
    public function get_detection_stats() {
        $all_ip_ranges = WP_Multi_Database_Crawler_IP_Database::get_all_crawler_ip_ranges();

        $stats = [
            'total_crawlers' => count($all_ip_ranges),
            'enabled_crawlers' => count($this->settings['enabled_crawlers'] ?? []),
            'total_ip_ranges' => 0,
            'custom_patterns' => count($this->settings['custom_patterns'] ?? []),
            'custom_ip_ranges' => count($this->settings['custom_ip_ranges'] ?? []),
            'features' => [
                'ip_detection' => !empty($this->settings['enable_ip_detection']),
                'ip_verification' => !empty($this->settings['enable_ip_verification']),
                'reverse_dns' => !empty($this->settings['enable_reverse_dns']),
                'logging' => !empty($this->settings['log_visits'])
            ]
        ];

        foreach ($all_ip_ranges as $ranges) {
            $stats['total_ip_ranges'] += count($ranges);
        }

        return $stats;
    }
}
