<?php
/**
 * PayPal Multi Gateway Admin Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Admin {

    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_add_account', array($this, 'ajax_add_account'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_delete_account', array($this, 'ajax_delete_account'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_update_account', array($this, 'ajax_update_account'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_get_account', array($this, 'ajax_get_account'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_get_stats', array($this, 'ajax_get_stats'));

        add_action('wp_ajax_yxjto_paypal_multi_gateway_toggle_account', array($this, 'ajax_toggle_account'));
        add_action('wp_ajax_yxjto_get_log_details', array($this, 'ajax_get_log_details'));
        add_action('wp_ajax_yxjto_delete_log_entry', array($this, 'ajax_delete_log_entry'));
        add_action('wp_ajax_yxjto_export_logs', array($this, 'ajax_export_logs'));
        add_action('wp_ajax_yxjto_bulk_delete_logs', array($this, 'ajax_bulk_delete_logs'));
        add_action('wp_ajax_yxjto_clear_old_logs', array($this, 'ajax_clear_old_logs'));
        add_action('wp_ajax_yxjto_clear_failed_logs', array($this, 'ajax_clear_failed_logs'));
        add_action('wp_ajax_yxjto_clear_logs_by_status', array($this, 'ajax_clear_logs_by_status'));
        add_action('wp_ajax_yxjto_clear_all_logs', array($this, 'ajax_clear_all_logs'));
        add_action('wp_ajax_yxjto_get_maintenance_stats', array($this, 'ajax_get_maintenance_stats'));
        add_action('wp_ajax_yxjto_optimize_database', array($this, 'ajax_optimize_database'));
        add_action('wp_ajax_yxjto_save_maintenance_settings', array($this, 'ajax_save_maintenance_settings'));

        // 配置管理相关的AJAX处理
        add_action('wp_ajax_yxjto_paypal_multi_gateway_migrate_config', array($this, 'ajax_migrate_config'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_verify_config', array($this, 'ajax_verify_config'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_get_config_status', array($this, 'ajax_get_config_status'));
    }

    /**
     * Add admin menu
     * 注意：此方法已被禁用，因为菜单现在由YXJTO主插件管理
     */
    public function add_admin_menu() {
        // 菜单现在由YXJTO主插件在yxjto-gateway.php中管理
        // 不再需要在这里注册菜单
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // 只在我们的管理页面加载资源
        if (strpos($hook, 'yxjto-paypal-multi-gateway') === false) {
            return;
        }

        $plugin_url = plugin_dir_url(dirname(dirname(__FILE__)));
        $version = defined('YXJTO_PAYPAL_MULTI_GATEWAY_VERSION') ? YXJTO_PAYPAL_MULTI_GATEWAY_VERSION : '1.0.0';

        // 加载 CSS
        wp_enqueue_style(
            'yxjto-paypal-multi-gateway-admin-statistics',
            $plugin_url . 'assets/css/admin-statistics.css',
            array(),
            $version
        );

        // 加载 Chart.js
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // 加载主要的管理 JavaScript
        wp_enqueue_script(
            'yxjto-paypal-multi-gateway-admin',
            $plugin_url . 'assets/js/paypal-multi-gateway-admin.js',
            array('jquery'),
            $version,
            true
        );

        // 加载统计页面的 JavaScript
        wp_enqueue_script(
            'yxjto-paypal-multi-gateway-admin-statistics',
            $plugin_url . 'assets/js/admin-statistics.js',
            array('jquery', 'chart-js'),
            $version,
            true
        );

        // 本地化主要管理脚本
        wp_localize_script(
            'yxjto-paypal-multi-gateway-admin',
            'yxjto_paypal_multi_gateway_admin',
            array(
                'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_nonce'),
                'ajax_url' => admin_url('admin-ajax.php'),
                'strings' => array(
                    'confirm_delete' => __('Are you sure you want to delete this account?', 'yxjto-paypal-multi-gateway'),
                    'confirm_enable' => __('Are you sure you want to enable this account?', 'yxjto-paypal-multi-gateway'),
                    'confirm_disable' => __('Are you sure you want to disable this account?', 'yxjto-paypal-multi-gateway'),
                    'select_account' => __('Please select an account.', 'yxjto-paypal-multi-gateway'),
                    'account_saved' => __('Account saved successfully!', 'yxjto-paypal-multi-gateway'),
                    'account_deleted' => __('Account deleted successfully!', 'yxjto-paypal-multi-gateway'),
                    'error_occurred' => __('An error occurred. Please try again.', 'yxjto-paypal-multi-gateway'),
                    'loading' => __('Loading...', 'yxjto-paypal-multi-gateway'),
                    'saving' => __('Saving...', 'yxjto-paypal-multi-gateway'),
                    'save' => __('Save', 'yxjto-paypal-multi-gateway'),
                    'cancel' => __('Cancel', 'yxjto-paypal-multi-gateway'),
                    'edit_account' => __('Edit Account', 'yxjto-paypal-multi-gateway'),
                    'add_new_account' => __('Add New Account', 'yxjto-paypal-multi-gateway'),
                    'invalid_account_id' => __('Invalid account ID', 'yxjto-paypal-multi-gateway'),
                    'get_account_data_failed' => __('Failed to get account data', 'yxjto-paypal-multi-gateway'),
                    'save_account_failed' => __('Failed to save account', 'yxjto-paypal-multi-gateway'),
                    'network_error' => __('Network error occurred', 'yxjto-paypal-multi-gateway'),
                    'operation_failed' => __('Operation failed', 'yxjto-paypal-multi-gateway'),
                    'request_failed' => __('Request failed', 'yxjto-paypal-multi-gateway'),
                    'active_status' => __('Active', 'yxjto-paypal-multi-gateway'),
                    'inactive_status' => __('Inactive', 'yxjto-paypal-multi-gateway')
                )
            )
        );

        // 本地化统计脚本
        wp_localize_script(
            'yxjto-paypal-multi-gateway-admin-statistics',
            'paypalMultiGatewayStats',
            array(
                'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_nonce'),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'confirmDelete' => __('Are you sure you want to delete this log entry?', 'yxjto-paypal-multi-gateway'),
                'confirmBulkDelete' => __('Are you sure you want to delete the selected log entries?', 'yxjto-paypal-multi-gateway'),
                'confirmExportAll' => __('No logs selected. Do you want to export all logs?', 'yxjto-paypal-multi-gateway'),
                'selectAction' => __('Please select an action.', 'yxjto-paypal-multi-gateway'),
                'selectLogs' => __('Please select at least one log entry.', 'yxjto-paypal-multi-gateway'),
                'transactions' => __('Transactions', 'yxjto-paypal-multi-gateway'),
                'amount' => __('Amount', 'yxjto-paypal-multi-gateway'),
                'date' => __('Date', 'yxjto-paypal-multi-gateway'),
                'successRate' => __('Success Rate', 'yxjto-paypal-multi-gateway'),
                'accounts' => __('Accounts', 'yxjto-paypal-multi-gateway'),
                'confirmClearOldLogs' => __('Are you sure you want to clear logs older than %d days? This action cannot be undone.', 'yxjto-paypal-multi-gateway'),
                'confirmClearFailedLogs' => __('Are you sure you want to clear all failed transaction logs? This action cannot be undone.', 'yxjto-paypal-multi-gateway'),
                'confirmClearByStatus' => __('Are you sure you want to clear logs with the selected status? This action cannot be undone.', 'yxjto-paypal-multi-gateway'),
                'confirmClearAllLogs' => __('⚠️ WARNING: This will permanently delete ALL transaction logs. Are you absolutely sure?', 'yxjto-paypal-multi-gateway'),
                'confirmClearAllLogsDouble' => __('This is your final warning! ALL transaction logs will be permanently deleted. Type "DELETE ALL" to confirm this action.', 'yxjto-paypal-multi-gateway'),
                'selectStatuses' => __('Please select at least one status to clear.', 'yxjto-paypal-multi-gateway'),
                'confirmOptimizeDb' => __('Are you sure you want to optimize the database? This may take a few moments.', 'yxjto-paypal-multi-gateway'),
                'optimizeDatabase' => __('Optimize Database', 'yxjto-paypal-multi-gateway')
            )
        );
    }



    /**
     * Admin page content
     */
    public function admin_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'accounts';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <nav class="nav-tab-wrapper">
                <a href="?page=yxjto-paypal-multi-gateway&tab=accounts" class="nav-tab <?php echo $active_tab === 'accounts' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Account Management', 'yxjto-paypal-multi-gateway'); ?>
                </a>
                <a href="?page=yxjto-paypal-multi-gateway&tab=settings" class="nav-tab <?php echo $active_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Plugin Settings', 'yxjto-paypal-multi-gateway'); ?>
                </a>

                <a href="?page=yxjto-paypal-multi-gateway&tab=statistics" class="nav-tab <?php echo $active_tab === 'statistics' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Statistics', 'yxjto-paypal-multi-gateway'); ?>
                </a>
                <a href="?page=yxjto-paypal-multi-gateway&tab=logs" class="nav-tab <?php echo $active_tab === 'logs' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Transaction Logs', 'yxjto-paypal-multi-gateway'); ?>
                </a>
            </nav>

            <div class="tab-content">
                <?php
                switch ($active_tab) {
                    case 'accounts':
                        $this->render_accounts_tab();
                        break;
                    case 'settings':
                        $this->render_settings_tab();
                        break;

                    case 'statistics':
                        $this->render_statistics_tab();
                        break;
                    case 'logs':
                        $this->render_logs_tab();
                        break;
                    default:
                        $this->render_accounts_tab();
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render accounts tab
     */
    private function render_accounts_tab() {
        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $all_accounts = $accounts->get_all_accounts();

        // 一次性获取所有账户的统计数据，避免在循环中重复查询
        $all_stats = $accounts->get_account_stats('');
        $stats_by_account = array();
        foreach ($all_stats as $stat) {
            $stats_by_account[$stat->account_id] = $stat;
        }
        ?>
        <div class="paypal-multi-gateway-accounts">
            <div class="accounts-header">
                <h2><?php _e('PayPal Account Management', 'yxjto-paypal-multi-gateway'); ?></h2>
                <button type="button" class="button button-primary" id="add-account-btn">
                    <?php _e('Add New Account', 'yxjto-paypal-multi-gateway'); ?>
                </button>
            </div>

            <div class="accounts-list">
                <?php if (empty($all_accounts)): ?>
                    <div class="no-accounts">
                        <p><?php _e('No PayPal accounts configured yet.', 'yxjto-paypal-multi-gateway'); ?></p>
                        <p><?php _e('Click "Add New Account" to start configuration.', 'yxjto-paypal-multi-gateway'); ?></p>
                    </div>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Account ID', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Type', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Status', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Weight', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Success Rate', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Last Used', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th><?php _e('Actions', 'yxjto-paypal-multi-gateway'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($all_accounts as $account): ?>
                                <?php
                                // 确保账户对象有必要的属性
                                if (!isset($account->account_id) || !isset($account->account_type) || !isset($account->account_data)) {
                                    continue;
                                }

                                // 从预先获取的统计数据中获取该账户的统计
                                $account_stat = isset($stats_by_account[$account->account_id]) ? $stats_by_account[$account->account_id] : null;

                                $total_transactions = $account_stat ? $account_stat->total_transactions : 0;
                                $success_rate = $account_stat ? $account_stat->success_rate : 0;
                                $account_data = json_decode($account->account_data, true);

                                // 如果json_decode失败，account_data可能已经是数组
                                if ($account_data === null && is_string($account->account_data)) {
                                    $account_data = array();
                                } elseif (!is_array($account_data)) {
                                    $account_data = array();
                                }

                                // 确保必要的属性存在
                                $account_id = $account->account_id ?? '';
                                $account_type = $account->account_type ?? '';
                                $status = $account->status ?? 'active';
                                $weight = $account->weight ?? 100;
                                $last_used = $account->last_used ?? null;
                                ?>
                                <tr data-account-id="<?php echo esc_attr($account_id); ?>">
                                    <td>
                                        <strong><?php echo esc_html($account_id); ?></strong>
                                        <div class="account-details">
                                            <?php
                                            switch ($account_type) {
                                                case 'email':
                                                    echo esc_html($account_data['email'] ?? '');
                                                    break;
                                                case 'paypal_me':
                                                    echo esc_html($account_data['paypal_me_url'] ?? '');
                                                    break;
                                                case 'api':
                                                    $client_id = $account_data['client_id'] ?? '';
                                                    echo esc_html($client_id ? substr($client_id, 0, 20) . '...' : '');
                                                    break;
                                            }
                                            ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="account-type account-type-<?php echo esc_attr($account_type); ?>">
                                            <?php echo esc_html(ucfirst(str_replace('_', ' ', $account_type))); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status status-<?php echo esc_attr($status); ?>">
                                            <?php echo esc_html(ucfirst($status)); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html($weight); ?>%</td>
                                    <td>
                                        <?php echo esc_html(round($success_rate, 1)); ?>%
                                        <small>(<?php echo esc_html($account_stat ? $account_stat->successful_transactions : 0); ?>/<?php echo esc_html($total_transactions); ?>)</small>
                                    </td>
                                    <td>
                                        <?php
                                        if ($last_used) {
                                            echo esc_html(sprintf(__('%s ago', 'yxjto-paypal-multi-gateway'), human_time_diff(strtotime($last_used), current_time('timestamp'))));
                                        } else {
                                            echo esc_html(__('Never', 'yxjto-paypal-multi-gateway'));
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if ($status === 'active'): ?>
                                            <button type="button" class="button button-small button-secondary disable-account-btn" data-account-id="<?php echo esc_attr($account_id); ?>" title="<?php esc_attr_e('Disable this account', 'yxjto-paypal-multi-gateway'); ?>">
                                                <?php _e('Disable', 'yxjto-paypal-multi-gateway'); ?>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="button button-small button-primary enable-account-btn" data-account-id="<?php echo esc_attr($account_id); ?>" title="<?php esc_attr_e('Enable this account', 'yxjto-paypal-multi-gateway'); ?>">
                                                <?php _e('Enable', 'yxjto-paypal-multi-gateway'); ?>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="button button-small edit-account-btn" data-account-id="<?php echo esc_attr($account_id); ?>">
                                            <?php _e('Edit', 'yxjto-paypal-multi-gateway'); ?>
                                        </button>
                                        <button type="button" class="button button-small button-link-delete delete-account-btn" data-account-id="<?php echo esc_attr($account_id); ?>">
                                            <?php _e('Delete', 'yxjto-paypal-multi-gateway'); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <!-- Add/Edit Account Modal -->
        <div id="account-modal" class="paypal-modal" style="display: none;">
            <div class="paypal-modal-content">
                <div class="paypal-modal-header">
                    <h3 id="modal-title"><?php _e('Add New Account', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <button type="button" class="paypal-modal-close">&times;</button>
                </div>
                <div class="paypal-modal-body">
                    <form id="account-form">
                        <input type="hidden" id="account-id" name="account_id" value="">

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="account-type"><?php _e('Account Type', 'yxjto-paypal-multi-gateway'); ?></label>
                                </th>
                                <td>
                                    <select id="account-type" name="account_type" required>
                                        <option value=""><?php _e('Select Account Type', 'yxjto-paypal-multi-gateway'); ?></option>
                                        <option value="email"><?php _e('Email Configuration', 'yxjto-paypal-multi-gateway'); ?></option>
                                        <option value="paypal_me"><?php _e('PayPal.me Link', 'yxjto-paypal-multi-gateway'); ?></option>
                                        <option value="api"><?php _e('API Integration', 'yxjto-paypal-multi-gateway'); ?></option>
                                    </select>
                                </td>
                            </tr>
                        </table>

                        <!-- Email Configuration Fields -->
                        <div id="email-fields" class="account-type-fields" style="display: none;">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="paypal-email"><?php _e('PayPal Email', 'yxjto-paypal-multi-gateway'); ?></label>
                                    </th>
                                    <td>
                                        <input type="email" id="paypal-email" name="paypal_email" class="regular-text">
                                        <p class="description"><?php _e('Enter your PayPal account email address.', 'yxjto-paypal-multi-gateway'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- PayPal.me Configuration Fields -->
                        <div id="paypal-me-fields" class="account-type-fields" style="display: none;">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="paypal-me-url"><?php _e('PayPal.me URL', 'yxjto-paypal-multi-gateway'); ?></label>
                                    </th>
                                    <td>
                                        <input type="url" id="paypal-me-url" name="paypal_me_url" class="regular-text" placeholder="https://paypal.me/yourusername">
                                        <p class="description"><?php _e('Enter your PayPal.me link (e.g., https://paypal.me/yourusername).', 'yxjto-paypal-multi-gateway'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- API Configuration Fields -->
                        <div id="api-fields" class="account-type-fields" style="display: none;">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="client-id"><?php _e('Client ID', 'yxjto-paypal-multi-gateway'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" id="client-id" name="client_id" class="regular-text">
                                        <p class="description"><?php _e('Enter your PayPal application client ID.', 'yxjto-paypal-multi-gateway'); ?></p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="client-secret"><?php _e('Client Secret', 'yxjto-paypal-multi-gateway'); ?></label>
                                    </th>
                                    <td>
                                        <input type="password" id="client-secret" name="client_secret" class="regular-text">
                                        <p class="description"><?php _e('Enter your PayPal application client secret.', 'yxjto-paypal-multi-gateway'); ?></p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="sandbox-mode"><?php _e('Sandbox Mode', 'yxjto-paypal-multi-gateway'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" id="sandbox-mode" name="sandbox_mode" value="1">
                                            <?php _e('Use PayPal Sandbox (for testing)', 'yxjto-paypal-multi-gateway'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Common Fields -->
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="account-weight"><?php _e('Weight (%)', 'yxjto-paypal-multi-gateway'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="account-weight" name="weight" min="1" max="100" value="100" class="small-text">
                                    <p class="description"><?php _e('Load balancing weight (1-100). Higher values get more traffic.', 'yxjto-paypal-multi-gateway'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="paypal-modal-footer">
                    <button type="button" class="button button-secondary" id="cancel-account-btn"><?php _e('Cancel', 'yxjto-paypal-multi-gateway'); ?></button>
                    <button type="button" class="button button-primary" id="save-account-btn"><?php _e('Save Account', 'yxjto-paypal-multi-gateway'); ?></button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render settings tab
     */
    private function render_settings_tab() {
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();

        if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'yxjto_paypal_multi_gateway_settings')) {
            $new_settings = array(
                'load_balancing' => sanitize_text_field($_POST['load_balancing']),
                'retry_count' => intval($_POST['retry_count']),
                'timeout' => intval($_POST['timeout']),
                'test_mode' => isset($_POST['test_mode']) ? 'yes' : 'no'
            );

            YXJTO_PayPal_Multi_Gateway_Core::update_settings($new_settings);
            echo '<div class="notice notice-success"><p>' . esc_html__('Settings saved successfully!', 'yxjto-paypal-multi-gateway') . '</p></div>';
            $settings = $new_settings;
        }
        ?>
        <div class="paypal-multi-gateway-settings">
            <h2><?php _e('Plugin Settings', 'yxjto-paypal-multi-gateway'); ?></h2>

            <form method="post" action="">
                <?php wp_nonce_field('yxjto_paypal_multi_gateway_settings'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="load_balancing"><?php _e('Load Balancing Method', 'yxjto-paypal-multi-gateway'); ?></label>
                        </th>
                        <td>
                            <select id="load_balancing" name="load_balancing">
                                <option value="random" <?php selected($settings['load_balancing'], 'random'); ?>><?php _e('Random Selection', 'yxjto-paypal-multi-gateway'); ?></option>
                                <option value="round_robin" <?php selected($settings['load_balancing'], 'round_robin'); ?>><?php _e('Round Robin', 'yxjto-paypal-multi-gateway'); ?></option>
                                <option value="weighted" <?php selected($settings['load_balancing'], 'weighted'); ?>><?php _e('Weighted Distribution', 'yxjto-paypal-multi-gateway'); ?></option>
                                <option value="smart" <?php selected($settings['load_balancing'], 'smart'); ?>><?php _e('Smart Selection (Performance Based)', 'yxjto-paypal-multi-gateway'); ?></option>
                            </select>
                            <p class="description"><?php _e('Choose how to distribute payments among multiple PayPal accounts.', 'yxjto-paypal-multi-gateway'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="retry_count"><?php _e('Retry Count', 'yxjto-paypal-multi-gateway'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="retry_count" name="retry_count" min="1" max="10" value="<?php echo esc_attr($settings['retry_count']); ?>" class="small-text">
                            <p class="description"><?php _e('Number of times to retry failed payments with different accounts.', 'yxjto-paypal-multi-gateway'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="timeout"><?php _e('Request Timeout (seconds)', 'yxjto-paypal-multi-gateway'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="timeout" name="timeout" min="10" max="120" value="<?php echo esc_attr($settings['timeout']); ?>" class="small-text">
                            <p class="description"><?php _e('PayPal API request timeout duration.', 'yxjto-paypal-multi-gateway'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Test Mode', 'yxjto-paypal-multi-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="test_mode" value="yes" <?php checked($settings['test_mode'], 'yes'); ?>>
                                <?php _e('Enable Test Mode', 'yxjto-paypal-multi-gateway'); ?>
                            </label>
                            <p class="description"><?php _e('Use PayPal Sandbox for testing.', 'yxjto-paypal-multi-gateway'); ?></p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="save_settings" class="button-primary" value="<?php esc_attr_e('Save Settings', 'yxjto-paypal-multi-gateway'); ?>">
                </p>
            </form>
        </div>
        <?php
    }

    /**
     * Render statistics tab
     */
    private function render_statistics_tab() {
        global $wpdb;

        // 获取时间范围参数
        $time_range = isset($_GET['time_range']) ? sanitize_text_field($_GET['time_range']) : '30';
        $start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : '';
        $end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : '';

        // 构建时间条件
        $date_condition = $this->build_date_condition($time_range, $start_date, $end_date);

        // 获取统计数据
        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $stats = $accounts->get_account_stats($date_condition);
        $overview_stats = $this->get_overview_statistics($date_condition);
        $daily_stats = $this->get_daily_statistics($date_condition);

        ?>
        <div class="paypal-multi-gateway-statistics">
            <!-- 页面标题和操作区域 -->
            <div class="statistics-page-header">
                <div class="page-title-section">
                    <h2 class="page-title">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php _e('PayPal Multi Gateway Statistics', 'yxjto-paypal-multi-gateway'); ?>
                    </h2>
                    <p class="page-description"><?php _e('Monitor your PayPal transactions performance and analytics', 'yxjto-paypal-multi-gateway'); ?></p>
                </div>

                <div class="page-actions">
                    <button type="button" class="button button-secondary" onclick="refreshStatistics()" title="<?php _e('Refresh Data', 'yxjto-paypal-multi-gateway'); ?>">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Refresh', 'yxjto-paypal-multi-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="exportStatistics()" title="<?php _e('Export Statistics', 'yxjto-paypal-multi-gateway'); ?>">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Export', 'yxjto-paypal-multi-gateway'); ?>
                    </button>
                </div>
            </div>

            <!-- 筛选控制面板 -->
            <div class="statistics-filter-panel">
                <div class="filter-panel-header">
                    <h3 class="filter-title">
                        <span class="dashicons dashicons-filter"></span>
                        <?php _e('Filter Options', 'yxjto-paypal-multi-gateway'); ?>
                    </h3>
                    <button type="button" class="filter-toggle-btn" onclick="toggleFilterPanel()">
                        <span class="dashicons dashicons-arrow-up-alt2"></span>
                    </button>
                </div>

                <div class="filter-panel-content">
                    <form method="get" class="statistics-filter-form">
                        <input type="hidden" name="page" value="yxjto-paypal-multi-gateway">
                        <input type="hidden" name="tab" value="statistics">

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="time_range" class="filter-label">
                                    <span class="dashicons dashicons-calendar-alt"></span>
                                    <?php _e('Time Range', 'yxjto-paypal-multi-gateway'); ?>
                                </label>
                                <select name="time_range" id="time_range" class="filter-select" onchange="toggleCustomDateRange(this.value)">
                                    <option value="7" <?php selected($time_range, '7'); ?>><?php _e('Last 7 days', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <option value="30" <?php selected($time_range, '30'); ?>><?php _e('Last 30 days', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <option value="90" <?php selected($time_range, '90'); ?>><?php _e('Last 90 days', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <option value="365" <?php selected($time_range, '365'); ?>><?php _e('Last year', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <option value="custom" <?php selected($time_range, 'custom'); ?>><?php _e('Custom range', 'yxjto-paypal-multi-gateway'); ?></option>
                                </select>
                            </div>

                            <div class="filter-group custom-date-range" id="custom-date-range" style="<?php echo $time_range === 'custom' ? '' : 'display:none;'; ?>">
                                <div class="date-range-inputs">
                                    <div class="date-input-wrapper">
                                        <label for="start_date" class="date-label"><?php _e('From', 'yxjto-paypal-multi-gateway'); ?></label>
                                        <input type="date" name="start_date" id="start_date" class="date-input" value="<?php echo esc_attr($start_date); ?>">
                                    </div>
                                    <div class="date-separator">
                                        <span class="dashicons dashicons-arrow-right-alt"></span>
                                    </div>
                                    <div class="date-input-wrapper">
                                        <label for="end_date" class="date-label"><?php _e('To', 'yxjto-paypal-multi-gateway'); ?></label>
                                        <input type="date" name="end_date" id="end_date" class="date-input" value="<?php echo esc_attr($end_date); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="button button-primary filter-apply-btn">
                                <span class="dashicons dashicons-search"></span>
                                <?php _e('Apply Filter', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                            <a href="?page=yxjto-paypal-multi-gateway&tab=statistics" class="button button-secondary filter-clear-btn">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Clear Filter', 'yxjto-paypal-multi-gateway'); ?>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 概览统计卡片 -->
            <div class="statistics-overview">
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo wc_price($overview_stats['total_revenue']); ?></div>
                        <div class="stat-label"><?php _e('Total Revenue', 'yxjto-paypal-multi-gateway'); ?></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo number_format($overview_stats['total_transactions']); ?></div>
                        <div class="stat-label"><?php _e('Total Transactions', 'yxjto-paypal-multi-gateway'); ?></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo round($overview_stats['success_rate'], 1); ?>%</div>
                        <div class="stat-label"><?php _e('Success Rate', 'yxjto-paypal-multi-gateway'); ?></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💳</div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo wc_price($overview_stats['avg_transaction']); ?></div>
                        <div class="stat-label"><?php _e('Avg Transaction', 'yxjto-paypal-multi-gateway'); ?></div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="statistics-charts">
                <div class="chart-container">
                    <h3><?php _e('Daily Transaction Volume', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <canvas id="dailyTransactionChart" width="400" height="200"></canvas>
                </div>

                <div class="chart-container">
                    <h3><?php _e('Account Performance', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <canvas id="accountPerformanceChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- 账户详细统计表格 -->
            <div class="statistics-table">
                <h3><?php _e('Account Performance Details', 'yxjto-paypal-multi-gateway'); ?></h3>

                <?php if (empty($stats)): ?>
                    <div class="no-data-message">
                        <p><?php _e('No transaction data available for the selected time period.', 'yxjto-paypal-multi-gateway'); ?></p>
                    </div>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="account_id"><?php _e('Account ID', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="account_type"><?php _e('Type', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="total_transactions"><?php _e('Total Transactions', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="successful_transactions"><?php _e('Successful', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="failed_transactions"><?php _e('Failed', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="success_rate"><?php _e('Success Rate', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="total_amount"><?php _e('Total Amount', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th class="sortable" data-sort="avg_amount"><?php _e('Avg Amount', 'yxjto-paypal-multi-gateway'); ?> <span class="sort-indicator"></span></th>
                                <th><?php _e('Actions', 'yxjto-paypal-multi-gateway'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stats as $stat): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($stat->account_id); ?></strong>
                                        <div class="account-status status-<?php echo esc_attr($stat->status ?? 'active'); ?>">
                                            <?php echo esc_html(ucfirst($stat->status ?? 'active')); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="account-type type-<?php echo esc_attr($stat->account_type); ?>">
                                            <?php echo esc_html(ucfirst(str_replace('_', ' ', $stat->account_type))); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($stat->total_transactions); ?></td>
                                    <td><span class="success-count"><?php echo number_format($stat->successful_transactions); ?></span></td>
                                    <td><span class="failed-count"><?php echo number_format($stat->failed_transactions); ?></span></td>
                                    <td>
                                        <div class="success-rate-bar">
                                            <div class="rate-fill" style="width: <?php echo esc_attr($stat->success_rate); ?>%"></div>
                                            <span class="rate-text"><?php echo round($stat->success_rate, 1); ?>%</span>
                                        </div>
                                    </td>
                                    <td><?php echo wc_price($stat->total_amount); ?></td>
                                    <td><?php echo wc_price($stat->avg_amount); ?></td>
                                    <td>
                                        <button type="button" class="button button-small view-details" data-account-id="<?php echo esc_attr($stat->account_id); ?>">
                                            <?php _e('View Details', 'yxjto-paypal-multi-gateway'); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <script type="text/javascript">
            // 传递数据给 JavaScript
            window.paypalStatisticsData = {
                dailyStats: <?php echo wp_json_encode($daily_stats); ?>,
                accountStats: <?php echo wp_json_encode($stats); ?>,
                timeRange: '<?php echo esc_js($time_range); ?>'
            };
        </script>
        <?php
    }

    /**
     * Render logs tab
     */
    private function render_logs_tab() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 获取筛选参数
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        $status_filter = isset($_GET['status_filter']) ? sanitize_text_field($_GET['status_filter']) : '';
        $account_filter = isset($_GET['account_filter']) ? sanitize_text_field($_GET['account_filter']) : '';
        $date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
        $date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;

        // 构建查询条件
        $where_conditions = array('1=1');
        $query_params = array();

        if (!empty($search)) {
            $where_conditions[] = "(transaction_id LIKE %s OR order_id LIKE %s)";
            $query_params[] = '%' . $search . '%';
            $query_params[] = '%' . $search . '%';
        }

        if (!empty($status_filter)) {
            $where_conditions[] = "status = %s";
            $query_params[] = $status_filter;
        }

        if (!empty($account_filter)) {
            $where_conditions[] = "account_id = %s";
            $query_params[] = $account_filter;
        }

        if (!empty($date_from)) {
            $where_conditions[] = "DATE(created_at) >= %s";
            $query_params[] = $date_from;
        }

        if (!empty($date_to)) {
            $where_conditions[] = "DATE(created_at) <= %s";
            $query_params[] = $date_to;
        }

        $where_clause = implode(' AND ', $where_conditions);

        // 获取总数
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where_clause}";
        if (!empty($query_params)) {
            $total_logs = $wpdb->get_var($wpdb->prepare($count_query, $query_params));
        } else {
            $total_logs = $wpdb->get_var($count_query);
        }

        // 获取日志数据
        $logs_query = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY created_at DESC LIMIT %d OFFSET %d";
        $final_params = array_merge($query_params, array($per_page, $offset));
        $logs = $wpdb->get_results($wpdb->prepare($logs_query, $final_params));

        $total_pages = ceil($total_logs / $per_page);

        // 获取可用的账户和状态选项
        $available_accounts = $wpdb->get_col("SELECT DISTINCT account_id FROM {$table_name} ORDER BY account_id");
        $available_statuses = $wpdb->get_col("SELECT DISTINCT status FROM {$table_name} ORDER BY status");
        ?>
        <div class="paypal-multi-gateway-logs">
            <div class="logs-header">
                <h2><?php _e('Transaction Logs', 'yxjto-paypal-multi-gateway'); ?></h2>

                <!-- 筛选和搜索表单 -->
                <div class="logs-filters">
                    <form method="get" class="logs-filter-form">
                        <input type="hidden" name="page" value="yxjto-paypal-multi-gateway">
                        <input type="hidden" name="tab" value="logs">

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="search"><?php _e('Search:', 'yxjto-paypal-multi-gateway'); ?></label>
                                <input type="text" name="search" id="search" value="<?php echo esc_attr($search); ?>"
                                       placeholder="<?php _e('Transaction ID or Order ID', 'yxjto-paypal-multi-gateway'); ?>">
                            </div>

                            <div class="filter-group">
                                <label for="status_filter"><?php _e('Status:', 'yxjto-paypal-multi-gateway'); ?></label>
                                <select name="status_filter" id="status_filter">
                                    <option value=""><?php _e('All Statuses', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <?php foreach ($available_statuses as $status): ?>
                                        <option value="<?php echo esc_attr($status); ?>" <?php selected($status_filter, $status); ?>>
                                            <?php echo esc_html(ucfirst($status)); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="account_filter"><?php _e('Account:', 'yxjto-paypal-multi-gateway'); ?></label>
                                <select name="account_filter" id="account_filter">
                                    <option value=""><?php _e('All Accounts', 'yxjto-paypal-multi-gateway'); ?></option>
                                    <?php foreach ($available_accounts as $account): ?>
                                        <option value="<?php echo esc_attr($account); ?>" <?php selected($account_filter, $account); ?>>
                                            <?php echo esc_html($account); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="date_from"><?php _e('From Date:', 'yxjto-paypal-multi-gateway'); ?></label>
                                <input type="date" name="date_from" id="date_from" value="<?php echo esc_attr($date_from); ?>">
                            </div>

                            <div class="filter-group">
                                <label for="date_to"><?php _e('To Date:', 'yxjto-paypal-multi-gateway'); ?></label>
                                <input type="date" name="date_to" id="date_to" value="<?php echo esc_attr($date_to); ?>">
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="button button-primary"><?php _e('Filter', 'yxjto-paypal-multi-gateway'); ?></button>
                                <a href="?page=yxjto-paypal-multi-gateway&tab=logs" class="button"><?php _e('Clear Filters', 'yxjto-paypal-multi-gateway'); ?></a>
                                <button type="button" class="button" onclick="exportLogs()"><?php _e('Export CSV', 'yxjto-paypal-multi-gateway'); ?></button>
                                <button type="button" class="button" onclick="refreshLogs()"><?php _e('Refresh', 'yxjto-paypal-multi-gateway'); ?></button>

                                <!-- 清理日志选项 -->
                                <div class="clear-logs-dropdown">
                                    <button type="button" class="button button-secondary clear-logs-toggle" onclick="toggleClearLogsMenu()">
                                        <?php _e('Clear Logs', 'yxjto-paypal-multi-gateway'); ?> ▼
                                    </button>
                                    <div class="clear-logs-menu" id="clear-logs-menu" style="display: none;">
                                        <div class="clear-logs-options">
                                            <h4><?php _e('Clear Transaction Logs', 'yxjto-paypal-multi-gateway'); ?></h4>

                                            <div class="clear-option">
                                                <button type="button" class="button clear-old-logs" onclick="clearOldLogs(30)">
                                                    <?php _e('Clear logs older than 30 days', 'yxjto-paypal-multi-gateway'); ?>
                                                </button>
                                                <p class="description"><?php _e('Remove logs older than 30 days to free up space.', 'yxjto-paypal-multi-gateway'); ?></p>
                                            </div>

                                            <div class="clear-option">
                                                <button type="button" class="button clear-old-logs" onclick="clearOldLogs(90)">
                                                    <?php _e('Clear logs older than 90 days', 'yxjto-paypal-multi-gateway'); ?>
                                                </button>
                                                <p class="description"><?php _e('Remove logs older than 90 days.', 'yxjto-paypal-multi-gateway'); ?></p>
                                            </div>

                                            <div class="clear-option">
                                                <button type="button" class="button clear-failed-logs" onclick="clearFailedLogs()">
                                                    <?php _e('Clear failed transactions only', 'yxjto-paypal-multi-gateway'); ?>
                                                </button>
                                                <p class="description"><?php _e('Remove only failed and error transactions.', 'yxjto-paypal-multi-gateway'); ?></p>
                                            </div>

                                            <div class="clear-option">
                                                <button type="button" class="button clear-by-status" onclick="showClearByStatusDialog()">
                                                    <?php _e('Clear by status', 'yxjto-paypal-multi-gateway'); ?>
                                                </button>
                                                <p class="description"><?php _e('Remove logs with specific status.', 'yxjto-paypal-multi-gateway'); ?></p>
                                            </div>

                                            <div class="clear-option danger-zone">
                                                <hr>
                                                <h5 class="danger-title"><?php _e('Danger Zone', 'yxjto-paypal-multi-gateway'); ?></h5>
                                                <button type="button" class="button button-delete clear-all-logs" onclick="clearAllLogs()">
                                                    <?php _e('Clear ALL logs', 'yxjto-paypal-multi-gateway'); ?>
                                                </button>
                                                <p class="description danger-text"><?php _e('⚠️ This will permanently delete ALL transaction logs. This action cannot be undone!', 'yxjto-paypal-multi-gateway'); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 统计摘要 -->
            <div class="logs-summary">
                <div class="summary-item">
                    <span class="summary-label"><?php _e('Total Records:', 'yxjto-paypal-multi-gateway'); ?></span>
                    <span class="summary-value"><?php echo number_format($total_logs); ?></span>
                </div>
                <?php if (!empty($query_params)): ?>
                    <div class="summary-item">
                        <span class="summary-label"><?php _e('Filtered Results:', 'yxjto-paypal-multi-gateway'); ?></span>
                        <span class="summary-value"><?php echo number_format(count($logs)); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            <!-- 日志表格 -->
            <?php if (empty($logs)): ?>
                <div class="no-logs-message">
                    <p><?php _e('No transaction logs found matching your criteria.', 'yxjto-paypal-multi-gateway'); ?></p>
                </div>
            <?php else: ?>
                <div class="logs-table-container">
                    <table class="wp-list-table widefat fixed striped logs-table">
                        <thead>
                            <tr>
                                <th class="column-checkbox">
                                    <input type="checkbox" id="select-all-logs">
                                </th>
                                <th class="column-transaction-id sortable" data-sort="transaction_id">
                                    <?php _e('Transaction ID', 'yxjto-paypal-multi-gateway'); ?>
                                    <span class="sort-indicator"></span>
                                </th>
                                <th class="column-order-id sortable" data-sort="order_id">
                                    <?php _e('Order ID', 'yxjto-paypal-multi-gateway'); ?>
                                    <span class="sort-indicator"></span>
                                </th>
                                <th class="column-account-id"><?php _e('Account ID', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th class="column-amount sortable" data-sort="amount">
                                    <?php _e('Amount', 'yxjto-paypal-multi-gateway'); ?>
                                    <span class="sort-indicator"></span>
                                </th>
                                <th class="column-status"><?php _e('Status', 'yxjto-paypal-multi-gateway'); ?></th>
                                <th class="column-date sortable" data-sort="created_at">
                                    <?php _e('Date', 'yxjto-paypal-multi-gateway'); ?>
                                    <span class="sort-indicator"></span>
                                </th>
                                <th class="column-actions"><?php _e('Actions', 'yxjto-paypal-multi-gateway'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                                <tr data-log-id="<?php echo esc_attr($log->id); ?>">
                                    <td class="column-checkbox">
                                        <input type="checkbox" name="log_ids[]" value="<?php echo esc_attr($log->id); ?>">
                                    </td>
                                    <td class="column-transaction-id">
                                        <strong><?php echo esc_html($log->transaction_id); ?></strong>
                                        <?php if (strlen($log->transaction_id) > 20): ?>
                                            <div class="transaction-id-full" style="display:none;"><?php echo esc_html($log->transaction_id); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="column-order-id">
                                        <?php if ($log->order_id): ?>
                                            <a href="<?php echo esc_url(admin_url('post.php?post=' . $log->order_id . '&action=edit')); ?>" target="_blank">
                                                #<?php echo esc_html($log->order_id); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="no-order">—</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="column-account-id">
                                        <span class="account-badge account-<?php echo esc_attr(explode('_', $log->account_id)[0]); ?>">
                                            <?php echo esc_html($log->account_id); ?>
                                        </span>
                                    </td>
                                    <td class="column-amount">
                                        <strong><?php echo wc_price($log->amount); ?></strong>
                                        <div class="currency-info"><?php echo esc_html($log->currency); ?></div>
                                    </td>
                                    <td class="column-status">
                                        <span class="status-badge status-<?php echo esc_attr($log->status); ?>">
                                            <span class="status-indicator"></span>
                                            <?php echo esc_html(ucfirst($log->status)); ?>
                                        </span>
                                    </td>
                                    <td class="column-date">
                                        <div class="date-display">
                                            <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($log->created_at))); ?>
                                        </div>
                                        <div class="relative-time" title="<?php echo esc_attr($log->created_at); ?>">
                                            <?php echo esc_html(human_time_diff(strtotime($log->created_at), current_time('timestamp'))); ?> <?php _e('ago', 'yxjto-paypal-multi-gateway'); ?>
                                        </div>
                                    </td>
                                    <td class="column-actions">
                                        <div class="action-buttons">
                                            <button type="button" class="button button-small view-log-details"
                                                    data-log-id="<?php echo esc_attr($log->id); ?>"
                                                    title="<?php _e('View Details', 'yxjto-paypal-multi-gateway'); ?>">
                                                <span class="dashicons dashicons-visibility"></span>
                                            </button>
                                            <?php if ($log->order_id): ?>
                                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $log->order_id . '&action=edit')); ?>"
                                                   class="button button-small" target="_blank"
                                                   title="<?php _e('View Order', 'yxjto-paypal-multi-gateway'); ?>">
                                                    <span class="dashicons dashicons-external"></span>
                                                </a>
                                            <?php endif; ?>
                                            <button type="button" class="button button-small delete-log"
                                                    data-log-id="<?php echo esc_attr($log->id); ?>"
                                                    title="<?php _e('Delete Log', 'yxjto-paypal-multi-gateway'); ?>">
                                                <span class="dashicons dashicons-trash"></span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 批量操作 -->
                <div class="bulk-actions-container">
                    <div class="bulk-actions">
                        <select name="bulk_action" id="bulk-action">
                            <option value=""><?php _e('Bulk Actions', 'yxjto-paypal-multi-gateway'); ?></option>
                            <option value="delete"><?php _e('Delete Selected', 'yxjto-paypal-multi-gateway'); ?></option>
                            <option value="export"><?php _e('Export Selected', 'yxjto-paypal-multi-gateway'); ?></option>
                        </select>
                        <button type="button" class="button" onclick="applyBulkAction()"><?php _e('Apply', 'yxjto-paypal-multi-gateway'); ?></button>
                    </div>

                    <div class="logs-info">
                        <span class="logs-count">
                            <?php printf(__('Total logs in database: %s', 'yxjto-paypal-multi-gateway'), '<strong id="total-logs-count">' . number_format($total_logs) . '</strong>'); ?>
                        </span>
                        <button type="button" class="button button-small" onclick="showLogMaintenanceDialog()" title="<?php _e('Log Maintenance', 'yxjto-paypal-multi-gateway'); ?>">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Maintenance', 'yxjto-paypal-multi-gateway'); ?>
                        </button>
                    </div>
                </div>

                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                    <div class="tablenav bottom">
                        <div class="tablenav-pages">
                            <span class="displaying-num">
                                <?php printf(_n('%s item', '%s items', $total_logs, 'yxjto-paypal-multi-gateway'), number_format_i18n($total_logs)); ?>
                            </span>
                            <?php
                            echo paginate_links(array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo; Previous'),
                                'next_text' => __('Next &raquo;'),
                                'total' => $total_pages,
                                'current' => $current_page,
                                'show_all' => false,
                                'end_size' => 1,
                                'mid_size' => 2,
                                'type' => 'plain',
                                'add_args' => array(
                                    'search' => $search,
                                    'status_filter' => $status_filter,
                                    'account_filter' => $account_filter,
                                    'date_from' => $date_from,
                                    'date_to' => $date_to
                                )
                            ));
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- 日志详情模态框 -->
        <div id="log-details-modal" class="log-modal" style="display: none;">
            <div class="log-modal-content">
                <div class="log-modal-header">
                    <h3><?php _e('Transaction Log Details', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <button type="button" class="log-modal-close">&times;</button>
                </div>
                <div class="log-modal-body">
                    <div id="log-details-content">
                        <!-- 内容将通过 AJAX 加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 按状态清理对话框 -->
        <div id="clear-by-status-modal" class="log-modal" style="display: none;">
            <div class="log-modal-content">
                <div class="log-modal-header">
                    <h3><?php _e('Clear Logs by Status', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <button type="button" class="log-modal-close">&times;</button>
                </div>
                <div class="log-modal-body">
                    <div class="clear-by-status-form">
                        <p><?php _e('Select the status of logs you want to clear:', 'yxjto-paypal-multi-gateway'); ?></p>

                        <div class="status-checkboxes">
                            <?php foreach ($available_statuses as $status): ?>
                                <label class="status-checkbox">
                                    <input type="checkbox" name="clear_statuses[]" value="<?php echo esc_attr($status); ?>">
                                    <span class="status-badge status-<?php echo esc_attr($status); ?>">
                                        <?php echo esc_html(ucfirst($status)); ?>
                                    </span>
                                </label>
                            <?php endforeach; ?>
                        </div>

                        <div class="date-range-option">
                            <label>
                                <input type="checkbox" id="include-date-range" onchange="toggleDateRangeForClear()">
                                <?php _e('Also filter by date range', 'yxjto-paypal-multi-gateway'); ?>
                            </label>

                            <div class="date-range-inputs" id="clear-date-range" style="display: none;">
                                <div class="date-input-group">
                                    <label><?php _e('From:', 'yxjto-paypal-multi-gateway'); ?></label>
                                    <input type="date" id="clear-date-from">
                                </div>
                                <div class="date-input-group">
                                    <label><?php _e('To:', 'yxjto-paypal-multi-gateway'); ?></label>
                                    <input type="date" id="clear-date-to">
                                </div>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" class="button button-primary" onclick="executeClearByStatus()">
                                <?php _e('Clear Selected Logs', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                            <button type="button" class="button" onclick="closeClearByStatusModal()">
                                <?php _e('Cancel', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志维护对话框 -->
        <div id="log-maintenance-modal" class="log-modal" style="display: none;">
            <div class="log-modal-content">
                <div class="log-modal-header">
                    <h3><?php _e('Log Maintenance', 'yxjto-paypal-multi-gateway'); ?></h3>
                    <button type="button" class="log-modal-close">&times;</button>
                </div>
                <div class="log-modal-body">
                    <div class="maintenance-options">
                        <div class="maintenance-section">
                            <h4><?php _e('Database Statistics', 'yxjto-paypal-multi-gateway'); ?></h4>
                            <div class="db-stats">
                                <div class="stat-item">
                                    <span class="stat-label"><?php _e('Total Logs:', 'yxjto-paypal-multi-gateway'); ?></span>
                                    <span class="stat-value" id="maintenance-total-logs">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><?php _e('Database Size:', 'yxjto-paypal-multi-gateway'); ?></span>
                                    <span class="stat-value" id="maintenance-db-size">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><?php _e('Oldest Log:', 'yxjto-paypal-multi-gateway'); ?></span>
                                    <span class="stat-value" id="maintenance-oldest-log">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="maintenance-section">
                            <h4><?php _e('Automatic Cleanup', 'yxjto-paypal-multi-gateway'); ?></h4>
                            <div class="auto-cleanup-options">
                                <label>
                                    <input type="checkbox" id="enable-auto-cleanup">
                                    <?php _e('Enable automatic log cleanup', 'yxjto-paypal-multi-gateway'); ?>
                                </label>

                                <div class="cleanup-settings" id="cleanup-settings" style="display: none;">
                                    <div class="setting-group">
                                        <label><?php _e('Keep logs for:', 'yxjto-paypal-multi-gateway'); ?></label>
                                        <select id="auto-cleanup-days">
                                            <option value="30">30 <?php _e('days', 'yxjto-paypal-multi-gateway'); ?></option>
                                            <option value="60">60 <?php _e('days', 'yxjto-paypal-multi-gateway'); ?></option>
                                            <option value="90" selected>90 <?php _e('days', 'yxjto-paypal-multi-gateway'); ?></option>
                                            <option value="180">180 <?php _e('days', 'yxjto-paypal-multi-gateway'); ?></option>
                                            <option value="365">365 <?php _e('days', 'yxjto-paypal-multi-gateway'); ?></option>
                                        </select>
                                    </div>

                                    <div class="setting-group">
                                        <label>
                                            <input type="checkbox" id="keep-successful-logs">
                                            <?php _e('Always keep successful transactions', 'yxjto-paypal-multi-gateway'); ?>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" class="button button-primary" onclick="saveMaintenanceSettings()">
                                <?php _e('Save Settings', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                            <button type="button" class="button" onclick="optimizeDatabase()">
                                <?php _e('Optimize Database', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                            <button type="button" class="button" onclick="closeMaintenanceModal()">
                                <?php _e('Close', 'yxjto-paypal-multi-gateway'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }



    /**
     * AJAX: Add account
     */
    public function ajax_add_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_type = sanitize_text_field($_POST['account_type']);
        $weight = intval($_POST['weight']);

        $account_data = array();

        switch ($account_type) {
            case 'email':
                $account_data['email'] = sanitize_email($_POST['paypal_email']);
                break;
            case 'paypal_me':
                $account_data['paypal_me_url'] = esc_url_raw($_POST['paypal_me_url']);
                break;
            case 'api':
                $account_data['client_id'] = sanitize_text_field($_POST['client_id']);
                $account_data['client_secret'] = sanitize_text_field($_POST['client_secret']);
                $account_data['sandbox'] = isset($_POST['sandbox_mode']) && $_POST['sandbox_mode'] === '1';
                break;
            default:
                wp_send_json_error(__('Invalid account type.', 'yxjto-paypal-multi-gateway'));
        }

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $account_id = $accounts->add_account($account_type, $account_data, $weight);

        if ($account_id) {
            wp_send_json_success(array(
                'message' => __('Account added successfully.', 'yxjto-paypal-multi-gateway'),
                'account_id' => $account_id
            ));
        } else {
            wp_send_json_error(__('Failed to add account.', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * AJAX: Delete account
     */
    public function ajax_delete_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_id = sanitize_text_field($_POST['account_id']);

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $result = $accounts->delete_account($account_id);

        if ($result) {
            wp_send_json_success(__('Account deleted successfully.', 'yxjto-paypal-multi-gateway'));
        } else {
            wp_send_json_error(__('Failed to delete account.', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * AJAX: Update account
     */
    public function ajax_update_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_id = sanitize_text_field($_POST['account_id']);
        $account_type = sanitize_text_field($_POST['account_type']);
        $weight = intval($_POST['weight']);

        $account_data = array();

        switch ($account_type) {
            case 'email':
                $account_data['email'] = sanitize_email($_POST['paypal_email']);
                break;
            case 'paypal_me':
                $account_data['paypal_me_url'] = esc_url_raw($_POST['paypal_me_url']);
                break;
            case 'api':
                $account_data['client_id'] = sanitize_text_field($_POST['client_id']);
                $account_data['client_secret'] = sanitize_text_field($_POST['client_secret']);
                $account_data['sandbox'] = isset($_POST['sandbox_mode']) && $_POST['sandbox_mode'] === '1';
                break;
            default:
                wp_send_json_error(__('Invalid account type.', 'yxjto-paypal-multi-gateway'));
        }

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $result = $accounts->update_account($account_id, $account_data, $weight);

        if ($result) {
            wp_send_json_success(__('Account updated successfully.', 'yxjto-paypal-multi-gateway'));
        } else {
            wp_send_json_error(__('Failed to update account.', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * AJAX: Get account
     */
    public function ajax_get_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_id = sanitize_text_field($_POST['account_id']);

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $account = $accounts->get_account($account_id);

        if ($account) {
            $account_data = json_decode($account->account_data, true);
            wp_send_json_success(array(
                'account_type' => $account->account_type,
                'weight' => $account->weight,
                'account_data' => $account_data
            ));
        } else {
            wp_send_json_error(__('Account not found.', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * AJAX: Toggle account status
     */
    public function ajax_toggle_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_id = sanitize_text_field($_POST['account_id']);
        $new_status = sanitize_text_field($_POST['status']);

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $result = $accounts->update_account_status($account_id, $new_status);

        if ($result) {
            wp_send_json_success(__('Account status updated successfully.', 'yxjto-paypal-multi-gateway'));
        } else {
            wp_send_json_error(__('Failed to update account status.', 'yxjto-paypal-multi-gateway'));
        }
    }

    /**
     * AJAX: Get stats
     */
    public function ajax_get_stats() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $stats = $accounts->get_account_stats('');

        wp_send_json_success($stats);
    }

    /**
     * 构建日期条件
     */
    private function build_date_condition($time_range, $start_date = '', $end_date = '') {
        global $wpdb;

        if ($time_range === 'custom' && $start_date && $end_date) {
            return $wpdb->prepare(
                "AND created_at >= %s AND created_at <= %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            );
        } else {
            $days = intval($time_range);
            return $wpdb->prepare(
                "AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                $days
            );
        }
    }

    /**
     * 获取概览统计数据（带缓存）
     */
    private function get_overview_statistics($date_condition = '') {
        // 生成缓存键
        $cache_key = 'yxjto_paypal_overview_stats_' . md5($date_condition);
        $cached_stats = get_transient($cache_key);

        if ($cached_stats !== false) {
            return $cached_stats;
        }

        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $stats = $wpdb->get_row("
            SELECT
                COUNT(*) as total_transactions,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_transactions,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_transaction
            FROM {$table_name}
            WHERE 1=1 {$date_condition}
        ");

        if (!$stats) {
            $result = [
                'total_transactions' => 0,
                'successful_transactions' => 0,
                'total_revenue' => 0,
                'avg_transaction' => 0,
                'success_rate' => 0
            ];
        } else {
            $success_rate = $stats->total_transactions > 0
                ? ($stats->successful_transactions / $stats->total_transactions) * 100
                : 0;

            $result = [
                'total_transactions' => $stats->total_transactions,
                'successful_transactions' => $stats->successful_transactions,
                'total_revenue' => $stats->total_revenue ?: 0,
                'avg_transaction' => $stats->avg_transaction ?: 0,
                'success_rate' => $success_rate
            ];
        }

        // 缓存结果 5 分钟
        set_transient($cache_key, $result, 5 * MINUTE_IN_SECONDS);

        return $result;
    }

    /**
     * 获取每日统计数据（带缓存）
     */
    private function get_daily_statistics($date_condition = '') {
        // 生成缓存键
        $cache_key = 'yxjto_paypal_daily_stats_' . md5($date_condition);
        $cached_stats = get_transient($cache_key);

        if ($cached_stats !== false) {
            return $cached_stats;
        }

        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $daily_stats = $wpdb->get_results("
            SELECT
                DATE(created_at) as date,
                COUNT(*) as total_transactions,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_transactions,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount
            FROM {$table_name}
            WHERE 1=1 {$date_condition}
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");

        $result = $daily_stats ?: [];

        // 缓存结果 10 分钟
        set_transient($cache_key, $result, 10 * MINUTE_IN_SECONDS);

        return $result;
    }

    /**
     * AJAX: Get log details
     */
    public function ajax_get_log_details() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $log_id = isset($_POST['log_id']) ? intval($_POST['log_id']) : 0;

        if (!$log_id) {
            wp_send_json_error(__('Invalid log ID.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $log = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE id = %d",
            $log_id
        ));

        if (!$log) {
            wp_send_json_error(__('Log entry not found.', 'yxjto-paypal-multi-gateway'));
        }

        // 解析 gateway_response
        $gateway_response = json_decode($log->gateway_response, true);

        // 获取订单信息
        $order = wc_get_order($log->order_id);
        $order_info = null;
        if ($order) {
            $order_info = [
                'order_number' => $order->get_order_number(),
                'customer_name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
                'customer_email' => $order->get_billing_email(),
                'order_status' => $order->get_status(),
                'order_total' => $order->get_total(),
                'order_date' => $order->get_date_created()->format('Y-m-d H:i:s')
            ];
        }

        wp_send_json_success([
            'log' => $log,
            'gateway_response' => $gateway_response,
            'order_info' => $order_info
        ]);
    }

    /**
     * AJAX: Delete log entry
     */
    public function ajax_delete_log_entry() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $log_id = isset($_POST['log_id']) ? intval($_POST['log_id']) : 0;

        if (!$log_id) {
            wp_send_json_error(__('Invalid log ID.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $result = $wpdb->delete($table_name, array('id' => $log_id), array('%d'));

        if ($result === false) {
            wp_send_json_error(__('Failed to delete log entry.', 'yxjto-paypal-multi-gateway'));
        }

        wp_send_json_success(__('Log entry deleted successfully.', 'yxjto-paypal-multi-gateway'));
    }

    /**
     * AJAX: Export logs
     */
    public function ajax_export_logs() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $log_ids = isset($_POST['log_ids']) ? array_map('intval', $_POST['log_ids']) : array();
        $export_all = isset($_POST['export_all']) && $_POST['export_all'] === 'true';

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        if ($export_all) {
            $logs = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC");
        } elseif (!empty($log_ids)) {
            $placeholders = implode(',', array_fill(0, count($log_ids), '%d'));
            $logs = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE id IN ({$placeholders}) ORDER BY created_at DESC",
                $log_ids
            ));
        } else {
            wp_send_json_error(__('No logs selected for export.', 'yxjto-paypal-multi-gateway'));
        }

        if (empty($logs)) {
            wp_send_json_error(__('No logs found to export.', 'yxjto-paypal-multi-gateway'));
        }

        // 生成 CSV 内容
        $csv_content = $this->generate_logs_csv($logs);
        $filename = 'paypal-multi-gateway-logs-' . date('Y-m-d-H-i-s') . '.csv';

        wp_send_json_success([
            'csv_content' => $csv_content,
            'filename' => $filename
        ]);
    }

    /**
     * 生成日志 CSV 内容
     */
    private function generate_logs_csv($logs) {
        $csv_data = array();

        // CSV 头部
        $csv_data[] = array(
            __('ID', 'yxjto-paypal-multi-gateway'),
            __('Transaction ID', 'yxjto-paypal-multi-gateway'),
            __('Order ID', 'yxjto-paypal-multi-gateway'),
            __('Account ID', 'yxjto-paypal-multi-gateway'),
            __('Amount', 'yxjto-paypal-multi-gateway'),
            __('Currency', 'yxjto-paypal-multi-gateway'),
            __('Status', 'yxjto-paypal-multi-gateway'),
            __('Gateway Response', 'yxjto-paypal-multi-gateway'),
            __('Created At', 'yxjto-paypal-multi-gateway')
        );

        // 数据行
        foreach ($logs as $log) {
            $csv_data[] = array(
                $log->id,
                $log->transaction_id,
                $log->order_id,
                $log->account_id,
                $log->amount,
                $log->currency,
                $log->status,
                $log->gateway_response,
                $log->created_at
            );
        }

        // 转换为 CSV 字符串
        $output = fopen('php://temp', 'r+');
        foreach ($csv_data as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csv_content = stream_get_contents($output);
        fclose($output);

        return $csv_content;
    }

    /**
     * AJAX: Bulk delete logs
     */
    public function ajax_bulk_delete_logs() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $log_ids = isset($_POST['log_ids']) ? array_map('intval', $_POST['log_ids']) : array();

        if (empty($log_ids)) {
            wp_send_json_error(__('No logs selected for deletion.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $placeholders = implode(',', array_fill(0, count($log_ids), '%d'));
        $result = $wpdb->query($wpdb->prepare(
            "DELETE FROM {$table_name} WHERE id IN ({$placeholders})",
            $log_ids
        ));

        if ($result === false) {
            wp_send_json_error(__('Failed to delete log entries.', 'yxjto-paypal-multi-gateway'));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();

        wp_send_json_success(sprintf(
            __('%d log entries deleted successfully.', 'yxjto-paypal-multi-gateway'),
            $result
        ));
    }

    /**
     * 清除统计缓存
     */
    public function clear_statistics_cache() {
        global $wpdb;

        // 删除所有相关的缓存
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_yxjto_paypal_%_stats_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_yxjto_paypal_%_stats_%'");

        // 清除对象缓存（如果使用）
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('yxjto_paypal_statistics');
        }
    }

    /**
     * 添加数据库索引以优化查询性能
     */
    public function add_database_indexes() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return;
        }

        // 添加索引（如果不存在）
        $indexes = array(
            'idx_account_id' => "ALTER TABLE {$table_name} ADD INDEX idx_account_id (account_id)",
            'idx_status' => "ALTER TABLE {$table_name} ADD INDEX idx_status (status)",
            'idx_created_at' => "ALTER TABLE {$table_name} ADD INDEX idx_created_at (created_at)",
            'idx_account_status' => "ALTER TABLE {$table_name} ADD INDEX idx_account_status (account_id, status)",
            'idx_status_created' => "ALTER TABLE {$table_name} ADD INDEX idx_status_created (status, created_at)",
            'idx_account_created' => "ALTER TABLE {$table_name} ADD INDEX idx_account_created (account_id, created_at)"
        );

        foreach ($indexes as $index_name => $sql) {
            // 检查索引是否已存在
            $existing_index = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                 WHERE table_schema = %s AND table_name = %s AND index_name = %s",
                DB_NAME,
                $table_name,
                $index_name
            ));

            if (!$existing_index) {
                $wpdb->query($sql);
            }
        }
    }

    /**
     * AJAX: Clear old logs
     */
    public function ajax_clear_old_logs() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $days = isset($_POST['days']) ? intval($_POST['days']) : 30;

        if ($days < 1 || $days > 3650) { // Max 10 years
            wp_send_json_error(__('Invalid number of days.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $result = $wpdb->query($wpdb->prepare(
            "DELETE FROM {$table_name} WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days
        ));

        if ($result === false) {
            wp_send_json_error(__('Failed to clear old logs.', 'yxjto-paypal-multi-gateway'));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();

        wp_send_json_success(sprintf(
            __('%d old log entries cleared successfully.', 'yxjto-paypal-multi-gateway'),
            $result
        ));
    }

    /**
     * AJAX: Clear failed logs
     */
    public function ajax_clear_failed_logs() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        $result = $wpdb->query(
            "DELETE FROM {$table_name} WHERE status IN ('failed', 'error', 'denied', 'cancelled')"
        );

        if ($result === false) {
            wp_send_json_error(__('Failed to clear failed logs.', 'yxjto-paypal-multi-gateway'));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();

        wp_send_json_success(sprintf(
            __('%d failed log entries cleared successfully.', 'yxjto-paypal-multi-gateway'),
            $result
        ));
    }

    /**
     * AJAX: Clear logs by status
     */
    public function ajax_clear_logs_by_status() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $statuses = isset($_POST['statuses']) ? array_map('sanitize_text_field', $_POST['statuses']) : array();
        $date_from = isset($_POST['date_from']) ? sanitize_text_field($_POST['date_from']) : '';
        $date_to = isset($_POST['date_to']) ? sanitize_text_field($_POST['date_to']) : '';

        if (empty($statuses)) {
            wp_send_json_error(__('No statuses selected.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 构建查询条件
        $where_conditions = array();
        $params = array();

        // 状态条件
        $status_placeholders = implode(',', array_fill(0, count($statuses), '%s'));
        $where_conditions[] = "status IN ({$status_placeholders})";
        $params = array_merge($params, $statuses);

        // 日期条件
        if (!empty($date_from)) {
            $where_conditions[] = "DATE(created_at) >= %s";
            $params[] = $date_from;
        }

        if (!empty($date_to)) {
            $where_conditions[] = "DATE(created_at) <= %s";
            $params[] = $date_to;
        }

        $where_clause = implode(' AND ', $where_conditions);
        $query = "DELETE FROM {$table_name} WHERE {$where_clause}";

        $result = $wpdb->query($wpdb->prepare($query, $params));

        if ($result === false) {
            wp_send_json_error(__('Failed to clear logs by status.', 'yxjto-paypal-multi-gateway'));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();

        wp_send_json_success(sprintf(
            __('%d log entries cleared successfully.', 'yxjto-paypal-multi-gateway'),
            $result
        ));
    }

    /**
     * AJAX: Clear all logs
     */
    public function ajax_clear_all_logs() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 获取总数用于返回信息
        $total_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");

        $result = $wpdb->query("TRUNCATE TABLE {$table_name}");

        if ($result === false) {
            wp_send_json_error(__('Failed to clear all logs.', 'yxjto-paypal-multi-gateway'));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();

        wp_send_json_success(sprintf(
            __('All %d log entries cleared successfully.', 'yxjto-paypal-multi-gateway'),
            $total_count
        ));
    }

    /**
     * AJAX: Get maintenance statistics
     */
    public function ajax_get_maintenance_stats() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 获取统计信息
        $total_logs = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");

        // 获取数据库大小
        $db_size = $wpdb->get_var($wpdb->prepare(
            "SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'DB Size in MB'
             FROM information_schema.tables
             WHERE table_schema = %s AND table_name = %s",
            DB_NAME,
            $table_name
        ));

        // 获取最老的日志
        $oldest_log = $wpdb->get_var("SELECT MIN(created_at) FROM {$table_name}");

        // 获取当前设置
        $auto_cleanup_enabled = get_option('yxjto_paypal_multi_gateway_auto_cleanup_enabled', false);
        $auto_cleanup_days = get_option('yxjto_paypal_multi_gateway_auto_cleanup_days', 90);
        $keep_successful = get_option('yxjto_paypal_multi_gateway_keep_successful_logs', true);

        wp_send_json_success(array(
            'total_logs' => number_format($total_logs),
            'db_size' => $db_size ? $db_size . ' MB' : __('Unknown', 'yxjto-paypal-multi-gateway'),
            'oldest_log' => $oldest_log ? date_i18n(get_option('date_format'), strtotime($oldest_log)) : __('No logs', 'yxjto-paypal-multi-gateway'),
            'auto_cleanup_enabled' => $auto_cleanup_enabled,
            'auto_cleanup_days' => $auto_cleanup_days,
            'keep_successful' => $keep_successful
        ));
    }

    /**
     * AJAX: Optimize database
     */
    public function ajax_optimize_database() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 优化表
        $result = $wpdb->query("OPTIMIZE TABLE {$table_name}");

        if ($result === false) {
            wp_send_json_error(__('Failed to optimize database.', 'yxjto-paypal-multi-gateway'));
        }

        wp_send_json_success(__('Database optimized successfully.', 'yxjto-paypal-multi-gateway'));
    }

    /**
     * AJAX: Save maintenance settings
     */
    public function ajax_save_maintenance_settings() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $auto_cleanup_enabled = isset($_POST['auto_cleanup_enabled']) && $_POST['auto_cleanup_enabled'] === 'true';
        $auto_cleanup_days = isset($_POST['auto_cleanup_days']) ? intval($_POST['auto_cleanup_days']) : 90;
        $keep_successful = isset($_POST['keep_successful']) && $_POST['keep_successful'] === 'true';

        // 验证输入
        if ($auto_cleanup_days < 1 || $auto_cleanup_days > 3650) {
            wp_send_json_error(__('Invalid cleanup days value.', 'yxjto-paypal-multi-gateway'));
        }

        // 保存设置
        update_option('yxjto_paypal_multi_gateway_auto_cleanup_enabled', $auto_cleanup_enabled);
        update_option('yxjto_paypal_multi_gateway_auto_cleanup_days', $auto_cleanup_days);
        update_option('yxjto_paypal_multi_gateway_keep_successful_logs', $keep_successful);

        // 设置或清除定时任务
        if ($auto_cleanup_enabled) {
            if (!wp_next_scheduled('yxjto_paypal_multi_gateway_auto_cleanup')) {
                wp_schedule_event(time(), 'daily', 'yxjto_paypal_multi_gateway_auto_cleanup');
            }
        } else {
            wp_clear_scheduled_hook('yxjto_paypal_multi_gateway_auto_cleanup');
        }

        wp_send_json_success(__('Maintenance settings saved successfully.', 'yxjto-paypal-multi-gateway'));
    }

    /**
     * 自动清理日志的定时任务
     */
    public function auto_cleanup_logs() {
        $auto_cleanup_enabled = get_option('yxjto_paypal_multi_gateway_auto_cleanup_enabled', false);

        if (!$auto_cleanup_enabled) {
            return;
        }

        $auto_cleanup_days = get_option('yxjto_paypal_multi_gateway_auto_cleanup_days', 90);
        $keep_successful = get_option('yxjto_paypal_multi_gateway_keep_successful_logs', true);

        global $wpdb;
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        if ($keep_successful) {
            // 只删除非成功的旧日志
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$table_name}
                 WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)
                 AND status NOT IN ('completed', 'success')",
                $auto_cleanup_days
            ));
        } else {
            // 删除所有旧日志
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$table_name} WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
                $auto_cleanup_days
            ));
        }

        // 清除相关缓存
        $this->clear_statistics_cache();
    }



    /**
     * AJAX: Migrate config
     */
    public function ajax_migrate_config() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        wp_send_json_success(array('message' => 'Config migration feature coming soon.'));
    }

    /**
     * AJAX: Verify config
     */
    public function ajax_verify_config() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        wp_send_json_success(array('message' => 'Config verification feature coming soon.'));
    }

    /**
     * AJAX: Get config status
     */
    public function ajax_get_config_status() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        wp_send_json_success(array('message' => 'Config status feature coming soon.'));
    }
}
