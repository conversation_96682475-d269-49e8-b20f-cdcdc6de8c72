<?php
/**
 * 生成.mo文件的PHP脚本
 * 用于创建WordPress可以读取的二进制翻译文件
 */

// 中文翻译数组
$translations = [
    'Multi Database Settings' => '多数据库设置',
    'Multi Database' => '多数据库',
    'Insufficient permissions' => '权限不足',
    'Testing connection...' => '正在测试连接...',
    'Connection successful!' => '连接成功！',
    'Connection failed!' => '连接失败！',
    'Switching database...' => '正在切换数据库...',
    'Database switched successfully!' => '数据库切换成功！',
    'Database switch failed!' => '数据库切换失败！',
    'Databases' => '数据库',
    'IP Rules' => 'IP规则',
    'URL Rules' => 'URL规则',
    'Status' => '状态',
    'Logs' => '日志',
    'Database Configurations' => '数据库配置',
    'Configure your database connections here.' => '在这里配置您的数据库连接。',
    'Current Database' => '当前数据库',
    'Test Connection' => '测试连接',
    'Switch to This' => '切换到此数据库',
    'Remove' => '删除',
    'Name' => '名称',
    'Host' => '主机',
    'Database' => '数据库',
    'Username' => '用户名',
    'Password' => '密码',
    'Charset' => '字符集',
    'Enabled' => '启用',
    'Add New Database' => '添加新数据库',
    'IP-based Database Switching Rules' => '基于IP的数据库切换规则',
    'Configure rules to automatically switch databases based on visitor IP addresses.' => '配置规则以根据访问者IP地址自动切换数据库。',
    'Rule %d' => '规则 %d',
    'IP Range' => 'IP范围',
    'Examples: ***********, ***********/24, ***********-*************' => '示例：***********, ***********/24, ***********-*************',
    'Add IP Rule' => '添加IP规则',
    'URL Parameter Database Switching Rules' => '基于URL参数的数据库切换规则',
    'Configure rules to switch databases based on URL parameters.' => '配置规则以根据URL参数切换数据库。',
    'Parameter Name' => '参数名称',
    'Parameter Value' => '参数值',
    'Add URL Rule' => '添加URL规则',
    'System Status' => '系统状态',
    'Monitor the status of your database connections and system performance.' => '监控您的数据库连接状态和系统性能。',
    'Current Status' => '当前状态',
    'Active Database' => '活动数据库',
    'Plugin Version' => '插件版本',
    'WordPress Version' => 'WordPress版本',
    'PHP Version' => 'PHP版本',
    'Database Connections' => '数据库连接',
    'Disabled' => '已禁用',
    'Switching Rules' => '切换规则',
    'Active IP Rules' => '活动IP规则',
    'Active URL Rules' => '活动URL规则',
    'Performance' => '性能',
    'Memory Usage' => '内存使用',
    'Peak Memory' => '峰值内存',
    'Debug Mode' => '调试模式',
    'Activity Logs' => '活动日志',
    'View database switching activity and system events.' => '查看数据库切换活动和系统事件。',
    'Logs table not found. Please deactivate and reactivate the plugin.' => '未找到日志表。请停用并重新激活插件。',
    'No logs found.' => '未找到日志。',
    'Timestamp' => '时间戳',
    'Action' => '操作',
    'From Database' => '源数据库',
    'To Database' => '目标数据库',
    'IP Address' => 'IP地址',
    'Message' => '消息',
    'Success' => '成功',
    'Failed' => '失败',
    'Refresh Logs' => '刷新日志',
    'Clear Logs' => '清除日志',
    'Are you sure you want to clear all logs?' => '您确定要清除所有日志吗？',
    'Failed to clear logs.' => '清除日志失败。',
    'Settings saved successfully!' => '设置保存成功！',
    'Logs cleared successfully!' => '日志清除成功！',
    'Failed to clear logs!' => '清除日志失败！',
    'Select Database' => '选择数据库',
    'Are you sure you want to remove this database configuration?' => '您确定要删除此数据库配置吗？',
    'Are you sure you want to remove this IP rule?' => '您确定要删除此IP规则吗？',
    'Are you sure you want to remove this URL rule?' => '您确定要删除此URL规则吗？',
    'Are you sure you want to switch to this database?' => '您确定要切换到此数据库吗？',
    'New Database' => '新数据库',
    'Cannot delete the default database configuration.' => '无法删除默认数据库配置。',
    'Database configuration deleted successfully.' => '数据库配置删除成功。',
    'Failed to delete database configuration.' => '删除数据库配置失败。',
    'URL rule deleted successfully.' => 'URL规则删除成功。',
    'Failed to delete URL rule.' => '删除URL规则失败。',
    'IP rule deleted successfully.' => 'IP规则删除成功。',
    'Failed to delete IP rule.' => '删除IP规则失败。',
    'Crawler Detection' => '爬虫检测',
    'Highest' => '最高',
    'Crawler Detection Settings' => '爬虫检测设置',
    'Configure crawler detection to automatically use the default database when search engine crawlers visit your site. This ensures consistent SEO indexing.' => '配置爬虫检测以在搜索引擎爬虫访问您的网站时自动使用默认数据库。这确保了一致的SEO索引。',
    'General Settings' => '常规设置',
    'Enable Crawler Detection' => '启用爬虫检测',
    'Automatically detect crawlers and use default database' => '自动检测爬虫并使用默认数据库',
    'When enabled, detected crawlers will always access the default database regardless of other rules.' => '启用后，检测到的爬虫将始终访问默认数据库，无论其他规则如何。',
    'Log Crawler Visits' => '记录爬虫访问',
    'Log crawler visits for monitoring' => '记录爬虫访问以便监控',
    'Keep a log of crawler visits to help with SEO monitoring and debugging.' => '保留爬虫访问日志以帮助SEO监控和调试。',
    'Supported Crawlers' => '支持的爬虫',
    'The following crawlers are automatically detected. You can enable/disable detection for specific crawlers.' => '以下爬虫会被自动检测。您可以启用/禁用特定爬虫的检测。',
    'Crawler Name' => '爬虫名称',
    'User Agent Patterns' => '用户代理模式',
    'Description' => '描述',
    'Custom Crawler Patterns' => '自定义爬虫模式',
    'Add custom User-Agent patterns to detect additional crawlers. One pattern per line.' => '添加自定义用户代理模式以检测其他爬虫。每行一个模式。',
    'Custom Patterns' => '自定义模式',
    'Examples: MyBot, CustomCrawler, SpecialSpider' => '示例：MyBot, CustomCrawler, SpecialSpider',
    'Crawler detection settings saved successfully!' => '爬虫检测设置保存成功！',
    'Failed to save crawler detection settings.' => '保存爬虫检测设置失败。',
];

/**
 * 生成.mo文件
 */
function generateMoFile($translations, $filename) {
    $mo = '';
    
    // MO文件头部
    $mo .= pack('V', 0x950412de); // 魔数
    $mo .= pack('V', 0); // 版本
    $mo .= pack('V', count($translations)); // 字符串数量
    $mo .= pack('V', 28); // 原始字符串偏移
    $mo .= pack('V', 28 + count($translations) * 8); // 翻译字符串偏移
    $mo .= pack('V', 0); // 哈希表大小
    $mo .= pack('V', 0); // 哈希表偏移
    
    $keys = array_keys($translations);
    $values = array_values($translations);
    
    // 计算字符串偏移
    $key_offsets = [];
    $value_offsets = [];
    $current_offset = 28 + count($translations) * 16;
    
    // 原始字符串偏移
    foreach ($keys as $key) {
        $key_offsets[] = $current_offset;
        $current_offset += strlen($key) + 1;
    }
    
    // 翻译字符串偏移
    foreach ($values as $value) {
        $value_offsets[] = $current_offset;
        $current_offset += strlen($value) + 1;
    }
    
    // 写入偏移表
    for ($i = 0; $i < count($translations); $i++) {
        $mo .= pack('V', strlen($keys[$i])); // 长度
        $mo .= pack('V', $key_offsets[$i]); // 偏移
    }
    
    for ($i = 0; $i < count($translations); $i++) {
        $mo .= pack('V', strlen($values[$i])); // 长度
        $mo .= pack('V', $value_offsets[$i]); // 偏移
    }
    
    // 写入字符串
    foreach ($keys as $key) {
        $mo .= $key . "\0";
    }
    
    foreach ($values as $value) {
        $mo .= $value . "\0";
    }
    
    file_put_contents($filename, $mo);
}

// 生成中文.mo文件
generateMoFile($translations, __DIR__ . '/languages/yxjto-gateway-zh_CN.mo');

echo "中文.mo文件生成成功！\n";
echo "文件位置: " . __DIR__ . '/languages/yxjto-gateway-zh_CN.mo' . "\n";
echo "文件大小: " . filesize(__DIR__ . '/languages/yxjto-gateway-zh_CN.mo') . " 字节\n";
