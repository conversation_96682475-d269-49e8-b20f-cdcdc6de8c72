<?php
/**
 * WordPress多数据库插件 - 爬虫IP数据库
 * 包含主流搜索引擎和社交媒体爬虫的IP地址段
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 爬虫IP数据库类
 */
class WP_Multi_Database_Crawler_IP_Database {
    
    /**
     * 获取所有爬虫IP段
     */
    public static function get_all_crawler_ip_ranges() {
        return [
            'googlebot' => self::get_google_ip_ranges(),
            'bingbot' => self::get_bing_ip_ranges(),
            'facebookbot' => self::get_facebook_ip_ranges(),
            'twitterbot' => self::get_twitter_ip_ranges(),
            'linkedinbot' => self::get_linkedin_ip_ranges(),
            'baidubot' => self::get_baidu_ip_ranges(),
            'yandexbot' => self::get_yandex_ip_ranges(),
            'yahoobot' => self::get_yahoo_ip_ranges(),
            'duckduckbot' => self::get_duckduckgo_ip_ranges()
        ];
    }
    
    /**
     * Google爬虫IP段
     * 来源: https://developers.google.com/search/docs/crawling-indexing/verifying-googlebot
     */
    public static function get_google_ip_ranges() {
        return [
            // Google主要IP段
            '***********/19',
            '***********/24',
            '***********/22',
            '***********/21',
            '***********/20',
            '***********/19',
            
            // Google Cloud IP段
            '************/19',
            '***********/18',
            '**********/16',
            '***********/21',
            '***********/16',
            '************/17',
            '************/19',
            
            // Google特殊服务IP段
            '*************/20',
            '***********/15',
            '***********/16',
            '************/19',
            
            // IPv6段（如果需要）
            // '2001:4860::/32'
        ];
    }
    
    /**
     * Bing爬虫IP段
     * 来源: Microsoft官方文档
     */
    public static function get_bing_ip_ranges() {
        return [
            // Bing主要IP段
            '***********/24',
            '***********/24',
            '***********/24',
            '***********/24',
            
            // Microsoft网络IP段
            '***********/24',
            '***********/24',
            '***********/24',
            '************/24',
            
            // 传统Bing IP段
            '***********/24',
            '************/24',
            '************/24',
            
            // Azure IP段
            '***********/24',
            '***********/24',
            '***********/20',
            '*************/32',
            '************/18'
        ];
    }
    
    /**
     * Facebook爬虫IP段
     * 来源: Facebook开发者文档
     */
    public static function get_facebook_ip_ranges() {
        return [
            // Facebook主要IP段
            '**********/21',
            '**********/18',
            '**********/19',
            
            // Facebook CDN IP段
            '************/20',
            '***********/20',
            '************/19',
            '***********/22',
            
            // Facebook亚太IP段
            '**********/22',
            '************/18',
            '************/21',
            '************/19',
            
            // Facebook其他IP段
            '************/22',
            '************/22',
            '***********/22'
        ];
    }
    
    /**
     * Twitter爬虫IP段
     */
    public static function get_twitter_ip_ranges() {
        return [
            '************/22',
            '************/22',
            '************/22',
            '************/24',
            '************/24'
        ];
    }
    
    /**
     * LinkedIn爬虫IP段
     */
    public static function get_linkedin_ip_ranges() {
        return [
            '***********/16',
            '************/23',
            '*********/24'
        ];
    }
    
    /**
     * 百度爬虫IP段
     * 来源: 百度官方文档
     */
    public static function get_baidu_ip_ranges() {
        return [
            // 百度主要IP段
            '**********/16',
            '***********/16',
            '************/24',
            '************/24',
            '*************/24',
            
            // 百度其他IP段
            '**********/16',
            '************/21',
            '*************/24',
            '************/24',
            '************/24'
        ];
    }
    
    /**
     * Yandex爬虫IP段
     * 来源: Yandex官方文档
     */
    public static function get_yandex_ip_ranges() {
        return [
            // Yandex主要IP段
            '**********/18',
            '*********/18',
            '************/18',
            '*********/16',
            '************/19',
            '************/17',
            '*************/17',
            '***********/22',
            
            // Yandex其他服务IP段
            '*************/19',
            '************/17'
        ];
    }
    
    /**
     * Yahoo爬虫IP段
     */
    public static function get_yahoo_ip_ranges() {
        return [
            '************/19',
            '*********/16',
            '**********/14',
            '*************/20',
            '************/18'
        ];
    }
    
    /**
     * DuckDuckGo爬虫IP段
     */
    public static function get_duckduckgo_ip_ranges() {
        return [
            '*************/32',
            '************/32',
            '*************/32',
            '*************/32',
            '*************/32',
            '************/32',
            '***********/32',
            '**************/32',
            '**************/32',
            '*************/32'
        ];
    }
    
    /**
     * 获取爬虫的反向DNS验证域名
     */
    public static function get_crawler_reverse_dns_domains() {
        return [
            'googlebot' => ['googlebot.com', 'google.com'],
            'bingbot' => ['search.msn.com', 'msn.com'],
            'facebookbot' => ['facebook.com'],
            'twitterbot' => ['twitter.com'],
            'linkedinbot' => ['linkedin.com'],
            'baidubot' => ['baidu.com', 'baidu.jp'],
            'yandexbot' => ['yandex.ru', 'yandex.net', 'yandex.com'],
            'yahoobot' => ['crawl.yahoo.net'],
            'duckduckbot' => ['duckduckgo.com']
        ];
    }
    
    /**
     * 获取特定爬虫的IP段
     */
    public static function get_crawler_ip_ranges($crawler_type) {
        $all_ranges = self::get_all_crawler_ip_ranges();
        return isset($all_ranges[$crawler_type]) ? $all_ranges[$crawler_type] : [];
    }
    
    /**
     * 检查IP是否属于特定爬虫
     */
    public static function is_crawler_ip($ip, $crawler_type) {
        $ip_ranges = self::get_crawler_ip_ranges($crawler_type);
        
        foreach ($ip_ranges as $range) {
            if (self::ip_in_range($ip, $range)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查IP是否在指定范围内
     */
    private static function ip_in_range($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $mask) = explode('/', $range);
        
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        
        if ($ip_long === false || $subnet_long === false) {
            return false;
        }
        
        $mask_long = -1 << (32 - (int)$mask);
        
        return ($ip_long & $mask_long) === ($subnet_long & $mask_long);
    }
    
    /**
     * 获取IP段统计信息
     */
    public static function get_ip_ranges_stats() {
        $all_ranges = self::get_all_crawler_ip_ranges();
        $stats = [];
        
        foreach ($all_ranges as $crawler => $ranges) {
            $stats[$crawler] = [
                'count' => count($ranges),
                'ranges' => $ranges
            ];
        }
        
        return $stats;
    }
    
    /**
     * 更新IP段数据（从外部API获取最新数据）
     */
    public static function update_ip_ranges() {
        // 这里可以实现从官方API获取最新IP段的功能
        // 例如Google的API: https://www.gstatic.com/ipranges/goog.json
        
        $updated = [];
        
        // Google IP段更新
        $google_ranges = self::fetch_google_ip_ranges();
        if ($google_ranges) {
            $updated['googlebot'] = $google_ranges;
        }
        
        // 其他爬虫的IP段更新...
        
        return $updated;
    }
    
    /**
     * 从Google API获取最新IP段
     */
    private static function fetch_google_ip_ranges() {
        $url = 'https://www.gstatic.com/ipranges/goog.json';
        
        $response = wp_remote_get($url, [
            'timeout' => 10,
            'user-agent' => 'WordPress Multi Database Plugin'
        ]);
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['prefixes'])) {
            return false;
        }
        
        $ip_ranges = [];
        foreach ($data['prefixes'] as $prefix) {
            if (isset($prefix['ipv4Prefix'])) {
                $ip_ranges[] = $prefix['ipv4Prefix'];
            }
        }
        
        return $ip_ranges;
    }
}
