<?php
/**
 * Coupon Sync Admin Page
 * 
 * @package YXJTO_Gateway
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Coupon_Sync_Admin {
    
    /**
     * Initialize the admin page
     */
    public static function init() {
        // add_action('admin_menu', [__CLASS__, 'add_admin_menu']); // 菜单已在主插件文件中注册
        add_action('admin_init', [__CLASS__, 'handle_form_submission']);
        add_action('wp_ajax_yxjto_manual_coupon_sync', [__CLASS__, 'ajax_manual_coupon_sync']);
        add_action('wp_ajax_yxjto_sql_delete_all_coupons', [__CLASS__, 'ajax_sql_delete_all_coupons']);
        add_action('wp_ajax_yxjto_sync_trash_status', [__CLASS__, 'ajax_sync_trash_status']);
        add_action('wp_ajax_yxjto_cleanup_trashed_coupons', [__CLASS__, 'ajax_cleanup_trashed_coupons']);
        add_action('wp_ajax_yxjto_load_coupons_for_deletion', [__CLASS__, 'ajax_load_coupons_for_deletion']);
        add_action('wp_ajax_yxjto_delete_selected_coupons', [__CLASS__, 'ajax_delete_selected_coupons']);
        add_action('wp_ajax_yxjto_delete_selected_coupons_all_databases', [__CLASS__, 'ajax_delete_selected_coupons_all_databases']);
    }
    
    /**
     * Add admin menu
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'yxjto',
            __('Coupon Sync', 'yxjto-gateway'),
            __('Coupon Sync', 'yxjto-gateway'),
            'manage_options',
            'yxjto-coupon-sync',
            [__CLASS__, 'render_page']
        );
    }
    
    /**
     * Render the admin page
     */
    public static function render_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Coupon Synchronization Settings', 'yxjto-gateway'); ?></h1>
            
            <?php settings_errors('yxjto_gateway_coupon_sync'); ?>
            
            <form method="post" action="" id="coupon-sync-form">
                <?php wp_nonce_field('yxjto_gateway_coupon_sync', 'yxjto_gateway_coupon_sync_nonce'); ?>
                <input type="hidden" name="action" value="save_coupon_sync" />

                <p><?php _e('Configure coupon synchronization settings across multiple databases.', 'yxjto-gateway'); ?></p>
                
                <?php
                $coupon_settings = WP_Multi_DB_Config_Manager::get_coupon_sync_settings();
                ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Coupon Sync', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_coupon_sync" value="1" 
                                       <?php checked(!empty($coupon_settings['enable_coupon_sync'])); ?> />
                                <?php _e('Enable automatic coupon synchronization across all databases', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, coupons will be automatically synchronized to all configured databases.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sync Mode', 'yxjto-gateway'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="radio" name="sync_mode" value="realtime" 
                                           <?php checked($coupon_settings['sync_mode'] ?? 'realtime', 'realtime'); ?> />
                                    <?php _e('Real-time Sync', 'yxjto-gateway'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="sync_mode" value="batch" 
                                           <?php checked($coupon_settings['sync_mode'] ?? 'realtime', 'batch'); ?> />
                                    <?php _e('Batch Sync', 'yxjto-gateway'); ?>
                                </label>
                            </fieldset>
                            <p class="description">
                                <?php _e('Real-time sync happens immediately when coupons are created/updated. Batch sync runs at scheduled intervals.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr id="batch-interval-row" style="<?php echo ($coupon_settings['sync_mode'] ?? 'realtime') === 'batch' ? '' : 'display: none;'; ?>">
                        <th scope="row"><?php _e('Batch Interval', 'yxjto-gateway'); ?></th>
                        <td>
                            <select name="batch_interval">
                                <option value="hourly" <?php selected($coupon_settings['batch_interval'] ?? 'daily', 'hourly'); ?>><?php _e('Hourly', 'yxjto-gateway'); ?></option>
                                <option value="twicedaily" <?php selected($coupon_settings['batch_interval'] ?? 'daily', 'twicedaily'); ?>><?php _e('Twice Daily', 'yxjto-gateway'); ?></option>
                                <option value="daily" <?php selected($coupon_settings['batch_interval'] ?? 'daily', 'daily'); ?>><?php _e('Daily', 'yxjto-gateway'); ?></option>
                                <option value="weekly" <?php selected($coupon_settings['batch_interval'] ?? 'daily', 'weekly'); ?>><?php _e('Weekly', 'yxjto-gateway'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('How often to run batch synchronization.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Conflict Resolution', 'yxjto-gateway'); ?></th>
                        <td>
                            <select name="conflict_resolution">
                                <option value="newest" <?php selected($coupon_settings['conflict_resolution'] ?? 'newest', 'newest'); ?>><?php _e('Use Newest', 'yxjto-gateway'); ?></option>
                                <option value="source" <?php selected($coupon_settings['conflict_resolution'] ?? 'newest', 'source'); ?>><?php _e('Source Wins', 'yxjto-gateway'); ?></option>
                                <option value="manual" <?php selected($coupon_settings['conflict_resolution'] ?? 'newest', 'manual'); ?>><?php _e('Manual Resolution', 'yxjto-gateway'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('How to handle conflicts when the same coupon exists in multiple databases with different values.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Enable Logging', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_sync_logging" value="1" 
                                       <?php checked(!empty($coupon_settings['enable_sync_logging'])); ?> />
                                <?php _e('Log coupon synchronization activities', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Enable detailed logging of coupon sync operations for debugging and monitoring.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Exclude Expired Coupons', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="exclude_expired" value="1" 
                                       <?php checked(!empty($coupon_settings['exclude_expired'])); ?> />
                                <?php _e('Do not sync expired coupons', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, expired coupons will be excluded from synchronization.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sync Coupon Usage', 'yxjto-gateway'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="sync_usage_data" value="1" 
                                       <?php checked(!empty($coupon_settings['sync_usage_data'])); ?> />
                                <?php _e('Synchronize coupon usage data and statistics', 'yxjto-gateway'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, coupon usage counts and related data will also be synchronized.', 'yxjto-gateway'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit_coupon_sync" class="button-primary" 
                           value="<?php _e('Save Coupon Sync Settings', 'yxjto-gateway'); ?>" />
                </p>
            </form>

            <!-- 选择性删除优惠券区域 -->
            <div class="coupon-selective-delete" style="margin-top: 30px; padding: 20px; border: 2px solid #f39c12; border-radius: 5px; background-color: #fef9e7;">
                <h3 style="color: #f39c12; margin-top: 0;">
                    🎯 <?php _e('Selective Coupon Management', 'yxjto-gateway'); ?>
                </h3>
                <p style="color: #8a6d3b;">
                    <?php _e('Select specific coupons to delete from all configured databases.', 'yxjto-gateway'); ?>
                </p>

                <table class="form-table" style="margin-bottom: 20px;">
                    <tr>
                        <th scope="row">
                            <?php _e('Select Coupons to Delete', 'yxjto-gateway'); ?>
                        </th>
                        <td>
                            <div id="coupon-selection-area">
                                <p class="description" style="margin-bottom: 15px;">
                                    <?php _e('Choose coupons to delete from all databases. Selected coupons will be permanently removed from ALL configured databases.', 'yxjto-gateway'); ?>
                                </p>

                                <!-- 搜索框 -->
                                <div style="margin-bottom: 15px;">
                                    <input type="text" id="coupon-search" placeholder="<?php _e('Search coupons...', 'yxjto-gateway'); ?>" style="width: 300px; margin-right: 10px;">
                                    <button type="button" class="button button-secondary" onclick="loadCoupons()">
                                        🔍 <?php _e('Load Coupons', 'yxjto-gateway'); ?>
                                    </button>
                                </div>

                                <!-- 优惠券列表 -->
                                <div id="coupon-list" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: white;">
                                    <p style="text-align: center; color: #666;">
                                        <?php _e('Click "Load Coupons" to display available coupons', 'yxjto-gateway'); ?>
                                    </p>
                                </div>

                                <!-- 操作按钮 -->
                                <div style="margin-top: 15px;">
                                    <button type="button" class="button button-secondary" onclick="selectAllCoupons()" style="margin-right: 10px;">
                                        ☑️ <?php _e('Select All', 'yxjto-gateway'); ?>
                                    </button>
                                    <button type="button" class="button button-secondary" onclick="deselectAllCoupons()" style="margin-right: 10px;">
                                        ☐ <?php _e('Deselect All', 'yxjto-gateway'); ?>
                                    </button>
                                    <button type="button" class="button" style="background-color: #dc3545; border-color: #dc3545; color: white;" onclick="deleteSelectedCoupons()">
                                        🗑️ <?php _e('Delete Selected Coupons', 'yxjto-gateway'); ?>
                                    </button>
                                    <button type="button" class="button delete-all-databases-btn" onclick="deleteSelectedCouponsFromAllDatabases()">
                                        💥 <?php _e('Delete from ALL Databases', 'yxjto-gateway'); ?>
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>

                <div id="selective-delete-result" style="margin-top: 15px;"></div>
            </div>

            <!-- 同步状态和操作 -->
            <div class="coupon-sync-status">
                <h3><?php _e('Synchronization Status', 'yxjto-gateway'); ?></h3>
                
                <?php 
                // 获取同步统计信息
                if (class_exists('YXJTO_Coupon_Sync')) {
                    $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                    $sync_stats = $coupon_sync->get_sync_stats();
                } else {
                    $sync_stats = [
                        'total_coupons' => 0,
                        'synced_coupons' => 0,
                        'failed_syncs' => 0,
                        'last_sync' => null
                    ];
                }
                ?>
                
                <div class="sync-stats">
                    <p>
                        <strong><?php _e('Total Coupons:', 'yxjto-gateway'); ?></strong>
                        <?php echo esc_html($sync_stats['total_coupons'] ?? 0); ?>
                    </p>
                    <p>
                        <strong><?php _e('Successfully Synced:', 'yxjto-gateway'); ?></strong>
                        <?php echo esc_html($sync_stats['synced_coupons'] ?? 0); ?>
                    </p>
                    <p>
                        <strong><?php _e('Failed Syncs:', 'yxjto-gateway'); ?></strong>
                        <?php echo esc_html($sync_stats['failed_syncs'] ?? 0); ?>
                    </p>
                    <p>
                        <strong><?php _e('Last Sync:', 'yxjto-gateway'); ?></strong>
                        <?php
                        $last_sync = $sync_stats['last_sync'] ?? null;
                        if ($last_sync && $last_sync !== __('Never', 'yxjto-gateway')) {
                            echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($last_sync)));
                        } else {
                            echo __('Never', 'yxjto-gateway');
                        }
                       ?>
                    </p>
                    <?php if (!$coupon_settings['enable_coupon_sync']): ?>
                    <p class="sync-disabled-notice">
                        <strong><?php _e('Notice:', 'yxjto-gateway'); ?></strong> 
                        <?php _e('Coupon synchronization is currently disabled.', 'yxjto-gateway'); ?>
                    </p>
                    <?php endif; ?>
                </div>
                
                <div class="sync-actions">
                    <button type="button" class="button button-primary" id="manual-sync-btn" onclick="performManualCouponSync()">
                        <?php _e('Run Manual Sync', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="sqlDeleteAllCoupons()" style="background-color: #dc3545; border-color: #dc3545; color: white;" title="<?php _e('⚠️ DANGER: This will permanently delete ALL coupons from ALL databases using direct SQL!', 'yxjto-gateway'); ?>">
                        <?php _e('🗑️ SQL Delete All Coupons', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="syncTrashStatus()">
                        <?php _e('🔄 Sync Trash Status', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="viewCouponSyncLogs()">
                        <?php _e('📋 View Sync Logs', 'yxjto-gateway'); ?>
                    </button>
                    <button type="button" class="button button-secondary" onclick="refreshSyncStatus()">
                        <?php _e('🔄 Refresh Status', 'yxjto-gateway'); ?>
                    </button>
                </div>
                
                <div id="coupon-sync-result-message" style="display: none;"></div>
            </div>
        </div>

        <style>
        .coupon-sync-status {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .sync-stats p {
            margin: 8px 0;
        }
        
        .sync-actions {
            margin-top: 15px;
        }
        
        .sync-actions .button {
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        .sync-disabled-notice {
            color: #d63638;
            font-weight: bold;
        }
        
        #batch-interval-row {
            transition: all 0.3s ease;
        }

        .coupon-selective-delete {
            background: #fef9e7;
            border: 2px solid #f39c12;
            border-radius: 5px;
            padding: 20px;
            margin-top: 30px;
        }

        .coupon-selective-delete h3 {
            color: #f39c12;
            margin-top: 0;
            font-size: 18px;
            font-weight: bold;
        }

        .coupon-selective-delete p {
            color: #8a6d3b;
            margin-bottom: 15px;
        }

        #coupon-search {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #coupon-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        #coupon-list label {
            display: block;
            cursor: pointer;
            margin-bottom: 5px;
        }

        #coupon-list input[type="checkbox"] {
            margin-right: 8px;
        }

        #selective-delete-result {
            margin-top: 15px;
        }

        #selective-delete-result .notice {
            padding: 12px;
            margin: 5px 0;
            border-left: 4px solid;
            border-radius: 4px;
        }

        #selective-delete-result .notice-success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        #selective-delete-result .notice-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        #selective-delete-result .notice-info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .delete-all-databases-btn {
            background-color: #8b0000 !important;
            border-color: #8b0000 !important;
            color: white !important;
            font-weight: bold;
            margin-left: 10px;
            position: relative;
            overflow: hidden;
        }

        .delete-all-databases-btn:hover {
            background-color: #a00000 !important;
            border-color: #a00000 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(139, 0, 0, 0.3);
        }

        .delete-all-databases-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .delete-all-databases-btn:hover:before {
            left: 100%;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // 切换批量同步间隔显示
            $('input[name="sync_mode"]').change(function() {
                if ($(this).val() === 'batch') {
                    $('#batch-interval-row').show();
                } else {
                    $('#batch-interval-row').hide();
                }
            });
        });

        // 手动执行优惠券同步
        function performManualCouponSync() {
            if (!confirm('<?php _e('Are you sure you want to run manual coupon synchronization? This may take some time.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_manual_coupon_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_manual_coupon_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('Manual coupon synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    // 刷新同步状态显示
                    location.reload();
                } else {
                    alert('<?php _e('Manual coupon synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 查看优惠券同步日志
        function viewCouponSyncLogs() {
            // 这里可以打开一个新窗口或模态框显示日志
            window.open('<?php echo admin_url('admin.php?page=yxjto-coupon-sync-logs'); ?>', '_blank');
        }

        // 刷新同步状态
        function refreshSyncStatus() {
            location.reload();
        }

        // 加载优惠券列表
        function loadCoupons() {
            const resultDiv = document.getElementById('selective-delete-result');
            const couponList = document.getElementById('coupon-list');
            const searchTerm = document.getElementById('coupon-search').value;

            resultDiv.innerHTML = '<div class="notice notice-info"><p><?php _e('Loading coupons...', 'yxjto-gateway'); ?></p></div>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_load_coupons_for_deletion',
                search: searchTerm,
                nonce: '<?php echo wp_create_nonce('yxjto_load_coupons_for_deletion'); ?>'
            }).done(function(response) {
                if (response.success) {
                    couponList.innerHTML = response.data.html;
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Failed to load coupons:', 'yxjto-gateway'); ?> ' + errorMessage + '</p></div>';
                }
            }).fail(function(xhr, status, error) {
                resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?></p></div>';
            });
        }

        // 选择所有优惠券
        function selectAllCoupons() {
            const checkboxes = document.querySelectorAll('#coupon-list input[type="checkbox"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = true;
            });
        }

        // 取消选择所有优惠券
        function deselectAllCoupons() {
            const checkboxes = document.querySelectorAll('#coupon-list input[type="checkbox"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = false;
            });
        }

        // 删除选中的优惠券
        function deleteSelectedCoupons() {
            const checkboxes = document.querySelectorAll('#coupon-list input[type="checkbox"]:checked');
            const selectedCoupons = [];

            checkboxes.forEach(function(checkbox) {
                selectedCoupons.push({
                    id: checkbox.value,
                    code: checkbox.getAttribute('data-code')
                });
            });

            if (selectedCoupons.length === 0) {
                alert('<?php _e('Please select at least one coupon to delete.', 'yxjto-gateway'); ?>');
                return;
            }

            const couponCodes = selectedCoupons.map(c => c.code).join(', ');
            if (!confirm('<?php _e('Are you sure you want to delete the following coupons from ALL databases?', 'yxjto-gateway'); ?>\n\n' + couponCodes)) {
                return;
            }

            const resultDiv = document.getElementById('selective-delete-result');
            resultDiv.innerHTML = '<div class="notice notice-info"><p><?php _e('Deleting selected coupons from all databases...', 'yxjto-gateway'); ?></p></div>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_delete_selected_coupons',
                coupons: selectedCoupons,
                nonce: '<?php echo wp_create_nonce('yxjto_delete_selected_coupons'); ?>'
            }).done(function(response) {
                if (response.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                    // 重新加载优惠券列表
                    setTimeout(function() {
                        loadCoupons();
                    }, 2000);
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Delete failed:', 'yxjto-gateway'); ?> ' + errorMessage + '</p></div>';
                }
            }).fail(function(xhr, status, error) {
                resultDiv.innerHTML = '<div class="notice notice-error"><p><?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?></p></div>';
            });
        }

        // 从所有数据库删除选中的优惠券
        function deleteSelectedCouponsFromAllDatabases() {
            const checkboxes = document.querySelectorAll('#coupon-list input[type="checkbox"]:checked');
            const selectedCoupons = [];

            checkboxes.forEach(function(checkbox) {
                selectedCoupons.push({
                    id: checkbox.value,
                    code: checkbox.getAttribute('data-code')
                });
            });

            if (selectedCoupons.length === 0) {
                alert('<?php _e('Please select at least one coupon to delete.', 'yxjto-gateway'); ?>');
                return;
            }

            const couponCodes = selectedCoupons.map(c => c.code).join(', ');

            // 三重确认，因为这是极其危险的操作
            if (!confirm('<?php _e('⚠️ EXTREME DANGER: This will PERMANENTLY DELETE the selected coupons from ALL DATABASES!', 'yxjto-gateway'); ?>')) {
                return;
            }

            if (!confirm('<?php _e('This includes the main database and ALL configured additional databases. This action CANNOT be undone!', 'yxjto-gateway'); ?>\n\n<?php _e('Selected coupons:', 'yxjto-gateway'); ?>\n' + couponCodes)) {
                return;
            }

            if (!confirm('<?php _e('Are you absolutely sure you want to delete these coupons from ALL databases? This action cannot be undone!', 'yxjto-gateway'); ?>')) {
                return;
            }

            const resultDiv = document.getElementById('selective-delete-result');
            resultDiv.innerHTML = '<div class="notice notice-info"><p><?php _e('Deleting selected coupons from ALL databases... This may take a while.', 'yxjto-gateway'); ?></p></div>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_delete_selected_coupons_all_databases',
                coupons: selectedCoupons,
                nonce: '<?php echo wp_create_nonce('yxjto_delete_selected_coupons_all_databases'); ?>'
            }).done(function(response) {
                if (response.success) {
                    resultDiv.innerHTML = '<div class="notice notice-success"><p><strong><?php _e('SUCCESS:', 'yxjto-gateway'); ?></strong> ' + response.data.message + '</p></div>';
                    // 重新加载优惠券列表
                    setTimeout(function() {
                        loadCoupons();
                    }, 3000);
                } else {
                    var errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                    resultDiv.innerHTML = '<div class="notice notice-error"><p><strong><?php _e('ERROR:', 'yxjto-gateway'); ?></strong> ' + errorMessage + '</p></div>';
                }
            }).fail(function(xhr, status, error) {
                resultDiv.innerHTML = '<div class="notice notice-error"><p><strong><?php _e('REQUEST FAILED:', 'yxjto-gateway'); ?></strong> <?php _e('Please try again or check the server logs.', 'yxjto-gateway'); ?></p></div>';
            });
        }

        // SQL直接删除所有优惠券
        function sqlDeleteAllCoupons() {
            // 双重确认，因为这是不可逆的危险操作
            if (!confirm('<?php _e('⚠️ DANGER: This will PERMANENTLY DELETE ALL COUPONS from ALL DATABASES using direct SQL queries!', 'yxjto-gateway'); ?>')) {
                return;
            }
            
            if (!confirm('<?php _e('This action cannot be undone! Are you absolutely sure?', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Deleting...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_sql_delete_all_coupons',
                nonce: '<?php echo wp_create_nonce('yxjto_sql_delete_all_coupons'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('All coupons have been deleted from all databases!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    location.reload();
                } else {
                    alert('<?php _e('Failed to delete coupons:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 同步回收站状态
        function syncTrashStatus() {
            if (!confirm('<?php _e('Are you sure you want to sync trash status across all databases? This will ensure trashed coupons are consistent across all databases.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_sync_trash_status',
                nonce: '<?php echo wp_create_nonce('yxjto_sync_trash_status'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('Trash status synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    location.reload();
                } else {
                    alert('<?php _e('Trash status synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }
        </script>
        <?php
    }
    
    /**
     * Handle form submission
     */
    public static function handle_form_submission() {
        if (!isset($_POST['action']) || $_POST['action'] !== 'save_coupon_sync') {
            return;
        }
        
        if (!wp_verify_nonce($_POST['yxjto_gateway_coupon_sync_nonce'], 'yxjto_gateway_coupon_sync')) {
            wp_die(__('Security check failed', 'yxjto-gateway'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'yxjto-gateway'));
        }
        
        // 收集设置数据
        $coupon_settings = [
            'enable_coupon_sync' => !empty($_POST['enable_coupon_sync']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'realtime'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'daily'),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'newest'),
            'enable_sync_logging' => !empty($_POST['enable_sync_logging']),
            'exclude_expired' => !empty($_POST['exclude_expired']),
            'sync_usage_data' => !empty($_POST['sync_usage_data'])
        ];

        // 保存设置
        $result = WP_Multi_DB_Config_Manager::save_coupon_sync_settings($coupon_settings);

        if ($result) {
            add_settings_error('yxjto_gateway_coupon_sync', 'coupon_sync_updated', __('Coupon synchronization settings saved successfully!', 'yxjto-gateway'), 'updated');
            
            // 如果启用了批量同步，安排 WP Cron 任务
            if ($coupon_settings['enable_coupon_sync'] && $coupon_settings['sync_mode'] === 'batch') {
                self::schedule_coupon_batch_sync($coupon_settings['batch_interval']);
            } else {
                // 如果禁用了批量同步，清除现有的 Cron 任务
                wp_clear_scheduled_hook('yxjto_coupon_batch_sync');
            }
        } else {
            add_settings_error('yxjto_gateway_coupon_sync', 'coupon_sync_error', __('Failed to save coupon synchronization settings.', 'yxjto-gateway'), 'error');
        }
    }
    
    /**
     * Schedule coupon batch sync
     */
    private static function schedule_coupon_batch_sync($interval = 'daily') {
        // 清除现有任务
        wp_clear_scheduled_hook('yxjto_coupon_batch_sync');
        
        // 安排新任务
        if (!wp_next_scheduled('yxjto_coupon_batch_sync')) {
            wp_schedule_event(time(), $interval, 'yxjto_coupon_batch_sync');
        }
    }
    
    /**
     * AJAX: Manual coupon sync
     */
    public static function ajax_manual_coupon_sync() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_manual_coupon_sync')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $result = $coupon_sync->run_manual_sync();
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => sprintf(__('Synchronized %d coupons successfully.', 'yxjto-gateway'), $result['synced_count'])
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Coupon sync class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error running manual coupon sync: ', 'yxjto-gateway') . $e->getMessage());
        }
    }
    
    /**
     * AJAX: SQL delete all coupons
     */
    public static function ajax_sql_delete_all_coupons() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sql_delete_all_coupons')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $result = $coupon_sync->sql_delete_all_coupons();
                
                if ($result['success']) {
                    $message = sprintf(__('Deleted %d coupons from all databases successfully.', 'yxjto-gateway'), $result['total_deleted']);

                    // 添加详细结果
                    if (!empty($result['results'])) {
                        $details = [];
                        foreach ($result['results'] as $db_key => $db_result) {
                            if ($db_result['success']) {
                                $details[] = sprintf(__('%s: %d coupons deleted', 'yxjto-gateway'), $db_key, $db_result['deleted_count']);
                            } else {
                                $details[] = sprintf(__('%s: Failed - %s', 'yxjto-gateway'), $db_key, $db_result['error']);
                            }
                        }
                        $message .= '<br><br>' . __('Details:', 'yxjto-gateway') . '<br>' . implode('<br>', $details);
                    }

                    wp_send_json_success([
                        'message' => $message
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Coupon sync class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error deleting coupons: ', 'yxjto-gateway') . $e->getMessage());
        }
    }
    
    /**
     * AJAX: Sync trash status
     */
    public static function ajax_sync_trash_status() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sync_trash_status')) {
            wp_die('Security check failed');
        }
        
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        try {
            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $result = $coupon_sync->sync_trash_status();
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => sprintf(__('Synchronized trash status for %d coupons.', 'yxjto-gateway'), $result['synced_count'])
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Coupon sync class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error syncing trash status: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX: Cleanup trashed coupons
     */
    public static function ajax_cleanup_trashed_coupons() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_cleanup_trashed_coupons')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $result = $coupon_sync->cleanup_trashed_coupons();

                if ($result['success']) {
                    wp_send_json_success([
                        'message' => sprintf(__('Cleaned up %d trashed coupons.', 'yxjto-gateway'), $result['cleaned_count'])
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error('Coupon sync class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Cleanup failed: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Load coupons for deletion selection
     */
    public static function ajax_load_coupons_for_deletion() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_load_coupons_for_deletion')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $search = sanitize_text_field($_POST['search'] ?? '');

            // 获取优惠券
            $args = [
                'post_type' => 'shop_coupon',
                'post_status' => ['publish', 'draft', 'private'],
                'posts_per_page' => 100,
                'orderby' => 'title',
                'order' => 'ASC'
            ];

            if (!empty($search)) {
                $args['s'] = $search;
            }

            $coupons = get_posts($args);

            if (empty($coupons)) {
                wp_send_json_success([
                    'message' => __('No coupons found.', 'yxjto-gateway'),
                    'html' => '<p style="text-align: center; color: #666;">' . __('No coupons found.', 'yxjto-gateway') . '</p>'
                ]);
                return;
            }

            // 生成HTML
            $html = '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;">';

            foreach ($coupons as $coupon) {
                $coupon_obj = new WC_Coupon($coupon->ID);
                $coupon_code = $coupon_obj->get_code();
                $coupon_type = $coupon_obj->get_discount_type();
                $coupon_amount = $coupon_obj->get_amount();
                $usage_count = $coupon_obj->get_usage_count();
                $status = $coupon->post_status;

                $status_color = $status === 'publish' ? '#28a745' : ($status === 'draft' ? '#ffc107' : '#6c757d');
                $status_text = $status === 'publish' ? __('Active', 'yxjto-gateway') : ($status === 'draft' ? __('Draft', 'yxjto-gateway') : __('Private', 'yxjto-gateway'));

                $html .= '<div style="border: 1px solid #ddd; padding: 10px; border-radius: 4px; background: #f9f9f9;">';
                $html .= '<label style="display: block; cursor: pointer;">';
                $html .= '<input type="checkbox" value="' . $coupon->ID . '" data-code="' . esc_attr($coupon_code) . '" style="margin-right: 8px;">';
                $html .= '<strong>' . esc_html($coupon_code) . '</strong>';
                $html .= '<span style="color: ' . $status_color . '; font-size: 12px; margin-left: 8px;">(' . $status_text . ')</span>';
                $html .= '<br><small style="color: #666;">';
                $html .= __('Type:', 'yxjto-gateway') . ' ' . esc_html($coupon_type) . ' | ';
                $html .= __('Amount:', 'yxjto-gateway') . ' ' . esc_html($coupon_amount) . ' | ';
                $html .= __('Used:', 'yxjto-gateway') . ' ' . $usage_count . ' ' . __('times', 'yxjto-gateway');
                $html .= '</small>';
                $html .= '</label>';
                $html .= '</div>';
            }

            $html .= '</div>';

            wp_send_json_success([
                'message' => sprintf(__('Found %d coupons.', 'yxjto-gateway'), count($coupons)),
                'html' => $html
            ]);

        } catch (Exception $e) {
            wp_send_json_error('Failed to load coupons: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Delete selected coupons from all databases
     */
    public static function ajax_delete_selected_coupons() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_delete_selected_coupons')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $selected_coupons = $_POST['coupons'] ?? [];

            if (empty($selected_coupons)) {
                wp_send_json_error(__('No coupons selected.', 'yxjto-gateway'));
                return;
            }

            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $deleted_count = 0;
                $failed_count = 0;
                $results = [];

                foreach ($selected_coupons as $coupon_data) {
                    $coupon_id = intval($coupon_data['id']);
                    $coupon_code = sanitize_text_field($coupon_data['code']);

                    try {
                        // 从所有数据库删除优惠券（使用优惠券码）
                        $delete_results = $coupon_sync->delete_coupon_from_all_databases($coupon_code, $coupon_id);

                        // 从主数据库删除
                        wp_delete_post($coupon_id, true);

                        $deleted_count++;
                        $results[] = sprintf(__('Deleted coupon: %s', 'yxjto-gateway'), $coupon_code);

                    } catch (Exception $e) {
                        $failed_count++;
                        $results[] = sprintf(__('Failed to delete coupon %s: %s', 'yxjto-gateway'), $coupon_code, $e->getMessage());
                    }
                }

                $message = sprintf(__('Deletion completed. Success: %d, Failed: %d', 'yxjto-gateway'), $deleted_count, $failed_count);
                if (!empty($results)) {
                    $message .= '<br><br>' . __('Details:', 'yxjto-gateway') . '<br>' . implode('<br>', $results);
                }

                wp_send_json_success([
                    'message' => $message,
                    'deleted_count' => $deleted_count,
                    'failed_count' => $failed_count
                ]);
            } else {
                wp_send_json_error('Coupon sync class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Delete failed: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Delete selected coupons from ALL databases (including main database)
     */
    public static function ajax_delete_selected_coupons_all_databases() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_delete_selected_coupons_all_databases')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $selected_coupons = $_POST['coupons'] ?? [];

            if (empty($selected_coupons)) {
                wp_send_json_error(__('No coupons selected.', 'yxjto-gateway'));
                return;
            }

            if (class_exists('YXJTO_Coupon_Sync')) {
                $coupon_sync = YXJTO_Coupon_Sync::get_instance();
                $deleted_count = 0;
                $failed_count = 0;
                $results = [];
                $database_results = [];

                // 获取所有数据库配置
                if (class_exists('WP_Multi_DB_Config_Manager')) {
                    $databases = WP_Multi_DB_Config_Manager::get_databases();
                } else {
                    wp_send_json_error('Multi-database configuration manager not available');
                    return;
                }

                foreach ($selected_coupons as $coupon_data) {
                    $coupon_id = intval($coupon_data['id']);
                    $coupon_code = sanitize_text_field($coupon_data['code']);

                    try {
                        // 从所有数据库删除优惠券（使用优惠券码，现在包括当前数据库）
                        $delete_results = $coupon_sync->delete_coupon_from_all_databases($coupon_code, $coupon_id);

                        $coupon_deleted_successfully = false;

                        // 记录每个数据库的删除结果
                        foreach ($delete_results as $db_key => $db_result) {
                            if ($db_result['success']) {
                                $results[] = sprintf(__('Deleted coupon: %s from database %s', 'yxjto-gateway'), $coupon_code, $db_key);
                                $coupon_deleted_successfully = true;
                            } else {
                                $results[] = sprintf(__('Failed to delete coupon %s from database %s: %s', 'yxjto-gateway'), $coupon_code, $db_key, $db_result['error'] ?? 'Unknown error');
                                $failed_count++;
                            }
                        }

                        if ($coupon_deleted_successfully) {
                            $deleted_count++;
                        }

                    } catch (Exception $e) {
                        $failed_count++;
                        $results[] = sprintf(__('Failed to delete coupon %s: %s', 'yxjto-gateway'), $coupon_code, $e->getMessage());
                    }
                }

                // 构建详细的成功消息
                $total_databases = count($databases);
                $total_operations = count($selected_coupons) * $total_databases;
                $success_operations = $total_operations - $failed_count;

                $message = sprintf(
                    __('Deletion completed. Selected %d coupons from %d databases (including current database). Success: %d operations, Failed: %d operations', 'yxjto-gateway'),
                    count($selected_coupons),
                    $total_databases,
                    $success_operations,
                    $failed_count
                );

                if (!empty($results)) {
                    $message .= '<br><br>' . __('Detailed Results:', 'yxjto-gateway') . '<br>' . implode('<br>', array_slice($results, 0, 20)); // 限制显示前20条结果
                    if (count($results) > 20) {
                        $message .= '<br>' . sprintf(__('... and %d more results', 'yxjto-gateway'), count($results) - 20);
                    }
                }

                wp_send_json_success([
                    'message' => $message,
                    'deleted_count' => $deleted_count,
                    'failed_count' => $failed_count,
                    'total_operations' => $total_operations,
                    'success_operations' => $success_operations
                ]);
            } else {
                wp_send_json_error('Coupon sync class not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Delete from all databases failed: ' . $e->getMessage());
        }
    }
}

// Initialize the admin page
if (class_exists('YXJTO_Coupon_Sync_Admin')) {
    YXJTO_Coupon_Sync_Admin::init();
}
