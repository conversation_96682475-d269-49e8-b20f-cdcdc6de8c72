<?php
/**
 * 订单起始ID管理页面类
 * 
 * 管理所有数据库的订单起始ID
 * 
 * @package YXJTO_Gateway
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 订单起始ID管理页面类
 */
class YXJTO_Order_Manager_Admin {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        // 构造函数为私有，确保单例模式
    }
    
    /**
     * 渲染管理页面
     */
    public function render_admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Order Start ID Manager', 'yxjto-gateway'); ?></h1>
            <p><?php _e('Manage order starting IDs across all databases', 'yxjto-gateway'); ?></p>
            
            <?php settings_errors(); ?>
            
            <div id="poststuff">
                <div id="post-body" class="metabox-holder columns-1">
                    <div id="post-body-content">
                        
                        <!-- 订单起始ID列表 -->
                        <div class="postbox">
                            <h2 class="hndle"><span><?php _e('Database Order Start IDs', 'yxjto-gateway'); ?></span></h2>
                            <div class="inside">
                                <?php $this->render_order_start_ids(); ?>
                            </div>
                        </div>
                        
                        <!-- 批量设置 -->
                        <div class="postbox">
                            <h2 class="hndle"><span><?php _e('Batch Update', 'yxjto-gateway'); ?></span></h2>
                            <div class="inside">
                                <?php $this->render_batch_update(); ?>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
        <?php $this->enqueue_scripts(); ?>
        <?php
    }
    
    /**
     * 渲染订单起始ID列表
     */
    private function render_order_start_ids() {
        $start_ids = $this->get_all_order_start_ids();
        
        if (empty($start_ids)) {
            echo '<p>' . __('No databases configured.', 'yxjto-gateway') . '</p>';
            return;
        }
        
        ?>
        <table class="order-start-id-table">
            <thead>
                <tr>
                    <th><?php _e('Database Name', 'yxjto-gateway'); ?></th>
                    <th><?php _e('Status', 'yxjto-gateway'); ?></th>
                    <th><?php _e('Current Start ID', 'yxjto-gateway'); ?></th>
                    <th><?php _e('Current Max ID', 'yxjto-gateway'); ?></th>
                    <th><?php _e('New Start ID', 'yxjto-gateway'); ?></th>
                    <th><?php _e('Actions', 'yxjto-gateway'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($start_ids as $db_key => $data): ?>
                <tr data-database="<?php echo esc_attr($db_key); ?>">
                    <td><strong><?php echo esc_html($data['name']); ?></strong></td>
                    <td><span class="status-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></span></td>
                    <td><?php echo number_format($data['start_id']); ?></td>
                    <td><?php echo number_format($data['current_max_id']); ?></td>
                    <td>
                        <input type="number" 
                               class="order-start-id-input" 
                               min="1" 
                               step="1" 
                               value="<?php echo esc_attr($data['start_id']); ?>"
                               data-original="<?php echo esc_attr($data['start_id']); ?>"
                               id="start-id-<?php echo esc_attr($db_key); ?>">
                    </td>
                    <td>
                        <button type="button" 
                                class="update-button" 
                                onclick="updateOrderStartId('<?php echo esc_js($db_key); ?>')"
                                id="btn-<?php echo esc_attr($db_key); ?>">
                            <?php _e('Update', 'yxjto-gateway'); ?>
                        </button>
                        <span class="update-status" id="status-<?php echo esc_attr($db_key); ?>"></span>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * 渲染批量更新
     */
    private function render_batch_update() {
        ?>
        <div class="batch-update-section">
            <p><?php _e('Apply the same starting ID to all databases at once.', 'yxjto-gateway'); ?></p>
            
            <div class="batch-input-group">
                <label for="batch-start-id"><?php _e('New Start ID:', 'yxjto-gateway'); ?></label>
                <input type="number" 
                       id="batch-start-id" 
                       class="batch-input" 
                       min="1" 
                       step="1" 
                       placeholder="<?php _e('e.g., 10000', 'yxjto-gateway'); ?>">
                
                <button type="button" 
                        class="batch-button" 
                        onclick="batchUpdateStartIds()">
                    <?php _e('Update All', 'yxjto-gateway'); ?>
                </button>
                <span id="batch-status"></span>
            </div>
            
            <div class="batch-input-group">
                <label for="increment-start-id"><?php _e('Increment by:', 'yxjto-gateway'); ?></label>
                <input type="number" 
                       id="increment-start-id" 
                       class="batch-input" 
                       min="1" 
                       step="1" 
                       placeholder="<?php _e('e.g., 1000', 'yxjto-gateway'); ?>">
                
                <button type="button" 
                        class="batch-button" 
                        onclick="incrementAllStartIds()">
                    <?php _e('Increment All', 'yxjto-gateway'); ?>
                </button>
            </div>
        </div>
        <?php
    }
    
    /**
     * 获取所有数据库的订单起始ID
     */
    private function get_all_order_start_ids() {
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        $start_ids = array();
        
        foreach ($databases as $db_key => $db_config) {
            if ($db_config['enabled']) {
                $start_ids[$db_key] = array(
                    'name' => $db_config['name'] ?? $db_key,
                    'start_id' => $this->get_order_start_id($db_key),
                    'current_max_id' => $this->get_current_max_order_id($db_key)
                );
            }
        }
        
        return $start_ids;
    }
    
    /**
     * 获取订单起始ID
     */
    private function get_order_start_id($database) {
        $option_name = "yxjto_order_start_id_{$database}";
        return get_option($option_name, 10000); // 默认起始ID为10000
    }
    
    /**
     * 获取当前最大订单ID
     */
    private function get_current_max_order_id($database) {
        // 这里应该连接到指定数据库查询最大订单ID
        // 暂时返回模拟数据
        return rand(10000, 50000);
    }
    
    /**
     * 加载脚本和样式
     */
    private function enqueue_scripts() {
        ?>
        <style>
        .order-start-id-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .order-start-id-table th,
        .order-start-id-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .order-start-id-table th {
            background-color: #f1f1f1;
            font-weight: bold;
        }
        
        .order-start-id-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .order-start-id-input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .update-button {
            background-color: #0073aa;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 5px;
        }
        
        .update-button:hover {
            background-color: #005a87;
        }
        
        .update-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .status-enabled {
            color: #46b450;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #dc3232;
        }
        
        .batch-update-section {
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .batch-input-group {
            margin-bottom: 15px;
        }
        
        .batch-input-group label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        
        .batch-input {
            width: 150px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .batch-button {
            background-color: #46b450;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
        }
        
        .batch-button:hover {
            background-color: #3e9b47;
        }
        
        .success-message {
            color: #46b450;
            margin-left: 10px;
        }
        
        .error-message {
            color: #dc3232;
            margin-left: 10px;
        }
        
        .update-status {
            margin-left: 10px;
            font-weight: bold;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // 监听输入框变化，显示/隐藏更新按钮
            $('.order-start-id-input').on('input', function() {
                var original = $(this).data('original');
                var current = $(this).val();
                var database = $(this).closest('tr').data('database');
                var button = $('#btn-' + database);
                
                if (current != original && current !== '') {
                    button.prop('disabled', false).text('<?php _e('Update', 'yxjto-gateway'); ?>');
                } else {
                    button.prop('disabled', true).text('<?php _e('No changes', 'yxjto-gateway'); ?>');
                }
            });
        });
        
        function updateOrderStartId(database) {
            var input = document.getElementById('start-id-' + database);
            var button = document.getElementById('btn-' + database);
            var status = document.getElementById('status-' + database);
            var newStartId = input.value;
            
            if (!newStartId || newStartId < 1) {
                status.innerHTML = '<span class="error-message"><?php _e('Please enter a valid start ID', 'yxjto-gateway'); ?></span>';
                return;
            }
            
            button.disabled = true;
            button.textContent = '<?php _e('Updating...', 'yxjto-gateway'); ?>';
            status.innerHTML = '';
            
            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'yxjto_update_order_start_id',
                    database: database,
                    start_id: newStartId,
                    nonce: '<?php echo wp_create_nonce('yxjto_order_manager'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        status.innerHTML = '<span class="success-message"><?php _e('Updated successfully', 'yxjto-gateway'); ?></span>';
                        input.setAttribute('data-original', newStartId);
                        button.textContent = '<?php _e('No changes', 'yxjto-gateway'); ?>';
                        
                        setTimeout(function() {
                            status.innerHTML = '';
                        }, 3000);
                    } else {
                        status.innerHTML = '<span class="error-message">' + response.data + '</span>';
                        button.disabled = false;
                        button.textContent = '<?php _e('Update', 'yxjto-gateway'); ?>';
                    }
                },
                error: function() {
                    status.innerHTML = '<span class="error-message"><?php _e('Network error', 'yxjto-gateway'); ?></span>';
                    button.disabled = false;
                    button.textContent = '<?php _e('Update', 'yxjto-gateway'); ?>';
                }
            });
        }
        
        function batchUpdateStartIds() {
            var batchStartId = document.getElementById('batch-start-id').value;
            var status = document.getElementById('batch-status');
            
            if (!batchStartId || batchStartId < 1) {
                status.innerHTML = '<span class="error-message"><?php _e('Please enter a valid start ID', 'yxjto-gateway'); ?></span>';
                return;
            }
            
            // 更新所有输入框
            var inputs = document.querySelectorAll('.order-start-id-input');
            inputs.forEach(function(input) {
                input.value = batchStartId;
                // 触发input事件来更新按钮状态
                input.dispatchEvent(new Event('input'));
            });
            
            status.innerHTML = '<span class="success-message"><?php _e('All values updated, click individual Update buttons to save', 'yxjto-gateway'); ?></span>';
            
            setTimeout(function() {
                status.innerHTML = '';
            }, 5000);
        }
        
        function incrementAllStartIds() {
            var increment = parseInt(document.getElementById('increment-start-id').value);
            var status = document.getElementById('batch-status');
            
            if (!increment || increment < 1) {
                status.innerHTML = '<span class="error-message"><?php _e('Please enter a valid increment value', 'yxjto-gateway'); ?></span>';
                return;
            }
            
            // 为所有输入框增加数值
            var inputs = document.querySelectorAll('.order-start-id-input');
            inputs.forEach(function(input) {
                var currentValue = parseInt(input.value) || 0;
                input.value = currentValue + increment;
                // 触发input事件来更新按钮状态
                input.dispatchEvent(new Event('input'));
            });
            
            status.innerHTML = '<span class="success-message"><?php _e('All values incremented, click individual Update buttons to save', 'yxjto-gateway'); ?></span>';
            
            setTimeout(function() {
                status.innerHTML = '';
            }, 5000);
        }
        </script>
        <?php
    }
}
