<?php
/**
 * 测试运行器 - 在WordPress管理界面中运行测试
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Test_Runner {
    
    /**
     * 添加测试页面到管理菜单
     */
    public static function add_test_page() {
        add_submenu_page(
            'yxjto-gateway',
            '测试运行器',
            '测试运行器',
            'manage_options',
            'yxjto-test-runner',
            [self::class, 'render_test_page']
        );
    }
    
    /**
     * 渲染测试页面
     */
    public static function render_test_page() {
        // 处理测试运行请求
        if (isset($_POST['run_order_comparison_test']) && wp_verify_nonce($_POST['_wpnonce'], 'run_test')) {
            self::run_order_comparison_test();
            return;
        }
        
        ?>
        <div class="wrap">
            <h1>🧪 YXJTO 测试运行器</h1>
            
            <div class="card">
                <h2>订单比较功能测试</h2>
                <p>这个测试将验证跨数据库订单比较功能是否正常工作。</p>
                
                <form method="post">
                    <?php wp_nonce_field('run_test'); ?>
                    <input type="submit" name="run_order_comparison_test" class="button button-primary" value="🚀 运行订单比较测试">
                </form>
            </div>
            
            <div class="card">
                <h2>其他测试选项</h2>
                <p>更多测试功能正在开发中...</p>
            </div>
        </div>
        
        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        </style>
        <?php
    }
    
    /**
     * 运行订单比较测试
     */
    private static function run_order_comparison_test() {
        // 包含测试脚本
        require_once plugin_dir_path(__FILE__) . '../../test/order-comparison-test.php';
        
        ?>
        <div class="wrap">
            <h1>🧪 订单比较测试结果</h1>
            <div style="background: #f0f0f1; padding: 20px; border-radius: 4px; font-family: monospace;">
                <?php
                // 捕获输出
                ob_start();
                
                try {
                    $test = new YXJTO_Order_Comparison_Test();
                    $test->run_tests();
                } catch (Exception $e) {
                    echo "❌ 测试运行失败: " . $e->getMessage() . "\n";
                }
                
                $output = ob_get_clean();
                echo nl2br(esc_html($output));
                ?>
            </div>
            <br>
            <a href="<?php echo admin_url('admin.php?page=yxjto-test-runner'); ?>" class="button">« 返回测试页面</a>
        </div>
        <?php
    }
    
    /**
     * AJAX处理器：运行快速测试
     */
    public static function ajax_quick_test() {
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('权限不足');
        }
        
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_quick_test')) {
            wp_die('安全验证失败');
        }
        
        $test_type = sanitize_text_field($_POST['test_type']);
        
        ob_start();
        
        switch ($test_type) {
            case 'database_connections':
                self::test_database_connections();
                break;
            case 'database_switching':
                self::test_database_switching();
                break;
            case 'order_search':
                self::test_order_search();
                break;
            default:
                echo "未知测试类型: " . $test_type;
        }
        
        $output = ob_get_clean();
        
        wp_send_json_success([
            'output' => $output,
            'test_type' => $test_type
        ]);
    }
    
    /**
     * 测试数据库连接
     */
    private static function test_database_connections() {
        echo "🔗 测试数据库连接...\n";
        
        if (!class_exists('WP_Multi_DB_Config_Manager')) {
            echo "❌ WP_Multi_DB_Config_Manager 类不存在\n";
            return;
        }
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        
        if (empty($databases)) {
            echo "⚠️ 没有配置任何数据库\n";
            return;
        }
        
        foreach ($databases as $db_key => $db_config) {
            echo "测试数据库: {$db_key}\n";
            
            $connection = WP_Multi_DB_Config_Manager::get_connection($db_key);
            if ($connection) {
                echo "✅ {$db_key} 连接成功\n";
            } else {
                echo "❌ {$db_key} 连接失败\n";
            }
        }
    }
    
    /**
     * 测试数据库切换
     */
    private static function test_database_switching() {
        echo "🔄 测试数据库切换...\n";
        
        if (!class_exists('YXJTO_Gateway')) {
            echo "❌ YXJTO_Gateway 类不存在\n";
            return;
        }
        
        $gateway = YXJTO_Gateway::get_instance();
        $original_db = $gateway->get_current_database();
        
        echo "当前数据库: {$original_db}\n";
        
        $databases = WP_Multi_DB_Config_Manager::get_databases();
        
        foreach ($databases as $db_key => $db_config) {
            if ($db_key === $original_db) {
                continue;
            }
            
            echo "切换到数据库: {$db_key}\n";
            $result = $gateway->switch_database($db_key);
            
            if ($result) {
                echo "✅ 切换到 {$db_key} 成功\n";
                
                // 验证切换
                $current = $gateway->get_current_database();
                if ($current === $db_key) {
                    echo "✅ 数据库切换验证成功\n";
                } else {
                    echo "❌ 数据库切换验证失败\n";
                }
            } else {
                echo "❌ 切换到 {$db_key} 失败\n";
            }
        }
        
        // 恢复原始数据库
        $gateway->switch_database($original_db);
        echo "恢复到原始数据库: {$original_db}\n";
    }
    
    /**
     * 测试订单搜索
     */
    private static function test_order_search() {
        echo "🔍 测试订单搜索...\n";
        
        if (!class_exists('YXJTO_Order_Comparison')) {
            echo "❌ YXJTO_Order_Comparison 类不存在\n";
            return;
        }
        
        // 获取最近的几个订单ID进行测试
        $recent_orders = wc_get_orders([
            'limit' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
            'return' => 'ids'
        ]);
        
        if (empty($recent_orders)) {
            echo "⚠️ 没有找到任何订单用于测试\n";
            return;
        }
        
        $comparison = new YXJTO_Order_Comparison();
        
        foreach ($recent_orders as $order_id) {
            echo "搜索订单 ID: {$order_id}\n";
            
            $results = $comparison->search_order_across_databases($order_id);
            
            if (!empty($results)) {
                echo "✅ 在 " . count($results) . " 个数据库中找到订单 {$order_id}\n";
                foreach ($results as $db_key => $order_data) {
                    echo "  - 数据库: {$db_key}, 状态: {$order_data['status']}\n";
                }
            } else {
                echo "⚠️ 订单 {$order_id} 在其他数据库中未找到\n";
            }
        }
    }
}

// 注册AJAX处理器
add_action('wp_ajax_yxjto_quick_test', ['YXJTO_Test_Runner', 'ajax_quick_test']);
