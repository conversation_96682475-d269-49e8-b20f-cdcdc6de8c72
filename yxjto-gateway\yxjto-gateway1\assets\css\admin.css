/**
 * WordPress Multi Database Admin Styles
 */

.wp-multi-db-admin {
    margin-top: 20px;
}

/* 标签页样式 */
.nav-tab-wrapper {
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
}

.nav-tab {
    position: relative;
    display: inline-block;
    padding: 8px 12px;
    margin: 0;
    text-decoration: none;
    border: 1px solid transparent;
    border-bottom: none;
    background: #f1f1f1;
    color: #666;
    font-size: 12px;
    line-height: 16px;
}

.nav-tab:hover {
    background-color: #fff;
    color: #464646;
}

.nav-tab-active {
    background-color: #fff;
    border-color: #ccd0d4;
    border-bottom-color: #fff;
    color: #000;
}

/* 标签页内容 */
.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

/* 当前数据库显示 */
.current-database {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #00a32a;
    padding: 12px;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.current-database h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.current-database p {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #00a32a;
}

/* 数据库配置 */
.database-config {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.database-config h3 {
    margin: 0;
    padding: 12px 20px;
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.database-config h3 .button {
    margin-left: 8px;
    font-size: 11px;
    height: auto;
    padding: 4px 8px;
    line-height: 1.4;
}

.database-config .form-table {
    margin: 0;
    padding: 20px;
}

.database-config .form-table th {
    width: 150px;
    padding: 10px 0;
}

.database-config .form-table td {
    padding: 10px 0;
}

/* 连接状态 */
.connection-status {
    padding: 0 20px 20px;
}

.connection-status .notice {
    margin: 0;
    padding: 8px 12px;
}

.connection-status .spinner {
    float: none;
    margin: 0;
}

/* 规则配置 */
.ip-rule,
.url-rule {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.ip-rule h3,
.url-rule h3 {
    margin: 0;
    padding: 12px 20px;
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ip-rule h3 .button,
.url-rule h3 .button {
    margin-left: 8px;
    font-size: 11px;
    height: auto;
    padding: 4px 8px;
    line-height: 1.4;
}

.ip-rule .form-table,
.url-rule .form-table {
    margin: 0;
    padding: 20px;
}

.ip-rule .form-table th,
.url-rule .form-table th {
    width: 150px;
    padding: 10px 0;
}

.ip-rule .form-table td,
.url-rule .form-table td {
    padding: 10px 0;
}

/* 添加按钮 */
.add-database,
.add-ip-rule,
.add-url-rule {
    margin: 20px 0;
}

/* 状态页面 */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.status-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.status-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #23282d;
}

.status-card .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.status-card .status-item:last-child {
    border-bottom: none;
}

.status-card .status-label {
    font-weight: 500;
    color: #50575e;
}

.status-card .status-value {
    font-weight: 600;
}

.status-card .status-value.success {
    color: #00a32a;
}

.status-card .status-value.error {
    color: #d63638;
}

.status-card .status-value.warning {
    color: #dba617;
}

/* 日志表格 */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.logs-table th,
.logs-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
}

.logs-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #23282d;
}

.logs-table tr:hover {
    background: #f6f7f7;
}

.logs-table .log-success {
    color: #00a32a;
}

.logs-table .log-error {
    color: #d63638;
}

.logs-table .log-timestamp {
    white-space: nowrap;
    font-family: monospace;
    font-size: 12px;
}

.logs-table .log-ip {
    font-family: monospace;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 782px) {
    .database-config h3,
    .ip-rule h3,
    .url-rule h3 {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .database-config h3 .button,
    .ip-rule h3 .button,
    .url-rule h3 .button {
        margin-left: 0;
        margin-right: 8px;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .logs-table {
        font-size: 12px;
    }
    
    .logs-table th,
    .logs-table td {
        padding: 8px;
    }
}

/* 加载动画 */
.wp-multi-db-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.wp-multi-db-notice {
    padding: 12px;
    margin: 20px 0;
    border-left: 4px solid;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
}

.wp-multi-db-notice.success {
    border-left-color: #00a32a;
    background: #f0f6fc;
}

.wp-multi-db-notice.error {
    border-left-color: #d63638;
    background: #fcf0f1;
}

.wp-multi-db-notice.warning {
    border-left-color: #dba617;
    background: #fcf9e8;
}

.wp-multi-db-notice.info {
    border-left-color: #0073aa;
    background: #f0f6fc;
}

.wp-multi-db-notice p {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.wp-multi-db-notice p:last-child {
    margin-bottom: 0;
}

.wp-multi-db-notice strong {
    color: #23282d;
    font-weight: 600;
}

/* 优先级标识 */
.priority-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 8px;
}

.priority-indicator.highest {
    background: #d63638;
    color: #fff;
    animation: pulse 2s infinite;
}

.priority-indicator.high {
    background: #00a32a;
    color: #fff;
}

.priority-indicator.medium {
    background: #dba617;
    color: #fff;
}

.priority-indicator.low {
    background: #666;
    color: #fff;
}

/* 最高优先级的脉冲动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 工具提示 */
.wp-multi-db-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.wp-multi-db-tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #23282d;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    font-size: 12px;
    line-height: 1.4;
}

.wp-multi-db-tooltip:hover .tooltip-text {
    visibility: visible;
}

.wp-multi-db-tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #23282d transparent transparent transparent;
}

/* 税收同步样式 */
.tax-sync-actions {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.tax-sync-actions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    border-bottom: 1px solid #e6e6e6;
    padding-bottom: 10px;
}

.sync-buttons {
    margin-bottom: 20px;
}

.sync-buttons .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.sync-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.sync-status h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
}

.sync-status p {
    margin-bottom: 8px;
    font-size: 13px;
}

.status-enabled {
    color: #00a32a;
    font-weight: 600;
}

.status-disabled {
    color: #d63638;
    font-weight: 600;
}

#tax-sync-result-message {
    margin-top: 15px;
}

#tax-sync-result-message .notice {
    margin: 0;
}

/* 税收标签页特定样式 */
#tax-sync .form-table th {
    font-weight: 600;
    color: #23282d;
}

#tax-sync .form-table td fieldset label {
    display: block;
    margin-bottom: 8px;
    font-size: 13px;
}

#tax-sync .form-table td fieldset label input[type="checkbox"] {
    margin-right: 8px;
}

#tax-sync .tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

#tax-sync .tab-header h2 {
    margin: 0;
    font-size: 18px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sync-buttons .button {
        display: block;
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    #tax-sync .tab-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    #tax-sync .tab-header .button {
        margin-top: 10px;
    }
}
