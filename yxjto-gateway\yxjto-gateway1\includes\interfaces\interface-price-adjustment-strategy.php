<?php
/**
 * 价格调整策略接口
 * 定义不同的价格调整策略
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 价格调整策略接口
 */
interface YXJTO_Price_Adjustment_Strategy_Interface {
    
    /**
     * 调整商品价格
     * 
     * @param array $item_data 商品数据数组
     * @param float $target_total 目标总价
     * @param float $current_total 当前总价
     * @return array 调整后的商品数据
     */
    public function adjust_prices($item_data, $target_total, $current_total);
    
    /**
     * 获取策略名称
     * 
     * @return string 策略名称
     */
    public function get_strategy_name();
    
    /**
     * 验证策略是否适用
     * 
     * @param array $item_data 商品数据
     * @param float $difference 价格差异
     * @return bool 是否适用
     */
    public function is_applicable($item_data, $difference);
}

/**
 * 比例分配策略
 * 按商品原价比例分配差价
 */
class YXJTO_Proportional_Adjustment_Strategy implements YXJTO_Price_Adjustment_Strategy_Interface {
    
    /**
     * 最小商品价格
     */
    const MIN_ITEM_PRICE = 0.01;
    
    /**
     * 调整商品价格
     */
    public function adjust_prices($item_data, $target_total, $current_total) {
        $adjustment_ratio = $target_total / $current_total;
        $adjusted_items = [];
        $adjusted_total = 0;
        
        // 调整前N-1个商品
        for ($i = 0; $i < count($item_data) - 1; $i++) {
            $item = $item_data[$i];
            $new_total = $item['original_total'] * $adjustment_ratio;
            
            // 确保价格不低于最小值
            $new_total = max($new_total, self::MIN_ITEM_PRICE);
            
            $adjusted_items[] = array_merge($item, [
                'new_total' => $new_total,
                'new_unit_price' => $item['quantity'] > 0 ? $new_total / $item['quantity'] : $new_total
            ]);
            
            $adjusted_total += $new_total;
        }
        
        // 最后一个商品承担剩余差额
        if (count($item_data) > 0) {
            $last_item = $item_data[count($item_data) - 1];
            $remaining_amount = $target_total - $adjusted_total;
            
            // 确保最后一个商品价格合理
            $remaining_amount = max($remaining_amount, self::MIN_ITEM_PRICE);
            
            $adjusted_items[] = array_merge($last_item, [
                'new_total' => $remaining_amount,
                'new_unit_price' => $last_item['quantity'] > 0 ? $remaining_amount / $last_item['quantity'] : $remaining_amount
            ]);
        }
        
        return $adjusted_items;
    }
    
    /**
     * 获取策略名称
     */
    public function get_strategy_name() {
        return 'proportional';
    }
    
    /**
     * 验证策略是否适用
     */
    public function is_applicable($item_data, $difference) {
        // 比例分配策略适用于大多数情况
        return count($item_data) > 0;
    }
}

/**
 * 平均分配策略
 * 将差价平均分配到所有商品
 */
class YXJTO_Equal_Distribution_Strategy implements YXJTO_Price_Adjustment_Strategy_Interface {
    
    /**
     * 最小商品价格
     */
    const MIN_ITEM_PRICE = 0.01;
    
    /**
     * 调整商品价格
     */
    public function adjust_prices($item_data, $target_total, $current_total) {
        $difference = $target_total - $current_total;
        $per_item_adjustment = $difference / count($item_data);
        $adjusted_items = [];
        $adjusted_total = 0;
        
        // 调整前N-1个商品
        for ($i = 0; $i < count($item_data) - 1; $i++) {
            $item = $item_data[$i];
            $new_total = $item['original_total'] + $per_item_adjustment;
            
            // 确保价格不低于最小值
            $new_total = max($new_total, self::MIN_ITEM_PRICE);
            
            $adjusted_items[] = array_merge($item, [
                'new_total' => $new_total,
                'new_unit_price' => $item['quantity'] > 0 ? $new_total / $item['quantity'] : $new_total
            ]);
            
            $adjusted_total += $new_total;
        }
        
        // 最后一个商品承担剩余差额
        if (count($item_data) > 0) {
            $last_item = $item_data[count($item_data) - 1];
            $remaining_amount = $target_total - $adjusted_total;
            
            // 确保最后一个商品价格合理
            $remaining_amount = max($remaining_amount, self::MIN_ITEM_PRICE);
            
            $adjusted_items[] = array_merge($last_item, [
                'new_total' => $remaining_amount,
                'new_unit_price' => $last_item['quantity'] > 0 ? $remaining_amount / $last_item['quantity'] : $remaining_amount
            ]);
        }
        
        return $adjusted_items;
    }
    
    /**
     * 获取策略名称
     */
    public function get_strategy_name() {
        return 'equal_distribution';
    }
    
    /**
     * 验证策略是否适用
     */
    public function is_applicable($item_data, $difference) {
        // 平均分配策略适用于差价较小的情况
        $per_item_adjustment = abs($difference) / count($item_data);
        return $per_item_adjustment <= 10; // 每个商品调整不超过10元
    }
}

/**
 * 最高价商品优先策略
 * 优先调整价格最高的商品
 */
class YXJTO_Highest_Price_First_Strategy implements YXJTO_Price_Adjustment_Strategy_Interface {
    
    /**
     * 最小商品价格
     */
    const MIN_ITEM_PRICE = 0.01;
    
    /**
     * 调整商品价格
     */
    public function adjust_prices($item_data, $target_total, $current_total) {
        $difference = $target_total - $current_total;
        
        // 为每个商品添加原始索引，以便后续恢复顺序
        $indexed_items = [];
        foreach ($item_data as $index => $item) {
            $indexed_items[] = array_merge($item, ['original_index' => $index]);
        }
        
        // 按价格从高到低排序
        $sorted_items = $indexed_items;
        usort($sorted_items, function($a, $b) {
            return $b['original_total'] <=> $a['original_total'];
        });
        
        $adjusted_items = [];
        $remaining_difference = $difference;
        
        foreach ($sorted_items as $item) {
            $adjustment = 0;
            
            if ($remaining_difference != 0) {
                // 计算这个商品可以承担的最大调整量
                if ($remaining_difference > 0) {
                    // 正向调整，可以无限增加
                    $max_adjustment = $remaining_difference;
                } else {
                    // 负向调整，不能低于最小价格
                    $max_adjustment = max($remaining_difference, self::MIN_ITEM_PRICE - $item['original_total']);
                }
                
                $adjustment = $max_adjustment;
                $remaining_difference -= $adjustment;
            }
            
            $new_total = $item['original_total'] + $adjustment;
            $adjusted_items[] = array_merge($item, [
                'new_total' => $new_total,
                'new_unit_price' => $item['quantity'] > 0 ? $new_total / $item['quantity'] : $new_total
            ]);
        }
        
        // 恢复原始顺序
        usort($adjusted_items, function($a, $b) {
            return $a['original_index'] <=> $b['original_index'];
        });
        
        // 移除临时添加的原始索引
        foreach ($adjusted_items as &$item) {
            unset($item['original_index']);
        }
        
        return $adjusted_items;
    }
    
    /**
     * 获取策略名称
     */
    public function get_strategy_name() {
        return 'highest_price_first';
    }
    
    /**
     * 验证策略是否适用
     */
    public function is_applicable($item_data, $difference) {
        // 适用于有明显价格差异的商品组合
        if (count($item_data) < 2) {
            return false;
        }
        
        $prices = array_column($item_data, 'original_total');
        $max_price = max($prices);
        $min_price = min($prices);
        
        // 最高价和最低价差异超过2倍时适用
        return $max_price > $min_price * 2;
    }
}

/**
 * 价格调整策略工厂
 */
class YXJTO_Price_Adjustment_Strategy_Factory {
    
    /**
     * 可用的策略列表
     */
    private static $strategies = [
        'proportional' => 'YXJTO_Proportional_Adjustment_Strategy',
        'equal_distribution' => 'YXJTO_Equal_Distribution_Strategy',
        'highest_price_first' => 'YXJTO_Highest_Price_First_Strategy'
    ];
    
    /**
     * 创建策略实例
     * 
     * @param string $strategy_name 策略名称
     * @return YXJTO_Price_Adjustment_Strategy_Interface|null 策略实例
     */
    public static function create_strategy($strategy_name) {
        if (isset(self::$strategies[$strategy_name])) {
            $class_name = self::$strategies[$strategy_name];
            if (class_exists($class_name)) {
                return new $class_name();
            }
        }
        
        return null;
    }
    
    /**
     * 获取最适合的策略
     * 
     * @param array $item_data 商品数据
     * @param float $difference 价格差异
     * @return YXJTO_Price_Adjustment_Strategy_Interface 最适合的策略
     */
    public static function get_best_strategy($item_data, $difference) {
        foreach (self::$strategies as $name => $class_name) {
            $strategy = self::create_strategy($name);
            if ($strategy && $strategy->is_applicable($item_data, $difference)) {
                return $strategy;
            }
        }
        
        // 默认使用比例分配策略
        return self::create_strategy('proportional');
    }
    
    /**
     * 获取所有可用策略
     * 
     * @return array 策略列表
     */
    public static function get_available_strategies() {
        return array_keys(self::$strategies);
    }
}
