<?php
/**
 * PayPal 多账号网关账户管理类
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Accounts {

    private static $instance = null;
    private $config_manager;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // 不再使用数据库表，只使用配置文件
        // 初始化配置管理器
        $this->config_manager = YXJTO_PayPal_Multi_Gateway_Config::get_instance();
    }

    /**
     * 添加新账户
     */
    public function add_account($account_type, $account_data, $weight = 100) {
        $account_id = $this->generate_account_id($account_type, $account_data);

        $new_account_data = array(
            'account_id' => $account_id,
            'account_type' => $account_type,
            'account_data' => $account_data,
            'weight' => $weight,
            'status' => 'active',
            'success_count' => 0,
            'failure_count' => 0,
            'last_used' => null
        );

        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $result = WP_Multi_DB_Config_Manager::add_paypal_account($account_id, $new_account_data);
        } else {
            // 使用本地配置管理器
            $accounts = $this->config_manager->get_accounts();
            $accounts[$account_id] = array_merge($new_account_data, [
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ]);
            $result = $this->config_manager->save_accounts($accounts);
        }

        if ($result) {
            $this->clear_cache();
            return $account_id;
        }

        return false;
    }
    
    /**
     * Update account
     */
    public function update_account($account_id, $account_data, $weight = null) {
        $update_data = array(
            'account_data' => $account_data
        );

        if ($weight !== null) {
            $update_data['weight'] = $weight;
        }

        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $result = WP_Multi_DB_Config_Manager::update_paypal_account($account_id, $update_data);
        } else {
            // 使用本地配置管理器
            $accounts = $this->config_manager->get_accounts();

            // 检查账户是否存在
            if (!isset($accounts[$account_id])) {
                return false;
            }

            // 更新账户数据
            $accounts[$account_id]['account_data'] = $account_data;
            $accounts[$account_id]['updated_at'] = current_time('mysql');

            if ($weight !== null) {
                $accounts[$account_id]['weight'] = $weight;
            }

            $result = $this->config_manager->save_accounts($accounts);
        }

        if ($result) {
            $this->clear_cache();
            return true;
        }

        return false;
    }

    /**
     * Delete account
     */
    public function delete_account($account_id) {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $result = WP_Multi_DB_Config_Manager::delete_paypal_account($account_id);
        } else {
            // 使用本地配置管理器
            $accounts = $this->config_manager->get_accounts();

            // 检查账户是否存在
            if (!isset($accounts[$account_id])) {
                return false;
            }

            // 删除账户
            unset($accounts[$account_id]);

            $result = $this->config_manager->save_accounts($accounts);
        }

        if ($result) {
            $this->clear_cache();
            return true;
        }

        return false;
    }
    
    /**
     * Get all accounts
     */
    public function get_all_accounts() {
        $cache_key = 'yxjto_paypal_multi_gateway_all_accounts';
        $accounts = get_transient($cache_key);

        if (false === $accounts) {
            // 只使用配置管理器，不再使用数据库
            if (class_exists('WP_Multi_DB_Config_Manager')) {
                $accounts_data = WP_Multi_DB_Config_Manager::get_paypal_accounts();
            } else {
                $accounts_data = $this->config_manager->get_accounts();
            }

            // 转换为对象格式以保持兼容性
            $accounts = array();
            foreach ($accounts_data as $account_id => $account_info) {
                // 确保account_data是JSON字符串，以保持与数据库格式的兼容性
                if (isset($account_info['account_data']) && is_array($account_info['account_data'])) {
                    $account_info['account_data'] = wp_json_encode($account_info['account_data']);
                }
                $accounts[] = (object) $account_info;
            }

            set_transient($cache_key, $accounts, HOUR_IN_SECONDS);
        }

        return $accounts;
    }
    
    /**
     * Get active accounts
     */
    public function get_active_accounts() {
        $cache_key = 'yxjto_paypal_multi_gateway_active_accounts';
        $accounts = get_transient($cache_key);

        if (false === $accounts) {
            // 从PHP配置文件获取账户
            $accounts_data = $this->config_manager->get_accounts();

            // 过滤活跃账户并转换为对象格式
            $accounts = array();
            foreach ($accounts_data as $account_id => $account_info) {
                if (isset($account_info['status']) && $account_info['status'] === 'active') {
                    // 确保account_data是JSON字符串，以保持与数据库格式的兼容性
                    if (isset($account_info['account_data']) && is_array($account_info['account_data'])) {
                        $account_info['account_data'] = wp_json_encode($account_info['account_data']);
                    }
                    $accounts[] = (object) $account_info;
                }
            }

            // 按权重和成功次数排序
            usort($accounts, function($a, $b) {
                $weight_diff = ($b->weight ?? 100) - ($a->weight ?? 100);
                if ($weight_diff !== 0) {
                    return $weight_diff;
                }
                return ($b->success_count ?? 0) - ($a->success_count ?? 0);
            });

            set_transient($cache_key, $accounts, 15 * MINUTE_IN_SECONDS);
        }

        return $accounts;
    }
    
    /**
     * Select account based on load balancing strategy
     */
    public function select_account() {
        $active_accounts = $this->get_active_accounts();

        if (empty($active_accounts)) {
            return false;
        }

        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $method = $settings['load_balancing'];

        switch ($method) {
            case 'round_robin':
                return $this->select_round_robin($active_accounts);
            case 'weighted':
                return $this->select_weighted($active_accounts);
            case 'smart':
                return $this->select_smart($active_accounts);
            case 'random':
            default:
                return $this->select_random($active_accounts);
        }
    }

    /**
     * Select account for checkout (with simplified validation for email/url types)
     */
    public function select_account_for_checkout() {
        $active_accounts = $this->get_active_accounts();

        if (empty($active_accounts)) {
            return false;
        }

        // 过滤出有效的账户（邮箱和URL类型跳过深度验证）
        $valid_accounts = array();

        foreach ($active_accounts as $account) {
            $account_data = json_decode($account->account_data, true);

            if ($account->account_type === 'email') {
                // 邮箱类型：只检查邮箱格式
                if (!empty($account_data['email']) && is_email($account_data['email'])) {
                    $valid_accounts[] = $account;
                }
            } elseif ($account->account_type === 'paypal_me') {
                // PayPal.me 类型：只检查 URL 格式
                if (!empty($account_data['paypal_me_url']) && filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL)) {
                    $valid_accounts[] = $account;
                }
            } elseif ($account->account_type === 'api') {
                // API 类型：检查基本配置（不进行实际连接测试）
                if (!empty($account_data['client_id']) && !empty($account_data['client_secret'])) {
                    $valid_accounts[] = $account;
                }
            }
        }

        if (empty($valid_accounts)) {
            return false;
        }

        // 使用相同的负载均衡策略选择账户
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $method = $settings['load_balancing'];

        switch ($method) {
            case 'round_robin':
                return $this->select_round_robin($valid_accounts);
            case 'weighted':
                return $this->select_weighted($valid_accounts);
            case 'smart':
                return $this->select_smart($valid_accounts);
            case 'random':
            default:
                return $this->select_random($valid_accounts);
        }
    }
    
    /**
     * Random selection
     */
    private function select_random($accounts) {
        $random_index = array_rand($accounts);
        $selected = $accounts[$random_index];
        
        $this->update_last_used($selected->account_id);
        return $selected;
    }
    
    /**
     * Round robin selection
     */
    private function select_round_robin($accounts) {
        $last_used_account = get_option('yxjto_paypal_multi_gateway_last_used_account', '');
        
        // Find the next account after the last used one
        $found_last = false;
        foreach ($accounts as $account) {
            if ($found_last) {
                $this->update_last_used($account->account_id);
                update_option('yxjto_paypal_multi_gateway_last_used_account', $account->account_id);
                return $account;
            }
            
            if ($account->account_id === $last_used_account) {
                $found_last = true;
            }
        }
        
        // If we didn't find the last used account or reached the end, use the first one
        $selected = $accounts[0];
        $this->update_last_used($selected->account_id);
        update_option('yxjto_paypal_multi_gateway_last_used_account', $selected->account_id);
        return $selected;
    }
    
    /**
     * Weighted selection
     */
    private function select_weighted($accounts) {
        $total_weight = array_sum(array_column($accounts, 'weight'));
        $random_weight = mt_rand(1, $total_weight);
        
        $current_weight = 0;
        foreach ($accounts as $account) {
            $current_weight += $account->weight;
            if ($random_weight <= $current_weight) {
                $this->update_last_used($account->account_id);
                return $account;
            }
        }
        
        // Fallback to first account
        $selected = $accounts[0];
        $this->update_last_used($selected->account_id);
        return $selected;
    }
    
    /**
     * Smart selection based on performance
     */
    private function select_smart($accounts) {
        $best_account = null;
        $best_score = -1;

        foreach ($accounts as $account) {
            $score = $this->calculate_performance_score($account);
            if ($score > $best_score) {
                $best_score = $score;
                $best_account = $account;
            }
        }

        if ($best_account) {
            $this->update_last_used($best_account->account_id);
            return $best_account;
        }

        // Fallback to first account
        $selected = $accounts[0];
        $this->update_last_used($selected->account_id);
        return $selected;
    }

    /**
     * Calculate performance score for smart selection
     */
    private function calculate_performance_score($account) {
        $total_transactions = $account->success_count + $account->failure_count;

        if ($total_transactions === 0) {
            // New account gets medium priority
            return 50 + ($account->weight / 2);
        }

        $success_rate = ($account->success_count / $total_transactions) * 100;

        // Score based on success rate and weight
        $score = ($success_rate * 0.7) + ($account->weight * 0.3);

        // Penalty for recent failures
        $recent_failures = $this->get_recent_failures($account->account_id);
        $score -= ($recent_failures * 10);

        return max(0, $score);
    }

    /**
     * Get recent failures for an account
     * 现在通过配置文件管理，不再使用数据库
     */
    private function get_recent_failures($account_id) {
        // 从配置文件中获取失败计数
        $account = $this->get_account($account_id);
        return $account ? intval($account->failure_count) : 0;
    }

    /**
     * Update account status
     */
    public function update_account_status($account_id, $status) {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $result = WP_Multi_DB_Config_Manager::update_paypal_account_status($account_id, $status);
        } else {
            // 使用本地配置管理器
            $accounts = $this->config_manager->get_accounts();

            // 检查账户是否存在
            if (!isset($accounts[$account_id])) {
                return false;
            }

            // 更新账户状态
            $accounts[$account_id]['status'] = $status;
            $accounts[$account_id]['updated_at'] = current_time('mysql');

            $result = $this->config_manager->save_accounts($accounts);
        }

        if ($result) {
            $this->clear_cache();
            return true;
        }

        return false;
    }

    /**
     * Update last used timestamp
     */
    private function update_last_used($account_id) {
        // 通过配置管理器更新最后使用时间
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            WP_Multi_DB_Config_Manager::update_paypal_account($account_id, [
                'last_used' => current_time('mysql')
            ]);
        } else {
            $accounts = $this->config_manager->get_accounts();
            if (isset($accounts[$account_id])) {
                $accounts[$account_id]['last_used'] = current_time('mysql');
                $this->config_manager->save_accounts($accounts);
            }
        }
    }

    /**
     * Update success/failure counts
     */
    public function update_transaction_count($account_id, $success = true) {
        $field = $success ? 'success_count' : 'failure_count';

        // 通过配置管理器更新计数
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $account = WP_Multi_DB_Config_Manager::get_paypal_account($account_id);
            if ($account) {
                $current_count = isset($account[$field]) ? intval($account[$field]) : 0;
                WP_Multi_DB_Config_Manager::update_paypal_account($account_id, [
                    $field => $current_count + 1
                ]);
            }
        } else {
            $accounts = $this->config_manager->get_accounts();
            if (isset($accounts[$account_id])) {
                $current_count = isset($accounts[$account_id][$field]) ? intval($accounts[$account_id][$field]) : 0;
                $accounts[$account_id][$field] = $current_count + 1;
                $accounts[$account_id]['updated_at'] = current_time('mysql');
                $this->config_manager->save_accounts($accounts);
            }
        }

        $this->clear_cache();
    }

    /**
     * Generate unique account ID
     */
    private function generate_account_id($account_type, $account_data) {
        $identifier = '';

        switch ($account_type) {
            case 'email':
                $identifier = $account_data['email'];
                break;
            case 'paypal_me':
                $identifier = $account_data['paypal_me_url'];
                break;
            case 'api':
                $identifier = $account_data['client_id'];
                break;
        }

        return $account_type . '_' . md5($identifier . time());
    }

    /**
     * Get account by ID
     */
    public function get_account($account_id) {
        // 只使用配置管理器，不再使用数据库
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $account_info = WP_Multi_DB_Config_Manager::get_paypal_account($account_id);
            if ($account_info) {
                // 确保account_data是JSON字符串，以保持与数据库格式的兼容性
                if (isset($account_info['account_data']) && is_array($account_info['account_data'])) {
                    $account_info['account_data'] = wp_json_encode($account_info['account_data']);
                }
                return (object) $account_info;
            }
            return null;
        }

        // 使用本地配置管理器
        $accounts = $this->config_manager->get_accounts();

        if (isset($accounts[$account_id])) {
            $account_info = $accounts[$account_id];
            // 确保account_data是JSON字符串，以保持与数据库格式的兼容性
            if (isset($account_info['account_data']) && is_array($account_info['account_data'])) {
                $account_info['account_data'] = wp_json_encode($account_info['account_data']);
            }
            return (object) $account_info;
        }

        return null;
    }

    /**
     * Clear accounts cache
     */
    private function clear_cache() {
        delete_transient('yxjto_paypal_multi_gateway_all_accounts');
        delete_transient('yxjto_paypal_multi_gateway_active_accounts');
    }

    /**
     * Get account statistics (with caching)
     */
    public function get_account_stats($date_condition = '', $account_id = null) {
        // 生成缓存键
        $cache_key = 'yxjto_paypal_account_stats_' . md5($date_condition . '_' . ($account_id ?: 'all'));
        $cached_stats = get_transient($cache_key);

        if ($cached_stats !== false) {
            return $cached_stats;
        }

        // 直接执行统计查询，避免无限递归
        global $wpdb;
        $logs_table = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 在统计子查询中添加日期条件
        $stats_date_condition = $date_condition ? str_replace('AND', 'WHERE', $date_condition) : '';

        // 从配置文件获取账户数据
        $accounts_data = array();
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $accounts_data = WP_Multi_DB_Config_Manager::get_paypal_accounts();
        } else {
            $accounts_data = $this->config_manager->get_accounts();
        }

        // 如果指定了账户ID，只获取该账户
        if ($account_id && isset($accounts_data[$account_id])) {
            $accounts_data = array($account_id => $accounts_data[$account_id]);
        } elseif ($account_id && !isset($accounts_data[$account_id])) {
            return array(); // 账户不存在
        }

        // 获取所有账户的统计数据
        $stats_sql = "SELECT
            account_id,
            COUNT(*) as total_transactions,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_transactions,
            SUM(CASE WHEN status IN ('failed', 'error', 'denied', 'cancelled') THEN 1 ELSE 0 END) as failed_transactions,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_transactions,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
            AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_amount
        FROM {$logs_table}
        WHERE 1=1 {$date_condition}
        GROUP BY account_id";

        $stats_results = $wpdb->get_results($stats_sql, OBJECT_K);

        // 合并账户配置和统计数据
        $result = array();
        foreach ($accounts_data as $acc_id => $account_info) {
            $stats = isset($stats_results[$acc_id]) ? $stats_results[$acc_id] : null;

            $account_with_stats = (object) array(
                'account_id' => $acc_id,
                'account_type' => isset($account_info['account_type']) ? $account_info['account_type'] : '',
                'status' => isset($account_info['status']) ? $account_info['status'] : 'active',
                'weight' => isset($account_info['weight']) ? intval($account_info['weight']) : 100,
                'last_used' => isset($account_info['last_used']) ? $account_info['last_used'] : null,
                'total_transactions' => $stats ? intval($stats->total_transactions) : 0,
                'successful_transactions' => $stats ? intval($stats->successful_transactions) : 0,
                'failed_transactions' => $stats ? intval($stats->failed_transactions) : 0,
                'pending_transactions' => $stats ? intval($stats->pending_transactions) : 0,
                'total_amount' => $stats ? floatval($stats->total_amount) : 0,
                'avg_amount' => $stats ? floatval($stats->avg_amount) : 0,
                'success_rate' => $stats && $stats->total_transactions > 0
                    ? round(($stats->successful_transactions / $stats->total_transactions) * 100, 2)
                    : 0
            );

            $result[] = $account_with_stats;
        }

        // 按成功率和交易总数排序
        usort($result, function($a, $b) {
            if ($a->success_rate == $b->success_rate) {
                return $b->total_transactions - $a->total_transactions;
            }
            return $b->success_rate - $a->success_rate;
        });

        // 缓存结果 5 分钟
        set_transient($cache_key, $result, 5 * MINUTE_IN_SECONDS);

        return $result;
    }

    /**
     * Get accounts with statistics (separate method for compatibility)
     */
    public function get_accounts_with_stats($account_id = null, $days = 30) {
        // 构建日期条件
        $date_condition = '';
        if ($days > 0) {
            $date_condition = " AND created_at >= DATE_SUB(NOW(), INTERVAL {$days} DAY)";
        }

        // 直接调用get_account_stats方法，避免无限递归
        // 生成缓存键
        $cache_key = 'yxjto_paypal_account_stats_with_' . md5($date_condition . '_' . ($account_id ?: 'all'));
        $cached_stats = get_transient($cache_key);

        if ($cached_stats !== false) {
            return $cached_stats;
        }

        // 直接执行统计查询
        global $wpdb;
        $logs_table = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';

        // 从配置文件获取账户数据
        $accounts_data = array();
        if (class_exists('WP_Multi_DB_Config_Manager')) {
            $accounts_data = WP_Multi_DB_Config_Manager::get_paypal_accounts();
        } else {
            $accounts_data = $this->config_manager->get_accounts();
        }

        // 如果指定了账户ID，只获取该账户
        if ($account_id && isset($accounts_data[$account_id])) {
            $accounts_data = array($account_id => $accounts_data[$account_id]);
        } elseif ($account_id && !isset($accounts_data[$account_id])) {
            return array(); // 账户不存在
        }

        // 获取所有账户的统计数据
        $stats_sql = "SELECT
            account_id,
            COUNT(*) as total_transactions,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_transactions,
            SUM(CASE WHEN status IN ('failed', 'error', 'denied', 'cancelled') THEN 1 ELSE 0 END) as failed_transactions,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_transactions,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
            AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_amount
        FROM {$logs_table}
        WHERE 1=1 {$date_condition}
        GROUP BY account_id";

        $stats_results = $wpdb->get_results($stats_sql, OBJECT_K);

        // 合并账户配置和统计数据
        $result = array();
        foreach ($accounts_data as $acc_id => $account_info) {
            $stats = isset($stats_results[$acc_id]) ? $stats_results[$acc_id] : null;

            $account_with_stats = (object) array(
                'account_id' => $acc_id,
                'account_type' => isset($account_info['account_type']) ? $account_info['account_type'] : '',
                'status' => isset($account_info['status']) ? $account_info['status'] : 'active',
                'weight' => isset($account_info['weight']) ? intval($account_info['weight']) : 100,
                'last_used' => isset($account_info['last_used']) ? $account_info['last_used'] : null,
                'total_transactions' => $stats ? intval($stats->total_transactions) : 0,
                'successful_transactions' => $stats ? intval($stats->successful_transactions) : 0,
                'failed_transactions' => $stats ? intval($stats->failed_transactions) : 0,
                'pending_transactions' => $stats ? intval($stats->pending_transactions) : 0,
                'total_amount' => $stats ? floatval($stats->total_amount) : 0,
                'avg_amount' => $stats ? floatval($stats->avg_amount) : 0,
                'success_rate' => $stats && $stats->total_transactions > 0
                    ? round(($stats->successful_transactions / $stats->total_transactions) * 100, 2)
                    : 0
            );

            $result[] = $account_with_stats;
        }

        // 按成功率和交易总数排序
        usort($result, function($a, $b) {
            if ($a->success_rate == $b->success_rate) {
                return $b->total_transactions - $a->total_transactions;
            }
            return $b->success_rate - $a->success_rate;
        });

        // 缓存结果 5 分钟
        set_transient($cache_key, $result, 5 * MINUTE_IN_SECONDS);

        return $result;
    }

    /**
     * Validate account configuration
     */
    public function validate_account($account_type, $account_data) {
        $errors = array();

        switch ($account_type) {
            case 'email':
                if (empty($account_data['email']) || !is_email($account_data['email'])) {
                    $errors[] = __('Valid PayPal email is required.', 'yxjto-paypal-multi-gateway');
                }
                break;

            case 'paypal_me':
                if (empty($account_data['paypal_me_url']) || !filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL)) {
                    $errors[] = __('Valid PayPal.me URL is required.', 'yxjto-paypal-multi-gateway');
                }
                break;

            case 'api':
                if (empty($account_data['client_id'])) {
                    $errors[] = __('PayPal Client ID is required.', 'yxjto-paypal-multi-gateway');
                }
                if (empty($account_data['client_secret'])) {
                    $errors[] = __('PayPal Client Secret is required.', 'yxjto-paypal-multi-gateway');
                }
                break;

            default:
                $errors[] = __('Invalid account type.', 'yxjto-paypal-multi-gateway');
        }

        return $errors;
    }

    /**
     * 获取全局账户指针
     */
    public function get_global_account_pointer() {
        return get_option('yxjto_paypal_multi_gateway_global_account_pointer', 0);
    }

    /**
     * 设置全局账户指针
     */
    public function set_global_account_pointer($pointer) {
        update_option('yxjto_paypal_multi_gateway_global_account_pointer', intval($pointer));
    }

    /**
     * 获取下一个账户（全局轮询）
     */
    public function get_next_global_account() {
        $active_accounts = $this->get_active_accounts();

        if (empty($active_accounts)) {
            return null;
        }

        // 获取当前全局指针
        $current_pointer = $this->get_global_account_pointer();

        // 确保指针在有效范围内
        if ($current_pointer >= count($active_accounts)) {
            $current_pointer = 0;
            $this->set_global_account_pointer($current_pointer);
        }

        // 获取当前账户
        $selected_account = $active_accounts[$current_pointer];

        // 立即更新全局指针到下一个位置（每次访问都移动）
        $next_pointer = ($current_pointer + 1) % count($active_accounts);
        $this->set_global_account_pointer($next_pointer);

        // 记录账户使用时间
        update_option("yxjto_paypal_multi_gateway_account_{$selected_account->account_id}_last_used", time());

        // 记录负载均衡日志
        error_log("PayPal Multi Gateway: 负载均衡选择账户ID {$selected_account->account_id}，指针从 {$current_pointer} 移动到 {$next_pointer}");

        return $selected_account;
    }

    /**
     * 获取当前全局账户（不移动指针）
     */
    public function get_current_global_account() {
        $active_accounts = $this->get_active_accounts();

        if (empty($active_accounts)) {
            return null;
        }

        // 获取当前全局指针
        $current_pointer = $this->get_global_account_pointer();

        // 确保指针在有效范围内
        if ($current_pointer >= count($active_accounts)) {
            $current_pointer = 0;
            $this->set_global_account_pointer($current_pointer);
        }

        return $active_accounts[$current_pointer];
    }

    /**
     * 根据ID获取账户（用于支付时）
     */
    public function get_account_for_payment($account_id) {
        // 调试日志
        error_log("PayPal Multi Gateway [Accounts Debug]: get_account_for_payment called with ID: {$account_id}");

        // 从PHP配置文件获取账户（而不是数据库）
        $accounts_data = $this->config_manager->get_accounts();

        if (isset($accounts_data[$account_id])) {
            $account_info = $accounts_data[$account_id];

            // 检查账户状态
            if (isset($account_info['status']) && $account_info['status'] === 'active') {
                // 确保account_data是JSON字符串，以保持与数据库格式的兼容性
                if (isset($account_info['account_data']) && is_array($account_info['account_data'])) {
                    $account_info['account_data'] = wp_json_encode($account_info['account_data']);
                }

                $account = (object) $account_info;
                error_log("PayPal Multi Gateway [Accounts Debug]: Found account for payment: {$account->account_id}");
                return $account;
            } else {
                error_log("PayPal Multi Gateway [Accounts Debug]: Account {$account_id} is not active");
            }
        } else {
            error_log("PayPal Multi Gateway [Accounts Debug]: Account {$account_id} not found in config");
        }

        error_log("PayPal Multi Gateway: 支付时未找到账户ID {$account_id}");
        return null;
    }

    /**
     * 为结账选择账户（基于负载均衡方法）
     */
    public function select_account_for_checkout_with_load_balancing() {
        $active_accounts = $this->get_active_accounts();

        if (empty($active_accounts)) {
            return null;
        }

        // 获取负载均衡设置
        $settings = YXJTO_PayPal_Multi_Gateway_Core::get_settings();
        $load_balancing_method = $settings['load_balancing'] ?? 'round_robin';

        switch ($load_balancing_method) {
            case 'round_robin':
                return $this->get_next_global_account();

            case 'random':
                return $this->select_random_account($active_accounts);

            case 'weighted':
                return $this->select_weighted_account($active_accounts);

            case 'smart':
                return $this->select_smart_account($active_accounts);

            default:
                // 默认使用轮询
                return $this->get_next_global_account();
        }
    }

    /**
     * 随机选择账户（不移动指针）
     */
    private function select_random_account($accounts) {
        $random_index = array_rand($accounts);
        $selected_account = $accounts[$random_index];

        error_log("PayPal Multi Gateway: 随机选择账户ID {$selected_account->account_id}");
        return $selected_account;
    }

    /**
     * 权重选择账户
     */
    private function select_weighted_account($accounts) {
        $total_weight = 0;
        $weights = array();

        foreach ($accounts as $account) {
            $weight = max(1, intval($account->weight));
            $weights[] = $weight;
            $total_weight += $weight;
        }

        $random = mt_rand(1, $total_weight);
        $current_weight = 0;

        for ($i = 0; $i < count($accounts); $i++) {
            $current_weight += $weights[$i];
            if ($random <= $current_weight) {
                $selected_account = $accounts[$i];
                error_log("PayPal Multi Gateway: 权重选择账户ID {$selected_account->account_id}");
                return $selected_account;
            }
        }

        // 备用：返回第一个账户
        return $accounts[0];
    }

    /**
     * 智能选择账户
     */
    private function select_smart_account($accounts) {
        // 简化版智能选择：基于使用频率和权重
        $best_account = null;
        $best_score = -1;

        foreach ($accounts as $account) {
            $score = $this->calculate_account_score($account);

            if ($score > $best_score) {
                $best_score = $score;
                $best_account = $account;
            }
        }

        if ($best_account) {
            error_log("PayPal Multi Gateway: 智能选择账户ID {$best_account->account_id}，评分: {$best_score}");
            return $best_account;
        }

        return $accounts[0];
    }

    /**
     * 计算账户评分
     */
    private function calculate_account_score($account) {
        $score = 100;

        // 基于权重
        $score += intval($account->weight) - 100;

        // 基于最近使用时间（越久未使用评分越高）
        $last_used = get_option("yxjto_paypal_multi_gateway_account_{$account->account_id}_last_used", 0);
        $time_since_used = time() - $last_used;
        $score += min(50, $time_since_used / 3600); // 每小时加1分，最多50分

        return $score;
    }
}
