<?php
/**
 * 核心功能类
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_PayPal_Multi_Gateway_Core {

    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->init_hooks();
    }

    private function init_hooks() {
        // REST API 端点
        add_action('rest_api_init', array($this, 'register_rest_routes'));

        // AJAX 处理器
        add_action('wp_ajax_yxjto_paypal_multi_gateway_test_account', array($this, 'test_account'));
        add_action('wp_ajax_yxjto_paypal_multi_gateway_get_stats', array($this, 'get_stats'));

        // Cron jobs
        add_action('yxjto_paypal_multi_gateway_health_check', array($this, 'health_check'));
        add_action('yxjto_paypal_multi_gateway_auto_cleanup', array($this, 'auto_cleanup_logs'));

        // Schedule health check if not already scheduled
        if (!wp_next_scheduled('yxjto_paypal_multi_gateway_health_check')) {
            wp_schedule_event(time(), 'hourly', 'yxjto_paypal_multi_gateway_health_check');
        }

        // Schedule auto cleanup if enabled
        $auto_cleanup_enabled = get_option('yxjto_paypal_multi_gateway_auto_cleanup_enabled', false);
        if ($auto_cleanup_enabled && !wp_next_scheduled('yxjto_paypal_multi_gateway_auto_cleanup')) {
            wp_schedule_event(time(), 'daily', 'yxjto_paypal_multi_gateway_auto_cleanup');
        }
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        register_rest_route('paypal-multi-gateway/v1', '/webhook', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_webhook'),
            'permission_callback' => array($this, 'webhook_permission_check'),
        ));
        
        register_rest_route('paypal-multi-gateway/v1', '/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_status'),
            'permission_callback' => '__return_true',
        ));
    }
    
    /**
     * Handle webhook requests
     */
    public function handle_webhook($request) {
        $webhook_handler = new YXJTO_PayPal_Multi_Gateway_Webhook();
        return $webhook_handler->process_webhook($request);
    }

    /**
     * Webhook permission check
     */
    public function webhook_permission_check($request) {
        // Verify PayPal webhook signature
        $security = new YXJTO_PayPal_Multi_Gateway_Security();
        return $security->verify_webhook_signature($request);
    }
    
    /**
     * Get plugin status
     */
    public function get_status($request) {
        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $active_accounts = $accounts->get_active_accounts();

        return new WP_REST_Response(array(
            'status' => 'active',
            'version' => YXJTO_PAYPAL_MULTI_GATEWAY_VERSION,
            'active_accounts' => count($active_accounts),
            'last_health_check' => get_option('yxjto_paypal_multi_gateway_last_health_check', 'never')
        ), 200);
    }
    
    /**
     * Test account connection (AJAX)
     */
    public function test_account() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'yxjto-paypal-multi-gateway'));
        }

        $account_type = sanitize_text_field($_POST['account_type']);
        $account_data = array();

        if (empty($account_type)) {
            wp_send_json_error(__('Please select account type.', 'yxjto-paypal-multi-gateway'));
        }

        switch ($account_type) {
            case 'email':
                if (empty($_POST['email'])) {
                    wp_send_json_error(__('Please enter email address.', 'yxjto-paypal-multi-gateway'));
                }
                $account_data['email'] = sanitize_email($_POST['email']);
                if (!is_email($account_data['email'])) {
                    wp_send_json_error(__('Please enter a valid email address.', 'yxjto-paypal-multi-gateway'));
                }
                break;
            case 'paypal_me':
                if (empty($_POST['paypal_me_url'])) {
                    wp_send_json_error(__('Please enter PayPal.me URL.', 'yxjto-paypal-multi-gateway'));
                }
                $account_data['paypal_me_url'] = esc_url_raw($_POST['paypal_me_url']);
                if (!filter_var($account_data['paypal_me_url'], FILTER_VALIDATE_URL)) {
                    wp_send_json_error(__('Please enter a valid URL.', 'yxjto-paypal-multi-gateway'));
                }
                break;
            case 'api':
                if (empty($_POST['client_id'])) {
                    wp_send_json_error(__('Please enter client ID.', 'yxjto-paypal-multi-gateway'));
                }
                if (empty($_POST['client_secret'])) {
                    wp_send_json_error(__('Please enter client secret.', 'yxjto-paypal-multi-gateway'));
                }
                $account_data['client_id'] = sanitize_text_field($_POST['client_id']);
                $account_data['client_secret'] = sanitize_text_field($_POST['client_secret']);
                $account_data['sandbox'] = isset($_POST['sandbox']) && ($_POST['sandbox'] === 'yes' || $_POST['sandbox'] === '1');
                break;
            default:
                wp_send_json_error(__('Invalid account type.', 'yxjto-paypal-multi-gateway'));
        }

        $api = new YXJTO_PayPal_Multi_Gateway_API();
        $result = $api->test_account($account_type, $account_data);

        wp_send_json($result);
    }
    
    /**
     * Get statistics (AJAX)
     */
    public function get_stats() {
        check_ajax_referer('yxjto_paypal_multi_gateway_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'yxjto-paypal-multi-gateway'));
        }
        
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';
        
        // Get stats for last 30 days
        $stats = $wpdb->get_results($wpdb->prepare("
            SELECT 
                account_id,
                COUNT(*) as total_transactions,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_transactions,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_transactions,
                SUM(amount) as total_amount
            FROM {$table_name} 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY account_id
        "));
        
        wp_send_json_success($stats);
    }
    
    /**
     * Health check for all accounts
     */
    public function health_check() {
        $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
        $all_accounts = $accounts->get_all_accounts();

        $api = new YXJTO_PayPal_Multi_Gateway_API();

        foreach ($all_accounts as $account) {
            $account_data = json_decode($account->account_data, true);
            $result = $api->test_account($account->account_type, $account_data);

            if ($result['success']) {
                $accounts->update_account_status($account->account_id, 'active');
            } else {
                $accounts->update_account_status($account->account_id, 'inactive');
            }
        }
        
        update_option('yxjto_paypal_multi_gateway_last_health_check', current_time('mysql'));
    }
    
    /**
     * Get plugin settings
     */
    public static function get_settings() {
        // 默认使用PHP配置文件存储
        $config_manager = YXJTO_PayPal_Multi_Gateway_Config::get_instance();
        $settings = $config_manager->get_settings();

        // 如果PHP配置文件中没有设置，使用默认值
        $defaults = array(
            'load_balancing' => 'random',
            'retry_count' => 1,
            'timeout' => 30,
            'test_mode' => 'no'
        );

        return array_merge($defaults, $settings);
    }

    /**
     * Update plugin settings
     */
    public static function update_settings($settings) {
        // 默认使用PHP配置文件存储
        $config_manager = YXJTO_PayPal_Multi_Gateway_Config::get_instance();
        return $config_manager->update_settings($settings);
    }

    /**
     * 检查是否启用PHP配置文件存储
     */
    public static function is_php_config_enabled() {
        // 默认启用PHP配置文件存储
        return true;
    }

    /**
     * 启用PHP配置文件存储
     */
    public static function enable_php_config() {
        update_option('yxjto_paypal_multi_gateway_use_php_config', 'yes');

        // 如果还没有定义常量，可以在下次加载时生效
        if (!defined('YXJTO_PAYPAL_MULTI_GATEWAY_USE_PHP_CONFIG')) {
            define('YXJTO_PAYPAL_MULTI_GATEWAY_USE_PHP_CONFIG', true);
        }
    }

    /**
     * 禁用PHP配置文件存储
     */
    public static function disable_php_config() {
        update_option('yxjto_paypal_multi_gateway_use_php_config', 'no');
    }
    
    /**
     * Get available load balancing methods
     */
    public static function get_load_balancing_methods() {
        return array(
            'random' => __('Random Selection', 'yxjto-paypal-multi-gateway'),
            'round_robin' => __('Round Robin', 'yxjto-paypal-multi-gateway'),
            'weighted' => __('Weighted Distribution', 'yxjto-paypal-multi-gateway'),
            'smart' => __('Smart Selection (Based on Performance)', 'yxjto-paypal-multi-gateway')
        );
    }
    
    /**
     * Plugin activation tasks
     */
    public static function activate() {
        // 创建交易日志表（账户配置现在通过配置文件管理）
        self::create_database_tables();

        // 添加数据库索引
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Admin')) {
            $admin = new YXJTO_PayPal_Multi_Gateway_Admin();
            $admin->add_database_indexes();
        }

        // 设置默认选项
        self::set_default_options();

        // 清除缓存
        wp_cache_flush();
    }

    /**
     * 创建数据库表（只创建交易日志表，账户配置使用配置文件）
     */
    private static function create_database_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // 交易日志表（保留用于记录交易记录）
        $logs_table = $wpdb->prefix . 'yxjto_paypal_multi_gateway_logs';
        $logs_sql = "CREATE TABLE $logs_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            transaction_id varchar(255) NOT NULL,
            order_id bigint(20) unsigned DEFAULT NULL,
            account_id varchar(255) NOT NULL,
            amount decimal(10,2) NOT NULL DEFAULT '0.00',
            currency varchar(3) NOT NULL DEFAULT 'USD',
            status varchar(50) NOT NULL,
            gateway_response longtext,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_transaction_id (transaction_id),
            KEY idx_order_id (order_id),
            KEY idx_account_id (account_id),
            KEY idx_status (status),
            KEY idx_created_at (created_at),
            KEY idx_account_status (account_id, status),
            KEY idx_status_created (status, created_at),
            KEY idx_account_created (account_id, created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($logs_sql);
    }

    /**
     * 设置默认选项
     */
    private static function set_default_options() {
        $default_settings = array(
            'testmode' => 'yes',
            'load_balancing_strategy' => 'round_robin',
            'enable_logging' => 'yes',
            'log_level' => 'info',
            'cache_duration' => 300, // 5 minutes
            'max_retries' => 3,
            'timeout' => 30
        );

        foreach ($default_settings as $key => $value) {
            $option_name = 'yxjto_paypal_multi_gateway_' . $key;
            if (get_option($option_name) === false) {
                add_option($option_name, $value);
            }
        }
    }

    /**
     * 自动清理日志的定时任务
     */
    public function auto_cleanup_logs() {
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Admin')) {
            $admin = YXJTO_PayPal_Multi_Gateway_Admin::get_instance();
            $admin->auto_cleanup_logs();
        }
    }

    /**
     * Clean up on plugin deactivation
     */
    public static function cleanup() {
        // Clear scheduled events
        wp_clear_scheduled_hook('yxjto_paypal_multi_gateway_health_check');
        wp_clear_scheduled_hook('yxjto_paypal_multi_gateway_auto_cleanup');

        // Clean up transients
        delete_transient('yxjto_paypal_multi_gateway_accounts_cache');
        delete_transient('yxjto_paypal_multi_gateway_stats_cache');

        // Clear statistics cache
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Admin')) {
            $admin = new YXJTO_PayPal_Multi_Gateway_Admin();
            $admin->clear_statistics_cache();
        }
    }
}
