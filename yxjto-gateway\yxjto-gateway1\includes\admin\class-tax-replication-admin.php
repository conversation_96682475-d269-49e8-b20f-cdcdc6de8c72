<?php
/**
 * 税收复制设置页面
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Tax_Replication_Admin {
    
    public function __construct() {
        // 处理表单提交
        add_action('admin_post_save_tax_replication_settings', array($this, 'handle_form_submission'));
    }

    /**
     * 加载脚本和样式
     */
    private static function enqueue_scripts() {
        // 加载jQuery和自定义JavaScript
        wp_enqueue_script('jquery');
        wp_enqueue_script('tax-replication-admin', YXJTO_GATEWAY_PLUGIN_URL . 'assets/js/tax-replication-admin.js', array('jquery'), '1.0.0', true);
        
        // 向JavaScript传递AJAX URL和nonce
        wp_localize_script('tax-replication-admin', 'taxReplicationAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_tax_replication_nonce')
        ));
    }

    /**
     * 渲染管理页面
     */
    public static function render_admin_page() {
        // 检查用户权限
        if (!current_user_can('manage_options')) {
            wp_die(__('您没有足够的权限访问此页面。', 'yxjto-gateway'));
        }

        // 加载CSS和JavaScript
        self::enqueue_scripts();

        // 处理表单提交
        if (isset($_POST['action']) && $_POST['action'] === 'save_tax_replication_settings') {
            self::handle_form_submission();
        }

        // 获取当前设置
        $tax_settings = WP_Multi_DB_Config_Manager::get_tax_replication_settings();
        
        ?>
        <div class="wrap">
            <div class="wp-multi-db-admin">
                <h1><?php _e('税收复制设置', 'yxjto-gateway'); ?></h1>
                
                <form method="post" action="" id="tax-replication-form">
                    <?php wp_nonce_field('yxjto_gateway_tax_replication', 'yxjto_gateway_tax_replication_nonce'); ?>
                    <input type="hidden" name="action" value="save_tax_replication_settings" />

                    <div class="tab-header">
                        <h2><?php _e('税收复制配置', 'yxjto-gateway'); ?></h2>
                        <button type="submit" class="button button-primary"><?php _e('保存设置', 'yxjto-gateway'); ?></button>
                    </div>
                    
                    <p class="description">
                        <?php _e('配置税率、税收类别和税收设置在多个数据库之间的同步。包括标准费率、减税税率、零税率等完整的复制。', 'yxjto-gateway'); ?>
                    </p>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('启用税收复制', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_tax_replication" value="1" 
                                           <?php checked(!empty($tax_settings['enable_tax_replication'])); ?> />
                                    <?php _e('启用税收数据在所有数据库之间的自动复制', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('启用后，税率、税收类别和税收设置将自动同步到所有配置的数据库。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('税率复制选项', 'yxjto-gateway'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="replicate_standard_rates" value="1" 
                                               <?php checked(!empty($tax_settings['replicate_standard_rates'])); ?> />
                                        <?php _e('标准税率', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="replicate_reduced_rates" value="1" 
                                               <?php checked(!empty($tax_settings['replicate_reduced_rates'])); ?> />
                                        <?php _e('减税税率', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="replicate_zero_rates" value="1" 
                                               <?php checked(!empty($tax_settings['replicate_zero_rates'])); ?> />
                                        <?php _e('零税率', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="replicate_tax_classes" value="1" 
                                               <?php checked(!empty($tax_settings['replicate_tax_classes'])); ?> />
                                        <?php _e('税收类别', 'yxjto-gateway'); ?>
                                    </label>
                                </fieldset>
                                <p class="description">
                                    <?php _e('选择要复制的税率类型。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('税收设置同步', 'yxjto-gateway'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="sync_tax_options" value="1" 
                                               <?php checked(!empty($tax_settings['sync_tax_options'])); ?> />
                                        <?php _e('同步税收相关选项', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="sync_tax_display" value="1" 
                                               <?php checked(!empty($tax_settings['sync_tax_display'])); ?> />
                                        <?php _e('同步税收显示设置', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="sync_tax_rounding" value="1" 
                                               <?php checked(!empty($tax_settings['sync_tax_rounding'])); ?> />
                                        <?php _e('同步税收舍入设置', 'yxjto-gateway'); ?>
                                    </label>
                                </fieldset>
                                <p class="description">
                                    <?php _e('选择要同步的税收设置类型。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('地址设置同步', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="sync_tax_based_on" value="1" 
                                           <?php checked(!empty($tax_settings['sync_tax_based_on'])); ?> />
                                    <?php _e('同步税收计算基于的地址设置', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('同步"计算税收基于"设置（账单地址、配送地址或店铺地址）。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('同步模式', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="sync_mode">
                                    <option value="realtime" <?php selected($tax_settings['sync_mode'] ?? 'realtime', 'realtime'); ?>>
                                        <?php _e('实时同步', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="batch" <?php selected($tax_settings['sync_mode'] ?? 'realtime', 'batch'); ?>>
                                        <?php _e('批量同步', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="manual" <?php selected($tax_settings['sync_mode'] ?? 'realtime', 'manual'); ?>>
                                        <?php _e('手动同步', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('选择税收数据的同步方式。实时同步在数据变更时立即同步，批量同步定期执行，手动同步仅在手动触发时执行。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr id="tax-batch-interval-row" style="<?php echo ($tax_settings['sync_mode'] !== 'batch') ? 'display: none;' : ''; ?>">
                            <th scope="row"><?php _e('批量同步间隔', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="batch_interval">
                                    <option value="hourly" <?php selected($tax_settings['batch_interval'] ?? 'hourly', 'hourly'); ?>>
                                        <?php _e('每小时', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="twicedaily" <?php selected($tax_settings['batch_interval'] ?? 'hourly', 'twicedaily'); ?>>
                                        <?php _e('每天两次', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="daily" <?php selected($tax_settings['batch_interval'] ?? 'hourly', 'daily'); ?>>
                                        <?php _e('每天', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('批量同步的执行间隔。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('高级选项', 'yxjto-gateway'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="enable_logging" value="1" 
                                               <?php checked(!empty($tax_settings['enable_logging'])); ?> />
                                        <?php _e('启用详细日志记录', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="validate_before_sync" value="1" 
                                               <?php checked(!empty($tax_settings['validate_before_sync'])); ?> />
                                        <?php _e('同步前验证数据', 'yxjto-gateway'); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="backup_before_sync" value="1" 
                                               <?php checked(!empty($tax_settings['backup_before_sync'])); ?> />
                                        <?php _e('同步前自动备份', 'yxjto-gateway'); ?>
                                    </label>
                                </fieldset>
                                <p class="description">
                                    <?php _e('高级同步选项。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('冲突解决策略', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="conflict_resolution">
                                    <option value="source_wins" <?php selected($tax_settings['conflict_resolution'] ?? 'source_wins', 'source_wins'); ?>>
                                        <?php _e('源数据库优先', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="target_wins" <?php selected($tax_settings['conflict_resolution'] ?? 'source_wins', 'target_wins'); ?>>
                                        <?php _e('目标数据库优先', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="newest_wins" <?php selected($tax_settings['conflict_resolution'] ?? 'source_wins', 'newest_wins'); ?>>
                                        <?php _e('最新数据优先', 'yxjto-gateway'); ?>
                                    </option>
                                    <option value="manual" <?php selected($tax_settings['conflict_resolution'] ?? 'source_wins', 'manual'); ?>>
                                        <?php _e('手动解决', 'yxjto-gateway'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('当同一税收项在不同数据库中有冲突时的解决策略。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('最大重试次数', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="max_retries" min="0" max="10" 
                                       value="<?php echo esc_attr($tax_settings['max_retries'] ?? 3); ?>" 
                                       class="small-text" />
                                <p class="description">
                                    <?php _e('同步失败时的最大重试次数（0-10）。', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </form>

                <!-- 税收同步操作 -->
                <div class="tax-sync-actions" style="margin-top: 30px;">
                    <h2><?php _e('税收同步操作', 'yxjto-gateway'); ?></h2>
                    
                    <div class="sync-buttons" style="margin-bottom: 20px;">
                        <button type="button" id="tax-sync-now" class="button button-primary">
                            <?php _e('立即同步税收数据', 'yxjto-gateway'); ?>
                        </button>
                        <button type="button" id="tax-validate-data" class="button button-secondary">
                            <?php _e('验证税收数据', 'yxjto-gateway'); ?>
                        </button>
                        <button type="button" id="tax-backup-data" class="button button-secondary">
                            <?php _e('备份税收数据', 'yxjto-gateway'); ?>
                        </button>
                        <button type="button" id="tax-cleanup-data" class="button button-secondary">
                            <?php _e('清理孤立数据', 'yxjto-gateway'); ?>
                        </button>
                    </div>
                    
                    <div class="sync-status">
                        <h3><?php _e('同步状态', 'yxjto-gateway'); ?></h3>
                        <?php
                        $tax_stats = WP_Multi_DB_Config_Manager::get_tax_sync_stats();
                        ?>
                        <p><strong><?php _e('状态', 'yxjto-gateway'); ?>:</strong> 
                            <span class="status-<?php echo $tax_stats['enabled'] ? 'enabled' : 'disabled'; ?>">
                                <?php echo $tax_stats['enabled'] ? __('已启用', 'yxjto-gateway') : __('已禁用', 'yxjto-gateway'); ?>
                            </span>
                        </p>
                        <p><strong><?php _e('同步模式', 'yxjto-gateway'); ?>:</strong> 
                            <?php echo esc_html($tax_stats['sync_mode']); ?>
                        </p>
                        <?php if ($tax_stats['last_sync']): ?>
                        <p><strong><?php _e('最后同步时间', 'yxjto-gateway'); ?>:</strong> 
                            <?php echo esc_html($tax_stats['last_sync']); ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div id="tax-sync-result-message" style="display: none;"></div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // 同步模式切换
            $('select[name="sync_mode"]').change(function() {
                if ($(this).val() === 'batch') {
                    $('#tax-batch-interval-row').show();
                } else {
                    $('#tax-batch-interval-row').hide();
                }
            });
        });
        </script>

        <style>
        .wp-multi-db-admin .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ccc;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .wp-multi-db-admin .tab-header h2 {
            margin: 0;
        }
        
        .tax-sync-actions {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .sync-buttons button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .sync-status {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        
        .status-enabled {
            color: #46b450;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #dc3232;
            font-weight: bold;
        }
        
        #tax-sync-result-message {
            margin-top: 15px;
        }
        
        .notice {
            padding: 12px;
            margin: 15px 0;
            border-left: 4px solid #0073aa;
            background: #f9f9f9;
        }
        
        .notice-success {
            border-left-color: #46b450;
            background: #ecf7ed;
            color: #00a32a;
        }
        
        .notice-error {
            border-left-color: #dc3232;
            background: #fbeaea;
            color: #d63638;
        }
        </style>
        <?php
    }

    /**
     * 处理表单提交
     */
    public static function handle_form_submission() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['yxjto_gateway_tax_replication_nonce'], 'yxjto_gateway_tax_replication')) {
            wp_die(__('安全验证失败。', 'yxjto-gateway'));
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die(__('您没有足够的权限执行此操作。', 'yxjto-gateway'));
        }

        // 收集表单数据
        $settings = array(
            'enable_tax_replication' => !empty($_POST['enable_tax_replication']),
            'replicate_standard_rates' => !empty($_POST['replicate_standard_rates']),
            'replicate_reduced_rates' => !empty($_POST['replicate_reduced_rates']),
            'replicate_zero_rates' => !empty($_POST['replicate_zero_rates']),
            'replicate_tax_classes' => !empty($_POST['replicate_tax_classes']),
            'sync_tax_options' => !empty($_POST['sync_tax_options']),
            'sync_tax_display' => !empty($_POST['sync_tax_display']),
            'sync_tax_rounding' => !empty($_POST['sync_tax_rounding']),
            'sync_tax_based_on' => !empty($_POST['sync_tax_based_on']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'realtime'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'hourly'),
            'enable_logging' => !empty($_POST['enable_logging']),
            'validate_before_sync' => !empty($_POST['validate_before_sync']),
            'backup_before_sync' => !empty($_POST['backup_before_sync']),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'source_wins'),
            'max_retries' => intval($_POST['max_retries'] ?? 3)
        );

        // 保存设置
        $result = WP_Multi_DB_Config_Manager::save_tax_replication_settings($settings);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' . 
                     __('税收复制设置已成功保存。', 'yxjto-gateway') . 
                     '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error is-dismissible"><p>' . 
                     __('保存税收复制设置时出错。', 'yxjto-gateway') . 
                     '</p></div>';
            });
        }
    }

    /**
     * AJAX处理：立即同步税收数据
     */
    public static function ajax_tax_sync_now() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['security'], 'yxjto_tax_replication_nonce')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 获取目标数据库（如果指定）
            $target_databases = isset($_POST['target_databases']) ? (array) $_POST['target_databases'] : [];
            
            // 检查是否存在税收复制类
            if (!class_exists('WP_Multi_DB_Tax_Replication')) {
                wp_send_json_error(__('税收复制类不存在。请确保已正确安装相关文件。', 'yxjto-gateway'));
                return;
            }
            
            // 实例化税收复制类
            $tax_replication = new WP_Multi_DB_Tax_Replication();
            
            // 执行同步
            $result = $tax_replication::sync_tax_data($target_databases);
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('税收数据同步成功！', 'yxjto-gateway'),
                    'synced_items' => $result['synced_items'],
                    'target_databases' => $result['target_databases'],
                    'timestamp' => current_time('mysql')
                ));
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('同步过程中发生错误：', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：验证税收数据
     */
    public static function ajax_tax_validate_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['security'], 'yxjto_tax_replication_nonce')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在税收复制类
            if (!class_exists('WP_Multi_DB_Tax_Replication')) {
                wp_send_json_error(__('税收复制类不存在。', 'yxjto-gateway'));
                return;
            }
            
            $tax_replication = new WP_Multi_DB_Tax_Replication();
            $result = $tax_replication::validate_tax_data();
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('税收数据验证完成！', 'yxjto-gateway'),
                    'validation_results' => $result['validation_results'],
                    'issues_found' => $result['issues_found'],
                    'timestamp' => current_time('mysql')
                ));
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('验证过程中发生错误：', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：备份税收数据
     */
    public static function ajax_tax_backup_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['security'], 'yxjto_tax_replication_nonce')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在税收复制类
            if (!class_exists('WP_Multi_DB_Tax_Replication')) {
                wp_send_json_error(__('税收复制类不存在。', 'yxjto-gateway'));
                return;
            }
            
            $tax_replication = new WP_Multi_DB_Tax_Replication();
            $result = $tax_replication::backup_tax_data();
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('税收数据备份完成！', 'yxjto-gateway'),
                    'backup_file' => $result['backup_file'],
                    'backup_size' => $result['backup_size'],
                    'timestamp' => current_time('mysql')
                ));
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('备份过程中发生错误：', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：清理税收数据
     */
    public static function ajax_tax_cleanup_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['security'], 'yxjto_tax_replication_nonce')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在税收复制类
            if (!class_exists('WP_Multi_DB_Tax_Replication')) {
                wp_send_json_error(__('税收复制类不存在。', 'yxjto-gateway'));
                return;
            }
            
            $tax_replication = new WP_Multi_DB_Tax_Replication();
            $result = $tax_replication::cleanup_orphaned_data();
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('清理完成！', 'yxjto-gateway') . sprintf(__('删除了 %d 个孤立记录。', 'yxjto-gateway'), $result['deleted_count']),
                    'deleted_count' => $result['deleted_count'],
                    'cleanup_details' => $result['cleanup_details'],
                    'timestamp' => current_time('mysql')
                ));
            } else {
                wp_send_json_error($result['message']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('清理过程中发生错误：', 'yxjto-gateway') . $e->getMessage());
        }
    }
}
?>
