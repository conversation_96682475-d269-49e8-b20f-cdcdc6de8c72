<?php
/**
 * 支付调试日志记录器
 * 专门用于记录支付跳转和验证过程的详细信息
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Payment_Debug_Logger {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 日志文件路径
     */
    private $log_file;
    
    /**
     * 是否启用调试
     */
    private $debug_enabled;
    
    /**
     * 日志级别
     */
    const LEVEL_DEBUG = 'DEBUG';
    const LEVEL_INFO = 'INFO';
    const LEVEL_WARNING = 'WARNING';
    const LEVEL_ERROR = 'ERROR';
    const LEVEL_CRITICAL = 'CRITICAL';
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->debug_enabled = defined('YXJTO_PAYMENT_DEBUG') && YXJTO_PAYMENT_DEBUG;
        $this->log_file = WP_CONTENT_DIR . '/uploads/yxjto-payment-debug.log';
        
        // 确保日志目录存在
        $log_dir = dirname($this->log_file);
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 记录支付跳转开始
     */
    public function log_payment_redirect_start($order_id, $payment_method = '') {
        $context = [
            'order_id' => $order_id,
            'payment_method' => $payment_method,
            'current_database' => $this->get_current_database(),
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ];
        
        $this->log(self::LEVEL_INFO, "支付跳转开始", $context);
    }
    
    /**
     * 记录支付验证过程
     */
    public function log_payment_verification($order_id, $verification_result, $details = []) {
        $context = [
            'order_id' => $order_id,
            'verification_result' => $verification_result,
            'current_database' => $this->get_current_database(),
            'verification_time' => microtime(true)
        ];
        
        $context = array_merge($context, $details);
        
        $level = $verification_result ? self::LEVEL_INFO : self::LEVEL_WARNING;
        $message = $verification_result ? "支付验证通过" : "支付验证失败";
        
        $this->log($level, $message, $context);
    }
    
    /**
     * 记录订单详细信息
     */
    public function log_order_details($order) {
        if (!is_a($order, 'WC_Order')) {
            return;
        }
        
        $order_id = $order->get_id();
        
        // 基本订单信息
        $order_data = [
            'order_id' => $order_id,
            'status' => $order->get_status(),
            'total' => $order->get_total(),
            'currency' => $order->get_currency(),
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'created_date' => $order->get_date_created()->format('Y-m-d H:i:s'),
            'customer_email' => $order->get_billing_email(),
            'customer_phone' => $order->get_billing_phone()
        ];
        
        // 商品信息
        $items = [];
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $items[] = [
                'item_id' => $item_id,
                'product_id' => $item->get_product_id(),
                'variation_id' => $item->get_variation_id(),
                'name' => $item->get_name(),
                'sku' => $product ? $product->get_sku() : '',
                'quantity' => $item->get_quantity(),
                'price' => $order->get_item_total($item),
                'total' => $order->get_line_total($item)
            ];
        }
        $order_data['items'] = $items;
        
        // 费用信息
        $order_data['fees'] = [
            'subtotal' => $order->get_subtotal(),
            'shipping_total' => $order->get_shipping_total(),
            'tax_total' => $order->get_total_tax(),
            'discount_total' => $order->get_total_discount()
        ];
        
        // 地址信息
        $order_data['billing_address'] = [
            'first_name' => $order->get_billing_first_name(),
            'last_name' => $order->get_billing_last_name(),
            'company' => $order->get_billing_company(),
            'address_1' => $order->get_billing_address_1(),
            'address_2' => $order->get_billing_address_2(),
            'city' => $order->get_billing_city(),
            'state' => $order->get_billing_state(),
            'postcode' => $order->get_billing_postcode(),
            'country' => $order->get_billing_country()
        ];
        
        $this->log(self::LEVEL_DEBUG, "订单详细信息", $order_data);
    }
    
    /**
     * 记录支付URL生成
     */
    public function log_payment_url_generation($order_id, $original_url, $verified_url, $generation_time = 0) {
        $context = [
            'order_id' => $order_id,
            'original_url' => $original_url,
            'verified_url' => $verified_url,
            'url_changed' => $original_url !== $verified_url,
            'generation_time_ms' => $generation_time,
            'current_database' => $this->get_current_database()
        ];
        
        $this->log(self::LEVEL_INFO, "支付URL生成", $context);
    }
    
    /**
     * 记录数据库切换
     */
    public function log_database_switch($from_db, $to_db, $success, $switch_time = 0) {
        $context = [
            'from_database' => $from_db,
            'to_database' => $to_db,
            'switch_success' => $success,
            'switch_time_ms' => $switch_time,
            'timestamp' => time()
        ];
        
        $level = $success ? self::LEVEL_INFO : self::LEVEL_ERROR;
        $message = $success ? "数据库切换成功" : "数据库切换失败";
        
        $this->log($level, $message, $context);
    }
    
    /**
     * 记录支付网关处理
     */
    public function log_payment_gateway_processing($order_id, $gateway_id, $gateway_title, $processing_result, $details = []) {
        $context = [
            'order_id' => $order_id,
            'gateway_id' => $gateway_id,
            'gateway_title' => $gateway_title,
            'processing_result' => $processing_result,
            'current_database' => $this->get_current_database()
        ];
        
        $context = array_merge($context, $details);
        
        $level = $processing_result ? self::LEVEL_INFO : self::LEVEL_ERROR;
        $message = $processing_result ? "支付网关处理成功" : "支付网关处理失败";
        
        $this->log($level, $message, $context);
    }
    
    /**
     * 记录错误信息
     */
    public function log_error($message, $context = []) {
        $this->log(self::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * 记录警告信息
     */
    public function log_warning($message, $context = []) {
        $this->log(self::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * 记录信息
     */
    public function log_info($message, $context = []) {
        $this->log(self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * 记录调试信息
     */
    public function log_debug($message, $context = []) {
        $this->log(self::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * 核心日志记录方法
     */
    private function log($level, $message, $context = []) {
        if (!$this->debug_enabled) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $memory_usage = round(memory_get_usage() / 1024 / 1024, 2);
        
        $log_entry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'memory_mb' => $memory_usage,
            'request_id' => $this->get_request_id()
        ];
        
        $log_line = json_encode($log_entry, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n";
        
        // 写入日志文件
        file_put_contents($this->log_file, $log_line, FILE_APPEND | LOCK_EX);
        
        // 如果是错误级别，同时写入WordPress错误日志
        if (in_array($level, [self::LEVEL_ERROR, self::LEVEL_CRITICAL])) {
            error_log("YXJTO Payment Debug [{$level}]: {$message} - " . json_encode($context));
        }
    }
    
    /**
     * 获取当前数据库
     */
    private function get_current_database() {
        if (class_exists('YXJTO_Gateway')) {
            try {
                $gateway = YXJTO_Gateway::get_instance();
                return $gateway->get_current_database();
            } catch (Exception $e) {
                return 'unknown';
            }
        }
        return 'default';
    }
    
    /**
     * 获取客户端IP
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * 获取请求ID
     */
    private function get_request_id() {
        static $request_id = null;
        
        if ($request_id === null) {
            $request_id = uniqid('req_', true);
        }
        
        return $request_id;
    }
    
    /**
     * 获取日志文件内容
     */
    public function get_log_contents($lines = 100) {
        if (!file_exists($this->log_file)) {
            return [];
        }
        
        $content = file_get_contents($this->log_file);
        $log_lines = array_filter(explode("\n", $content));
        
        // 获取最后N行
        $log_lines = array_slice($log_lines, -$lines);
        
        $logs = [];
        foreach ($log_lines as $line) {
            $decoded = json_decode($line, true);
            if ($decoded) {
                $logs[] = $decoded;
            }
        }
        
        return array_reverse($logs); // 最新的在前面
    }
    
    /**
     * 清空日志文件
     */
    public function clear_log() {
        if (file_exists($this->log_file)) {
            file_put_contents($this->log_file, '');
        }
    }
    
    /**
     * 获取日志文件大小
     */
    public function get_log_file_size() {
        if (file_exists($this->log_file)) {
            return filesize($this->log_file);
        }
        return 0;
    }
    
    /**
     * 获取日志统计信息
     */
    public function get_log_stats() {
        $logs = $this->get_log_contents(1000); // 获取最近1000条
        
        $stats = [
            'total_entries' => count($logs),
            'levels' => [],
            'databases' => [],
            'recent_errors' => 0,
            'file_size' => $this->get_log_file_size()
        ];
        
        foreach ($logs as $log) {
            // 统计级别
            $level = $log['level'] ?? 'UNKNOWN';
            $stats['levels'][$level] = ($stats['levels'][$level] ?? 0) + 1;
            
            // 统计数据库
            $db = $log['context']['current_database'] ?? 'unknown';
            $stats['databases'][$db] = ($stats['databases'][$db] ?? 0) + 1;
            
            // 统计最近错误
            if (in_array($level, ['ERROR', 'CRITICAL'])) {
                $timestamp = strtotime($log['timestamp']);
                if ($timestamp > (time() - 3600)) { // 最近1小时
                    $stats['recent_errors']++;
                }
            }
        }
        
        return $stats;
    }
}

// 全局函数，方便调用
if (!function_exists('yxjto_payment_debug_log')) {
    function yxjto_payment_debug_log($level, $message, $context = []) {
        $logger = YXJTO_Payment_Debug_Logger::get_instance();
        $logger->log($level, $message, $context);
    }
}
