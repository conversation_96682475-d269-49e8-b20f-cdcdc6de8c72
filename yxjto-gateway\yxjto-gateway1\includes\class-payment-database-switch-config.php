<?php
/**
 * 支付数据库切换配置类
 * 
 * 管理支付过程中的数据库切换行为配置
 */

if (!defined('ABSPATH')) {
    exit;
}

class YXJTO_Payment_Database_Switch_Config {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 配置选项
     */
    private $options = array();
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->load_options();
        $this->init_hooks();
    }
    
    /**
     * 加载配置选项
     */
    private function load_options() {
        $default_options = array(
            'force_default_database_for_payment' => true,  // 强制支付时使用默认数据库
            'switch_back_after_payment' => false,          // 支付完成后是否切换回原数据库
            'log_database_switches' => true,               // 是否记录数据库切换日志
            'transient_timeout' => 3600,                   // Transient超时时间（秒）
            'enable_payment_verification' => true,         // 启用支付验证
            'strict_mode' => true,                         // 严格模式：验证失败时阻止支付
            'enable_totals_verification' => true,          // 启用订单总价验证
            'totals_precision' => 0.01,                   // 总价比较精度（允许的差异）
            'verify_item_details' => true,                // 验证商品详细信息
            'verify_shipping_fees' => true,               // 验证运费
            'verify_tax_amounts' => true,                 // 验证税费
            'verify_discounts' => true                    // 验证折扣
        );

        $saved_options = get_option('yxjto_payment_db_switch_config', array());
        $this->options = wp_parse_args($saved_options, $default_options);
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 添加过滤器来控制支付后是否切换回原数据库
        add_filter('yxjto_order_replication_switch_back_after_payment', array($this, 'should_switch_back_after_payment'), 10, 3);
        
        // 添加管理页面设置
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * 获取配置选项
     */
    public function get_option($key, $default = null) {
        return isset($this->options[$key]) ? $this->options[$key] : $default;
    }
    
    /**
     * 设置配置选项
     */
    public function set_option($key, $value) {
        $this->options[$key] = $value;
        update_option('yxjto_payment_db_switch_config', $this->options);
    }
    
    /**
     * 获取所有配置选项
     */
    public function get_all_options() {
        return $this->options;
    }
    
    /**
     * 判断支付后是否应该切换回原数据库
     */
    public function should_switch_back_after_payment($default, $order_id, $original_database) {
        // 检查配置
        if (!$this->get_option('switch_back_after_payment', false)) {
            return false;
        }
        
        // 可以根据订单类型、用户角色等添加更复杂的逻辑
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }
        
        // 例如：VIP用户或特定产品类型可能需要不同的处理
        $switch_back = apply_filters('yxjto_payment_switch_back_custom_logic', $this->get_option('switch_back_after_payment'), $order, $original_database);
        
        return $switch_back;
    }
    
    /**
     * 是否强制使用默认数据库进行支付
     */
    public function should_force_default_database() {
        return $this->get_option('force_default_database_for_payment', true);
    }
    
    /**
     * 是否启用支付验证
     */
    public function is_payment_verification_enabled() {
        return $this->get_option('enable_payment_verification', true);
    }
    
    /**
     * 是否启用严格模式
     */
    public function is_strict_mode_enabled() {
        return $this->get_option('strict_mode', true);
    }
    
    /**
     * 是否记录数据库切换日志
     */
    public function should_log_switches() {
        return $this->get_option('log_database_switches', true);
    }
    
    /**
     * 获取Transient超时时间
     */
    public function get_transient_timeout() {
        return $this->get_option('transient_timeout', 3600);
    }

    /**
     * 是否启用订单总价验证
     */
    public function is_totals_verification_enabled() {
        return $this->get_option('enable_totals_verification', true);
    }

    /**
     * 获取总价比较精度
     */
    public function get_totals_precision() {
        return $this->get_option('totals_precision', 0.01);
    }

    /**
     * 是否验证商品详细信息
     */
    public function should_verify_item_details() {
        return $this->get_option('verify_item_details', true);
    }

    /**
     * 是否验证运费
     */
    public function should_verify_shipping_fees() {
        return $this->get_option('verify_shipping_fees', true);
    }

    /**
     * 是否验证税费
     */
    public function should_verify_tax_amounts() {
        return $this->get_option('verify_tax_amounts', true);
    }

    /**
     * 是否验证折扣
     */
    public function should_verify_discounts() {
        return $this->get_option('verify_discounts', true);
    }
    
    /**
     * 注册设置
     */
    public function register_settings() {
        register_setting('yxjto_payment_db_switch_config', 'yxjto_payment_db_switch_config');
    }
    
    /**
     * 重置为默认配置
     */
    public function reset_to_defaults() {
        delete_option('yxjto_payment_db_switch_config');
        $this->load_options();
    }
    
    /**
     * 验证配置
     */
    public function validate_config() {
        $errors = array();

        // 验证Transient超时时间
        $timeout = $this->get_option('transient_timeout');
        if (!is_numeric($timeout) || $timeout < 60 || $timeout > 86400) {
            $errors[] = __('Transient timeout must be between 60 and 86400 seconds.', 'yxjto-gateway');
        }

        // 验证总价精度
        $precision = $this->get_option('totals_precision');
        if (!is_numeric($precision) || $precision < 0 || $precision > 1) {
            $errors[] = __('Totals precision must be between 0 and 1.', 'yxjto-gateway');
        }

        return $errors;
    }
    
    /**
     * 获取配置状态摘要
     */
    public function get_config_summary() {
        return array(
            'force_default_db' => $this->should_force_default_database() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'switch_back' => $this->get_option('switch_back_after_payment') ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'verification' => $this->is_payment_verification_enabled() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'totals_verification' => $this->is_totals_verification_enabled() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'strict_mode' => $this->is_strict_mode_enabled() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'logging' => $this->should_log_switches() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'transient_timeout' => $this->get_transient_timeout() . ' ' . __('seconds', 'yxjto-gateway'),
            'totals_precision' => $this->get_totals_precision(),
            'verify_items' => $this->should_verify_item_details() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'verify_shipping' => $this->should_verify_shipping_fees() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'verify_tax' => $this->should_verify_tax_amounts() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway'),
            'verify_discounts' => $this->should_verify_discounts() ? __('Enabled', 'yxjto-gateway') : __('Disabled', 'yxjto-gateway')
        );
    }
}
