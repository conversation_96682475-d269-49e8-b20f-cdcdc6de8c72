<?php
/**
 * PayPal 多账号网关 WooCommerce Blocks 支持类
 */

if (!defined('ABSPATH')) {
    exit;
}

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

/**
 * PayPal Multi Gateway Blocks 支付方式类
 */
class YXJTO_PayPal_Multi_Gateway_Blocks extends AbstractPaymentMethodType {

    /**
     * 支付方式名称
     */
    protected $name = 'yxjto_paypal_multi_gateway';

    /**
     * 初始化
     */
    public function initialize() {
        $this->settings = get_option('woocommerce_yxjto_paypal_multi_gateway_settings', array());
    }

    /**
     * 检查是否激活
     */
    public function is_active() {
        return !empty($this->settings['enabled']) && 'yes' === $this->settings['enabled'];
    }

    /**
     * 获取支付方式脚本句柄
     */
    public function get_payment_method_script_handles() {
        $script_path = 'assets/js/blocks/paypal-multi-gateway-blocks.js';
        $script_asset_path = YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_DIR . 'assets/js/blocks/paypal-multi-gateway-blocks.asset.php';
        $script_asset = file_exists($script_asset_path)
            ? require($script_asset_path)
            : array(
                'dependencies' => array(),
                'version' => YXJTO_PAYPAL_MULTI_GATEWAY_VERSION
            );

        wp_register_script(
            'yxjto-paypal-multi-gateway-blocks',
            YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL . $script_path,
            $script_asset['dependencies'],
            $script_asset['version'],
            true
        );

        // 为 Blocks 脚本提供数据
        wp_localize_script('yxjto-paypal-multi-gateway-blocks', 'yxjtoPaypalMultiGatewayData', array(
            'enabled' => !empty($this->settings['enabled']) && 'yes' === $this->settings['enabled'] ? 'yes' : 'no',
            'title' => !empty($this->settings['title']) ? $this->settings['title'] : 'YXJTO PayPal',
            'description' => !empty($this->settings['description']) ? $this->settings['description'] : '',
            'testmode' => !empty($this->settings['testmode']) && 'yes' === $this->settings['testmode'] ? 'yes' : 'no',
            'supports' => array('products', 'refunds'),
            'plugin_url' => YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL,
            'ajax_url' => admin_url('admin-ajax.php'),
            'admin_ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_checkout'),
            'wc_ajax_url' => function_exists('wc_ajax_url') ? wc_ajax_url() : admin_url('admin-ajax.php')
        ));

        return array('yxjto-paypal-multi-gateway-blocks');
    }

    /**
     * 获取支付方式数据
     */
    public function get_payment_method_data() {
        $gateway = new YXJTO_PayPal_Multi_Gateway_Payment();
        
        // 获取活跃账户数量
        $accounts_count = 0;
        if (class_exists('YXJTO_PayPal_Multi_Gateway_Accounts')) {
            $accounts = YXJTO_PayPal_Multi_Gateway_Accounts::get_instance();
            $accounts_count = count($accounts->get_active_accounts());
        }

        return array(
            'title' => $gateway->get_title(),
            'description' => $gateway->get_description(),
            'icon' => $gateway->get_icon(),
            'supports' => $gateway->supports,
            'testmode' => $gateway->testmode,
            'accounts_count' => $accounts_count,
            'currency_supported' => $this->is_currency_supported(),
            'plugin_url' => YXJTO_PAYPAL_MULTI_GATEWAY_PLUGIN_URL,
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yxjto_paypal_multi_gateway_checkout'),
            'messages' => array(
                'processing' => __('Processing payment...', 'yxjto-paypal-multi-gateway'),
                'redirect' => __('Redirecting to PayPal...', 'yxjto-paypal-multi-gateway'),
                'error' => __('Payment processing error, please try again.', 'yxjto-paypal-multi-gateway'),
                'no_accounts' => __('No PayPal accounts available', 'yxjto-paypal-multi-gateway'),
                'currency_not_supported' => __('Current currency is not supported for PayPal payments', 'yxjto-paypal-multi-gateway')
            )
        );
    }

    /**
     * 检查货币是否支持
     */
    private function is_currency_supported() {
        $supported_currencies = array(
            'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'NOK', 'SEK', 'DKK',
            'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP', 'TWD', 'THB',
            'SGD', 'HKD', 'CNY', 'NZD', 'TRY', 'INR', 'RUB'
        );
        
        $current_currency = get_woocommerce_currency();
        return in_array($current_currency, $supported_currencies);
    }
}
