<?php
/**
 * 后台管理页面
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 管理页面类
 */
class WP_Multi_Database_Admin {

    /**
     * 初始化管理页面
     */
    public static function init() {
        // 注册AJAX处理器
        add_action('wp_ajax_yxjto_gateway_save_database', [__CLASS__, 'ajax_save_database']);
        add_action('wp_ajax_yxjto_gateway_save_url_rule', [__CLASS__, 'ajax_save_url_rule']);
        add_action('wp_ajax_yxjto_gateway_save_ip_rule', [__CLASS__, 'ajax_save_ip_rule']);
        add_action('wp_ajax_yxjto_gateway_enable_wp_config', [__CLASS__, 'ajax_enable_wp_config']);
        add_action('wp_ajax_yxjto_gateway_disable_wp_config', [__CLASS__, 'ajax_disable_wp_config']);
        add_action('wp_ajax_yxjto_gateway_refresh_debug_status', [__CLASS__, 'ajax_refresh_debug_status']);
        
        // 配送同步相关AJAX处理器
        add_action('wp_ajax_yxjto_create_test_shipping_data', [__CLASS__, 'ajax_create_test_shipping_data']);
        add_action('wp_ajax_yxjto_run_full_shipping_sync', [__CLASS__, 'ajax_run_full_shipping_sync']);
        add_action('wp_ajax_yxjto_debug_shipping_sync', [__CLASS__, 'ajax_debug_shipping_sync']);
        add_action('wp_ajax_yxjto_sync_specific_zone', [__CLASS__, 'ajax_sync_specific_zone']);
        add_action('wp_ajax_yxjto_validate_shipping_data', [__CLASS__, 'ajax_validate_shipping_data']);
        add_action('wp_ajax_yxjto_cleanup_orphaned_shipping_data', [__CLASS__, 'ajax_cleanup_orphaned_shipping_data']);

        // 价格调整相关AJAX处理器
        add_action('wp_ajax_yxjto_save_price_adjustment_config', [__CLASS__, 'ajax_save_price_adjustment_config']);
        add_action('wp_ajax_yxjto_reset_price_adjustment_config', [__CLASS__, 'ajax_reset_price_adjustment_config']);
        add_action('wp_ajax_yxjto_test_price_adjustment', [__CLASS__, 'ajax_test_price_adjustment']);
        add_action('wp_ajax_yxjto_export_price_adjustment_config', [__CLASS__, 'ajax_export_price_adjustment_config']);
        add_action('wp_ajax_yxjto_import_price_adjustment_config', [__CLASS__, 'ajax_import_price_adjustment_config']);
    }

    /**
     * 处理表单提交
     */
    private static function handle_form_submissions() {
        if (!isset($_POST['action'])) {
            return;
        }

        $action = $_POST['action'];

        switch ($action) {
            case 'save_databases':
                if (wp_verify_nonce($_POST['yxjto_gateway_databases_nonce'], 'yxjto_gateway_databases')) {
                    self::save_databases();
                }
                break;

            case 'save_url_rules':
                if (wp_verify_nonce($_POST['yxjto_gateway_url_rules_nonce'], 'yxjto_gateway_url_rules')) {
                    self::save_url_rules();
                }
                break;

            case 'save_ip_rules':
                if (wp_verify_nonce($_POST['yxjto_gateway_ip_rules_nonce'], 'yxjto_gateway_ip_rules')) {
                    self::save_ip_rules();
                }
                break;

            case 'save_coupon_sync':
                if (wp_verify_nonce($_POST['yxjto_gateway_coupon_sync_nonce'], 'yxjto_gateway_coupon_sync')) {
                    self::save_coupon_sync();
                }
                break;

            case 'save_shipping_sync':
                if (wp_verify_nonce($_POST['yxjto_gateway_shipping_sync_nonce'], 'yxjto_gateway_shipping_sync')) {
                    self::save_shipping_sync();
                }
                break;

            case 'save_tax_sync':
                if (wp_verify_nonce($_POST['yxjto_gateway_tax_sync_nonce'], 'yxjto_gateway_tax_sync')) {
                    self::save_tax_sync();
                }
                break;

            case 'save_settings':
                if (wp_verify_nonce($_POST['yxjto_gateway_settings_nonce'], 'yxjto_gateway_settings')) {
                    self::save_settings();
                }
                break;

            case 'save_crawler_detection':
                if (wp_verify_nonce($_POST['yxjto_gateway_crawler_detection_nonce'], 'yxjto_gateway_crawler_detection')) {
                    self::save_crawler_detection();
                }
                break;

            case 'clear_database_cookies':
                if (wp_verify_nonce($_POST['yxjto_gateway_clear_cookies_nonce'], 'yxjto_gateway_clear_cookies')) {
                    self::clear_database_cookies();
                }
                break;
        }
    }

    /**
     * 处理GET请求的action参数
     */
    private static function handle_get_actions() {
        if (!isset($_GET['action']) || !current_user_can('manage_options')) {
            return;
        }

        $action = sanitize_text_field($_GET['action']);

        switch ($action) {
            case 'delete_database':
                if (isset($_GET['key'])) {
                    $key = sanitize_text_field($_GET['key']);
                    self::handle_delete_database($key);
                }
                break;

            case 'delete_url_rule':
                if (isset($_GET['index'])) {
                    $index = intval($_GET['index']);
                    self::handle_delete_url_rule($index);
                }
                break;

            case 'delete_ip_rule':
                if (isset($_GET['index'])) {
                    $index = intval($_GET['index']);
                    self::handle_delete_ip_rule($index);
                }
                break;
        }
    }

    /**
     * 渲染管理页面
     */
    public static function render_admin_page() {
        // 处理表单提交
        self::handle_form_submissions();

        // 处理GET请求的action参数
        self::handle_get_actions();

        // 获取当前设置
        $database_configs = WP_Multi_DB_Config_Manager::get_databases();
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
        $current_database = YXJTO_Gateway::get_instance()->get_current_database();
        
        ?>
        <div class="wrap">
            <h1><?php _e('YXJTO Gateway Settings', 'yxjto-gateway'); ?></h1>
            
            <div class="wp-multi-db-admin">
                <div class="nav-tab-wrapper">
                    <a href="#databases" class="nav-tab nav-tab-active"><?php _e('Databases', 'yxjto-gateway'); ?></a>
                    <a href="#url-rules" class="nav-tab">
                        <?php _e('URL Rules', 'yxjto-gateway'); ?>
                        <span class="priority-indicator high"><?php _e('High', 'yxjto-gateway'); ?></span>
                    </a>
                    <a href="#ip-rules" class="nav-tab">
                        <?php _e('IP Rules', 'yxjto-gateway'); ?>
                        <span class="priority-indicator medium"><?php _e('Medium', 'yxjto-gateway'); ?></span>
                    </a>
                    <a href="#crawler-detection" class="nav-tab">
                        <?php _e('Crawler Detection', 'yxjto-gateway'); ?>
                        <span class="priority-indicator highest"><?php _e('Highest', 'yxjto-gateway'); ?></span>
                    </a>
                    <a href="#debug-status" class="nav-tab">
                        <?php _e('Debug Status', 'yxjto-gateway'); ?>
                        <span class="priority-indicator debug"><?php _e('Debug', 'yxjto-gateway'); ?></span>
                    </a>
                    <a href="#price-adjustment" class="nav-tab"><?php _e('Price Adjustment', 'yxjto-gateway'); ?></a>
                    <a href="#settings" class="nav-tab"><?php _e('Settings', 'yxjto-gateway'); ?></a>
                </div>
                
                <!-- 数据库配置标签页 -->
                    <div id="databases" class="tab-content active">
                        <form method="post" action="" id="databases-form">
                            <?php wp_nonce_field('yxjto_gateway_databases', 'yxjto_gateway_databases_nonce'); ?>
                            <input type="hidden" name="action" value="save_databases" />

                            <div class="tab-header">
                                <h2><?php _e('Database Configurations', 'yxjto-gateway'); ?></h2>
                                <button type="button" class="button button-primary" onclick="openDatabaseModal()"><?php _e('Add Database', 'yxjto-gateway'); ?></button>
                            </div>
                            <p><?php _e('Configure your database connections here.', 'yxjto-gateway'); ?></p>

                        <div class="current-database">
                            <h3><?php _e('Current Database', 'yxjto-gateway'); ?></h3>
                            <p><strong><?php echo esc_html($current_database); ?></strong></p>
                        </div>
                        
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col"><?php _e('Name', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Host', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Database', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Username', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Status', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Actions', 'yxjto-gateway'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($database_configs as $key => $config): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($config['name'] ?? $key); ?></strong>
                                        <?php if ($key === 'default'): ?>
                                        <span class="badge default"><?php _e('Default', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>
                                        <?php if ($current_database === $key): ?>
                                        <span class="badge current"><?php _e('Current', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html($config['host'] ?? ''); ?></td>
                                    <td><?php echo esc_html($config['database'] ?? ''); ?></td>
                                    <td><?php echo esc_html($config['username'] ?? ''); ?></td>
                                    <td>
                                        <?php if (!empty($config['enabled'])): ?>
                                        <span class="status-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></span>
                                        <?php else: ?>
                                        <span class="status-disabled"><?php _e('Disabled', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="database-actions">
                                            <button type="button" class="button button-small" onclick="editDatabase('<?php echo esc_js($key); ?>')"><?php _e('Edit', 'yxjto-gateway'); ?></button>
                                            <button type="button" class="button button-small test-connection" data-key="<?php echo esc_attr($key); ?>"><?php _e('Test', 'yxjto-gateway'); ?></button>
                                            <?php if ($key !== 'default'): ?>
                                            <button type="button" class="button button-small switch-database" data-key="<?php echo esc_attr($key); ?>"><?php _e('Switch', 'yxjto-gateway'); ?></button>
                                            <button type="button" class="button button-small button-link-delete" onclick="deleteDatabase('<?php echo esc_js($key); ?>')"><?php _e('Delete', 'yxjto-gateway'); ?></button>
                                            <?php endif; ?>
                                        </div>
                                        <div id="status_<?php echo esc_attr($key); ?>" class="connection-status" style="margin-top: 5px;"></div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </form>
                    </div>

                    <!-- URL规则标签页 -->
                    <div id="url-rules" class="tab-content">
                        <form method="post" action="" id="url-rules-form">
                            <?php wp_nonce_field('yxjto_gateway_url_rules', 'yxjto_gateway_url_rules_nonce'); ?>
                            <input type="hidden" name="action" value="save_url_rules" />

                            <div class="tab-header">
                                <h2><?php _e('URL Parameter Database Switching Rules', 'yxjto-gateway'); ?></h2>
                                <button type="button" class="button button-primary" onclick="openUrlRuleModal()"><?php _e('Add URL Rule', 'yxjto-gateway'); ?></button>
                            </div>
                            <p><?php _e('Configure rules to switch databases based on URL parameters.', 'yxjto-gateway'); ?></p>
                        <div class="wp-multi-db-notice success">
                            <p><strong><?php _e('Priority Level: High', 'yxjto-gateway'); ?></strong></p>
                            <p><?php _e('URL parameter rules have the highest priority. They will override IP-based rules when both conditions are met.', 'yxjto-gateway'); ?></p>
                        </div>

                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col"><?php _e('Parameter', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Value', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Target Database', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Status', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Actions', 'yxjto-gateway'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($url_rules)): ?>
                                <tr>
                                    <td colspan="5" class="no-items"><?php _e('No URL rules configured.', 'yxjto-gateway'); ?></td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($url_rules as $index => $rule): ?>
                                <tr>
                                    <td><code><?php echo esc_html($rule['parameter'] ?? ''); ?></code></td>
                                    <td><code><?php echo esc_html($rule['value'] ?? ''); ?></code></td>
                                    <td>
                                        <?php
                                        $db_key = $rule['database'] ?? '';
                                        echo esc_html($database_configs[$db_key]['name'] ?? $db_key);
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($rule['enabled'])): ?>
                                        <span class="status-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></span>
                                        <?php else: ?>
                                        <span class="status-disabled"><?php _e('Disabled', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="button button-small" onclick="editUrlRule(<?php echo esc_js($index); ?>)"><?php _e('Edit', 'yxjto-gateway'); ?></button>
                                        <button type="button" class="button button-small button-link-delete" onclick="deleteUrlRule(<?php echo esc_js($index); ?>)"><?php _e('Delete', 'yxjto-gateway'); ?></button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        </form>
                    </div>
                    
                    <!-- IP规则标签页 -->
                    <div id="ip-rules" class="tab-content">
                        <form method="post" action="" id="ip-rules-form">
                            <?php wp_nonce_field('yxjto_gateway_ip_rules', 'yxjto_gateway_ip_rules_nonce'); ?>
                            <input type="hidden" name="action" value="save_ip_rules" />

                            <div class="tab-header">
                                <h2><?php _e('IP-based Database Switching Rules', 'yxjto-gateway'); ?></h2>
                                <button type="button" class="button button-primary" onclick="openIpRuleModal()"><?php _e('Add IP Rule', 'yxjto-gateway'); ?></button>
                            </div>
                            <p><?php _e('Configure rules to automatically switch databases based on visitor IP addresses.', 'yxjto-gateway'); ?></p>
                        <div class="wp-multi-db-notice warning">
                            <p><strong><?php _e('Priority Level: Medium', 'yxjto-gateway'); ?></strong></p>
                            <p><?php _e('IP rules have lower priority than URL parameter rules. If both rules match, URL parameter rules will take precedence.', 'yxjto-gateway'); ?></p>
                        </div>

                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col"><?php _e('IP Range', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Target Database', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Type', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Status', 'yxjto-gateway'); ?></th>
                                    <th scope="col"><?php _e('Actions', 'yxjto-gateway'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($ip_rules)): ?>
                                <tr>
                                    <td colspan="5" class="no-items"><?php _e('No IP rules configured.', 'yxjto-gateway'); ?></td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($ip_rules as $index => $rule): ?>
                                <tr>
                                    <td>
                                        <code><?php echo esc_html($rule['ip_range'] ?? ''); ?></code>
                                        <div class="row-actions">
                                            <span class="description"><?php _e('Examples: ***********, ***********/24, ***********-***********00', 'yxjto-gateway'); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $db_key = $rule['database'] ?? '';
                                        echo esc_html($database_configs[$db_key]['name'] ?? $db_key);
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($rule['auto_added'])): ?>
                                            <span class="rule-type auto-added" title="<?php _e('Automatically added before payment processing', 'yxjto-gateway'); ?>">
                                                <span class="dashicons dashicons-admin-users"></span>
                                                <?php _e('Auto', 'yxjto-gateway'); ?>
                                            </span>
                                            <?php if (!empty($rule['created_at'])): ?>
                                                <div class="row-actions">
                                                    <span class="description"><?php echo sprintf(__('Added: %s', 'yxjto-gateway'), esc_html($rule['created_at'])); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="rule-type manual" title="<?php _e('Manually configured rule', 'yxjto-gateway'); ?>">
                                                <span class="dashicons dashicons-admin-settings"></span>
                                                <?php _e('Manual', 'yxjto-gateway'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($rule['enabled'])): ?>
                                        <span class="status-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></span>
                                        <?php else: ?>
                                        <span class="status-disabled"><?php _e('Disabled', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="button button-small" onclick="editIpRule(<?php echo esc_js($index); ?>)"><?php _e('Edit', 'yxjto-gateway'); ?></button>
                                        <button type="button" class="button button-small button-link-delete" onclick="deleteIpRule(<?php echo esc_js($index); ?>)"><?php _e('Delete', 'yxjto-gateway'); ?></button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        </form>
                    </div>

                    <!-- 爬虫检测标签页 -->
                    <div id="crawler-detection" class="tab-content">
                        <form method="post" action="" id="crawler-detection-form">
                            <?php wp_nonce_field('yxjto_gateway_crawler_detection', 'yxjto_gateway_crawler_detection_nonce'); ?>
                            <input type="hidden" name="action" value="save_crawler_detection" />

                            <div class="tab-header">
                                <h2><?php _e('Crawler Detection Settings', 'yxjto-gateway'); ?></h2>
                                <button type="submit" class="button button-primary"><?php _e('Save Changes', 'yxjto-gateway'); ?></button>
                            </div>

                            <p class="description">
                                <?php _e('Configure crawler detection to automatically use the default database when search engine crawlers visit your site. This ensures consistent SEO indexing.', 'yxjto-gateway'); ?>
                            </p>

                            <div class="crawler-settings">
                                <h3><?php _e('General Settings', 'yxjto-gateway'); ?></h3>

                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Enable Crawler Detection', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <?php $crawler_settings = WP_Multi_DB_Config_Manager::get_crawler_settings(); ?>
                                            <label>
                                                <input type="checkbox" name="crawler_detection_enabled" value="1"
                                                       <?php checked(!empty($crawler_settings['enabled'])); ?> />
                                                <?php _e('Automatically detect crawlers and use default database', 'yxjto-gateway'); ?>
                                            </label>
                                            <p class="description">
                                                <?php _e('When enabled, detected crawlers will always access the default database regardless of other rules.', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>

                                    <tr>
                                        <th scope="row"><?php _e('Log Crawler Visits', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <label>
                                                <input type="checkbox" name="log_crawler_visits" value="1"
                                                       <?php checked(!empty($crawler_settings['log_visits'])); ?> />
                                                <?php _e('Log crawler visits for monitoring', 'yxjto-gateway'); ?>
                                            </label>
                                            <p class="description">
                                                <?php _e('Keep a log of crawler visits to help with SEO monitoring and debugging.', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>

                                    <tr>
                                        <th scope="row"><?php _e('Enable IP Detection', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <label>
                                                <input type="checkbox" name="enable_ip_detection" value="1"
                                                       <?php checked(!empty($crawler_settings['enable_ip_detection'])); ?> />
                                                <?php _e('Detect crawlers by IP address ranges', 'yxjto-gateway'); ?>
                                            </label>
                                            <p class="description">
                                                <?php _e('Use known IP ranges to identify crawlers. This is more reliable than User-Agent detection.', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>

                                    <tr>
                                        <th scope="row"><?php _e('Enable IP Verification', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <label>
                                                <input type="checkbox" name="enable_ip_verification" value="1"
                                                       <?php checked(!empty($crawler_settings['enable_ip_verification'])); ?> />
                                                <?php _e('Verify User-Agent claims with IP address', 'yxjto-gateway'); ?>
                                            </label>
                                            <p class="description">
                                                <?php _e('When a User-Agent claims to be a crawler, verify that the IP address matches known crawler IP ranges.', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>

                                    <tr>
                                        <th scope="row"><?php _e('Enable Reverse DNS Verification', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <label>
                                                <input type="checkbox" name="enable_reverse_dns" value="1"
                                                       <?php checked(!empty($crawler_settings['enable_reverse_dns'])); ?> />
                                                <?php _e('Verify crawlers using reverse DNS lookup', 'yxjto-gateway'); ?>
                                            </label>
                                            <p class="description">
                                                <?php _e('Perform reverse DNS lookup to verify that the IP truly belongs to the claimed crawler. Most secure but slower.', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>

                                <h3><?php _e('Supported Crawlers', 'yxjto-gateway'); ?></h3>
                                <p class="description">
                                    <?php _e('The following crawlers are automatically detected. You can enable/disable detection for specific crawlers.', 'yxjto-gateway'); ?>
                                </p>

                                <div class="crawler-list">
                                    <?php
                                    $default_crawlers = [
                                        'googlebot' => [
                                            'name' => 'Google Bot',
                                            'user_agents' => ['Googlebot', 'Google-InspectionTool'],
                                            'description' => 'Google search engine crawler'
                                        ],
                                        'bingbot' => [
                                            'name' => 'Bing Bot',
                                            'user_agents' => ['bingbot', 'BingPreview'],
                                            'description' => 'Microsoft Bing search engine crawler'
                                        ],
                                        'facebookbot' => [
                                            'name' => 'Facebook Bot',
                                            'user_agents' => ['facebookexternalhit', 'Facebot'],
                                            'description' => 'Facebook social media crawler'
                                        ],
                                        'twitterbot' => [
                                            'name' => 'Twitter Bot',
                                            'user_agents' => ['Twitterbot'],
                                            'description' => 'Twitter social media crawler'
                                        ],
                                        'linkedinbot' => [
                                            'name' => 'LinkedIn Bot',
                                            'user_agents' => ['LinkedInBot'],
                                            'description' => 'LinkedIn social media crawler'
                                        ],
                                        'baidubot' => [
                                            'name' => 'Baidu Bot',
                                            'user_agents' => ['Baiduspider'],
                                            'description' => 'Baidu search engine crawler (China)'
                                        ],
                                        'yandexbot' => [
                                            'name' => 'Yandex Bot',
                                            'user_agents' => ['YandexBot'],
                                            'description' => 'Yandex search engine crawler (Russia)'
                                        ]
                                    ];

                                    $enabled_crawlers = isset($crawler_settings['enabled_crawlers']) ? $crawler_settings['enabled_crawlers'] : array_keys($default_crawlers);
                                    ?>

                                    <table class="widefat striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 50px;"><?php _e('Enabled', 'yxjto-gateway'); ?></th>
                                                <th><?php _e('Crawler Name', 'yxjto-gateway'); ?></th>
                                                <th><?php _e('User Agent Patterns', 'yxjto-gateway'); ?></th>
                                                <th><?php _e('Description', 'yxjto-gateway'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($default_crawlers as $key => $crawler): ?>
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="enabled_crawlers[]" value="<?php echo esc_attr($key); ?>"
                                                           <?php checked(in_array($key, $enabled_crawlers)); ?> />
                                                </td>
                                                <td><strong><?php echo esc_html($crawler['name']); ?></strong></td>
                                                <td><code><?php echo esc_html(implode(', ', $crawler['user_agents'])); ?></code></td>
                                                <td><?php echo esc_html($crawler['description']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <h3><?php _e('IP Address Management', 'yxjto-gateway'); ?></h3>
                                <p class="description">
                                    <?php _e('Manage IP address ranges for crawler detection. The plugin includes built-in IP ranges for major crawlers.', 'yxjto-gateway'); ?>
                                </p>

                                <div class="ip-ranges-info">
                                    <h4><?php _e('Built-in IP Ranges Statistics', 'yxjto-gateway'); ?></h4>
                                    <?php
                                    // 加载IP数据库
                                    if (!class_exists('WP_Multi_Database_Crawler_IP_Database')) {
                                        require_once YXJTO_GATEWAY_PLUGIN_DIR . 'includes/crawler-ip-database.php';
                                    }
                                    $ip_stats = WP_Multi_Database_Crawler_IP_Database::get_ip_ranges_stats();
                                    ?>

                                    <table class="widefat striped">
                                        <thead>
                                            <tr>
                                                <th><?php _e('Crawler', 'yxjto-gateway'); ?></th>
                                                <th><?php _e('IP Ranges Count', 'yxjto-gateway'); ?></th>
                                                <th><?php _e('Sample IP Ranges', 'yxjto-gateway'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($ip_stats as $crawler => $stats): ?>
                                            <tr>
                                                <td><strong><?php echo esc_html(ucfirst($crawler)); ?></strong></td>
                                                <td><?php echo esc_html($stats['count']); ?></td>
                                                <td>
                                                    <code><?php echo esc_html(implode(', ', array_slice($stats['ranges'], 0, 3))); ?></code>
                                                    <?php if ($stats['count'] > 3): ?>
                                                        <small>(+<?php echo ($stats['count'] - 3); ?> more)</small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <h4><?php _e('Custom IP Ranges', 'yxjto-gateway'); ?></h4>
                                <p class="description">
                                    <?php _e('Add custom IP ranges for additional crawler detection. Use CIDR notation (e.g., ***********/24) or single IPs.', 'yxjto-gateway'); ?>
                                </p>

                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Custom IP Ranges', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <textarea name="custom_ip_ranges" rows="5" cols="50" class="large-text"><?php
                                                echo esc_textarea(isset($crawler_settings['custom_ip_ranges']) ? implode("\n", $crawler_settings['custom_ip_ranges']) : '');
                                            ?></textarea>
                                            <p class="description">
                                                <?php _e('Examples: ***********/24, ********, ***********/24', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>

                                <h3><?php _e('Custom Crawler Patterns', 'yxjto-gateway'); ?></h3>
                                <p class="description">
                                    <?php _e('Add custom User-Agent patterns to detect additional crawlers. One pattern per line.', 'yxjto-gateway'); ?>
                                </p>

                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Custom Patterns', 'yxjto-gateway'); ?></th>
                                        <td>
                                            <textarea name="custom_crawler_patterns" rows="5" cols="50" class="large-text"><?php
                                                echo esc_textarea(isset($crawler_settings['custom_patterns']) ? implode("\n", $crawler_settings['custom_patterns']) : '');
                                            ?></textarea>
                                            <p class="description">
                                                <?php _e('Examples: MyBot, CustomCrawler, SpecialSpider', 'yxjto-gateway'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </form>
                    </div>

                    <!-- 数据库状态调试标签页 -->
                    <div id="debug-status" class="tab-content">
                        <div class="tab-header">
                            <h2><?php _e('Database Status Debug', 'yxjto-gateway'); ?></h2>
                            <button type="button" class="button button-secondary" onclick="refreshDebugStatus()"><?php _e('Refresh Status', 'yxjto-gateway'); ?></button>
                        </div>

                        <p class="description">
                            <?php _e('This page helps debug issues with get_current_database() returning incorrect database information. Use this to identify database state inconsistencies.', 'yxjto-gateway'); ?>
                        </p>

                        <div id="debug-status-content">
                            <?php self::render_debug_status(); ?>
                        </div>
                    </div>










                    <!-- 价格调整配置 -->
                    <div class="tab-content" id="price-adjustment">
                        <?php self::render_price_adjustment_tab(); ?>
                    </div>

                    <!-- 插件设置 -->
                    <div class="tab-content" id="settings">
                        <form method="post" action="" id="settings-form">
                            <?php wp_nonce_field('yxjto_gateway_settings', 'yxjto_gateway_settings_nonce'); ?>
                            <input type="hidden" name="action" value="save_settings" />

                            <h3><?php _e('Plugin Settings', 'yxjto-gateway'); ?></h3>
                            <p><?php _e('Configure plugin behavior and display options.', 'yxjto-gateway'); ?></p>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Console Output', 'yxjto-gateway'); ?></th>
                                <td>
                                    <?php
                                    $settings = WP_Multi_DB_Config_Manager::get_settings();
                                    $disable_console = !empty($settings['disable_console_output']);
                                    ?>
                                    <label>
                                        <input type="checkbox" name="disable_console_output" value="1" <?php checked($disable_console); ?> />
                                        <?php _e('Disable console output', 'yxjto-gateway'); ?>
                                    </label>
                                    <p class="description">
                                        <?php _e('Check this option to disable database switching information output to browser console.', 'yxjto-gateway'); ?>
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Auto Add IP Rules', 'yxjto-gateway'); ?></th>
                                <td>
                                    <?php
                                    $auto_add_ip_rules = !empty($settings['auto_add_ip_rules']);
                                    ?>
                                    <label>
                                        <input type="checkbox" name="auto_add_ip_rules" value="1" <?php checked($auto_add_ip_rules); ?> />
                                        <?php _e('Automatically add customer IP to IP rules after successful payment', 'yxjto-gateway'); ?>
                                    </label>
                                    <p class="description">
                                        <?php _e('When enabled, after a successful payment, the customer\'s IP address will be automatically added to IP rules for the current database. This ensures the customer continues to use the same database for future visits. The customer\'s preferred database will also be saved to their account meta data.', 'yxjto-gateway'); ?>
                                    </p>
                                    <div class="notice notice-info inline" style="margin-top: 10px; padding: 8px 12px;">
                                        <p><strong><?php _e('How it works:', 'yxjto-gateway'); ?></strong></p>
                                        <ul style="margin: 5px 0 5px 20px;">
                                            <li><?php _e('Customer completes a successful payment', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('System detects current database and customer IP', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('IP rule is automatically created (if not exists)', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Customer account meta is updated with preferred database', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Future visits from same IP use the same database', 'yxjto-gateway'); ?></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('WP-Config Integration', 'yxjto-gateway'); ?></th>
                                <td>
                                    <?php
                                    $wp_config_status = WP_Multi_DB_Config_Manager::check_wp_config_integration();
                                    $wp_config_details = WP_Multi_DB_Config_Manager::get_wp_config_integration_details();
                                    ?>
                                    <p class="description">
                                        <?php _e('Integrate with wp-config.php to enable automatic database switching.', 'yxjto-gateway'); ?>
                                    </p>

                                    <div style="margin: 15px 0;">
                                        <strong><?php _e('Current Status:', 'yxjto-gateway'); ?></strong>
                                        <?php if ($wp_config_status): ?>
                                            <span style="color: #46b450;">✅ <?php _e('Integrated', 'yxjto-gateway'); ?></span>
                                        <?php else: ?>
                                            <span style="color: #dc3232;">❌ <?php _e('Not Integrated', 'yxjto-gateway'); ?></span>
                                        <?php endif; ?>

                                        <?php if (!$wp_config_details['file_exists']): ?>
                                            <br><small style="color: #dc3232;"><?php _e('wp-config.php file not found', 'yxjto-gateway'); ?></small>
                                        <?php elseif (!$wp_config_details['is_writable']): ?>
                                            <br><small style="color: #dc3232;"><?php _e('wp-config.php is not writable', 'yxjto-gateway'); ?></small>
                                        <?php elseif ($wp_config_details['has_hook'] && !$wp_config_details['has_plugin_comment']): ?>
                                            <br><small style="color: #856404;"><?php _e('Manual integration detected - use Diagnose tool for details', 'yxjto-gateway'); ?></small>
                                        <?php endif; ?>
                                    </div>

                                    <div style="margin: 15px 0;">
                                        <button type="button" class="button button-primary" id="enable-wp-config" <?php echo $wp_config_status ? 'disabled' : ''; ?>>
                                            <?php _e('Enable WP-Config Integration', 'yxjto-gateway'); ?>
                                        </button>

                                        <button type="button" class="button button-secondary" id="disable-wp-config" <?php echo !$wp_config_status ? 'disabled' : ''; ?>>
                                            <?php _e('Disable WP-Config Integration', 'yxjto-gateway'); ?>
                                        </button>

                                        <a href="<?php echo plugin_dir_url(YXJTO_GATEWAY_PLUGIN_FILE) . 'test/diagnose-wp-config.php'; ?>" class="button" target="_blank">
                                            <?php _e('Diagnose Status', 'yxjto-gateway'); ?>
                                        </a>
                                    </div>

                                    <div class="notice notice-info inline" style="margin: 15px 0; padding: 10px;">
                                        <p><strong><?php _e('What this does:', 'yxjto-gateway'); ?></strong></p>
                                        <ul style="margin-left: 20px;">
                                            <li><?php _e('Modifies wp-config.php to include multi-database hook', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Replaces default database configuration with plugin configuration', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Enables automatic database switching based on rules', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Backs up original configuration for safe restoration', 'yxjto-gateway'); ?></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Database Parameter Cookies', 'yxjto-gateway'); ?></th>
                                <td>
                                    <p class="description">
                                        <?php _e('Manage database parameter cookies that store URL-based database switching preferences.', 'yxjto-gateway'); ?>
                                    </p>

                                    <!-- Cookie 过期时间设置 -->
                                    <div style="margin: 15px 0; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
                                        <h4 style="margin: 0 0 10px 0;"><?php _e('Cookie Expiration Settings', 'yxjto-gateway'); ?></h4>

                                        <?php
                                        $settings = WP_Multi_DB_Config_Manager::get_settings();
                                        $cookie_expire_hours = isset($settings['cookie_expire_hours']) ? intval($settings['cookie_expire_hours']) : 24;
                                        ?>

                                        <table class="form-table" style="margin: 0;">
                                            <tr>
                                                <th scope="row" style="padding-left: 0;">
                                                    <label for="cookie_expire_hours"><?php _e('Cookie Expiration Time', 'yxjto-gateway'); ?></label>
                                                </th>
                                                <td style="padding-left: 0;">
                                                    <input type="number" name="cookie_expire_hours" id="cookie_expire_hours"
                                                           value="<?php echo esc_attr($cookie_expire_hours); ?>"
                                                           min="1" max="720" step="1" class="small-text" />
                                                    <span><?php _e('hours', 'yxjto-gateway'); ?></span>
                                                    <p class="description">
                                                        <?php _e('How long database parameter cookies should remain valid (1-720 hours). After expiration, users will need to use URL parameters or IP rules again.', 'yxjto-gateway'); ?>
                                                        <br>
                                                        <strong><?php _e('Common values:', 'yxjto-gateway'); ?></strong>
                                                        <?php _e('24 (1 day), 168 (1 week), 720 (1 month)', 'yxjto-gateway'); ?>
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>

                                    <div style="margin: 15px 0;">
                                        <p><strong><?php _e('Cookie Priority:', 'yxjto-gateway'); ?></strong></p>
                                        <ol style="margin-left: 20px;">
                                            <li><?php _e('URL Parameters (highest priority)', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('Cookies with URL Parameters (medium priority)', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('IP Address Rules (lowest priority)', 'yxjto-gateway'); ?></li>
                                        </ol>
                                    </div>

                                    <div style="margin: 15px 0; padding: 10px; background: #f0f8ff; border-left: 4px solid #0073aa;">
                                        <p><strong><?php _e('How it works:', 'yxjto-gateway'); ?></strong></p>
                                        <ul style="margin-left: 20px;">
                                            <li><?php printf(__('When a URL parameter matches a rule, it\'s saved to a cookie for %d hours', 'yxjto-gateway'), $cookie_expire_hours); ?></li>
                                            <li><?php _e('Future visits without URL parameters will use the cookie value', 'yxjto-gateway'); ?></li>
                                            <li><?php _e('URL parameters always override cookie values', 'yxjto-gateway'); ?></li>
                                            <li><?php printf(__('Cookies expire automatically after %d hours', 'yxjto-gateway'), $cookie_expire_hours); ?></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button(); ?>
                        </form>

                        <!-- 清除 Cookies 表单 -->
                        <div style="margin-top: 30px; padding: 20px; background: #fff; border: 1px solid #ccd0d4; border-radius: 4px;">
                            <h3><?php _e('Clear Database Parameter Cookies', 'yxjto-gateway'); ?></h3>
                            <p><?php _e('Clear all database parameter cookies from all users. This will force all users to use URL parameters or IP rules for database switching.', 'yxjto-gateway'); ?></p>

                            <!-- 当前配置信息 -->
                            <?php
                            $current_settings = WP_Multi_DB_Config_Manager::get_settings();
                            $current_expire_hours = isset($current_settings['cookie_expire_hours']) ? intval($current_settings['cookie_expire_hours']) : 24;
                            ?>
                            <div style="margin: 15px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #00a0d2;">
                                <p><strong><?php _e('Current Configuration:', 'yxjto-gateway'); ?></strong></p>
                                <ul style="margin: 5px 0 0 20px;">
                                    <li><?php printf(__('Cookie expiration time: %d hours', 'yxjto-gateway'), $current_expire_hours); ?></li>
                                    <li><?php printf(__('Equivalent to: %s', 'yxjto-gateway'), self::format_duration($current_expire_hours)); ?></li>
                                    <li><?php _e('Cookie name: yxjto_gateway_param', 'yxjto-gateway'); ?></li>
                                </ul>
                            </div>

                            <form method="post" action="" style="margin-top: 15px;">
                                <?php wp_nonce_field('yxjto_gateway_clear_cookies', 'yxjto_gateway_clear_cookies_nonce'); ?>
                                <input type="hidden" name="action" value="clear_database_cookies" />

                                <p style="color: #d63638; font-weight: bold;">
                                    ⚠️ <?php _e('Warning: This action will clear cookies for ALL users and cannot be undone.', 'yxjto-gateway'); ?>
                                </p>

                                <button type="submit" class="button button-secondary"
                                        onclick="return confirm('<?php echo esc_js(__('Are you sure you want to clear all database parameter cookies? This action cannot be undone.', 'yxjto-gateway')); ?>')">
                                    <?php _e('Clear All Database Cookies', 'yxjto-gateway'); ?>
                                </button>
                            </form>
                        </div>
                    </div>
            </div>
        </div>

        <!-- 弹窗模态框 -->
        <?php self::render_modals(); ?>

        <!-- 样式和脚本 -->
        <?php self::render_styles_and_scripts(); ?>

        <?php
    }


    
    /**
     * 保存数据库配置
     */
    private static function save_databases() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 保存数据库配置
        if (isset($_POST['databases'])) {
            $databases = [];
            foreach ($_POST['databases'] as $key => $config) {
                $databases[sanitize_key($key)] = [
                    'name' => sanitize_text_field($config['name']),
                    'host' => sanitize_text_field($config['host']),
                    'database' => sanitize_text_field($config['database']),
                    'username' => sanitize_text_field($config['username']),
                    'password' => $config['password'], // 不要sanitize密码
                    'charset' => sanitize_text_field($config['charset']),
                    'enabled' => isset($config['enabled'])
                ];
            }
            WP_Multi_DB_Config_Manager::save_databases($databases);
            add_settings_error('yxjto_gateway_databases', 'databases_updated', __('Database configurations saved successfully!', 'yxjto-gateway'), 'updated');
        } else {
            // 数据库配置主要通过AJAX弹窗处理
            add_settings_error('yxjto_gateway_databases', 'databases_info', __('Database configurations are managed through the modal dialogs.', 'yxjto-gateway'), 'info');
        }
    }

    /**
     * 保存URL规则
     */
    private static function save_url_rules() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 保存URL规则
        if (isset($_POST['url_rules'])) {
            $url_rules = [];
            foreach ($_POST['url_rules'] as $index => $rule) {
                $url_rules[] = [
                    'parameter' => sanitize_key($rule['parameter']),
                    'value' => sanitize_text_field($rule['value']),
                    'database' => sanitize_key($rule['database']),
                    'enabled' => isset($rule['enabled'])
                ];
            }
            WP_Multi_DB_Config_Manager::save_url_rules($url_rules);
            add_settings_error('yxjto_gateway_url_rules', 'url_rules_updated', __('URL rules saved successfully!', 'yxjto-gateway'), 'updated');
        } else {
            // URL规则主要通过AJAX弹窗处理
            add_settings_error('yxjto_gateway_url_rules', 'url_rules_info', __('URL rules are managed through the modal dialogs.', 'yxjto-gateway'), 'info');
        }
    }

    /**
     * 保存IP规则
     */
    private static function save_ip_rules() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 保存IP规则
        if (isset($_POST['ip_rules'])) {
            $ip_rules = [];
            foreach ($_POST['ip_rules'] as $index => $rule) {
                $ip_rules[] = [
                    'ip_range' => sanitize_text_field($rule['ip_range']),
                    'database' => sanitize_key($rule['database']),
                    'enabled' => isset($rule['enabled'])
                ];
            }
            WP_Multi_DB_Config_Manager::save_ip_rules($ip_rules);
            add_settings_error('yxjto_gateway_ip_rules', 'ip_rules_updated', __('IP rules saved successfully!', 'yxjto-gateway'), 'updated');
        } else {
            // IP规则主要通过AJAX弹窗处理
            add_settings_error('yxjto_gateway_ip_rules', 'ip_rules_info', __('IP rules are managed through the modal dialogs.', 'yxjto-gateway'), 'info');
        }
    }

    /**
     * 保存优惠券同步设置
     */
    private static function save_coupon_sync() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 收集优惠券同步设置
        $coupon_settings = [
            'enable_coupon_sync' => isset($_POST['enable_coupon_sync']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'realtime'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'daily'),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'newest'),
            'enable_sync_logging' => isset($_POST['enable_sync_logging']),
            'exclude_expired' => isset($_POST['exclude_expired']),
            'max_retries' => intval($_POST['max_retries'] ?? 3)
        ];

        // 验证 max_retries 范围
        if ($coupon_settings['max_retries'] < 1 || $coupon_settings['max_retries'] > 10) {
            $coupon_settings['max_retries'] = 3;
        }

        // 保存设置
        $result = WP_Multi_DB_Config_Manager::save_coupon_sync_settings($coupon_settings);

        if ($result) {
            add_settings_error('yxjto_gateway_coupon_sync', 'coupon_sync_updated', __('Coupon synchronization settings saved successfully!', 'yxjto-gateway'), 'updated');
            
            // 如果启用了批量同步，安排 WP Cron 任务
            if ($coupon_settings['enable_coupon_sync'] && $coupon_settings['sync_mode'] === 'batch') {
                self::schedule_coupon_batch_sync($coupon_settings['batch_interval']);
            } else {
                // 如果禁用了批量同步，清除现有的 Cron 任务
                wp_clear_scheduled_hook('yxjto_coupon_batch_sync');
            }
        } else {
            add_settings_error('yxjto_gateway_coupon_sync', 'coupon_sync_error', __('Failed to save coupon synchronization settings.', 'yxjto-gateway'), 'error');
        }
    }

    /**
     * 安排优惠券批量同步任务
     */
    private static function schedule_coupon_batch_sync($interval = 'daily') {
        // 清除现有任务
        wp_clear_scheduled_hook('yxjto_coupon_batch_sync');
        
        // 安排新任务
        if (!wp_next_scheduled('yxjto_coupon_batch_sync')) {
            wp_schedule_event(time(), $interval, 'yxjto_coupon_batch_sync');
        }
    }

    /**
     * 保存配送同步设置
     */
    private static function save_shipping_sync() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 收集配送同步设置
        $shipping_settings = [
            'enable_shipping_sync' => isset($_POST['enable_shipping_sync']),
            'sync_zones' => isset($_POST['sync_zones']),
            'sync_methods' => isset($_POST['sync_methods']),
            'sync_classes' => isset($_POST['sync_classes']),
            'sync_local_pickup' => isset($_POST['sync_local_pickup']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'realtime'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'daily'),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'newest'),
            'enable_sync_logging' => isset($_POST['enable_sync_logging']),
            'exclude_disabled' => isset($_POST['exclude_disabled']),
            'max_retries' => intval($_POST['max_retries'] ?? 3)
        ];

        // 验证 max_retries 范围
        if ($shipping_settings['max_retries'] < 1 || $shipping_settings['max_retries'] > 10) {
            $shipping_settings['max_retries'] = 3;
        }

        // 保存设置到WordPress选项
        $result = update_option('yxjto_shipping_sync_settings', $shipping_settings);

        if ($result !== false) {
            add_settings_error('yxjto_gateway_shipping_sync', 'shipping_sync_updated', __('Shipping synchronization settings saved successfully!', 'yxjto-gateway'), 'updated');
            
            // 如果启用了批量同步，安排 WP Cron 任务
            if ($shipping_settings['enable_shipping_sync'] && $shipping_settings['sync_mode'] === 'batch') {
                self::schedule_shipping_batch_sync($shipping_settings['batch_interval']);
            } else {
                // 如果禁用了批量同步，清除现有的 Cron 任务
                wp_clear_scheduled_hook('yxjto_shipping_batch_sync');
            }
        } else {
            add_settings_error('yxjto_gateway_shipping_sync', 'shipping_sync_error', __('Failed to save shipping synchronization settings.', 'yxjto-gateway'), 'error');
        }
    }

    /**
     * 安排配送批量同步任务
     */
    private static function schedule_shipping_batch_sync($interval = 'daily') {
        // 清除现有任务
        wp_clear_scheduled_hook('yxjto_shipping_batch_sync');
        
        // 安排新任务
        if (!wp_next_scheduled('yxjto_shipping_batch_sync')) {
            wp_schedule_event(time(), $interval, 'yxjto_shipping_batch_sync');
        }
    }

    /**
     * 保存税收同步设置
     */
    private static function save_tax_sync() {
        if (!current_user_can('manage_options')) {
            wp_die(__('权限不足', 'yxjto-gateway'));
        }

        // 收集税收同步设置
        $tax_settings = [
            'enable_tax_replication' => isset($_POST['enable_tax_replication']),
            'enable_tax_rates_sync' => isset($_POST['enable_tax_rates_sync']),
            'enable_tax_classes_sync' => isset($_POST['enable_tax_classes_sync']),
            'enable_tax_settings_sync' => isset($_POST['enable_tax_settings_sync']),
            'enable_standard_rates_sync' => isset($_POST['enable_standard_rates_sync']),
            'enable_reduced_rates_sync' => isset($_POST['enable_reduced_rates_sync']),
            'enable_zero_rates_sync' => isset($_POST['enable_zero_rates_sync']),
            'sync_tax_based_on' => isset($_POST['sync_tax_based_on']),
            'sync_shipping_tax_class' => isset($_POST['sync_shipping_tax_class']),
            'sync_tax_round_at_subtotal' => isset($_POST['sync_tax_round_at_subtotal']),
            'sync_tax_display_shop' => isset($_POST['sync_tax_display_shop']),
            'sync_tax_display_cart' => isset($_POST['sync_tax_display_cart']),
            'sync_prices_include_tax' => isset($_POST['sync_prices_include_tax']),
            'sync_calc_taxes' => isset($_POST['sync_calc_taxes']),
            'sync_default_customer_address' => isset($_POST['sync_default_customer_address']),
            'sync_store_address' => isset($_POST['sync_store_address']),
            'sync_mode' => sanitize_text_field($_POST['sync_mode'] ?? 'realtime'),
            'batch_interval' => sanitize_text_field($_POST['batch_interval'] ?? 'daily'),
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'newest'),
            'enable_sync_logging' => isset($_POST['enable_sync_logging']),
            'backup_before_sync' => isset($_POST['backup_before_sync']),
            'max_retries' => intval($_POST['max_retries'] ?? 3)
        ];

        // 验证 max_retries 范围
        if ($tax_settings['max_retries'] < 0 || $tax_settings['max_retries'] > 10) {
            $tax_settings['max_retries'] = 3;
        }

        // 保存设置到配置管理器
        $result = WP_Multi_DB_Config_Manager::save_tax_replication_settings($tax_settings);

        if ($result !== false) {
            add_settings_error('yxjto_gateway_tax_sync', 'tax_sync_updated', __('税收同步设置保存成功！', 'yxjto-gateway'), 'updated');
            
            // 如果启用了计划同步，安排 WP Cron 任务
            if ($tax_settings['enable_tax_replication'] && $tax_settings['sync_mode'] === 'scheduled') {
                self::schedule_tax_batch_sync($tax_settings['batch_interval']);
            } else {
                // 如果禁用了计划同步，清除现有的 Cron 任务
                wp_clear_scheduled_hook('yxjto_tax_sync_cron');
            }
        } else {
            add_settings_error('yxjto_gateway_tax_sync', 'tax_sync_error', __('税收同步设置保存失败。', 'yxjto-gateway'), 'error');
        }
    }

    /**
     * 安排税收批量同步任务
     */
    private static function schedule_tax_batch_sync($interval = 'daily') {
        // 清除现有任务
        wp_clear_scheduled_hook('yxjto_tax_sync_cron');
        
        // 安排新任务
        if (!wp_next_scheduled('yxjto_tax_sync_cron')) {
            wp_schedule_event(time(), $interval, 'yxjto_tax_sync_cron');
        }
    }

    /**
     * 保存设置
     */
    private static function save_settings() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 验证nonce（已在handle_form_submissions中验证过，这里不需要重复验证）
        // 如果需要额外验证，使用正确的nonce字段名
        // if (!isset($_POST['yxjto_gateway_settings_nonce']) || !wp_verify_nonce($_POST['yxjto_gateway_settings_nonce'], 'yxjto_gateway_settings')) {
        //     wp_die(__('Security check failed', 'yxjto-gateway'));
        // }

        // save_settings方法只处理插件设置，不处理数据库、IP规则、URL规则
        // 这些数据由各自的保存方法处理

        // 保存插件设置
        $settings = WP_Multi_DB_Config_Manager::get_settings();
        $settings['disable_console_output'] = isset($_POST['disable_console_output']);
        $settings['auto_add_ip_rules'] = isset($_POST['auto_add_ip_rules']);

        // 保存 cookie 过期时间设置
        if (isset($_POST['cookie_expire_hours'])) {
            $cookie_expire_hours = intval($_POST['cookie_expire_hours']);
            // 验证过期时间范围（1小时到30天）
            if ($cookie_expire_hours >= 1 && $cookie_expire_hours <= 720) {
                $settings['cookie_expire_hours'] = $cookie_expire_hours;
            } else {
                // 如果值无效，使用默认值24小时
                $settings['cookie_expire_hours'] = 24;
                add_settings_error(
                    'yxjto_gateway_settings',
                    'invalid_cookie_expire_hours',
                    __('Invalid cookie expiration time. Using default value of 24 hours.', 'yxjto-gateway'),
                    'error'
                );
            }
        }

        WP_Multi_DB_Config_Manager::save_settings($settings);

        add_settings_error('yxjto_gateway_settings', 'settings_updated', __('Settings saved successfully!', 'yxjto-gateway'), 'updated');
    }

    /**
     * 保存爬虫检测设置
     */
    private static function save_crawler_detection() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 处理爬虫检测设置
        $crawler_settings = [
            'enabled' => isset($_POST['crawler_detection_enabled']),
            'log_visits' => isset($_POST['log_crawler_visits']),
            'enable_ip_detection' => isset($_POST['enable_ip_detection']),
            'enable_ip_verification' => isset($_POST['enable_ip_verification']),
            'enable_reverse_dns' => isset($_POST['enable_reverse_dns']),
            'enabled_crawlers' => isset($_POST['enabled_crawlers']) ? $_POST['enabled_crawlers'] : [],
            'custom_patterns' => [],
            'custom_ip_ranges' => []
        ];

        // 处理自定义爬虫模式
        if (!empty($_POST['custom_crawler_patterns'])) {
            $patterns = explode("\n", $_POST['custom_crawler_patterns']);
            $crawler_settings['custom_patterns'] = array_filter(array_map('trim', $patterns));
        }

        // 处理自定义IP段
        if (!empty($_POST['custom_ip_ranges'])) {
            $ip_ranges = explode("\n", $_POST['custom_ip_ranges']);
            $crawler_settings['custom_ip_ranges'] = array_filter(array_map('trim', $ip_ranges));
        }

        // 保存设置
        if (WP_Multi_DB_Config_Manager::save_crawler_settings($crawler_settings)) {
            add_settings_error('yxjto_gateway_crawler_detection', 'crawler_detection_updated',
                __('Crawler detection settings saved successfully!', 'yxjto-gateway'), 'updated');
        } else {
            add_settings_error('yxjto_gateway_crawler_detection', 'crawler_detection_error',
                __('Failed to save crawler detection settings.', 'yxjto-gateway'), 'error');
        }
    }

    /**
     * 清除数据库参数 cookies
     */
    private static function clear_database_cookies() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'yxjto-gateway'));
        }

        // 清除当前用户的 cookie
        $cookie_name = 'yxjto_gateway_param';
        setcookie($cookie_name, '', time() - 3600, '/');

        // 由于无法直接清除所有用户的 cookies，我们设置一个全局标记
        // 当用户下次访问时，会检查这个标记并清除过期的 cookies
        $clear_timestamp = time();
        update_option('yxjto_gateway_clear_cookies_timestamp', $clear_timestamp);

        // 记录操作日志
        $current_user = wp_get_current_user();
        $log_message = sprintf(
            'Database parameter cookies cleared by admin user: %s (ID: %d) at %s',
            $current_user->user_login,
            $current_user->ID,
            date('Y-m-d H:i:s')
        );

        // 写入日志文件（如果启用了日志记录）
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[YXJTO Gateway] ' . $log_message);
        }

        add_settings_error(
            'yxjto_gateway_clear_cookies',
            'cookies_cleared',
            __('Database parameter cookies have been cleared. Users will need to use URL parameters or IP rules for database switching.', 'yxjto-gateway'),
            'updated'
        );
    }

    /**
     * 格式化时间长度显示
     */
    private static function format_duration($hours) {
        if ($hours < 24) {
            return sprintf(_n('%d hour', '%d hours', $hours, 'yxjto-gateway'), $hours);
        } elseif ($hours < 168) { // 小于一周
            $days = round($hours / 24, 1);
            if ($days == floor($days)) {
                $days = intval($days);
                return sprintf(_n('%d day', '%d days', $days, 'yxjto-gateway'), $days);
            } else {
                return sprintf(__('%.1f days', 'yxjto-gateway'), $days);
            }
        } else { // 一周或更长
            $weeks = round($hours / 168, 1);
            if ($weeks == floor($weeks)) {
                $weeks = intval($weeks);
                return sprintf(_n('%d week', '%d weeks', $weeks, 'yxjto-gateway'), $weeks);
            } else {
                return sprintf(__('%.1f weeks', 'yxjto-gateway'), $weeks);
            }
        }
    }

    /**
     * 渲染弹窗模态框
     */
    private static function render_modals() {
        $database_configs = WP_Multi_DB_Config_Manager::get_databases();
        ?>
        <!-- 数据库配置弹窗 -->
        <div id="database-modal" class="wp-multi-db-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="database-modal-title"><?php _e('Add Database', 'yxjto-gateway'); ?></h2>
                    <span class="modal-close" onclick="closeDatabaseModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="database-form">
                        <input type="hidden" id="database-key" name="database_key" />
                        <table class="form-table">
                            <tr>
                                <th><label for="database-name"><?php _e('Name', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="database-name" name="name" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="database-host"><?php _e('Host', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="database-host" name="host" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="database-database"><?php _e('Database', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="database-database" name="database" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="database-username"><?php _e('Username', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="database-username" name="username" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="database-password"><?php _e('Password', 'yxjto-gateway'); ?></label></th>
                                <td><input type="password" id="database-password" name="password" class="regular-text" /></td>
                            </tr>
                            <tr>
                                <th><label for="database-charset"><?php _e('Charset', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="database-charset" name="charset" class="regular-text" value="utf8mb4" /></td>
                            </tr>
                            <tr>
                                <th><label for="database-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></label></th>
                                <td><input type="checkbox" id="database-enabled" name="enabled" value="1" checked /></td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="button button-primary" onclick="saveDatabaseConfig()"><?php _e('Save', 'yxjto-gateway'); ?></button>
                    <button type="button" class="button" onclick="closeDatabaseModal()"><?php _e('Cancel', 'yxjto-gateway'); ?></button>
                </div>
            </div>
        </div>

        <!-- URL规则弹窗 -->
        <div id="url-rule-modal" class="wp-multi-db-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="url-rule-modal-title"><?php _e('Add URL Rule', 'yxjto-gateway'); ?></h2>
                    <span class="modal-close" onclick="closeUrlRuleModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="url-rule-form">
                        <input type="hidden" id="url-rule-index" name="rule_index" />
                        <table class="form-table">
                            <tr>
                                <th><label for="url-parameter"><?php _e('Parameter Name', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="url-parameter" name="parameter" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="url-value"><?php _e('Parameter Value', 'yxjto-gateway'); ?></label></th>
                                <td><input type="text" id="url-value" name="value" class="regular-text" required /></td>
                            </tr>
                            <tr>
                                <th><label for="url-database"><?php _e('Target Database', 'yxjto-gateway'); ?></label></th>
                                <td>
                                    <select id="url-database" name="database" required>
                                        <?php foreach ($database_configs as $db_key => $db_config): ?>
                                        <option value="<?php echo esc_attr($db_key); ?>">
                                            <?php echo esc_html($db_config['name'] ?? $db_key); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th><label for="url-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></label></th>
                                <td><input type="checkbox" id="url-enabled" name="enabled" value="1" checked /></td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="button button-primary" onclick="saveUrlRule()"><?php _e('Save', 'yxjto-gateway'); ?></button>
                    <button type="button" class="button" onclick="closeUrlRuleModal()"><?php _e('Cancel', 'yxjto-gateway'); ?></button>
                </div>
            </div>
        </div>

        <!-- IP规则弹窗 -->
        <div id="ip-rule-modal" class="wp-multi-db-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="ip-rule-modal-title"><?php _e('Add IP Rule', 'yxjto-gateway'); ?></h2>
                    <span class="modal-close" onclick="closeIpRuleModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="ip-rule-form">
                        <input type="hidden" id="ip-rule-index" name="rule_index" />
                        <table class="form-table">
                            <tr>
                                <th><label for="ip-range"><?php _e('IP Range', 'yxjto-gateway'); ?></label></th>
                                <td>
                                    <input type="text" id="ip-range" name="ip_range" class="regular-text" required />
                                    <p class="description"><?php _e('Examples: ***********, ***********/24, ***********-***********00', 'yxjto-gateway'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th><label for="ip-database"><?php _e('Target Database', 'yxjto-gateway'); ?></label></th>
                                <td>
                                    <select id="ip-database" name="database" required>
                                        <?php foreach ($database_configs as $db_key => $db_config): ?>
                                        <option value="<?php echo esc_attr($db_key); ?>">
                                            <?php echo esc_html($db_config['name'] ?? $db_key); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th><label for="ip-enabled"><?php _e('Enabled', 'yxjto-gateway'); ?></label></th>
                                <td><input type="checkbox" id="ip-enabled" name="enabled" value="1" checked /></td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="button button-primary" onclick="saveIpRule()"><?php _e('Save', 'yxjto-gateway'); ?></button>
                    <button type="button" class="button" onclick="closeIpRuleModal()"><?php _e('Cancel', 'yxjto-gateway'); ?></button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * 渲染样式和脚本
     */
    private static function render_styles_and_scripts() {
        $database_configs = WP_Multi_DB_Config_Manager::get_databases();
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();
        ?>
        <style>
        /* 标签切换样式 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .nav-tab-wrapper {
            margin-bottom: 20px;
        }

        /* 优先级指示器样式 */
        .priority-indicator {
            display: inline-block;
            padding: 2px 6px;
            margin-left: 8px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
            text-transform: uppercase;
        }

        .priority-indicator.highest {
            background: #d63638;
            color: white;
        }

        .priority-indicator.high {
            background: #dba617;
            color: white;
        }

        .priority-indicator.medium {
            background: #00a32a;
            color: white;
        }

        .priority-indicator.low {
            background: #8c8f94;
            color: white;
        }

        .priority-indicator.debug {
            background: #7e57c2;
            color: white;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .tab-header h2 {
            margin: 0;
        }

        .badge {
            display: inline-block;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: bold;
            border-radius: 3px;
            margin-left: 8px;
        }

        .badge.default {
            background: #0073aa;
            color: white;
        }

        .badge.current {
            background: #46b450;
            color: white;
        }

        .status-enabled {
            color: #46b450;
            font-weight: bold;
        }

        .status-disabled {
            color: #dc3232;
            font-weight: bold;
        }

        /* IP规则类型样式 */
        .rule-type {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .rule-type.auto-added {
            background-color: #e7f3ff;
            color: #0073aa;
            border: 1px solid #b8d4ea;
        }

        .rule-type.manual {
            background-color: #f0f0f1;
            color: #646970;
            border: 1px solid #c3c4c7;
        }

        .rule-type .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
        }

        .rule-type + .row-actions {
            margin-top: 4px;
        }

        .rule-type + .row-actions .description {
            font-size: 11px;
            color: #646970;
            font-style: italic;
        }

        .no-items {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        /* 弹窗样式 */
        .wp-multi-db-modal {
            position: fixed;
            z-index: 100000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
        }

        .modal-close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-close:hover {
            color: #000;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #ddd;
            text-align: right;
        }

        .modal-footer .button {
            margin-left: 10px;
        }

        /* 调试状态样式 */
        .debug-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .debug-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.04);
        }

        .debug-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #23282d;
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 10px;
        }

        .debug-table {
            width: 100%;
            border-collapse: collapse;
        }

        .debug-table td {
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
            vertical-align: top;
        }

        .debug-table td:first-child {
            width: 40%;
            font-weight: 500;
            color: #555;
        }

        .debug-table td:last-child {
            color: #333;
            word-break: break-all;
        }

        .debug-undefined {
            color: #999;
            font-style: italic;
        }

        .debug-true {
            color: #46b450;
            font-weight: bold;
        }

        .debug-false {
            color: #dc3232;
            font-weight: bold;
        }

        .debug-null {
            color: #999;
            font-style: italic;
        }

        .debug-error {
            color: #dc3232;
            font-weight: bold;
        }

        .debug-current-db {
            background: #e7f3ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: #0073aa;
        }

        .debug-actual-db {
            background: #f0f9ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: #0066cc;
        }

        .debug-wpdb-db {
            background: #fff2e6;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            color: #cc6600;
        }

        .debug-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }

        .debug-success .dashicons {
            margin-right: 8px;
            color: #28a745;
        }

        .debug-issues {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 4px;
        }

        .debug-issues .dashicons {
            color: #ffc107;
            margin-right: 8px;
        }

        .debug-issues ul {
            margin: 10px 0 0 20px;
        }

        .debug-issues li {
            margin-bottom: 5px;
        }

        /* 优惠券同步样式 */
        .coupon-sync-status {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        .coupon-sync-status h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #495057;
        }

        .sync-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .sync-stat-card {
            background: #ffffff;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sync-stat-card h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            display: block;
            color: #495057;
        }

        .stat-number.success {
            color: #28a745;
        }

        .stat-number.warning {
            color: #ffc107;
        }

        .stat-number.error {
            color: #dc3545;
        }

        .stat-number.muted {
            color: #6c757d;
        }

        .sync-info {
            margin-bottom: 20px;
        }

        .sync-info p {
            margin: 8px 0;
            color: #6c757d;
        }

        .sync-disabled-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
        }

        .sync-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .sync-actions .button {
            margin: 0;
        }

        #sync-result-message {
            padding: 10px;
            border-radius: 4px;
            margin-top: 15px;
        }

        #sync-result-message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        #sync-result-message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        #batch-interval-row {
            transition: all 0.3s ease;
        }

        /* 同步状态指示器 */
        .sync-status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .sync-status-indicator.active {
            background: #28a745;
        }

        .sync-status-indicator.inactive {
            background: #dc3545;
        }

        .sync-status-indicator.warning {
            background: #ffc107;
        }
        </style>

        <script>
        // 全局变量
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
        var databaseConfigs = <?php echo json_encode($database_configs); ?>;
        var urlRules = <?php echo json_encode($url_rules); ?>;
        var ipRules = <?php echo json_encode($ip_rules); ?>;

        // 数据库配置弹窗函数
        function openDatabaseModal(key = null) {
            if (key) {
                // 编辑模式
                document.getElementById('database-modal-title').textContent = '<?php _e('Edit Database', 'yxjto-gateway'); ?>';
                document.getElementById('database-key').value = key;
                var config = databaseConfigs[key];
                document.getElementById('database-name').value = config.name || '';
                document.getElementById('database-host').value = config.host || '';
                document.getElementById('database-database').value = config.database || '';
                document.getElementById('database-username').value = config.username || '';
                document.getElementById('database-password').value = config.password || '';
                document.getElementById('database-charset').value = config.charset || 'utf8mb4';
                document.getElementById('database-enabled').checked = config.enabled || false;
            } else {
                // 添加模式
                document.getElementById('database-modal-title').textContent = '<?php _e('Add Database', 'yxjto-gateway'); ?>';
                document.getElementById('database-form').reset();
                document.getElementById('database-key').value = '';
                document.getElementById('database-charset').value = 'utf8mb4';
                document.getElementById('database-enabled').checked = true;
            }
            document.getElementById('database-modal').style.display = 'block';
        }

        function closeDatabaseModal() {
            document.getElementById('database-modal').style.display = 'none';
        }

        function editDatabase(key) {
            openDatabaseModal(key);
        }

        function deleteDatabase(key) {
            if (confirm('<?php _e('Are you sure you want to delete this database configuration?', 'yxjto-gateway'); ?>')) {
                location.href = '?page=yxjto-gateway&action=delete_database&key=' + encodeURIComponent(key);
            }
        }

        function saveDatabaseConfig() {
            var form = document.getElementById('database-form');
            var formData = new FormData(form);
            formData.append('action', 'yxjto_gateway_save_database');
            formData.append('nonce', '<?php echo wp_create_nonce('yxjto_gateway_save_database'); ?>');

            // 显示加载状态
            var saveBtn = document.querySelector('#database-modal .button-primary');
            var originalText = saveBtn.textContent;
            saveBtn.textContent = '<?php _e('Saving...', 'yxjto-gateway'); ?>';
            saveBtn.disabled = true;

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('<?php _e('Database configuration saved!', 'yxjto-gateway'); ?>');
                    closeDatabaseModal();
                    location.reload();
                } else {
                    alert('<?php _e('Error: ', 'yxjto-gateway'); ?>' + (data.data || '<?php _e('Unknown error', 'yxjto-gateway'); ?>'));
                }
            })
            .catch(error => {
                alert('<?php _e('Network error occurred', 'yxjto-gateway'); ?>');
            })
            .finally(() => {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
            });
        }

        // URL规则弹窗函数
        function openUrlRuleModal(index = null) {
            if (index !== null) {
                // 编辑模式
                document.getElementById('url-rule-modal-title').textContent = '<?php _e('Edit URL Rule', 'yxjto-gateway'); ?>';
                document.getElementById('url-rule-index').value = index;
                var rule = urlRules[index];
                document.getElementById('url-parameter').value = rule.parameter || '';
                document.getElementById('url-value').value = rule.value || '';
                document.getElementById('url-database').value = rule.database || '';
                document.getElementById('url-enabled').checked = rule.enabled || false;
            } else {
                // 添加模式
                document.getElementById('url-rule-modal-title').textContent = '<?php _e('Add URL Rule', 'yxjto-gateway'); ?>';
                document.getElementById('url-rule-form').reset();
                document.getElementById('url-rule-index').value = '';
                document.getElementById('url-enabled').checked = true;
            }
            document.getElementById('url-rule-modal').style.display = 'block';
        }

        function closeUrlRuleModal() {
            document.getElementById('url-rule-modal').style.display = 'none';
        }

        function editUrlRule(index) {
            openUrlRuleModal(index);
        }

        function deleteUrlRule(index) {
            if (confirm('<?php _e('Are you sure you want to delete this URL rule?', 'yxjto-gateway'); ?>')) {
                location.href = '?page=yxjto-gateway&action=delete_url_rule&index=' + index;
            }
        }

        function saveUrlRule() {
            var form = document.getElementById('url-rule-form');
            var formData = new FormData(form);
            formData.append('action', 'yxjto_gateway_save_url_rule');
            formData.append('nonce', '<?php echo wp_create_nonce('yxjto_gateway_save_url_rule'); ?>');

            // 显示加载状态
            var saveBtn = document.querySelector('#url-rule-modal .button-primary');
            var originalText = saveBtn.textContent;
            saveBtn.textContent = '<?php _e('Saving...', 'yxjto-gateway'); ?>';
            saveBtn.disabled = true;

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('<?php _e('URL rule saved!', 'yxjto-gateway'); ?>');
                    closeUrlRuleModal();
                    location.reload();
                } else {
                    alert('<?php _e('Error: ', 'yxjto-gateway'); ?>' + (data.data || '<?php _e('Unknown error', 'yxjto-gateway'); ?>'));
                }
            })
            .catch(error => {
                alert('<?php _e('Network error occurred', 'yxjto-gateway'); ?>');
            })
            .finally(() => {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
            });
        }

        // IP规则弹窗函数
        function openIpRuleModal(index = null) {
            if (index !== null) {
                // 编辑模式
                document.getElementById('ip-rule-modal-title').textContent = '<?php _e('Edit IP Rule', 'yxjto-gateway'); ?>';
                document.getElementById('ip-rule-index').value = index;
                var rule = ipRules[index];
                document.getElementById('ip-range').value = rule.ip_range || '';
                document.getElementById('ip-database').value = rule.database || '';
                document.getElementById('ip-enabled').checked = rule.enabled || false;
            } else {
                // 添加模式
                document.getElementById('ip-rule-modal-title').textContent = '<?php _e('Add IP Rule', 'yxjto-gateway'); ?>';
                document.getElementById('ip-rule-form').reset();
                document.getElementById('ip-rule-index').value = '';
                document.getElementById('ip-enabled').checked = true;
            }
            document.getElementById('ip-rule-modal').style.display = 'block';
        }

        function closeIpRuleModal() {
            document.getElementById('ip-rule-modal').style.display = 'none';
        }

        function editIpRule(index) {
            openIpRuleModal(index);
        }

        function deleteIpRule(index) {
            if (confirm('<?php _e('Are you sure you want to delete this IP rule?', 'yxjto-gateway'); ?>')) {
                location.href = '?page=yxjto-gateway&action=delete_ip_rule&index=' + index;
            }
        }

        function saveIpRule() {
            var form = document.getElementById('ip-rule-form');
            var formData = new FormData(form);
            formData.append('action', 'yxjto_gateway_save_ip_rule');
            formData.append('nonce', '<?php echo wp_create_nonce('yxjto_gateway_save_ip_rule'); ?>');

            // 显示加载状态
            var saveBtn = document.querySelector('#ip-rule-modal .button-primary');
            var originalText = saveBtn.textContent;
            saveBtn.textContent = '<?php _e('Saving...', 'yxjto-gateway'); ?>';
            saveBtn.disabled = true;

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('<?php _e('IP rule saved!', 'yxjto-gateway'); ?>');
                    closeIpRuleModal();
                    location.reload();
                } else {
                    alert('<?php _e('Error: ', 'yxjto-gateway'); ?>' + (data.data || '<?php _e('Unknown error', 'yxjto-gateway'); ?>'));
                }
            })
            .catch(error => {
                alert('<?php _e('Network error occurred', 'yxjto-gateway'); ?>');
            })
            .finally(() => {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
            });
        }

        // 标签切换功能
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有标签链接和内容
            var tabLinks = document.querySelectorAll('.nav-tab');
            var tabContents = document.querySelectorAll('.tab-content');

            // 为每个标签链接添加点击事件
            tabLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 获取目标标签ID
                    var targetId = this.getAttribute('href').substring(1);

                    // 移除所有活动状态
                    tabLinks.forEach(function(tab) {
                        tab.classList.remove('nav-tab-active');
                    });
                    tabContents.forEach(function(content) {
                        content.classList.remove('active');
                    });

                    // 激活当前标签
                    this.classList.add('nav-tab-active');
                    var targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });

            // 处理URL hash
            function handleHash() {
                var hash = window.location.hash.substring(1);
                if (hash) {
                    var targetTab = document.querySelector('a[href="#' + hash + '"]');
                    var targetContent = document.getElementById(hash);

                    if (targetTab && targetContent) {
                        // 移除所有活动状态
                        tabLinks.forEach(function(tab) {
                            tab.classList.remove('nav-tab-active');
                        });
                        tabContents.forEach(function(content) {
                            content.classList.remove('active');
                        });

                        // 激活目标标签
                        targetTab.classList.add('nav-tab-active');
                        targetContent.classList.add('active');
                    }
                }
            }

            // 页面加载时处理hash
            handleHash();

            // 监听hash变化
            window.addEventListener('hashchange', handleHash);
        });

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            var modals = document.querySelectorAll('.wp-multi-db-modal');
            modals.forEach(function(modal) {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // WP-Config集成按钮处理
        document.addEventListener('DOMContentLoaded', function() {
            var enableBtn = document.getElementById('enable-wp-config');
            var disableBtn = document.getElementById('disable-wp-config');

            if (enableBtn) {
                enableBtn.addEventListener('click', function() {
                    if (confirm('<?php _e('This will modify your wp-config.php file. A backup will be created. Continue?', 'yxjto-gateway'); ?>')) {
                        enableWpConfigIntegration();
                    }
                });
            }

            if (disableBtn) {
                disableBtn.addEventListener('click', function() {
                    if (confirm('<?php _e('This will restore your original wp-config.php file. Continue?', 'yxjto-gateway'); ?>')) {
                        disableWpConfigIntegration();
                    }
                });
            }
        });

        function enableWpConfigIntegration() {
            var enableBtn = document.getElementById('enable-wp-config');
            var disableBtn = document.getElementById('disable-wp-config');

            enableBtn.disabled = true;
            enableBtn.textContent = '<?php _e('Enabling...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_gateway_enable_wp_config',
                nonce: '<?php echo wp_create_nonce('yxjto_gateway_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('<?php _e('WP-Config integration enabled successfully!', 'yxjto-gateway'); ?>');
                    enableBtn.disabled = true;
                    disableBtn.disabled = false;
                    location.reload(); // 刷新页面以更新状态
                } else {
                    alert('<?php _e('Error:', 'yxjto-gateway'); ?> ' + response.data);
                    enableBtn.disabled = false;
                    enableBtn.textContent = '<?php _e('Enable WP-Config Integration', 'yxjto-gateway'); ?>';
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
                enableBtn.disabled = false;
                enableBtn.textContent = '<?php _e('Enable WP-Config Integration', 'yxjto-gateway'); ?>';
            });
        }

        function disableWpConfigIntegration() {
            var enableBtn = document.getElementById('enable-wp-config');
            var disableBtn = document.getElementById('disable-wp-config');

            disableBtn.disabled = true;
            disableBtn.textContent = '<?php _e('Disabling...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_gateway_disable_wp_config',
                nonce: '<?php echo wp_create_nonce('yxjto_gateway_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('<?php _e('WP-Config integration disabled successfully!', 'yxjto-gateway'); ?>');
                    enableBtn.disabled = false;
                    disableBtn.disabled = true;
                    location.reload(); // 刷新页面以更新状态
                } else {
                    alert('<?php _e('Error:', 'yxjto-gateway'); ?> ' + response.data);
                    disableBtn.disabled = false;
                    disableBtn.textContent = '<?php _e('Disable WP-Config Integration', 'yxjto-gateway'); ?>';
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
                disableBtn.disabled = false;
                disableBtn.textContent = '<?php _e('Disable WP-Config Integration', 'yxjto-gateway'); ?>';
            });
        }

        // 刷新调试状态
        function refreshDebugStatus() {
            var button = document.querySelector('button[onclick="refreshDebugStatus()"]');
            var originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Refreshing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_gateway_refresh_debug_status',
                nonce: '<?php echo wp_create_nonce('yxjto_gateway_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    document.getElementById('debug-status-content').innerHTML = response.data;
                } else {
                    alert('<?php _e('Error refreshing debug status:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 优惠券同步相关函数
        document.addEventListener('DOMContentLoaded', function() {
            // 监听同步模式变化
            const syncModeSelect = document.querySelector('select[name="sync_mode"]');
            const batchIntervalRow = document.getElementById('batch-interval-row');
            
            if (syncModeSelect && batchIntervalRow) {
                syncModeSelect.addEventListener('change', function() {
                    if (this.value === 'batch') {
                        batchIntervalRow.style.display = '';
                    } else {
                        batchIntervalRow.style.display = 'none';
                    }
                });
            }
        });

        // 手动执行优惠券同步
        function performManualCouponSync() {
            if (!confirm('<?php _e('Are you sure you want to run manual coupon synchronization? This may take some time.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_manual_coupon_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_manual_coupon_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('Manual coupon synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    // 刷新同步状态显示
                    location.reload();
                } else {
                    alert('<?php _e('Manual coupon synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 查看优惠券同步日志
        function viewCouponSyncLogs() {
            // 这里可以打开一个新窗口或模态框显示日志
            window.open('<?php echo admin_url('admin.php?page=yxjto-coupon-sync-logs'); ?>', '_blank');
        }

        // 刷新同步状态
        function refreshSyncStatus() {
            location.reload();
        }

        // SQL直接删除所有优惠券
        function sqlDeleteAllCoupons() {
            // 双重确认，因为这是不可逆的危险操作
            if (!confirm('<?php _e('⚠️ DANGER: This will PERMANENTLY DELETE ALL COUPONS from ALL DATABASES using direct SQL queries!', 'yxjto-gateway'); ?>')) {
                return;
            }
            
            if (!confirm('<?php _e('Are you absolutely sure? This action cannot be undone and will delete ALL coupons from ALL connected databases!', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Deleting...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_sql_delete_all_coupons',
                nonce: '<?php echo wp_create_nonce('yxjto_sql_delete_all_coupons'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('All coupons have been deleted successfully from all databases!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    refreshSyncStatus();
                } else {
                    alert('<?php _e('SQL deletion failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 同步回收站状态
        function syncTrashStatus() {
            if (!confirm('<?php _e('Are you sure you want to sync trash status across all databases? This will ensure trashed coupons are consistent across all databases.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_sync_trash_status',
                nonce: '<?php echo wp_create_nonce('yxjto_sync_trash_status'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('Trash status synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    refreshSyncStatus();
                } else {
                    alert('<?php _e('Trash status synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 清理回收站中的优惠券
        function cleanupTrashedCoupons() {
            if (!confirm('<?php _e('Are you sure you want to permanently delete all trashed coupons from all databases? This action cannot be undone!', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Cleaning...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_cleanup_trashed_coupons',
                nonce: '<?php echo wp_create_nonce('yxjto_cleanup_trashed_coupons'); ?>'
            }).done(function(response) {
                if (response.success) {
                    alert('<?php _e('Trashed coupons cleanup completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''));
                    refreshSyncStatus();
                } else {
                    alert('<?php _e('Trashed coupons cleanup failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'));
                }
            }).fail(function() {
                alert('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 显示同步结果消息
        function showSyncMessage(message, type) {
            const messageDiv = document.getElementById('sync-result-message');
            if (messageDiv) {
                messageDiv.textContent = message;
                messageDiv.className = type;
                messageDiv.style.display = 'block';
                
                // 5秒后自动隐藏消息
                setTimeout(function() {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }

        // 配送同步相关函数
        
        // 创建配送测试数据
        function createTestShippingData() {
            if (!confirm('<?php _e('Are you sure you want to create test shipping data? This will add sample zones, methods, and classes.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Creating...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_create_test_shipping_data',
                nonce: '<?php echo wp_create_nonce('yxjto_create_test_shipping_data'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Test shipping data created successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    showShippingSyncMessage('<?php _e('Failed to create test shipping data:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 运行完整配送同步
        function runFullShippingSync() {
            if (!confirm('<?php _e('Are you sure you want to run full shipping synchronization? This may take some time and will sync to all databases.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_run_full_shipping_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_run_full_shipping_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Full shipping synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    showShippingSyncMessage('<?php _e('Full shipping synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 调试配送同步状态
        function debugShippingSync() {
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Debugging...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_debug_shipping_sync',
                nonce: '<?php echo wp_create_nonce('yxjto_debug_shipping_sync'); ?>'
            }).done(function(response) {
                if (response.success) {
                    // 在新窗口中显示调试信息
                    const debugWindow = window.open('', 'shippingDebug', 'width=800,height=600,scrollbars=yes');
                    debugWindow.document.write('<html><head><title>Shipping Debug Info</title></head><body>');
                    debugWindow.document.write('<h2>Shipping Sync Debug Information</h2>');
                    debugWindow.document.write('<pre>' + (response.data.debug_info || 'No debug information available') + '</pre>');
                    debugWindow.document.write('</body></html>');
                    debugWindow.document.close();
                } else {
                    showShippingSyncMessage('<?php _e('Debug operation failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 查看配送同步日志
        function viewShippingSyncLogs() {
            // 在新窗口中显示日志
            window.open('<?php echo admin_url('admin.php?page=yxjto-shipping-sync-logs'); ?>', '_blank');
        }

        // 刷新配送状态
        function refreshShippingStatus() {
            location.reload();
        }

        // 同步特定区域
        function syncSpecificZone() {
            const zoneId = prompt('<?php _e('Please enter the zone ID to sync:', 'yxjto-gateway'); ?>');
            if (!zoneId || isNaN(zoneId)) {
                alert('<?php _e('Please enter a valid zone ID.', 'yxjto-gateway'); ?>');
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Syncing...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_sync_specific_zone',
                zone_id: zoneId,
                nonce: '<?php echo wp_create_nonce('yxjto_sync_specific_zone'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Zone synchronization completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                } else {
                    showShippingSyncMessage('<?php _e('Zone synchronization failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 验证配送数据完整性
        function validateShippingData() {
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Validating...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_validate_shipping_data',
                nonce: '<?php echo wp_create_nonce('yxjto_validate_shipping_data'); ?>'
            }).done(function(response) {
                if (response.success) {
                    // 在新窗口中显示验证结果
                    const validationWindow = window.open('', 'shippingValidation', 'width=800,height=600,scrollbars=yes');
                    validationWindow.document.write('<html><head><title>Shipping Data Validation</title></head><body>');
                    validationWindow.document.write('<h2>Shipping Data Validation Results</h2>');
                    validationWindow.document.write('<pre>' + (response.data.validation_report || 'No validation report available') + '</pre>');
                    validationWindow.document.write('</body></html>');
                    validationWindow.document.close();
                } else {
                    showShippingSyncMessage('<?php _e('Data validation failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 清理孤立数据
        function cleanupOrphanedData() {
            if (!confirm('<?php _e('Are you sure you want to cleanup orphaned shipping data? This will remove data that is not properly linked.', 'yxjto-gateway'); ?>')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '<?php _e('Cleaning...', 'yxjto-gateway'); ?>';

            jQuery.post(ajaxurl, {
                action: 'yxjto_cleanup_orphaned_shipping_data',
                nonce: '<?php echo wp_create_nonce('yxjto_cleanup_orphaned_shipping_data'); ?>'
            }).done(function(response) {
                if (response.success) {
                    showShippingSyncMessage('<?php _e('Orphaned data cleanup completed successfully!', 'yxjto-gateway'); ?>' + 
                          (response.data.message ? '\n' + response.data.message : ''), 'success');
                    refreshShippingStatus();
                } else {
                    showShippingSyncMessage('<?php _e('Orphaned data cleanup failed:', 'yxjto-gateway'); ?> ' + (response.data || 'Unknown error'), 'error');
                }
            }).fail(function() {
                showShippingSyncMessage('<?php _e('Request failed. Please try again.', 'yxjto-gateway'); ?>', 'error');
            }).always(function() {
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // 显示配送同步结果消息
        function showShippingSyncMessage(message, type) {
            const messageDiv = document.getElementById('shipping-sync-result-message');
            if (messageDiv) {
                messageDiv.textContent = message;
                messageDiv.className = 'notice notice-' + type;
                messageDiv.style.display = 'block';
                
                // 5秒后自动隐藏消息
                setTimeout(function() {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }
        </script>
        <?php
    }

    /**
     * AJAX处理：保存数据库配置
     */
    public static function ajax_save_database() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_gateway_save_database')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $database_key = sanitize_key($_POST['database_key']);
            $name = sanitize_text_field($_POST['name']);
            $host = sanitize_text_field($_POST['host']);
            $database = sanitize_text_field($_POST['database']);
            $username = sanitize_text_field($_POST['username']);
            $password = $_POST['password']; // 不要sanitize密码
            $charset = sanitize_text_field($_POST['charset']);
            $enabled = isset($_POST['enabled']);

            // 验证必填字段
            if (empty($name) || empty($host) || empty($database) || empty($username)) {
                wp_send_json_error(__('All fields except password are required', 'yxjto-gateway'));
                return;
            }

            // 获取现有配置
            $databases = WP_Multi_DB_Config_Manager::get_databases();

            // 如果是新增，生成唯一key
            if (empty($database_key)) {
                $database_key = sanitize_key(strtolower(str_replace(' ', '_', $name)));
                $counter = 1;
                $original_key = $database_key;
                while (isset($databases[$database_key])) {
                    $database_key = $original_key . '_' . $counter;
                    $counter++;
                }
            }

            // 保存配置
            $databases[$database_key] = [
                'name' => $name,
                'host' => $host,
                'database' => $database,
                'username' => $username,
                'password' => $password,
                'charset' => $charset,
                'enabled' => $enabled
            ];

            if (WP_Multi_DB_Config_Manager::save_databases($databases)) {
                wp_send_json_success(__('Database configuration saved successfully', 'yxjto-gateway'));
            } else {
                wp_send_json_error(__('Failed to save database configuration', 'yxjto-gateway'));
            }

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * AJAX处理：保存URL规则
     */
    public static function ajax_save_url_rule() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_gateway_save_url_rule')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $rule_index = $_POST['rule_index'];
            $parameter = sanitize_key($_POST['parameter']);
            $value = sanitize_text_field($_POST['value']);
            $database = sanitize_key($_POST['database']);
            $enabled = isset($_POST['enabled']);

            // 验证必填字段
            if (empty($parameter) || empty($value) || empty($database)) {
                wp_send_json_error(__('All fields are required', 'yxjto-gateway'));
                return;
            }

            // 获取现有规则
            $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();

            $new_rule = [
                'parameter' => $parameter,
                'value' => $value,
                'database' => $database,
                'enabled' => $enabled
            ];

            if ($rule_index !== '' && isset($url_rules[$rule_index])) {
                // 编辑现有规则
                $url_rules[$rule_index] = $new_rule;
            } else {
                // 添加新规则
                $url_rules[] = $new_rule;
            }

            if (WP_Multi_DB_Config_Manager::save_url_rules($url_rules)) {
                wp_send_json_success(__('URL rule saved successfully', 'yxjto-gateway'));
            } else {
                wp_send_json_error(__('Failed to save URL rule', 'yxjto-gateway'));
            }

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * AJAX处理：保存IP规则
     */
    public static function ajax_save_ip_rule() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_gateway_save_ip_rule')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            $rule_index = $_POST['rule_index'];
            $ip_range = sanitize_text_field($_POST['ip_range']);
            $database = sanitize_key($_POST['database']);
            $enabled = isset($_POST['enabled']);

            // 验证必填字段
            if (empty($ip_range) || empty($database)) {
                wp_send_json_error(__('All fields are required', 'yxjto-gateway'));
                return;
            }

            // 简单的IP格式验证
            if (!self::validate_ip_range($ip_range)) {
                wp_send_json_error(__('Invalid IP range format', 'yxjto-gateway'));
                return;
            }

            // 获取现有规则
            $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();

            $new_rule = [
                'ip_range' => $ip_range,
                'database' => $database,
                'enabled' => $enabled
            ];

            if ($rule_index !== '' && isset($ip_rules[$rule_index])) {
                // 编辑现有规则
                $ip_rules[$rule_index] = $new_rule;
            } else {
                // 添加新规则
                $ip_rules[] = $new_rule;
            }

            if (WP_Multi_DB_Config_Manager::save_ip_rules($ip_rules)) {
                wp_send_json_success(__('IP rule saved successfully', 'yxjto-gateway'));
            } else {
                wp_send_json_error(__('Failed to save IP rule', 'yxjto-gateway'));
            }

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * 验证IP范围格式
     */
    private static function validate_ip_range($ip_range) {
        // 单个IP地址
        if (filter_var($ip_range, FILTER_VALIDATE_IP)) {
            return true;
        }

        // CIDR格式 (例如: ***********/24)
        if (strpos($ip_range, '/') !== false) {
            list($ip, $mask) = explode('/', $ip_range);
            if (filter_var($ip, FILTER_VALIDATE_IP) && is_numeric($mask) && $mask >= 0 && $mask <= 32) {
                return true;
            }
        }

        // IP范围格式 (例如: ***********-***********00)
        if (strpos($ip_range, '-') !== false) {
            list($start_ip, $end_ip) = explode('-', $ip_range);
            if (filter_var(trim($start_ip), FILTER_VALIDATE_IP) && filter_var(trim($end_ip), FILTER_VALIDATE_IP)) {
                return true;
            }
        }

        return false;
    }

    /**
     * AJAX启用wp-config集成
     */
    public static function ajax_enable_wp_config() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'yxjto-gateway'));
        }

        $result = WP_Multi_DB_Config_Manager::enable_wp_config_integration();

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX禁用wp-config集成
     */
    public static function ajax_disable_wp_config() {
        check_ajax_referer('yxjto_gateway_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'yxjto-gateway'));
        }

        $result = WP_Multi_DB_Config_Manager::disable_wp_config_integration();

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * 处理删除数据库配置
     */
    private static function handle_delete_database($key) {
        if ($key === 'default') {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error is-dismissible"><p>' .
                     __('Cannot delete the default database configuration.', 'yxjto-gateway') .
                     '</p></div>';
            });
            return;
        }

        $result = WP_Multi_DB_Config_Manager::delete_database($key);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' .
                     __('Database configuration deleted successfully.', 'yxjto-gateway') .
                     '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error is-dismissible"><p>' .
                     __('Failed to delete database configuration.', 'yxjto-gateway') .
                     '</p></div>';
            });
        }
    }

    /**
     * 处理删除URL规则
     */
    private static function handle_delete_url_rule($index) {
        $url_rules = WP_Multi_DB_Config_Manager::get_url_rules();

        if (isset($url_rules[$index])) {
            unset($url_rules[$index]);
            // 重新索引数组
            $url_rules = array_values($url_rules);

            $result = WP_Multi_DB_Config_Manager::save_url_rules($url_rules);

            if ($result) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success is-dismissible"><p>' .
                         __('URL rule deleted successfully.', 'yxjto-gateway') .
                         '</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error is-dismissible"><p>' .
                         __('Failed to delete URL rule.', 'yxjto-gateway') .
                         '</p></div>';
                });
            }
        }
    }

    /**
     * 处理删除IP规则
     */
    private static function handle_delete_ip_rule($index) {
        $ip_rules = WP_Multi_DB_Config_Manager::get_ip_rules();

        if (isset($ip_rules[$index])) {
            unset($ip_rules[$index]);
            // 重新索引数组
            $ip_rules = array_values($ip_rules);

            $result = WP_Multi_DB_Config_Manager::save_ip_rules($ip_rules);

            if ($result) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success is-dismissible"><p>' .
                         __('IP rule deleted successfully.', 'yxjto-gateway') .
                         '</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error is-dismissible"><p>' .
                         __('Failed to delete IP rule.', 'yxjto-gateway') .
                         '</p></div>';
                });
            }
        }
    }

    /**
     * 渲染数据库状态调试信息
     */
    private static function render_debug_status() {
        $gateway = YXJTO_Gateway::get_instance();

        // 获取当前数据库状态
        $current_db = $gateway->get_current_database();
        $db_info = $gateway->get_actual_database_info();
        $db_list = $gateway->get_database_list();

        ?>
        <div class="debug-status-grid">
            <!-- 基本信息 -->
            <div class="debug-card">
                <h3><?php _e('Basic Information', 'yxjto-gateway'); ?></h3>
                <table class="debug-table">
                    <tr>
                        <td><strong><?php _e('Current Time', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo date('Y-m-d H:i:s'); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('WordPress Version', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo get_bloginfo('version'); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('PHP Version', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Plugin Version', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo defined('YXJTO_GATEWAY_VERSION') ? YXJTO_GATEWAY_VERSION : 'Unknown'; ?></td>
                    </tr>
                </table>
            </div>

            <!-- WordPress数据库常量 -->
            <div class="debug-card">
                <h3><?php _e('WordPress Database Constants', 'yxjto-gateway'); ?></h3>
                <table class="debug-table">
                    <tr>
                        <td><strong>DB_NAME</strong></td>
                        <td><?php echo defined('DB_NAME') ? DB_NAME : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>DB_HOST</strong></td>
                        <td><?php echo defined('DB_HOST') ? DB_HOST : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>DB_USER</strong></td>
                        <td><?php echo defined('DB_USER') ? DB_USER : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>DB_CHARSET</strong></td>
                        <td><?php echo defined('DB_CHARSET') ? DB_CHARSET : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>DB_COLLATE</strong></td>
                        <td><?php echo defined('DB_COLLATE') ? DB_COLLATE : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                </table>
            </div>

            <!-- WP Multi DB 常量 -->
            <div class="debug-card">
                <h3><?php _e('WP Multi DB Constants', 'yxjto-gateway'); ?></h3>
                <table class="debug-table">
                    <tr>
                        <td><strong>WP_MULTI_DB_SWITCHED</strong></td>
                        <td>
                            <?php
                            if (defined('WP_MULTI_DB_SWITCHED')) {
                                echo WP_MULTI_DB_SWITCHED ? '<span class="debug-true">true</span>' : '<span class="debug-false">false</span>';
                            } else {
                                echo '<span class="debug-undefined">Not defined</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>WP_MULTI_DB_CURRENT_DATABASE</strong></td>
                        <td><?php echo defined('WP_MULTI_DB_CURRENT_DATABASE') ? WP_MULTI_DB_CURRENT_DATABASE : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>WP_MULTI_DB_SWITCH_RULE</strong></td>
                        <td><?php echo defined('WP_MULTI_DB_SWITCH_RULE') ? WP_MULTI_DB_SWITCH_RULE : '<span class="debug-undefined">Not defined</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>WP_MULTI_DB_HOOK_EXECUTED</strong></td>
                        <td>
                            <?php
                            if (defined('WP_MULTI_DB_HOOK_EXECUTED')) {
                                echo WP_MULTI_DB_HOOK_EXECUTED ? '<span class="debug-true">true</span>' : '<span class="debug-false">false</span>';
                            } else {
                                echo '<span class="debug-undefined">Not defined</span>';
                            }
                            ?>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- YXJTO Gateway 状态 -->
            <div class="debug-card">
                <h3><?php _e('YXJTO Gateway Status', 'yxjto-gateway'); ?></h3>
                <table class="debug-table">
                    <tr>
                        <td><strong><?php _e('get_current_database()', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-current-db"><?php echo esc_html($current_db); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Configured Databases', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo implode(', ', $db_list); ?></td>
                    </tr>
                    <?php if ($db_info['success']): ?>
                    <tr>
                        <td><strong><?php _e('Actual Connected Database', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-actual-db"><?php echo esc_html($db_info['database']); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Database Host', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo esc_html($db_info['host']); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Database User', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo esc_html($db_info['user']); ?></td>
                    </tr>
                    <?php else: ?>
                    <tr>
                        <td><strong><?php _e('Database Connection Error', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-error"><?php echo esc_html($db_info['message']); ?></span></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td><strong><?php _e('Internal State', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo esc_html($db_info['internal_state']); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('WP-Config State', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo $db_info['wp_config_state'] ? esc_html($db_info['wp_config_state']) : '<span class="debug-null">null</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('WP-Config Switched', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo $db_info['wp_config_switched'] ? '<span class="debug-true">true</span>' : '<span class="debug-false">false</span>'; ?></td>
                    </tr>
                </table>
            </div>

            <!-- 全局 $wpdb 信息 -->
            <div class="debug-card">
                <h3><?php _e('Global $wpdb Information', 'yxjto-gateway'); ?></h3>
                <table class="debug-table">
                    <?php global $wpdb; ?>
                    <?php if ($wpdb): ?>
                    <tr>
                        <td><strong><?php _e('wpdb Class', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo get_class($wpdb); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Table Prefix', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo $wpdb->prefix; ?></td>
                    </tr>
                    <?php
                    try {
                        $actual_db = $wpdb->get_var("SELECT DATABASE()");
                        $connection_id = $wpdb->get_var("SELECT CONNECTION_ID()");
                        $version = $wpdb->get_var("SELECT VERSION()");
                    ?>
                    <tr>
                        <td><strong><?php _e('Actual Connected Database', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-wpdb-db"><?php echo esc_html($actual_db); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('Connection ID', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo esc_html($connection_id); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('MySQL Version', 'yxjto-gateway'); ?></strong></td>
                        <td><?php echo esc_html($version); ?></td>
                    </tr>
                    <?php } catch (Exception $e) { ?>
                    <tr>
                        <td><strong><?php _e('Database Query Error', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-error"><?php echo esc_html($e->getMessage()); ?></span></td>
                    </tr>
                    <?php } ?>
                    <?php else: ?>
                    <tr>
                        <td><strong><?php _e('Global $wpdb', 'yxjto-gateway'); ?></strong></td>
                        <td><span class="debug-error"><?php _e('Not available', 'yxjto-gateway'); ?></span></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>

            <!-- 状态一致性检查 -->
            <div class="debug-card">
                <h3><?php _e('Status Consistency Check', 'yxjto-gateway'); ?></h3>
                <?php
                $issues = [];
                $config_manager = WP_Multi_DB_Config_Manager::get_databases();

                // 检查内部状态与实际连接是否一致
                if ($db_info['success']) {
                    $actual_db_name = $db_info['database'];

                    // 查找匹配的配置
                    $matching_config = null;
                    foreach ($config_manager as $key => $config) {
                        if (isset($config['database']) && $config['database'] === $actual_db_name) {
                            $matching_config = $key;
                            break;
                        }
                    }

                    if ($matching_config) {
                        if ($current_db !== $matching_config) {
                            $issues[] = sprintf(__('Internal state (%s) does not match actual connection (%s)', 'yxjto-gateway'), $current_db, $matching_config);
                        }
                    } else {
                        $issues[] = sprintf(__('Actual connected database (%s) not found in configuration', 'yxjto-gateway'), $actual_db_name);
                    }
                }

                // 检查wp-config状态（注意：运行时状态与wp-config状态不同通常是正常的）
                if (defined('WP_MULTI_DB_SWITCHED') && WP_MULTI_DB_SWITCHED) {
                    if (defined('WP_MULTI_DB_CURRENT_DATABASE')) {
                        if ($current_db !== WP_MULTI_DB_CURRENT_DATABASE) {
                            // 这通常是正常的，因为wp-config在启动时设置，但运行时可能动态切换
                            // 只有在当前数据库是default且wp-config也是default时才认为有问题
                            if ($current_db === 'default' && WP_MULTI_DB_CURRENT_DATABASE !== 'default') {
                                $issues[] = sprintf(__('Runtime database is default but wp-config shows %s - possible switching failure', 'yxjto-gateway'), WP_MULTI_DB_CURRENT_DATABASE);
                            }
                            // 其他情况都是正常的动态切换
                        }
                    }
                }
                ?>

                <?php if (empty($issues)): ?>
                <div class="debug-success">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <?php _e('No status inconsistencies detected', 'yxjto-gateway'); ?>
                </div>
                <?php else: ?>
                <div class="debug-issues">
                    <span class="dashicons dashicons-warning"></span>
                    <?php _e('Issues detected:', 'yxjto-gateway'); ?>
                    <ul>
                        <?php foreach ($issues as $issue): ?>
                        <li><?php echo esc_html($issue); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX处理：刷新调试状态
     */
    public static function ajax_refresh_debug_status() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_gateway_nonce')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        ob_start();
        self::render_debug_status();
        $html = ob_get_clean();

        wp_send_json_success($html);
    }

    /**
     * AJAX处理：创建配送测试数据
     */
    public static function ajax_create_test_shipping_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_create_test_shipping_data')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $result = $shipping_replication->create_test_shipping_data();
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => $result['message'],
                        'details' => $result['details'] ?? []
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error creating test shipping data: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：运行完整配送同步
     */
    public static function ajax_run_full_shipping_sync() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_run_full_shipping_sync')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $result = $shipping_replication->batch_sync_all_shipping();
                
                if (is_array($result) && isset($result['success']) && $result['success']) {
                    wp_send_json_success([
                        'message' => isset($result['message']) ? $result['message'] : '同步完成',
                        'details' => isset($result['details']) ? $result['details'] : []
                    ]);
                } else {
                    $error_message = is_array($result) && isset($result['message']) ? $result['message'] : '同步失败';
                    wp_send_json_error($error_message);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error running full shipping sync: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：调试配送同步状态
     */
    public static function ajax_debug_shipping_sync() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_debug_shipping_sync')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $debug_info = $shipping_replication->debug_sync_status();
                
                wp_send_json_success([
                    'debug_info' => $debug_info
                ]);
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error debugging shipping sync: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：同步特定区域
     */
    public static function ajax_sync_specific_zone() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_sync_specific_zone')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $zone_id = intval($_POST['zone_id']);
        if (!$zone_id) {
            wp_send_json_error(__('Invalid zone ID provided.', 'yxjto-gateway'));
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $result = $shipping_replication->sync_specific_zone($zone_id);
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => $result['message']
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error syncing specific zone: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：验证配送数据完整性
     */
    public static function ajax_validate_shipping_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_validate_shipping_data')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $validation_report = $shipping_replication->validate_data_integrity();
                
                wp_send_json_success([
                    'validation_report' => $validation_report
                ]);
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error validating shipping data: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * AJAX处理：清理孤立的配送数据
     */
    public static function ajax_cleanup_orphaned_shipping_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yxjto_cleanup_orphaned_shipping_data')) {
            wp_die('Security check failed');
        }

        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        try {
            // 检查是否存在配送复制类
            if (class_exists('YXJTO_Shipping_Replication') && function_exists('YXJTO_Shipping_Replication')) {
                $shipping_replication = YXJTO_Shipping_Replication();
                $result = $shipping_replication->cleanup_orphaned_data();
                
                if ($result['success']) {
                    wp_send_json_success([
                        'message' => $result['message']
                    ]);
                } else {
                    wp_send_json_error($result['message']);
                }
            } else {
                wp_send_json_error(__('Shipping replication class not found. Please check if the plugin is properly loaded.', 'yxjto-gateway'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error cleaning up orphaned shipping data: ', 'yxjto-gateway') . $e->getMessage());
        }
    }

    /**
     * 渲染价格调整配置标签页
     */
    public static function render_price_adjustment_tab() {
        // 加载价格调整配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            echo '<div class="notice notice-error"><p>' . __('Price adjustment configuration class not found.', 'yxjto-gateway') . '</p></div>';
            return;
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            echo '<div class="notice notice-error"><p>' . __('Price adjustment configuration class not available.', 'yxjto-gateway') . '</p></div>';
            return;
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();
        $config = $config_manager->get_all();
        ?>
        <div class="price-adjustment-config">
            <h3><?php _e('Price Adjustment Settings', 'yxjto-gateway'); ?></h3>
            <p class="description"><?php _e('Configure how price differences are adjusted before payment processing.', 'yxjto-gateway'); ?></p>

            <form id="price-adjustment-config-form">
                <?php wp_nonce_field('yxjto_price_adjustment_config', 'price_adjustment_nonce'); ?>

                <!-- 基本设置 -->
                <div class="config-section">
                    <h4><?php _e('Basic Settings', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Price Adjustment', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enabled" value="1" <?php checked($config['enabled']); ?>>
                                    <?php _e('Enable automatic price adjustment before payment', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('When enabled, the system will automatically adjust item prices to match the order total.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Adjustment Strategy', 'yxjto-gateway'); ?></th>
                            <td>
                                <select name="strategy">
                                    <option value="auto" <?php selected($config['strategy'], 'auto'); ?>><?php _e('Auto (Recommended)', 'yxjto-gateway'); ?></option>
                                    <option value="proportional" <?php selected($config['strategy'], 'proportional'); ?>><?php _e('Proportional Distribution', 'yxjto-gateway'); ?></option>
                                    <option value="equal_distribution" <?php selected($config['strategy'], 'equal_distribution'); ?>><?php _e('Equal Distribution', 'yxjto-gateway'); ?></option>
                                    <option value="highest_price_first" <?php selected($config['strategy'], 'highest_price_first'); ?>><?php _e('Highest Price First', 'yxjto-gateway'); ?></option>
                                </select>
                                <p class="description">
                                    <?php _e('Choose how price differences should be distributed among items:', 'yxjto-gateway'); ?><br>
                                    <strong><?php _e('Auto:', 'yxjto-gateway'); ?></strong> <?php _e('Automatically selects the best strategy based on the situation.', 'yxjto-gateway'); ?><br>
                                    <strong><?php _e('Proportional:', 'yxjto-gateway'); ?></strong> <?php _e('Distributes differences proportionally based on item prices.', 'yxjto-gateway'); ?><br>
                                    <strong><?php _e('Equal Distribution:', 'yxjto-gateway'); ?></strong> <?php _e('Distributes differences equally among all items.', 'yxjto-gateway'); ?><br>
                                    <strong><?php _e('Highest Price First:', 'yxjto-gateway'); ?></strong> <?php _e('Adjusts the highest-priced items first.', 'yxjto-gateway'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Minimum Adjustment Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="min_adjustment_threshold" value="<?php echo esc_attr($config['min_adjustment_threshold']); ?>" step="0.01" min="0" style="width: 100px;">
                                <p class="description"><?php _e('Minimum price difference to trigger adjustment (in currency units). Differences smaller than this will be ignored.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Maximum Adjustment Percentage', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="max_adjustment_percentage" value="<?php echo esc_attr($config['max_adjustment_percentage']); ?>" min="1" max="100" style="width: 100px;">%
                                <p class="description"><?php _e('Maximum allowed adjustment as percentage of original total. Adjustments exceeding this limit will be rejected.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 验证设置 -->
                <div class="config-section">
                    <h4><?php _e('Validation Settings', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Validation', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_validation" value="1" <?php checked($config['enable_validation']); ?>>
                                    <?php _e('Validate shipping, discount, and tax amounts before adjustment', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('When enabled, the system will verify the accuracy of shipping, discount, and tax amounts before making adjustments.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Validation Rules', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="validation_rules[validate_shipping]" value="1" <?php checked($config['validation_rules']['validate_shipping']); ?>>
                                    <?php _e('Validate shipping amounts', 'yxjto-gateway'); ?>
                                </label><br>

                                <label>
                                    <input type="checkbox" name="validation_rules[validate_discount]" value="1" <?php checked($config['validation_rules']['validate_discount']); ?>>
                                    <?php _e('Validate discount amounts', 'yxjto-gateway'); ?>
                                </label><br>

                                <label>
                                    <input type="checkbox" name="validation_rules[validate_tax]" value="1" <?php checked($config['validation_rules']['validate_tax']); ?>>
                                    <?php _e('Validate tax amounts', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('Select which amounts should be validated against their calculated values.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 策略偏好设置 -->
                <div class="config-section">
                    <h4><?php _e('Strategy Preferences (Auto Mode)', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Small Difference Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="strategy_preferences[small_difference_threshold]" value="<?php echo esc_attr($config['strategy_preferences']['small_difference_threshold']); ?>" step="0.01" min="0" style="width: 100px;">
                                <p class="description"><?php _e('Threshold for considering a price difference as "small". Used in auto strategy selection.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Large Difference Threshold', 'yxjto-gateway'); ?></th>
                            <td>
                                <input type="number" name="strategy_preferences[large_difference_threshold]" value="<?php echo esc_attr($config['strategy_preferences']['large_difference_threshold']); ?>" step="0.01" min="0" style="width: 100px;">
                                <p class="description"><?php _e('Threshold for considering a price difference as "large". Used in auto strategy selection.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Strategy Preferences', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="strategy_preferences[prefer_proportional_for_small]" value="1" <?php checked($config['strategy_preferences']['prefer_proportional_for_small']); ?>>
                                    <?php _e('Prefer proportional distribution for small differences', 'yxjto-gateway'); ?>
                                </label><br>

                                <label>
                                    <input type="checkbox" name="strategy_preferences[prefer_highest_first_for_large]" value="1" <?php checked($config['strategy_preferences']['prefer_highest_first_for_large']); ?>>
                                    <?php _e('Prefer highest price first for large differences', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('Configure which strategies are preferred for different types of price differences.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 其他设置 -->
                <div class="config-section">
                    <h4><?php _e('Other Settings', 'yxjto-gateway'); ?></h4>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Logging', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_logging" value="1" <?php checked($config['enable_logging']); ?>>
                                    <?php _e('Log price adjustment activities for debugging', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('When enabled, detailed logs will be recorded for all price adjustment activities.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Enable Recalculation', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_recalculation" value="1" <?php checked($config['enable_recalculation']); ?>>
                                    <?php _e('Recalculate order totals after price adjustment', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('When enabled, order totals will be recalculated after price adjustments are applied.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Fallback to Legacy', 'yxjto-gateway'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="fallback_to_legacy" value="1" <?php checked($config['fallback_to_legacy']); ?>>
                                    <?php _e('Use legacy price adjustment method if new method fails', 'yxjto-gateway'); ?>
                                </label>
                                <p class="description"><?php _e('When enabled, the system will fall back to the legacy price adjustment method if the new method encounters errors.', 'yxjto-gateway'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 操作按钮 -->
                <div class="config-actions">
                    <button type="submit" class="button-primary"><?php _e('Save Settings', 'yxjto-gateway'); ?></button>
                    <button type="button" id="reset-price-config" class="button"><?php _e('Reset to Defaults', 'yxjto-gateway'); ?></button>
                    <button type="button" id="test-price-adjustment" class="button"><?php _e('Test Adjustment', 'yxjto-gateway'); ?></button>
                    <button type="button" id="export-price-config" class="button"><?php _e('Export Config', 'yxjto-gateway'); ?></button>
                    <button type="button" id="import-price-config" class="button"><?php _e('Import Config', 'yxjto-gateway'); ?></button>
                </div>
            </form>

            <!-- 测试结果区域 -->
            <div id="price-test-results" style="display: none; margin-top: 20px;">
                <h4><?php _e('Test Results', 'yxjto-gateway'); ?></h4>
                <div id="price-test-output" style="background: #fff; padding: 10px; border: 1px solid #ddd; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
            </div>

            <!-- 导入配置区域 -->
            <div id="price-import-config-area" style="display: none; margin-top: 20px;">
                <h4><?php _e('Import Configuration', 'yxjto-gateway'); ?></h4>
                <textarea id="price-import-config-text" rows="10" style="width: 100%; font-family: monospace;" placeholder="<?php _e('Paste configuration JSON here...', 'yxjto-gateway'); ?>"></textarea><br>
                <button type="button" id="do-price-import" class="button" style="margin-top: 10px;"><?php _e('Import', 'yxjto-gateway'); ?></button>
                <button type="button" id="cancel-price-import" class="button" style="margin-top: 10px;"><?php _e('Cancel', 'yxjto-gateway'); ?></button>
            </div>
        </div>

        <style>
        .price-adjustment-config .config-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border: 1px solid #c3c4c7;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }

        .price-adjustment-config .config-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #23282d;
        }

        .price-adjustment-config .config-actions {
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid #c3c4c7;
        }

        .price-adjustment-config .config-actions .button {
            margin-right: 10px;
        }

        .price-adjustment-config .form-table th {
            width: 200px;
        }

        .price-adjustment-config .description {
            font-style: italic;
            color: #666;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // 保存配置
            $('#price-adjustment-config-form').on('submit', function(e) {
                e.preventDefault();

                var formData = $(this).serialize();
                formData += '&action=yxjto_save_price_adjustment_config';

                $.post(ajaxurl, formData, function(response) {
                    if (response.success) {
                        alert('<?php _e('Settings saved successfully!', 'yxjto-gateway'); ?>');
                    } else {
                        alert('<?php _e('Failed to save settings: ', 'yxjto-gateway'); ?>' + response.data);
                    }
                });
            });

            // 重置配置
            $('#reset-price-config').on('click', function() {
                if (confirm('<?php _e('Are you sure you want to reset all settings to defaults?', 'yxjto-gateway'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'yxjto_reset_price_adjustment_config',
                        nonce: $('[name="price_adjustment_nonce"]').val()
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('<?php _e('Failed to reset settings: ', 'yxjto-gateway'); ?>' + response.data);
                        }
                    });
                }
            });

            // 测试调整
            $('#test-price-adjustment').on('click', function() {
                $.post(ajaxurl, {
                    action: 'yxjto_test_price_adjustment',
                    nonce: $('[name="price_adjustment_nonce"]').val()
                }, function(response) {
                    $('#price-test-output').text(response.data);
                    $('#price-test-results').show();
                });
            });

            // 导出配置
            $('#export-price-config').on('click', function() {
                $.post(ajaxurl, {
                    action: 'yxjto_export_price_adjustment_config',
                    nonce: $('[name="price_adjustment_nonce"]').val()
                }, function(response) {
                    if (response.success) {
                        var blob = new Blob([response.data], {type: 'application/json'});
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = 'price-adjustment-config.json';
                        a.click();
                        window.URL.revokeObjectURL(url);
                    }
                });
            });

            // 显示导入区域
            $('#import-price-config').on('click', function() {
                $('#price-import-config-area').show();
            });

            // 执行导入
            $('#do-price-import').on('click', function() {
                var configText = $('#price-import-config-text').val();
                if (!configText.trim()) {
                    alert('<?php _e('Please paste configuration JSON.', 'yxjto-gateway'); ?>');
                    return;
                }

                $.post(ajaxurl, {
                    action: 'yxjto_import_price_adjustment_config',
                    nonce: $('[name="price_adjustment_nonce"]').val(),
                    config: configText
                }, function(response) {
                    if (response.success) {
                        alert('<?php _e('Configuration imported successfully!', 'yxjto-gateway'); ?>');
                        location.reload();
                    } else {
                        alert('<?php _e('Failed to import configuration: ', 'yxjto-gateway'); ?>' + response.data);
                    }
                });
            });

            // 取消导入
            $('#cancel-price-import').on('click', function() {
                $('#price-import-config-area').hide();
                $('#price-import-config-text').val('');
            });
        });
        </script>
        <?php
    }

    /**
     * AJAX: 保存价格调整配置
     */
    public static function ajax_save_price_adjustment_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'price_adjustment_nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }

        // 加载配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            wp_send_json_error(__('Price adjustment configuration class not found.', 'yxjto-gateway'));
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            wp_send_json_error(__('Price adjustment configuration class not available.', 'yxjto-gateway'));
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();

        // 处理表单数据
        $config = [];
        $config['enabled'] = isset($_POST['enabled']);
        $config['strategy'] = sanitize_text_field($_POST['strategy'] ?? 'auto');
        $config['enable_validation'] = isset($_POST['enable_validation']);
        $config['enable_recalculation'] = isset($_POST['enable_recalculation']);
        $config['enable_logging'] = isset($_POST['enable_logging']);
        $config['fallback_to_legacy'] = isset($_POST['fallback_to_legacy']);
        $config['min_adjustment_threshold'] = floatval($_POST['min_adjustment_threshold'] ?? 0.01);
        $config['max_adjustment_percentage'] = intval($_POST['max_adjustment_percentage'] ?? 50);

        // 处理验证规则
        $config['validation_rules'] = [
            'validate_shipping' => isset($_POST['validation_rules']['validate_shipping']),
            'validate_discount' => isset($_POST['validation_rules']['validate_discount']),
            'validate_tax' => isset($_POST['validation_rules']['validate_tax'])
        ];

        // 处理策略偏好
        $config['strategy_preferences'] = [
            'small_difference_threshold' => floatval($_POST['strategy_preferences']['small_difference_threshold'] ?? 5.00),
            'large_difference_threshold' => floatval($_POST['strategy_preferences']['large_difference_threshold'] ?? 50.00),
            'prefer_proportional_for_small' => isset($_POST['strategy_preferences']['prefer_proportional_for_small']),
            'prefer_highest_first_for_large' => isset($_POST['strategy_preferences']['prefer_highest_first_for_large'])
        ];

        // 验证配置
        $validation = $config_manager->validate_config($config);
        if (!$validation['valid']) {
            wp_send_json_error(implode(', ', $validation['errors']));
        }

        // 保存配置
        foreach ($config as $key => $value) {
            $config_manager->set($key, $value);
        }

        if ($config_manager->save()) {
            wp_send_json_success(__('Settings saved successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to save settings.', 'yxjto-gateway'));
        }
    }

    /**
     * AJAX: 重置价格调整配置
     */
    public static function ajax_reset_price_adjustment_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }

        // 加载配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            wp_send_json_error(__('Price adjustment configuration class not found.', 'yxjto-gateway'));
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            wp_send_json_error(__('Price adjustment configuration class not available.', 'yxjto-gateway'));
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();

        if ($config_manager->reset()) {
            wp_send_json_success(__('Settings reset successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to reset settings.', 'yxjto-gateway'));
        }
    }

    /**
     * AJAX: 测试价格调整
     */
    public static function ajax_test_price_adjustment() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }

        // 加载配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            wp_send_json_error(__('Price adjustment configuration class not found.', 'yxjto-gateway'));
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            wp_send_json_error(__('Price adjustment configuration class not available.', 'yxjto-gateway'));
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();

        // 运行测试脚本
        ob_start();

        echo "Price Adjustment Test Results:\n";
        echo "=============================\n\n";
        echo "Current Configuration:\n";
        echo "- Enabled: " . ($config_manager->is_enabled() ? 'Yes' : 'No') . "\n";
        echo "- Strategy: " . $config_manager->get('strategy') . "\n";
        echo "- Min Threshold: " . $config_manager->get_min_threshold() . "\n";
        echo "- Max Adjustment: " . $config_manager->get_max_adjustment_percentage() . "%\n";
        echo "- Validation: " . ($config_manager->is_validation_enabled() ? 'Enabled' : 'Disabled') . "\n";
        echo "- Logging: " . ($config_manager->is_logging_enabled() ? 'Enabled' : 'Disabled') . "\n\n";

        echo "Strategy Recommendations:\n";
        echo "- Small difference (2.00): " . $config_manager->get_recommended_strategy(2.0, 3) . "\n";
        echo "- Medium difference (15.00): " . $config_manager->get_recommended_strategy(15.0, 3) . "\n";
        echo "- Large difference (60.00): " . $config_manager->get_recommended_strategy(60.0, 3) . "\n\n";

        echo "Adjustment Limits:\n";
        echo "- 100 to 110: " . ($config_manager->is_adjustment_within_limits(100, 110) ? 'Allowed' : 'Exceeded') . "\n";
        echo "- 100 to 200: " . ($config_manager->is_adjustment_within_limits(100, 200) ? 'Allowed' : 'Exceeded') . "\n\n";

        echo "Test Scenarios:\n";
        echo "Scenario 1: Order total 115, calculated 110 (difference: +5)\n";
        echo "- Recommended strategy: " . $config_manager->get_recommended_strategy(5.0, 3) . "\n";
        echo "- Within limits: " . ($config_manager->is_adjustment_within_limits(110, 115) ? 'Yes' : 'No') . "\n\n";

        echo "Scenario 2: Order total 95, calculated 100 (difference: -5)\n";
        echo "- Recommended strategy: " . $config_manager->get_recommended_strategy(-5.0, 2) . "\n";
        echo "- Within limits: " . ($config_manager->is_adjustment_within_limits(100, 95) ? 'Yes' : 'No') . "\n\n";

        echo "Configuration Status: Valid\n";
        echo "Test completed successfully.";

        $output = ob_get_clean();
        wp_send_json_success($output);
    }

    /**
     * AJAX: 导出价格调整配置
     */
    public static function ajax_export_price_adjustment_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }

        // 加载配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            wp_send_json_error(__('Price adjustment configuration class not found.', 'yxjto-gateway'));
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            wp_send_json_error(__('Price adjustment configuration class not available.', 'yxjto-gateway'));
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();
        $config_json = $config_manager->export_config();
        wp_send_json_success($config_json);
    }

    /**
     * AJAX: 导入价格调整配置
     */
    public static function ajax_import_price_adjustment_config() {
        check_ajax_referer('yxjto_price_adjustment_config', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'yxjto-gateway'));
        }

        // 加载配置管理器
        $config_file = YXJTO_GATEWAY_PLUGIN_DIR . 'includes/class-price-adjustment-config.php';
        if (!file_exists($config_file)) {
            wp_send_json_error(__('Price adjustment configuration class not found.', 'yxjto-gateway'));
        }

        require_once $config_file;

        if (!class_exists('YXJTO_Price_Adjustment_Config')) {
            wp_send_json_error(__('Price adjustment configuration class not available.', 'yxjto-gateway'));
        }

        $config_manager = YXJTO_Price_Adjustment_Config::get_instance();
        $config_json = sanitize_textarea_field($_POST['config'] ?? '');

        if ($config_manager->import_config($config_json)) {
            wp_send_json_success(__('Configuration imported successfully.', 'yxjto-gateway'));
        } else {
            wp_send_json_error(__('Failed to import configuration. Please check the JSON format.', 'yxjto-gateway'));
        }
    }
}
